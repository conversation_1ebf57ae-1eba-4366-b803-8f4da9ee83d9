<template>
  <el-dialog
    :title="modalTitle"
    :visible.sync="dialogVisible"
    width="80%"
    :before-close="handleClose">
    <el-row :gutter="20">
      <el-col :span="9">
        <el-card class="box-card" style=" height:64vh">
          <div slot="header" class="clearfix">
            <el-row :gutter="10">
              <el-col :span="18">
                <el-input
                  placeholder="输入关键字进行过滤"
                  v-model="filterText">
                </el-input>
              </el-col>
              <el-col :span="4" style="padding-top:2px;">
                <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
              </el-col>
            </el-row>
          </div>
          <checkeboxTree ref="tree" v-on:selectNode="selectNode" :treeList="treeList"
                         :checkedList="selectList" :personInfo="personInfo"
                         :selectType="selectType">
          </checkeboxTree>
        </el-card>
      </el-col>
      <el-col :span="15">
        <el-card class="box-card" shadow="never" style="height: 64vh">
          <div v-for="(staff, index) in checkedList">
            <div>
              <!--<el-tag-->
              <!--:key="staff.personId"-->
              <!--:closable="selectType == 'more'"-->
              <!--@close="handleCloseOne(staff, index)">-->
              <!--{{staff.personName}}-->
              <!--</el-tag>-->
              <div class="treeSelection margin-t5">
                <span class="float-left">{{ staff.personName }}</span>
                <i v-if="selectType == 'more'" class="el-icon-close icon iconfont"
                   @click="handleCloseOne(staff, index)"></i>
              </div>
              <el-form label-width="75px">
                <el-row>
                  <el-col :span="8">
                    <el-form-item label="类别">
                      <el-input v-model="staff.personTypeText" placeholder="职务" readonly v-if="selectType=='one'"></el-input>

                      <el-select
                        v-if="selectType=='more'"
                        v-model="staff.personType"
                        placeholder="类别"
                        clearable
                        @change="changePersonType(index)"
                        :style="{width: '100%'}"
                      >
                        <el-option
                          v-for="(item, index) in personInfoList"
                          :key="index"
                          :label="item.personTypeText"
                          :value="item.personType"
                        />
                      </el-select>
                      <!--                      <el-input v-model="staff.personTypeText" placeholder="类别" readonly></el-input>-->
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="职务">
                      <el-input v-model="staff.personDuty" placeholder="职务"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="办公电话">
                      <el-input v-model="staff.personPhone" placeholder="办公电话"></el-input>
                    </el-form-item>
                  </el-col>

                </el-row>
                <el-row>
                  <el-col :span="8">
                    <el-form-item label="电子邮箱">
                      <el-input type="email" v-model="staff.personEmail" placeholder="电子邮箱"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="传真">
                      <el-input v-model="staff.personFax" placeholder="传真"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="手机">
                      <el-input type="number" @input="handleInput($event,index)" v-model="staff.personMobilePhone"
                                placeholder="手机"></el-input>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>

                  <el-col :span="8">
                    <el-form-item label="地址">
                      <el-input v-model="staff.personAdress" placeholder="地址"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="邮编">
                      <el-input v-model="staff.personPostalCode" placeholder="邮编"></el-input>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    <span slot="footer" class="dialog-footer">
      <el-button size="mini" @click="handleClose()">取 消</el-button>
      <el-button size="mini" type="primary" @click="sub()">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script lang="ts">
import checkeboxTree from "./checkeboxTreeNew";
import {logger} from "runjs/lib/common";

export default {
  name: "orgStaffTreeModal",
  components: {checkeboxTree},
  props: {
    treeList: {
      type: Array,
      default: () => {
        return []
      }
    },
    dialogVisible: {
      type: Boolean,
      default: true
    },
    modalTitle: {
      type: String,
      default: ''
    },
    selectList: {//默认选中列表
      type: Array,
      default: () => {
        return []
      }
    },
    personInfoList: {//类别字典
      type: Array,
      default: () => {
        return []
      }
    },

    personInfo: {//人员类型信息
      type: Object,
      default: () => {
        return {}
      }
    },
    selectType: {//标识多选还是单选
      type: String,
      default: 'more'
    },
  },
  data() {
    return {
      filterText: '',
      defaultProps: {
        children: 'children',
        label: 'label'
      },
      checkedList: [],
    };
  },
  created() {
    this.checkedList = JSON.parse(JSON.stringify(this.selectList))
  },
  methods: {
    //校验手机号
    handleInput(value, index) {
      if (value.length > 11) {
        this.checkedList[index].personMobilePhone = value.slice(0, 11);
        return;
      }
    },

    /**关闭模态框*/
    handleClose() {
      this.$emit("closeModal");

    },
    changePersonType(index) {
      let personType = this.checkedList[index].personType;
      this.personInfoList.forEach(item => {
        if (item.personType == personType) {
          this.checkedList[index].personTypeText = item.personTypeText
        }
      })
    },
    /* 已选人员删除 */
    handleCloseOne(staff, index) {
      this.checkedList.splice(index, 1);
      this.$refs.tree.deleteTree(staff.personId,this.checkedList);
    },
    /**选择节点*/
    selectNode(data) {
      let dataList = data;
      let oldData = this.checkedList;
      dataList.forEach((item, index) => {
        oldData.forEach((obj, j) => {
          if (item.id == obj.id) {
            data[index] = obj
          }
        })
      })

      this.checkedList = data
    },
    handleQuery() {
      this.$emit("getStaffTreeData", this.filterText);
    },
    sub() {
      let personType = ''
      if(this.selectType == 'more'){
        personType = this.personInfoList[0].personType
      }
      console.log('this.checkedList',this.checkedList)
      const phoneRegex = /^1[3-9]\d{9}$/;
      const perRegex = /^(0\d{2,3}-)?\d{7,8}$/;
      let length = 0;
      let noPersonTypeName = '';
      let personMobilePhone = '';
      let personPhone = '';
      let dataVerification = [];
      this.checkedList.forEach((item, index) => {
        if(this.selectType == 'more') {
          if (item.personType == personType) {
            length++;
          }
        }
        if((item.personDuty || '').trim() ==''||item.personPhone==''){
          dataVerification.push(
            {
              personName:item.personName,
              personDuty:(item.personDuty || '').trim()=='',
              personPhone:item.personPhone==''
            }
          )
        }
        if (!phoneRegex.test(item.personMobilePhone) && item.personMobilePhone != '') {
          personMobilePhone += item.personName + '、'
        }
        if (!perRegex.test(item.personPhone) && item.personPhone) {
          personPhone += item.personName + '、'
        }
        if (!item.personType) {
          noPersonTypeName += item.personName + '、'
        }
      })
      // 去掉最后一个 "、"
      noPersonTypeName = noPersonTypeName.slice(0, -1);
      personMobilePhone = personMobilePhone.slice(0, -1);
      personPhone = personPhone.slice(0, -1);
      let verification = '';
      dataVerification.forEach(item=>{
        let verificationOne = item.personName+'的';
        if(item.personType){
          verificationOne += '【类别】'
        }
        if(item.personDuty){
          verificationOne += '【职务】'
        }
        verificationOne += '不能为空；';
        verification+=verificationOne;
      })
      if(dataVerification.length){
        this.$message({showClose: true,message: verification,type: 'error',duration: '8000'});
      }else if (noPersonTypeName != '') {
        this.$message({showClose: true,message: noPersonTypeName + '请选择【类别】！',type: 'error',duration: '8000'});
      } else if (personMobilePhone != '') {
        this.$message({showClose: true,message: personMobilePhone + '请填写正确格式的【手机】！',type: 'error',duration: '8000'});
      // }else if (this.selectType == 'more' && length < this.personInfoList[0].minLength && this.personInfoList[0].minLength != null) {
      }else if (this.selectType == 'more' && length <1) {
        // this.$message.error(this.personInfoList[0].personTypeText + '至少选择' + this.personInfoList[0].minLength + '个人！');
        this.$message({showClose: true,message:this.personInfoList[0].personTypeText + '至少选择1个人！',type: 'error',duration: '8000'});
         // this.$message.error(this.personInfoList[0].personTypeText + '至少选择1个人！');
      } else if (this.selectType == 'more' && length > this.personInfoList[0].maxLength && this.personInfoList[0].maxLength != null) {
        this.$message({showClose: true,message:this.personInfoList[0].personTypeText + '最多选择' + this.personInfoList[0].maxLength + '个人！',type: 'error',duration: '8000'});
      } else {
        this.checkedList.sort((a, b) => {
          const indexA = this.personInfoList.findIndex(item => item.personType === a.personType);
          const indexB = this.personInfoList.findIndex(item => item.personType === b.personType);
          return indexA - indexB;
        });
        console.log(this.checkedList)
        this.$emit("getOrg", this.checkedList);
        this.handleClose();
      }
    }
  }
};
</script>

<style scoped lang="scss">
.text {
  font-size: 14px;
}

.item {
  margin-bottom: 18px;
}

.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
}

.clearfix:after {
  clear: both
}

.is-never-shadow {
  ::v-deep .el-card__body {
    height: 100% !important;
    overflow: auto;
  }
}

.el-tag {
  width: 100%;
  margin-top: 10px;
  margin-bottom: 10px;
}

.el-icon-close {
  float: right;
  margin-top: 6px;
}

.el-tag--medium {
  height: 39px;
  line-height: 39px;
}
</style>
