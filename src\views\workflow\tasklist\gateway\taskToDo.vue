<template>
  <div class="todo">
    <div class="todo-content">
      <div class="todo-header">
        <el-radio-group v-model="tabPosition">
          <el-radio-button label="1">业务信息</el-radio-button>
          <el-radio-button label="2">流程历史</el-radio-button>
          <el-radio-button label="3">流程图</el-radio-button>
        </el-radio-group>
        <Opinion
          :activities="activities"
        />
      </div>
    </div>
    <el-scrollbar style="height:calc(100vh - 70px);overflow-x: hidden">
      <div v-show="tabPosition==='1'" class="todo-data">
        <Daily
          v-if="type==='daily'"
          :key="index"
          ref="todo"
          :select-value="selectValue"
          :center-variable="centerVariable"
          @handle="handle"
          @saveBtn="saveBtn"
          @openLoading="openLoading"
          @closeLoading="closeLoading"
        />
        <Regular
          v-if="type==='regular'"
          :key="index"
          ref="todo"
          :select-value="selectValue"
          :center-variable="centerVariable"
          @handle="handle"
          @saveBtn="saveBtn"
          @openLoading="openLoading"
          @closeLoading="closeLoading"
        />
        <Actual
          v-if="type==='actual'||type==='actualEdit'"
          :key="index"
          ref="todo"
          :type="type"
          :select-value="selectValue"
          :center-variable="centerVariable"
          @handle="handle"
          @saveBtn="saveBtn"
          @openLoading="openLoading"
          @closeLoading="closeLoading"
        />
        <component
          v-if="
            type !== 'actual' &&
            type !== 'actualEdit' &&
            type != 'regular' &&
            type != 'daily'
          "
          :is="businessUrl"
          :key="index"
          ref="todo"
          :center-variable="centerVariable"
          @nextStep="nextStepDirect"
          @collocation="saveBtn"
          @handle="handle"
          @openLoading="openLoading"
          @closeLoading="closeLoading"
        />
      </div>
      <div v-show="tabPosition==='2'" class="todo-data">
        <History
          :activities="activities"
        />
      </div>
      <div v-show="tabPosition==='3'" class="todo-data">
        <FlowChart
          :key="selectValue"
          :select-value="selectValue"
        />
      </div>
    </el-scrollbar>
    <div style="text-align: right;padding:0 10px">
      <RegularProcess
        v-if="type==='regular'"
        slot="footer"
        :key="centerVariable"
        ref="process"
        type="parent"
        :tab-flag="tabFlag"
        :select-value="selectValue"
        :center-variable="centerVariable"
        :flow-cfg-link="flowCfgLink"
        flow-params-url=""
        @close="close"
        @nextStep="nextStep"
        @publicSave="publicSave"
      />
      <DailyProcess
        v-else-if="type==='daily'"
        slot="footer"
        :key="centerVariable||saveBtnType"
        ref="process"
        :save-btn-type="saveBtnType"
        type="parent"
        :tab-flag="tabFlag"
        :select-value="selectValue"
        :center-variable="centerVariable"
        :flow-cfg-link="flowCfgLink"
        @close="close"
        @nextStep="nextStep"
        @publicSave="publicSave"
      />
      <ActualProcess
        v-else-if="type==='actual'||type==='actualEdit'"
        slot="footer"
        :key="centerVariable"
        ref="process"
        type="parent"
        :tab-flag="tabFlag"
        :select-value="selectValue"
        :center-variable="centerVariable"
        :flow-cfg-link="flowCfgLink"
        flow-params-url="/colligate/violActual/flowParams"
        @close="close"
        @nextStep="nextStep"
        @publicSave="publicSave"
      />
      <ProcessNew
        v-else
        slot="footer"
        :key="centerVariable || saveBtnType || refreshAssigneeUrl"
        ref="process"
        :refreshAssigneeUrl="refreshAssigneeUrl"
        :save-btn-type="saveBtnType"
        :tab-flag="tabFlagS"
        :select-value="selectValue"
        @loadProcessData="loadProcessData"
        :center-variable="centerVariable"
        :flow-cfg-link="flowCfgLink"
        @publicSave="publicSave"
        @close="close"
        @nextStep="nextStepCopy"
      />
    </div>
  </div>
</template>
<script>
import Opinion from './../common/opinion'
import Process from '@/components/Process'
import ProcessNew from "@/components/process-common";
import RegularProcess from '@/components/Process/regular'
import DailyProcess from '@/components/Process/daily'
import ActualProcess from '@/components/Process/actual'
import FlowChart from './../common/flowChart'
import Daily from '@/views/daily/dailyBox'// 日常
import Regular from '@/views/regular/flow'// 定期
import Actual from '@/views/actual/flow'// 实时
import History from './../common/history'
import { tasktodopath, taburls, histoicflow } from '@/api/components/process'
import { Loading } from 'element-ui'

export default {
  components: {
    Opinion,
    Process,
    ProcessNew,
    Daily,
    Regular,
    DailyProcess,
    FlowChart,
    History,
    RegularProcess,
    Actual,
    ActualProcess
  },
  inheritAttrs: false,
  props: {
    tabFlag: {
      type: String
    }
  },
  data() {
    return {
      index: 1,
      businessUrl: "",
      refreshAssigneeUrl: "",
      saveBtnType: true,
      centerVariable: {},
      flowCfgLink: {},
      visible: false, // 弹框
      tabPosition: '1',
      processType: 1,
      activities: [],
      type: '',
      loadingInstance: ''
    }
  },
  computed: {},
  watch: {},
  created() {
    this.selectValue = {
      linkKey: this.$route.query.linkKey,
      processInstanceId: this.$route.query.processInstanceId,
      readLinkId: this.$route.query.readLinkId,
      taskId: this.$route.query.taskId,
      typeId: this.$route.query.typeId
    }
    this.show()
  },
  mounted() {},
  methods: {
    //额外参数
    loadProcessData(callback) {
      let jsonData = this.$refs.todo.loadProcessData()
      console.log(jsonData)
      callback(jsonData); // 将数据返回给子组件
    },
    // 打开这招
    openLoading() {
      this.$store.dispatch('app/setSpyj', '1')
      this.loadingInstance = Loading.service({
        target: document.querySelector('#todo'),
        background: 'rgba(255,255,255,0)',
        spinner: 'el-icon-loading', // 自定义加载图标类名
        text: '正在加载...', // 显示在加载图标下方的加载文案
        lock: false // lock的修改符--默认是false
      })
      return this.loadingInstance
    },
    // 关闭这招
    closeLoading() {
      this.loadingInstance.close()
    },
    /** 点开弹窗 */
    show() {
      this.Tasktodopath()
      this.Histoicflow()
    },
    /** 保存按钮展示 */
    saveBtn(type) {
      console.log(type);
      this.saveBtnType = type;
      this.refreshAssigneeUrl = type.refreshAssigneeUrl; //业务url
    },
    /** 保存 */
    publicSave() {
      this.$refs.todo.publicSave()
    },
    /** 下一步 跳过校验*/
    nextStepDirect() {
      // 额外参数
      const loadProcessData = this.$refs.todo.loadProcessData();
      this.$refs.process.handle(1, loadProcessData);
    },
    /** 下一步 */
    nextStep() {
      this.$refs.todo.nextStep()
    },
    nextStepCopy() {
      // 额外参数
      const loadProcessData = this.$refs.todo.loadProcessData();
      // 校验是否通过
      const passValidate = this.$refs.todo.passValidate();
      if (passValidate) {
        // 校验通过
        this.$refs.process.handle(1, loadProcessData);
      }
    },
    /** 下一步回调 */
    handle(type, object) {
      this.$refs.process.handle(type, object)
    },
    /** 关闭弹窗 */
    close() {
      window.opener = null
      window.open('', '_self')
      window.close()
      // this.$emit('refresh');
    },
    /** 主要数据*/
    Tasktodopath() {
      tasktodopath(this.selectValue).then(
        response => {
          this.centerVariable = response.data.dataRows[0].centerVariable
          this.flowCfgLink = response.data.dataRows[0].flowCfgLink
          this.type = response.data.dataRows[0].url
          this.businessUrl = (resolve) =>
            require([`@/views/${response.data.dataRows[0].url}`], resolve);
          this.Taburls()
          this.index++
          this.visible = true
        }
      )
    },
    /** 主要数据*/
    Histoicflow() {
      histoicflow(this.selectValue.processInstanceId).then(
        response => {
          this.activities = response
        }
      )
    },
    /** 根据所在环节查询需展现的自定义标签*/
    Taburls() {
      taburls(this.centerVariable.flowKey, this.selectValue.linkKey, this.tabFlag).then(
        response => {

        }
      )
    }
  }
}

</script>
<style scoped lang="scss">
  .todo{
    .todo-header{
      ::v-deep.el-radio-button__inner{
        border-radius: 0 !important;
        border-color: #f4f4f4 !important;
        box-shadow:0 0 0 0 #f5222d !important;
        width: 120px;
      }
    }
    .todo-content{
    background: #F4F4F4;
     }
    .todo-data{
      background: #fff;
      margin-top:8px;
      overflow: auto;
      height: calc(100% - 10px);
    }
    ::v-deep.el-scrollbar__view{
      height: calc(100% - 10px);
    }
    ::v-deep.el-scrollbar__wrap {
      overflow-x: hidden !important;
    }
    ::v-deep.el-dialog__body{
      border-top: 2px solid #E9E8E8;
      padding:0 20px 10px;
      background: #F4F4F4;
      height: 70vh;
      overflow: auto;
    }
  }

</style>

