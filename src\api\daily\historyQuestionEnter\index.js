import request from '@/utils/request'


// 新增获取主键
export function queryNewDailyId() {
  return request({
    url: '/colligate/dailyhis/queryNewDailyId',
    method: 'post'
  })
}

// 获取当前环节
export function queryProblemStatus(problemId) {
  return request({
    url: '/colligate/dailyhis/queryProblemStatus/'+problemId,
    method: 'post'
  })
}

// 保存
export function saveHisViolateInfo(data) {
  return request({
    url: '/colligate/dailyhis/saveHisViolateInfo',
    method: 'post',
    data:data
  })
}
// 提交
export function submitHisData(id) {
  return request({
    url: '/colligate/dailyhis/submitHisData/'+id,
    method: 'post'
  })
}

// 查看查询左侧状态树
export function selectHisDailyFlowInfo(params) {
  return request({
    url: '/colligate/dailyhis/selectHisDailyFlowInfo',
    method: 'post',
    data:params
  })
}
