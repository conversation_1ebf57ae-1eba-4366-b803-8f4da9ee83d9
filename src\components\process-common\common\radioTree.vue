<template>
  <div style="background: #F4F4F4;padding:4px">
    <el-row>
      <el-col :span="16" class="tree-box" style="padding: 5px 4px;">
          <div class="position-form">
            <el-tabs v-model="activeName" @tab-click="handleClick" class="el-tabs-li">
              <el-tab-pane label="本部门" name="0"></el-tab-pane>
              <el-tab-pane label="全部" name="1"></el-tab-pane>
            </el-tabs>
          </div>
         <el-scrollbar class="tree-height">
           <treeDept
             v-show="activeName=='0'"
             @accept="selectTreeAray"
           />
           <treeAll
             v-show="activeName=='1'"
             @accept="selectTreeAray"
           />
         </el-scrollbar>
      </el-col>

      <el-col :span="8" class="tree-box">
        <BlockCard title="已选人员">
          <el-scrollbar  class="tree-height" style="height: calc(100vh - 432px);">
            <TreeSelect
              :selectTree="selectTree"
              type="radio"
            >
            </TreeSelect>
          </el-scrollbar>
        </BlockCard>
      </el-col>
    </el-row>
  </div>

</template>

<script>
import BlockCard from '@/components/BlockCard'
  import {userTree,treeUrl} from "@/api/common/index";
  import TreeSelect from '@/components/tree/treeLi/li';
  import treeAll from '@/components/process-common/common/tree/treeAll';
  import treeDept from '@/components/process-common/common/tree/treeDept';

  export default {
    components: {
      TreeSelect,
      treeAll,
      treeDept,
      BlockCard
    },
    props: ["selectTreeData"],
    data() {
      return {
        activeName:0,
        data:[],
        defaultTree:[],
        selectTree:[],
      }
    },
    methods: {
      //tab切换
      handleClick(tab, event) {
        this.activeName = tab.index;
      },
      //返回数据
      selectTreeAray(data){
        this.selectTree = data;
      },
      //返回数据
      save(){
        this.$emit('accept',this.selectTree)
      }
    },
    created(){
      this.selectTree = this.selectTreeData
    }
  }
</script>
<style  rel="stylesheet/scss" scoped lang="scss">
  .is-disabled{
    display: none !important;
  }
  .tree-height{
    padding:10px 0;
    box-sizing: border-box;
    background: #fff;
    height: calc(100vh - 400px);
    overflow: hidden;

  }
  ::v-deep .el-scrollbar__wrap{
    overflow: auto;
  }
  ::v-deep .position-form {
    padding: 0 16px;
    width: 100%;
    z-index: 99;
    background: #ffffff;
    border-bottom: 1px solid #ddd;
  }

  .el-tabs {
    ::v-deep.el-tabs__nav-wrap::after {
      height: 0 !important;
    }
  }
  .el-tabs-li{
    ::v-deep.el-tabs__header{
      margin:0;
    }
  }
  .tree-box{
    padding: 4px;
  }
</style>
