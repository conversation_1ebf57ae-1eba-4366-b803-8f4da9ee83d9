<template>
  <div>
    <el-form ref="dataDetails" :model="dataDetails" label-width="150px">
      <el-row>
        <el-col :span="8">
          <el-form-item label="禁止人姓名">
            <el-input v-model="dataDetails.userName" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="性别">
            <el-radio-group
              v-model="dataDetails.sex"
              size="medium"
            >
              <el-radio
                v-for="(item,index) in sexRadio"
                :label="item.value"
              >{{ item.label }}</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="身份证件号">
            <el-input v-model="dataDetails.idCard"  maxlength="18"/>
          </el-form-item>
        </el-col>
        <el-col :span="24" />
        <el-col :span="8">
          <el-form-item label="所在企业">
            <span>{{ dataDetails.involAreaName }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="企业层级">
            <span>{{ dataDetails.orgGradeName }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="处理前职位">
            <span>{{ dataDetails.postName }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="24" />
        <el-col :span="8">
          <el-form-item label="干部类别">
            <el-select v-model="dataDetails.cadreCategory" placeholder="请选择" style="width: 100%">
              <el-option
                v-for="item in dataDetails.cadreCategoryList"
                :key="item.dictValue"
                :label="item.dictLabel"
                :value="item.dictValue"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="禁入限制期间开始">
            <el-date-picker v-model="dataDetails.limitStartTime" type="date" placeholder="选择禁入限制期间开始" style="width: 100%;" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="禁入限制期间结束">
            <el-date-picker v-model="dataDetails.limitEndTime" type="date" placeholder="选择禁入限制期间结束" style="width: 100%;" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item label="责任追究处理部门">
            <div class="list1">
              <div v-for="(item,index) in relevorgByType" class="list1-one">
                <span>{{ item.orgName }}</span>
                <span class="close" @click="DelOrgList(item.orgId)"><i class="el-icon-close icon iconfont" /></span>
              </div>
            </div>
            <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="addA()">添加</el-button>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="责任处理联系人">
            <div v-for="(item,index) in relevpersonByType" class="list1">
              <div class="list1-one">
                <span>{{ item.userName }}</span>
                <span class="close" @click="DelConList(item.postId)"><i class="el-icon-close icon iconfont" /></span>
              </div>
            </div>
            <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="addB()">添加</el-button>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="责任处理联系方式">
            <el-input type="number" v-model="dataDetails.contactInformation" @input="handleInput($event)"  maxlength="11" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="工作简历">
            <el-input v-model="dataDetails.workResume" type="textarea" :rows="3" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="违规问题">
            <el-input v-model="dataDetails.violationsProblem" type="textarea" :rows="3" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="不良后果">
            <el-input v-model="dataDetails.adverseConsequences" type="textarea" :rows="3" maxlength="256" show-word-limit />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="责任认定情况">
            <el-input v-model="dataDetails.responIdenty" type="textarea" :rows="3" maxlength="120" show-word-limit />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="责任追究处理情况">
            <el-input v-model="dataDetails.accountabHandle" type="textarea" :rows="3" maxlength="120" show-word-limit />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="备注">
            <el-input v-model="dataDetails.remark" type="textarea" :rows="3" maxlength="256" show-word-limit />
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item label="附件列表">
            <div style="height: 36px; line-height: 36px; margin-bottom: 20px">
              <div style="float: right; padding: 3px 0; white-space:nowrap" type="text">
                <el-upload
                  class="upload-demo"
                  :headers="headers"
                  :data="fileParams"
                  :action="url"
                  :show-file-list="false"
                  :before-upload="handlePreview"
                  style="display: inline; margin-left: 8px;"
                  :on-success="handleFileSuccess"
                >
                  <el-button size="small" type="primary">禁入限制人员附件</el-button>
                </el-upload>
              </div>
            </div>

            <el-table :data="fileList" max-height="250" style="width: 100%" border :show-header="false" :cell-class-name="rowClass">
              <el-table-column label="序号" type="index" min-width="10%" align="center" />
              <el-table-column label="文档名称" prop="fileName" min-width="55%" />
              <el-table-column label="上传人" prop="uploaderName" min-width="13%" />
              <el-table-column label="上传时间" prop="createTime" :formatter="dateFormat" min-width="13%" />

              <el-table-column label="操作" fixed="right" min-width="9%" align="center" class-name="small-padding fixed-width">
                <template slot-scope="scope">
                  <i class="el-icon-download" style="color: red;margin-right: 15px;cursor: pointer;" title="下载" @click="fileDownload(scope.row)" />
                  <i class="el-icon-delete" style="color: red;cursor: pointer;" title="删除" @click="deleteFile(scope.row)" />
                </template>
              </el-table-column>
            </el-table>
          </el-form-item>
        </el-col>

      </el-row>
    </el-form>
    <el-dialog :visible.sync="VisibleRadioTree" width="60%" append-to-body title="责任追究处理部门">
      <RadioTree
        v-if="VisibleRadioTree"
        :key="id"
        ref="radioTree"
        url="/colligate/relevorg/queryRelevorgTree"
        :select-tree="[]"
        :params="{
          problemId:problemId,
          relevantTableId:relevantTableId,
          relevorgType:'HANDLE_DEPARTMENT'
        }"
        @accept="orgList"
      />
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="getTree">保存</el-button>
      </div>
    </el-dialog>
    <el-dialog :visible.sync="VisibleConTree" width="60%" append-to-body title="责任处理联系人">
      <RadioConTree
        v-if="VisibleConTree"
        :key="id"
        ref="conTree"
        url="/colligate/violDailyRelevperson/checkStaffOrgTree"
        :select-tree="[]"
        :params="{
          problemId:problemId,
          relevantTableId:relevantTableId,
          personType: 'HANDLE_PERSON'
        }"
        @accept="conList"
      />
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="conTree">保存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { queryLimitedPersonInfoById, queryRelevorgByType, saveRelevorg, delrelevorg, queryRelevpersonByType, delCheckGroupMember, saveCheckGroupMember, saveLimitedPersonInfo, selectViolFilesPage, deleteViolFile } from '@/api/daily/process/handlingAppealRecords'
import RadioTree from './../tree/radioTree'// tree
import RadioConTree from './../tree/radioConTree'// tree
import { getToken } from '@/utils/auth'
export default {
  name: 'Edit',
  components: {
    RadioConTree,
    RadioTree
  },
  props: {
    id: {
      type: String,
      default: ''
    },
    problemId: '',
    relevantTableId: '',
    relevantTableName: ''
  },
  data() {
    return {
      VisibleConTree: false,
      VisibleRadioTree: false,
      dataDetails: {},
      relevorgByType: [],
      relevpersonByType: [],
      sexRadio: [
        { label: '男', value: 1 },
        { label: '女', value: 2 }
      ],
      headers: { Authorization: 'Bearer ' + getToken() },
      url: process.env.VUE_APP_BASE_API + '/colligate/violFile//uploadViolFile',
      fileParams: {
        busiTableId: '',
        busiTable: 'T_COL_VIOL_DAILY_LIMIT_PERSON',
        fileType: 'DAILY_APPEAL_LIMIT_EXPAND_1',
        problemId: ''
      },
      fileList: ''
    }
  },
  watch: {
    'dataDetails.contactInformation': function(curVal, oldVal) {
      if (!curVal) {
        this.dataDetails.contactInformation = ''
        return false
      }
      // 实时把非数字的输入过滤掉
      this.dataDetails.contactInformation = curVal.match(/\d/gi) ? curVal.match(/\d/gi).join('') : ''
    }
  },
  created() {
    this.limitPersonInfo()
    this.selectViolFilesPage()
    this.fileParams.busiTableId = this.relevantTableId
    this.fileParams.problemId = this.problemId
  },
  methods: {
    //校验手机号
    handleInput(value) {
      if (value.length > 11) {
        this.dataDetails.contactInformation = value.slice(0, 11);
        return;
      }
      const phoneRegex = /^1[2-9]\d{9}$/;
      if (!phoneRegex.test(value) && value.length === 11) {
        this.dataDetails.contactInformation= value.slice(0, -1);
      }
    },
    /* 附件上传之前*/
    handlePreview: function(file) {
      if (file.size / 1024 / 1024 > 100) {
        this.$message.error('附件大小不能超过 100MB!')
        return false
      }
    },
    /** 附件上传成功*/
    handleFileSuccess(res) {
      if (res.code === 200) {
        this.$message.success('上传成功')
      }

      this.selectViolFilesPage()
    },
    addA() {
      this.VisibleRadioTree = true
    },
    addB() {
      this.VisibleConTree = true
    },
    // 保存
    getTree() {
      this.$refs.radioTree.save()
    },
    // 保存人
    conTree() {
      this.$refs.conTree.save()
    },
    // 查询file列表
    selectViolFilesPage() {
      selectViolFilesPage({
        busiTableId: this.relevantTableId,
        problemId: this.problemId,
        limit: 9999999,
        page: 1
      }).then(
        response => {
          if (response.code === 200) {
            this.fileList = response.data
          }
        }
      )
    },
    /* 删除附件*/
    deleteFile: function(row) {
      this.$confirm('确认删除附件【' + row.fileName + '】吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteViolFile(
          row.id
        ).then(response => {
          if (response.code === 200) {
            this.$message.success('删除成功')
            this.selectViolFilesPage()
          } else {
            this.$message.error(response.msg)
          }
        })
      })
    },
    // 下载附件
    fileDownload(obj) {
      this.download('/sys/attachment/downloadSysAttachment/' + obj.attachmentId, {
      }, obj.fileName)
    },

    orgList(data) {
      if (data.length) {
        saveRelevorg({
          checkStyle: 'radio',
          orgIds: [data[0].id],
          orgName: data[0].name,
          problemId: this.problemId,
          relevantTableId: this.relevantTableId,
          relevantTableName: this.relevantTableName,
          relevorgType: 'HANDLE_DEPARTMENT'
        }).then(
          response => {
            this.QueryRelevorgByType()
            this.VisibleRadioTree = false
            this.dataDetails.accountabDepartment = data[0].name
          }
        )
      }
    },
    DelOrgList(orgId) {
      delrelevorg({
        orgId: orgId,
        problemId: this.problemId,
        relevantTableId: this.relevantTableId,
        relevantTableName: this.relevantTableName,
        relevorgType: 'HANDLE_DEPARTMENT'
      }).then(
        response => {
          this.QueryRelevorgByType()
        }
      )
    },
    DelConList(postId) {
      delCheckGroupMember({
        personType: 'HANDLE_PERSON',
        postId: postId,
        problemId: this.problemId,
        relevantTableId: this.relevantTableId,
        relevantTableName: this.relevantTableName
      }).then(
        response => {
          this.QueryRelevpersonByType()
        }
      )
    },

    conList(data) {
      if (data.length) {
        saveCheckGroupMember({
          problemId: this.problemId,
          relevantTableId: this.relevantTableId,
          personType: 'HANDLE_PERSON',
          relevantTableName: this.relevantTableName,
          postIds: [data[0].id],
          userName: data[0].name,
          orgId: data[0].pId,
          phone: data[0].phone,
          checkStyle: 'radio'
        }).then(
          response => {
            this.QueryRelevpersonByType()
            this.VisibleConTree = false
            this.dataDetails.contactsName = data[0].name
          }
        )
      }
    },
    /** 查询禁入限制人员详情*/
    limitPersonInfo() {
      queryLimitedPersonInfoById({ id: this.id }).then(
        response => {
          this.dataDetails = {
            ...response.data
          }
          this.QueryRelevorgByType()
          this.QueryRelevpersonByType()
        }
      )
    },
    // 查询组织
    QueryRelevorgByType() {
      queryRelevorgByType({
        problemId: this.problemId,
        relevantTableId: this.relevantTableId,
        relevorgType: 'HANDLE_DEPARTMENT'
      }).then(
        response => {
          this.relevorgByType = response.data
        }
      )
    },
    // 查询联系人
    QueryRelevpersonByType() {
      queryRelevpersonByType({
        problemId: this.problemId,
        relevantTableId: this.relevantTableId,
        personType: 'HANDLE_PERSON'
      }).then(
        response => {
          this.relevpersonByType = response.data
        }
      )
    },
    // 最终保存
    saveLimitedPersonInfo() {
      const reg = /^[1][2,3,4,5,6,7,8,9][0-9]{9}$/
      let idCard = this.dataDetails.idCard;
      // 定义身份证号的正则表达式
      const idCardRegex = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
      if (this.dataDetails.contactInformation == '') {
        this.$modal.msgError('【联系电话】不能为空！')
        return false
      } else if ((!reg.test(this.dataDetails.contactInformation))) {
        this.$modal.msgError('【联系电话】格式不正确！')
        return false
      } else if (this.dataDetails.idCard && this.dataDetails.idCard.length > 18) {
        this.$modal.msgError('【身份证件号】最多输入18位！')
        return false
      } else if (!idCardRegex.test(idCard)) {
        this.$modal.msgError('请输入正确的【身份证件号】！')
      } else {
        saveLimitedPersonInfo(this.dataDetails).then(
          response => {
            this.$emit('close', this)
          }
        )
      }
    }
  }
}
</script>
<style scoped lang="scss">
  .list1 {
    overflow: hidden;
  .list1-one {
    background-color: #e6f7ff;
    color: #40a9ff;
    margin: 0 10px 2px 10px;
    float: left;
    height: 30px;
    line-height: 30px;
    padding: 0 12px 0 12px;
    border-radius: 2px;
  .close {
    padding: 8px;
    cursor: pointer;
  }
  }
  }
  ::v-deep.el-textarea .el-input__count {
    color: #909399;
    background: rgba(255,255,255,0);
    position: absolute;
    font-size: 12px;
    bottom: 5px;
    right: 10px;
  }
</style>
