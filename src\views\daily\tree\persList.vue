<template>
  <div class="de-list">
    <el-row
      :class="index === 0 ? 'de-li' : 'de-li ry-row'"
      v-for="(item, index) in persData"
      :key="item"
    >
      <div class="depart_list">
        <div class="depart_li_div float-left">
          <li
            class="depart_li depart_li_grey depart_li_1 width"
            style="width: 100%"
            :title="item.mainFlag == '1'?'（主责单位）' + item.involAreaName:item.involAreaName"
          >
            <span class="ovflowHidden">{{ item.mainFlag == '1'?'（主责单位）' + item.involAreaName:item.involAreaName }}</span>
          </li>
        </div>
      </div>
      <div class="ment_list">
        <div
          v-for="(obj, i) in item.involDepartList"
          :key="obj"
          class="depart_li_div depart_li_div_before"
          style="width: 100%"
        >
          <li
            class="depart_li depart_li_grey float-left depart_li_2"
            style="width: 390px; display: flex"
            :title="obj.involOrgName"
          >
            <span class="ovflowHidden" style="width: 270px">{{
              obj.involOrgName
            }}</span>
            <div class="flex-1"></div>
            <el-button
              class="el-button el-button--primary el-button--mini is-plain"
              v-if="edit"
              size="mini"
              @click="openDuty(obj.id)"
            >
              <i class="el-icon-plus" />添加不在岗人员</el-button
            >
          </li>

          <div class="float-right" style="width: calc(100% - 450px)">
            <li
              style="position: relative; margin-right: 6px"
              class="depart_li_green depart_li"
              v-for="(value, j) in obj.involPersonList"
              :key="j"
              :title="value.userName"
            >
              <i
                v-if="edit"
                class="el-icon-circle-close noWorkDelate"
                title="删除"
                @click="noWorkDelate(value.id)"
              ></i>
              {{ value.userName }}(
              <label
                @click="handlerPostName(obj.id, value)"
                style="cursor: pointer"
              >
                <!--<span v-if="!edit">{{ value.postName }}</span>-->
                <span>{{
                  "职务：" +
                  value.postName +
                  "；干部类型：" +
                  fromatComon(value.cadreType, dict.type.viold_cadre_type)
                }}</span>
                <!-- <el-input
                  v-else
                  v-model="value.postName"
                  placeholder="添加职务"
                  :style="{ width: '150px' }"
                  @change="handlerPostName(value)"
                ></el-input> -->
              </label>
              )
            </li>
          </div>
        </div>
      </div>
    </el-row>

    <el-dialog
      :visible.sync="visibleForm"
      width="40%"
      append-to-body
      title="人员岗位信息"
      classs="noWorkStyle"
      v-if="visibleForm"
    >
      <OffDutyPersonnel
        :closeBtn="closeBtn"
        :involve-dept-id="involveDeptId"
        :rowData="rowData"
      />
    </el-dialog>
  </div>
</template>

<script>
import {
  queryDepartmentSelectInfo,
  savePersonPostName,
  deleteUnitById,
} from "@/api/daily/tree";
import OffDutyPersonnel from "./Off-duty-personnel";

export default {
  dicts: ["viold_cadre_type"],
  name: "persList",
  components: {
    OffDutyPersonnel,
  },

  props: {
    title: {
      type: String,
      default: "",
    },
    edit: {
      type: Boolean,
      default: false,
    },
    problemId: {
      type: String,
      default: "",
    },
    relevantTableId: {
      type: String,
      default: "",
    },
    relevantTableName: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      persData: [],
      visibleForm: false,
      involveDeptId: "",
      rowData: {},
    };
  },
  methods: {
    fromatComon(value, list) {
      let lastLabel = "-";
      if (value && list.length > 0) {
        list.forEach((element) => {
          if (element.value === value) {
            lastLabel = element.label;
          }
        });
      }
      return lastLabel;
    },

    openDuty(deptId) {
      this.involveDeptId = deptId;
      this.rowData = {};
      this.visibleForm = true;
    },
    closeBtn() {
      this.visibleForm = false;
      this.DueryDepartmentSelectInfo();
    },
    DueryDepartmentSelectInfo() {
      let query = {
        problemId: this.problemId,
        relevantTableId: this.relevantTableId,
        relevantTableName: this.relevantTableName,
      };
      queryDepartmentSelectInfo(query).then((response) => {
        this.persData = response.data;
      });
    },
    handlerPostName(deptId, value) {
      if (this.edit) {
        console.log(value);
        this.involveDeptId = deptId;
        this.rowData = value;
        this.visibleForm = true;
      }
      // savePersonPostName(value).then((response) => {});
    },
    noWorkDelate(id) {
      this.$confirm("确认要删除吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
      .then(() => {
        deleteUnitById(id).then(response => {
          if (200 === response.code) {
            this.$modal.msgSuccess(response.msg);
            this.DueryDepartmentSelectInfo();
          } else {
            this.$modal.alertError(response.msg);
          }
        })
      })
        .catch(() => {});
    },
  },
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.ovflowHidden {
  text-align: left;
  display: inline-block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  word-break: break-all;
  max-width: 100%;
  padding-right: 6px;
  box-sizing: border-box;
}

.cursor {
  cursor: pointer;
}

.noWorkDelate {
  position: absolute;
  top: 0px;
  right: -13px;
  font-size: 16px;
  cursor: pointer;
  color: #f5222d;
}

.de-list {
  .de-li {
    .depart_list {
      .depart_li_div {
        width: 250px;
        padding-right: 10px;
        box-sizing: border-box;
        float: left;
        .depart_li_grey {
          background: #f4f4f4;
          border-radius: 4px;
          color: #000;
        }
        .depart_li {
          height: 30px;
          line-height: 30px;
          margin: 0 0 10px 0;
          display: inline-block;
          padding: 0 6px 0 12px;
          border-radius: 2px;
          box-sizing: border-box;
          position: relative;
        }
        .depart_li_1:before {
          position: absolute;
          content: "";
          right: -30px;
          top: 15px;
          width: 25px;
          height: 1px;
          background: #d9d9d9;
        }
      }
    }
    .ment_list {
      position: relative;
      float: right;
      width: calc(100% - 290px);
      .depart_li_div {
        width: 250px;
        padding-right: 10px;
        box-sizing: border-box;
        float: left;
        .depart_li_grey {
          background: #f4f4f4;
          border-radius: 4px;
          color: #000;
        }
        .depart_li {
          height: 30px;
          line-height: 30px;
          margin: 0 0 10px 0;
          display: inline-block;
          padding: 0 6px 0 12px;
          border-radius: 2px;
          box-sizing: border-box;
          position: relative;
        }
        .depart_li_2:before {
          position: absolute;
          content: "";
          left: -25px;
          top: 15px;
          width: 22px;
          height: 45px;
          border-top: 1px solid #d9d9c9;
        }
        .depart_li_2:after {
          position: absolute;
          content: "";
          right: -48px;
          top: 15px;
          width: 40px;
          height: 1px;
          background: #d9d9d9;
        }
      }
      .depart_li_div_before {
        position: relative;
      }
      .depart_li_div_before:before {
        position: absolute;
        content: "";
        left: -25px;
        top: 15px;
        width: 25px;
        height: 100%;
        border-left: 1px solid #d9d9d9;
      }
      .depart_li_div_before:last-child:before {
        position: absolute;
        content: "";
        left: -25px;
        top: 23px;
        width: 25px;
        height: 0;
        border-left: 1px solid #d9d9d9;
      }
      .float-right {
        float: right;
        .depart_li {
          height: 30px;
          line-height: 30px;
          margin: 0 0 10px 0;
          display: inline-block;
          padding: 0 6px 0 12px;
          border-radius: 2px;
          box-sizing: border-box;
          position: relative;
          .input {
            height: 24px;
            font-size: 14px;
            background: #fff;
            border: 1px solid #d9d9d9;
            border-radius: 2px;
          }
        }
      }
    }
  }
}
</style>
