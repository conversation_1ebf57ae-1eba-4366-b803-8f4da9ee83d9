<template>
  <el-dialog
    :title="title"
    :visible.sync="dialogVisible"
    width="90%"
    append-to-body
    :before-close="handleClose">

    <div v-loading="fileLoadng"
         element-loading-text="正在上传" element-loading-spinner="el-icon-loading">
      <el-form ref="dataDetails" :model="dataDetails" label-width="120px"  style="margin-top: 20px" :rules="editType != 'view' ? rules : ''">
      <el-row>
        <el-col :span="24">
          <el-form-item label="信息标题" prop="informationTitle">
            <el-input v-if="editType != 'view'" v-model="dataDetails.informationTitle"></el-input>
            <span v-else>{{dataDetails.informationTitle}}</span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="信息类型" prop="informationType">
            <el-select v-if="editType != 'view'"  v-model="dataDetails.informationType" placeholder="请选择">
              <el-option
                v-for="item in dict.type.INFORMATION_TYPE"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
            <span v-else>{{dataDetails.informationType | fromatComon(dict.type.INFORMATION_TYPE)}}</span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="文号" prop="informationNum">
            <el-input v-if="editType != 'view'" v-model="dataDetails.informationNum"></el-input>
            <span v-else>{{dataDetails.informationNum}}</span>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="信息内容" prop="informationContext">
            <el-input v-if="editType != 'view'" type="textarea" :rows="4" v-model="dataDetails.informationContext"></el-input>
            <span v-else>{{dataDetails.informationContext}}</span>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="是否发布" prop="isPublish">
            <el-radio-group v-if="editType != 'view'" v-model="dataDetails.isPublish">
              <el-radio label="1">是</el-radio>
              <el-radio label="0">否</el-radio>
            </el-radio-group>
            <span v-else>{{dataDetails.isPublish | fromatComon(dict.type.IS_PUBLISH)}}</span>
          </el-form-item>
        </el-col>
      </el-row>
        <BlockCard
          title="附件列表"
        >
          <div style="float: right; padding: 3px 0; white-space:nowrap" type="text"  >
            <el-upload
              class="upload-demo"
              :headers="headers"
              :data="fileParams"
              :action="url"
              :show-file-list="false"
              style="display: inline"
              :on-progress="handleFileProgress"
              :before-upload="handlePreview"
              :on-success="handleFileSuccess"
            >
              <el-button size="small" v-if="editType != 'view'" type="primary">附件上传</el-button>
            </el-upload>
          </div>
          <div>
            <el-table :data="files" max-height="250" style="width: 100%" border :show-header="false":cell-class-name="rowClass">
              <el-table-column label="序号" type="index"  min-width="5%" align="center" />
              <el-table-column label="文档名称" prop="fileName" min-width="40%"/>
              <el-table-column label="上传人" prop="createUserName"  min-width="10%"/>
              <el-table-column label="上传时间" prop="createTime" min-width="25%"/>
              <el-table-column label="操作" fixed="right" min-width="20%" align="center" class-name="small-padding fixed-width">
                <template slot-scope="scope">
                  <el-button icon="el-icon-delete" title="删除" size="mini" type="text" style="color: red" v-if="editType != 'view'" @click="deleteFile(scope.row)" ></el-button>
                  <el-button
                    size="mini"
                    type="text"
                    title="下载"
                    icon="el-icon-bottom"
                    @click="downloadFile(scope.row)"
                  >
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </BlockCard>


      </el-form>

    </div>

    <span slot="footer" class="dialog-footer">
      <el-button size="mini" type="primary" @click="saveInformation" v-if="editType != 'view'" :loading="buttonLoading=='save'">提交</el-button>
    </span>
  </el-dialog>
</template>

<script>
import {
  delInformationFile,
  getInformationById,
  initInformationId, queryInformationFileList,
  saveInformation
} from "@/api/learningGarden/gardenManagement/gardenManagement";
import moment from "moment";
import {getToken} from "@/utils/auth";
import BlockCard from '@/components/BlockCard';

export default {
  name: "table2Add",
  components:{BlockCard},
  dicts: ["IS_PUBLISH","INFORMATION_TYPE"],
  props: {
    dialogVisible: {
      type: Boolean,
      default: true
    },
    informationId: {
      type: String,
      default: null
    },
    editType: {
      type: String,
      default: true
    },
  },
  data() {
    return {
      fileLoadng: false,
      dataDetails: {

      },
      id:'',
      files: [],
      // 设置上传的请求头部
      headers: { Authorization: "Bearer " + getToken() },
      url: process.env.VUE_APP_BASE_API + '/gardenManage/uploadInformationFile',
      fileParams: {
        busiId: '',
        busiTableName: 'a_information_publish',
      },
      title: "信息发布新增",
      buttonLoading: null,
      rules: {
        informationTitle: [
          { required: true, message: '请输入信息标题', trigger: 'blur' },
          { min: 1, max: 500, message: '长度在 1 到 500 个字符', trigger: 'blur' }
        ],
        informationType: [
          { required: true, message: '请选择信息类型', trigger: 'blur' },
        ],
        informationNum: [
          { required: false, message: '请输入文号', trigger: 'blur' },
          { min: 0, max: 100, message: '长度在 0 到 100 个字符', trigger: 'blur' }
        ],
        informationContext: [
          { required: true, message: '请输入信息内容', trigger: 'blur' },
          { min: 1, max: 2000, message: '长度在 1 到 2000 个字符', trigger: 'blur' }
        ],
        isPublish: [
          { required: true, message: '请选择是否发布', trigger: 'change' }
        ],
      },
    };
  },
  created() {
    if (this.editType == 'add'){
      this.initInformationId()
    }
    if (this.editType == 'edit'){
      this.id = this.informationId;
      this.fileParams.busiId = this.id
      this.getInformationById();
      this.queryInformationFileList();
      this.title = "信息发布编辑";

    }
    if (this.editType == 'view'){
      this.id = this.informationId;
      this.fileParams.busiId = this.id
      this.getInformationById();
      this.queryInformationFileList();
      this.title = "信息发布查看";
    }
  },
  methods: {
    handleFileProgress(){
      this.fileLoadng = true;
    },
    /** 下载附件 */
    downloadFile(row) {
      this.download('/sys/documentTemplate/downloadByAttachmentId', { id: row.attachmentId }, row.fileName)
    },
    initInformationId(){
      initInformationId().then((response) => {
        this.id = response.data;
        this.fileParams.busiId = this.id
      })
    },
    /**查询基本信息详情*/
    getInformationById() {
      if(this.id){
        getInformationById({informationId: this.id,}).then(
          response => {
            this.dataDetails = response.data;
            this.fileParams = {
              ...this.fileParams,
              busiId: response.data.id,
            };
          }
        );

      }
    },
    /* 保存或提交 */
    saveInformation(){

      this.$refs["dataDetails"].validate((valid) => {
        if (!valid) {
          return false;
        }else{
          const params = {
            id:this.id,
            ...this.dataDetails,
          };
          saveInformation(params).then(
            response => {
              if(response.code == 200){
                this.$message({
                  message: response.msg,
                  type: 'success'
                });
                // if(params.commitFlag){
                //   this.handleClose();
                // }else{
                //   this.handleFileSuccess();
                // }
              }else {
                this.$message({
                  message: response.msg,
                  type: 'error'
                });
              }
              this.handleClose()
              this.buttonLoading = null;
            }
          ).catch(err=>{
            console.log(err);
          });
        }
      });
    },
    /**关闭模态框*/
    handleClose() {
      this.$emit("closeModal");
      this.buttonLoading = null;
    },
    /*日期处理*/
    dateFormat:function(date){
      if(date === undefined){
        return ''
      }
      return moment(date).format("YYYY-MM-DD")
    },
    queryInformationFileList(){
      queryInformationFileList( this.id).then(
        response =>{
          this.files = response.data;
          this.fileLoadng = false;
        }
      )
    },
    /*附件上传之前*/
    handlePreview: function(file){
      if(file.size / 1024 / 1024 > 100){
        this.$message.error('附件大小不能超过 100MB!');
        return false;
      }
    },
    /**附件上传成功*/
    handleFileSuccess(){
      this.queryInformationFileList();
    },
    /*删除附件*/
    deleteFile: function(row){
      this.$confirm('确认删除附件【' + row.fileName + '】吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        delInformationFile(row.id).then(response =>{
          if(response.code === 200){
            this.handleFileSuccess();
          }else{
            this.$message.error(response.msg);
          }
        })
      });
    },
    /** 修改附件表样式 */
    rowClass ({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 1 || columnIndex === 2 || columnIndex === 3) {
        return 'no-right-border'
      }else if(columnIndex === 0){
        return 'cell-color'
      }
    },
  }
};
</script>
<style>
</style>
