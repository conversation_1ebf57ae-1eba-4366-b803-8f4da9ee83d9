<!-- 监督追责实时报送-十五个工作日实时报告快报 -->
<template>
  <div class=" app-report">
    <el-dialog  title="15个工作日实时报告快报" class="app-report" :visible.sync="visible" width="80%" append-to-body>
      <Jscrollbar height="68vh">
        <el-row class="el-dialog-div">
          <el-col :span="24">
            <BlockCard
              title="基本信息"
            >
              <el-form ref="elForm" :model="detailInfo" :rules="rules" size="medium" label-width="150px">
                <el-row>
                  <el-col :span="8">
                    <el-form-item label="系统编号">
                      <span> {{detailInfo.auditCode}}</span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="问题编号">
                      <span> {{detailInfo.problemCode}}</span>
                    </el-form-item>
                  </el-col>

                  <el-col :span="24">
                    <el-form-item label="违规事项 ">
                      <span class="cursor text-red" @click="dailyDetail"> {{detailInfo.problemTitle}}</span>
                    </el-form-item>
                  </el-col>

                  <el-col :span="8">
                    <el-form-item label="发生时间" prop="findTime">
                      <el-date-picker v-model="detailInfo.findTime"   format="yyyy-MM-dd" value-format="yyyy-MM-dd"
                                      :style="{width: '100%'}" placeholder="请选择发生时间" clearable></el-date-picker>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="损失金额（万元）" prop="lossAmount">
                      <el-input-number
                        v-model="detailInfo.lossAmount"
                        :min="0"
                        :precision="2"
                        placeholder="损失金额（万元）"
                        controls-position="right"
                      />
                    </el-form-item>
                  </el-col>

                  <el-col :span="8">
                    <el-form-item label="损失风险（万元）" prop="lossRisk">
                      <el-input-number
                        v-model="detailInfo.lossRisk"
                        :min="0"
                        :precision="2"
                        placeholder="损失风险（万元）"
                        controls-position="right"
                      />
                    </el-form-item>
                  </el-col>

                  <el-col :span="8">
                    <el-form-item label="涉及企业名称" prop="detailInfo.otherside">
                      <div class="select-list">
                        <div v-for="(item,index) of unitData" :key="index" class="list-li">
                          <span>{{ item.involveUnitName }}</span>
                        </div>
                      </div>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="涉及企业级次" prop="involveUnitGrade">
                      <span>{{detailInfo.involveUnitGrade}}</span>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
            </BlockCard>
          </el-col>
          <el-col :span="24">
            <BlockCard
              title="15个工作日实时报告快报"
            >
              <el-form size="medium" label-width="150px">
                <el-row>
                  <el-col :span="24">
                    <el-form-item label="涉及企业基本情况" prop="companyInvolved">
                      <el-input v-model="detailInfo.companyInvolved" type="textarea" placeholder="涉及企业基本情况"
                                :autosize="{minRows: 4, maxRows: 4}" :style="{width: '100%'}" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="24">
                    <el-form-item label="问题线索来源描述" prop="problemDescribe">
                      <el-input v-model="detailInfo.problemDescribe" type="textarea" placeholder="问题线索来源描述"
                                :autosize="{minRows: 4, maxRows: 4}" :style="{width: '100%'}" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="24">
                    <el-form-item label="涉及损失及涉及风险" prop="involveLossRisk">
                      <el-input v-model="detailInfo.involveLossRisk" type="textarea" placeholder="涉及损失及涉及风险"
                                :autosize="{minRows: 4, maxRows: 4}" :style="{width: '100%'}" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="24">
                    <el-form-item label="问题性质" prop="problemNature">
                      <el-input v-model="detailInfo.problemNature" type="textarea" placeholder="问题性质"
                                :autosize="{minRows: 4, maxRows: 4}" :style="{width: '100%'}" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="24">
                    <el-form-item label="主要原因" prop="importReason">
                      <el-input v-model="detailInfo.importReason" type="textarea" placeholder="主要原因"
                                :autosize="{minRows: 4, maxRows: 4}" :style="{width: '100%'}" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="24">
                    <el-form-item label="初步核实违规违纪情况" prop="violationsInfo">
                      <el-input v-model="detailInfo.violationsInfo" type="textarea" placeholder="初步核实违规违纪情况"
                                :autosize="{minRows: 4, maxRows: 4}" :style="{width: '100%'}" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="24">
                    <el-form-item label="已采取的应对措施" prop="measuresTaken">
                      <el-input v-model="detailInfo.measuresTaken" type="textarea" placeholder="已采取的应对措施"
                                :autosize="{minRows: 4, maxRows: 4}" :style="{width: '100%'}" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="24">
                    <el-form-item label="已开展的应对处置、成效" prop="developDisposal">
                      <el-input v-model="detailInfo.developDisposal" type="textarea" placeholder="开展的应对处置、成效"
                                :autosize="{minRows: 4, maxRows: 4}" :style="{width: '100%'}" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="24">
                    <el-form-item label="下一步工作安排" prop="nextWork">
                      <el-input v-model="detailInfo.nextWork" type="textarea" placeholder="下一步工作安排"
                                :autosize="{minRows: 4, maxRows: 4}" :style="{width: '100%'}" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="24">
                    <el-form-item label="备注" prop="remark">
                      <el-input v-model="detailInfo.remark" type="textarea" placeholder="备注"
                                :autosize="{minRows: 4, maxRows: 4}" :style="{width: '100%'}" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="联系人" prop="companyContacts">
                      <el-input  v-model="detailInfo.companyContacts" controls-position="right" placeholder="联系人"  />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="联系电话" prop="contactsTel">
                      <el-input maxlength="11" v-model="detailInfo.contactsTel" controls-position="right" placeholder="联系电话"  />
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
            </BlockCard>
          </el-col>
          <el-col :span="24">
            <BlockCard
              title="报告附件"
            >
              <FileUpload
                :edit='edit'
                :problemId="field"
                :relevantTableId="relevantTableId"
                :relevantTableName="relevantTableName"
                flowType="VIOL_ACTUAL"
                problemStatus="2"
                ref="file"
                @fileDown="fileDown"
              ></FileUpload>
            </BlockCard>
          </el-col>
          <el-col :span="24">
            <BlockCard
              title="待阅接收人"
            >
              <el-form size="medium" label-width="50px">
                <el-row>
                  <el-col :span="24"  v-if="detailInfo.orgGrade=='A'||detailInfo.orgGrade=='G'||detailInfo.orgGrade=='P'">
                    <el-form-item label="集团">
                      <div  style="padding:4px 0">
                        <el-button class="float-left" type="primary" plain icon="el-icon-plus" @click="addReadPer('G')" size="mini">添加</el-button>
                        <ul class="float-left">
                          <li v-for="item in groupData.G" class="depart_li">
                            <span class="float-left">{{item.receiverShowName}}</span>
                            <i class="el-icon-close icon iconfont"  @click="DeleteActualReadReceiver(item)"></i>
                          </li>
                        </ul>
                      </div>
                    </el-form-item>
                  </el-col>
                  <el-col :span="24"  v-if="detailInfo.orgGrade=='A'||detailInfo.orgGrade=='P'">
                    <el-form-item label="省分">
                      <div   style="padding:4px 0">
                        <el-button class="float-left" type="primary" plain icon="el-icon-plus" @click="addReadPer('P')" size="mini">添加</el-button>
                        <ul class="float-left">
                          <li v-for="item in groupData.P" class="depart_li">
                            <span class="float-left">{{item.receiverShowName}}</span>
                            <i class="el-icon-close icon iconfont"  @click="DeleteActualReadReceiver(item)"></i>
                          </li>
                        </ul>
                      </div>
                    </el-form-item>
                  </el-col>
                  <el-col :span="24"  v-if="detailInfo.orgGrade=='A'">
                    <el-form-item label="地市">
                      <div   style="padding:4px 0">
                        <el-button class="float-left" type="primary" plain icon="el-icon-plus" @click="addReadPer('A')" size="mini">添加</el-button>
                        <ul class="float-left">
                          <li v-for="item in groupData.A" class="depart_li">
                            <span class="float-left">{{item.receiverShowName}}</span>
                            <i class="el-icon-close icon iconfont"  @click="DeleteActualReadReceiver(item)"></i>
                          </li>
                        </ul>
                      </div>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                </el-row>
              </el-form>
            </BlockCard>
          </el-col>
        </el-row>
      </Jscrollbar>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="save">保存</el-button>
        <el-button size="mini" type="primary" @click="submitForm">提交</el-button>
        <el-button size="mini" @click="cancel">取消</el-button>
      </div>
    </el-dialog>
    <el-dialog :visible.sync="VisibleCheckTree" width="60%" append-to-body title="涉及企业名称">
      <CheckTree
        :key="selectTree"
        ref="checkTree"
        :url="url"
        :selectTree="selectTree"
        :params="{
        actualProblemId:actualProblemId,
        involveUnitName:'',
        relevantTableId:relevantTableId
        }"
        @list="persList"
      />
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="savePers">保存</el-button>
      </div>
    </el-dialog>
    <Recipient
      ref="recipient"
      :key="receiverGrade||actualProblemId"
      :actualProblemId="actualProblemId"
      :relevantTableId="relevantTableId"
      :relevantTableName="relevantTableName"
      :receiverGrade="receiverGrade"
      @save="ActualReadReceiverGroupData"
    >
    </Recipient>
    <ModifyRecord
      ref="modify"
      :key="receiverGrade||actualProblemId"
      :actualProblemId="actualProblemId"
      :relevantTableId="relevantTableId"
      :relevantTableName="relevantTableName"
      :type="edit"
      @saveModify="saveModify"
    >
    </ModifyRecord>
    <el-dialog :visible.sync="dailyVisible" width="90%" :title="'日常问题-'+detailInfo.problemTitle" append-to-body>
      <Details
        :key="detailInfo"
        :selectValue="detailInfo"
        activeName="0"
      >
      </Details>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary"  @click="dailyClose" >确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
  import {waitHandleFifteenReport, saveFifteenReport, submitFifteenReport, fifteenReportCompareWithDailyProblem} from "@/api/actual/task/actualFifteenAndThirtyDaysReport";
  import {actualReadReceiverGroupData, deleteActualReadReceiver, actualReadReceiverCandidate} from '@/api/actual/common/actualReadReceiver';
  import {queryActualInvolveUnit, deleteActualInvolveUnit, saveActualInvolveUnitData} from '@/api/actual/common/actualInvolveUnit';
  import {generateTemplateWithContent} from "@/api/components/index";
  import BlockCard from "@/components/BlockCard";
  import actualSituationRange from '@/views/actual/common/actualSituationRange';//范围情形选择页面
  import FileUpload from './../../components/fileUpload';//附件
  import CheckTree from './../common/checkTree';// checkTree
  import Recipient from './../common/recipient';// recipient
  import ModifyRecord from './../common/modifyRecord';// modifyRecord
  import Details from '@/views/daily/actualDetail';//

  export default {
    components: {BlockCard,actualSituationRange,FileUpload,CheckTree,Recipient,ModifyRecord,Details},
    props: {
      field:{
        type: String
      },
    },
    data() {
      return {
        dailyVisible:false,
        selectTree:[],
        VisibleCheckTree:false,
        url:'colligate/violActualInvolveUnit/actualInvolveUnitTreeData',
        actualProblemId: "1",
        relevantTableId: undefined,
        relevantTableName: undefined,
        edit: true,
        flag:false,
        visible:false,
        visibleTree:false,
        detailInfo:{
          contactsTel:null
        },
        findTime: null,
        acceptTime: null,
        problemSource:null,
        problemTitle: null,
        problemDescribe: undefined,
        contactsTel: undefined,
        lossAmount: 0,
        lossRisk: 0,
        groupReceivers: undefined,
        provinceReceivers: undefined,
        seriousAdverseEffectsFlag: 1,
        otherSeriousAdverseEffects: undefined,
        illegalActivities: undefined,
        companyContacts: undefined,
        involveUnitGrade:'',
        specList: [],
        problemSourceList:[],
        unitData:[],
        groupData:{},//待阅接收人
        receiverGrade:'G'
      }
    },
    computed: {},
    watch: {
      "detailInfo.contactsTel": function(curVal, oldVal) {
        if (!curVal) {
          this.detailInfo.contactsTel = "";
          return false;
        }
        // 实时把非数字的输入过滤掉
        this.detailInfo.contactsTel = curVal.match(/\d/gi) ? curVal.match(/\d/gi).join("") : "";
      }
    },
    created() {
    },
    mounted() {
    },
    methods: {
      //附件下载
      fileDown(data){
        console.log(data);
        saveFifteenReport(this.detailInfo).then(response => {
          if (200 === response.code) {
            generateTemplateWithContent({
              problemId: this.actualProblemId,
              busiTableId: this.relevantTableId,
              busiTableName: this.relevantTableName,
              templateCode: data.fileTemplate
            }).then(response => {
              this.download('/sys/attachment/downloadSysAttachment/' + response.data, {}, data.modelFileName);
            });
          } else {
            this.$modal.alertError(response.msg);
          }
        });
      },
      cancel(){
        this.visible=false;
        this.$emit('reportList');
      },
      /**初始化数据*/
      show(){
        this.visible=true;
        waitHandleFifteenReport(this.field).then(
          response => {
            const { code, data } = response;
            if (code === 200) {
              this.detailInfo = Object.assign({}, data);
              this.actualProblemId = this.detailInfo.actualProblemId;
              this.relevantTableId = this.detailInfo.id;
              this.relevantTableName = this.detailInfo.businessTable;
              this.edit = true;
              this.detailInfo.businessTable = this.relevantTableName;
              this.$nextTick(()=>{
                this.$refs.file.ViolationFileItems();
              });
              this.QueryFiveReportInvolveUnit();
              this.ActualReadReceiverGroupData();
            }
          }
        );
      },
      //企业数据
      QueryFiveReportInvolveUnit(){
        queryActualInvolveUnit({actualProblemId: this.detailInfo.actualProblemId, relevantTableId: this.detailInfo.id}).then(
          response => {
            this.selectTree = [];
            this.detailInfo.involveUnitGrade = response.involveUnitGrade;
            this.unitData = response.data;
            for(let i=0;i<this.unitData.length;i++){
              this.selectTree.push({id:this.unitData[i].compareId,name:this.unitData[i].involveUnitName})
            }
          }
        );
      },
      //企业删除
      unitDel(item) {
        deleteActualInvolveUnit(item.id).then(response => {
          if (200 === response.code) {
            this.$modal.msgSuccess('删除成功');
            this.QueryFiveReportInvolveUnit();
          } else {
            this.$modal.alertError(response.msg);
          }
        });
      },
      //点击保存企业
      savePers(){
        this.$refs.checkTree.list();
      },
      //返回数据
      persList(data){
        let list=[];
        this.index++;
        if(!data.length)
          return false;
        for (let i = 0; i < data.length; i++) {
          list.push(data[i].id);
        }

        let parameter = {
          actualProblemId: this.detailInfo.actualProblemId,
          relevantTableId: this.relevantTableId,
          relevantTableName: this.relevantTableName,
          waitSaveUnitCodes: list
        };

        saveActualInvolveUnitData(parameter).then(response => {
          if (200 === response.code) {
            this.$modal.msgSuccess('保存成功');
            this.QueryFiveReportInvolveUnit();
            this.VisibleCheckTree = false;
          } else {
            this.$modal.alertError(response.msg);
          }
        });
      },
      /**提交数据*/
      submitForm() {
        const reg = /^[1][2,3,4,5,6,7,8,9][0-9]{9}$/;
        if (this.detailInfo.contactsTel == '') {
          this.$modal.msgError("【联系电话】不能为空！");
          return false;
        }else if ((!reg.test(this.detailInfo.contactsTel))) {
          this.$modal.msgError("【联系电话】格式不正确！");
          return false;
        } else {
        saveFifteenReport(this.detailInfo).then(response => {
          this.modifyRecord();
        });
        }
      },
      //修改记录
      modifyRecord() {
        fifteenReportCompareWithDailyProblem(this.detailInfo).then(response => {
          if (200 === response.code) {
            if (response.data.findDifferences) {
              this.$refs.modify.show(response.data);
            } else {
              this.submitReport();
            }
          } else {
            this.$modal.alertError(response.msg);
          }
        });
      },
      //修改记录保存
      saveModify(){
        this.submitReport();
      },
      //提交
      submitReport(){
        const loading = this.$loading({
          spinner: 'el-icon-loading', // 自定义加载图标类名
          text: '正在加载...', // 显示在加载图标下方的加载文案
          lock: false, // lock的修改符--默认是false
        });
        submitFifteenReport(this.detailInfo).then(response => {
          loading.close();
          if (200 === response.code) {
            this.$modal.msgSuccess("提交成功");
            this.visible=false;
            this.cancel();
          } else {
            this.$modal.alertError(response.msg);
          }
        }).catch(err => {
          loading.close();
        });
      },
      //保存
      save() {
        saveFifteenReport(this.detailInfo).then(response => {
          if (200 === response.code) {
            this.$modal.msgSuccess("保存成功");
          } else {
            this.$modal.alertError(response.msg);
          }
        });
      },
      resetForm() {
        this.$refs['elForm'].resetFields()
      },
      //选择企业单位
      addCheckTree(){
        this.VisibleCheckTree=true;
      },
      //选择人员
      treeOpen(){
        this.flag = !this.flag;
        this.visibleTree = true;
      },
      //待阅接收人
      ActualReadReceiverGroupData(){
        actualReadReceiverGroupData({actualProblemId: this.detailInfo.actualProblemId, relevantTableId: this.relevantTableId}).then(response => {
          this.groupData = response.data;
        });
      },
      //删除待阅接收人
      DeleteActualReadReceiver(row){
        deleteActualReadReceiver(row.id).then(response => {
          if (200 === response.code) {
            this.$modal.msgSuccess('删除成功');
            this.ActualReadReceiverGroupData();
          } else {
            this.$modal.alertError(response.msg);
          }
        });
      },
      //待阅接收人集团选择
      addReadPer(type){
        this.receiverGrade=type;
        this.$nextTick(
          ()=>{
            this.$refs.recipient.show();
          }
        )

      },
      //日常详情
      dailyDetail(){
        this.dailyVisible=true;
      },
      //日常关闭
      dailyClose(){
        this.dailyVisible=false;
      }
    }
  }

</script>
<style lang="scss" scoped>
  .dialog-body {
    height: 70vh;
  }
  .depart_li {
    min-width: 84px;
    height: auto;
    position: relative;
    background-color: #e6f7ff;
    color: #40a9ff;
    line-height: 30px;
    margin: 0 6px 0;
    display: inline-block;
    padding: 0 30px 0 12px;
    border-radius: 2px;
    box-sizing: border-box;
    .icon {
      float: right;
      cursor: pointer;
      position: absolute;
      right: 8px;
      top: 6px;
      font-size: 16px;
    }
  }
</style>
