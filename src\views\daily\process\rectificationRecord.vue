<!--6:整改记录-经办-->
<template>
  <div>
      <div>
        <BlockCard title="基本信息">
          <el-row>
            <el-form
              ref="elForm"
              :model="formData"
              :rules="rules"
              size="medium"
              label-width="138px"
            >
              <el-col :span="6">
                <el-form-item label="系统编号" prop="auditCode">
                  <span>{{ formData.auditCode }}</span>
                </el-form-item>
              </el-col>

              <el-col :span="6">
                <el-form-item label="问题编号" prop="problemCode">
                  <span>{{ formData.problemCode }}</span>
                </el-form-item>
              </el-col>

              <el-col :span="6">
                <el-form-item label="发现日期" prop="findTime">
                  <span>{{ formData.findTime }}</span>
                </el-form-item>
              </el-col>

              <el-col :span="6">
                <el-form-item label="受理日期" prop="acceptTime">
                  <span>{{ formData.acceptTime }}</span>
                </el-form-item>
              </el-col>

              <el-col :span="24">
                <el-form-item label="违规事项" prop="problemTitle">
                  <span>{{ formData.problemTitle }}</span>
                </el-form-item>
              </el-col>

              <el-col :span="24">
                <el-form-item label="问题线索描述" prop="problemDescribe">
                  <span>{{ formData.problemDescribe }}</span>
                </el-form-item>
              </el-col>

              <el-col :span="24">
                <el-form-item label="涉及专业线" prop="specLists">
                  <el-checkbox-group
                    :key="formData.specLists"
                    v-model="formData.specLists"
                    size="medium"
                    disabled="true"
                  >
                    <el-checkbox
                      v-for="item in specList"
                      :key="item.specCode"
                      border
                      :label="item.specCode"
                    >{{ item.specName }}</el-checkbox>
                  </el-checkbox-group>
                </el-form-item>
              </el-col>

              <el-col :span="24">
                <el-form-item  label="涉及单位/部门/人员" prop="field107">
                  <PersList
                    :key="problemId||relevantTableId||relevantTableName"
                    :edit='persEdit'
                    :problemId="problemId"
                    :relevantTableId="relevantTableId"
                    :relevantTableName="relevantTableName"
                    ref="pers"
                  ></PersList>
                </el-form-item>
              </el-col>
            </el-form>
          </el-row>
        </BlockCard>
        <Remind
          :key="formData.actualFlag"
          :actualFlag="formData.actualFlag"
        ></Remind>
        <BlockCard title="责任追究">
          <el-row>
            <el-form
              ref="elForm"
              :model="formData"
              :rules="rules"
              size="medium"
              label-width="138px"
            >
              <el-col :span="6">
                <el-form-item label="追责总人次" prop="auditCode">
                  <span>{{ formData.accountabilityNumber }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="责任追究结果" prop="accountabilityResult">
                  <el-input
                    v-model="formData.accountabilityResult"
                    type="textarea"
                    placeholder="请填写责任追究结果"
                    :autosize="{ minRows: 4, maxRows: 4 }"
                    :style="{ width: '100%' }"
                  />
                </el-form-item>
              </el-col>
            </el-form>
          </el-row>
        </BlockCard>
        <BlockCard title="整改信息">
          <el-row>
            <el-form
              ref="elForm1"
              :model="formData"
              :rules="rules"
              size="medium"
              label-width="138px"
            >

              <el-col :span="6">
                <el-form-item label="整改完成时间" prop="reformFinishDate">
                  <el-date-picker
                    v-model="formData.reformFinishDate"
                    format="yyyy-MM-dd"
                    value-format="yyyy-MM-dd"
                    :style="{ width: '100%' }"
                    placeholder="请选择整改完成时间"
                    :picker-options="pickerOptions"
                    clearable
                  />
                </el-form-item>
              </el-col>

              <el-col :span="24">
                <el-form-item label="整改责任单位" prop="reformResponsibleUnits" class="input-btn">
                  <div class="list1">
                    <div
                      v-for="(item, index) of formData.reformResponsibleUnits"
                      :key="index"
                      class="list1-one"
                    >
                      <span>{{ item.reformUnitName }}</span>
                      <span
                        class="close"
                        @click="deletetReformResponsibleUnits(item, index)"
                      ><i class="el-icon-close icon iconfont"></i></span>
                    </div>
                  </div>
                  <el-button  type="primary" plain icon="el-icon-plus" size="mini" @click="addReformResponsibleUnits">添加</el-button>
                </el-form-item>
              </el-col>

              <el-col :span="24">
                <el-form-item label="整改责任人" prop="reformResponsiblePersons" class="input-btn">
                  <div class="list1">
                    <div
                      v-for="(item, index) of formData.reformResponsiblePersons"
                      :key="index"
                      class="list1-one"
                    >
                      <span>{{ item.reformPersonName }}</span>
                      <span
                        class="close"
                        @click="deletetReformResponsiblePersons(item, index)"
                      ><i class="el-icon-close icon iconfont"></i>
                      </span>
                    </div>
                  </div>
                  <el-button  type="primary" plain icon="el-icon-plus" size="mini" @click="addReformResponsiblePersons">添加</el-button>
                </el-form-item>
              </el-col>

              <el-col :span="8">
                <el-form-item label="完善制度数量" prop="perfectSystemNumber">
                  <el-input-number
                    v-model="formData.perfectSystemNumber"
                    :min="0"
                    placeholder="完善制度数量"
                    controls-position="right"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="挽回损失金额(万元)" prop="retrieveLossAmount">
                <el-input-number
                  v-model="formData.retrieveLossAmount"
                  :min="0"
                  :precision="2"
                  placeholder="挽回损失金额(万元)"
                  controls-position="right"
                />
                </el-form-item>
              </el-col>

               <el-col :span="8">
                <el-form-item label="降低损失风险(万元)" prop="reduceLossRisk">
                <el-input-number
                  v-model="formData.reduceLossRisk"
                  :min="0"
                  :precision="2"
                  placeholder="降低损失风险(万元)"
                  controls-position="right"
                />
                </el-form-item>
              </el-col>

              <el-col :span="24">
                <el-form-item label="完善制度名称" prop="perfectSystemName">
                  <el-input
                    v-model="formData.perfectSystemName"
                    type="textarea"
                    placeholder="请填写完善制度名称"
                    :autosize="{ minRows: 4, maxRows: 4 }"
                    :style="{ width: '100%' }"
                  />
                </el-form-item>
              </el-col>

              <el-col :span="24">
                <el-form-item label="采取的主要措施" prop="reformMeasure">
                  <el-input
                    v-model="formData.reformMeasure"
                    type="textarea"
                    placeholder="请填写采取的主要措施"
                    :autosize="{ minRows: 4, maxRows: 4 }"
                    :style="{ width: '100%' }"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="整改效果" prop="reformEffect">
                  <el-input
                    v-model="formData.reformEffect"
                    type="textarea"
                    placeholder="请填写整改效果"
                    :autosize="{ minRows: 4, maxRows: 4 }"
                    :style="{ width: '100%' }"
                  />
                </el-form-item>
              </el-col>

              <el-col :span="24">
                <el-form-item label="整改复核情况" prop="reformReview">
                  <el-input
                    v-model="formData.reformReview"
                    type="textarea"
                    placeholder="请填写整改复核情况"
                    :autosize="{ minRows: 4, maxRows: 4 }"
                    :style="{ width: '100%' }"
                  />
                </el-form-item>
              </el-col>

              <el-col :span="24">
                <el-form-item label="其他工作成效" prop="otherAchievement">
                  <el-input
                    v-model="formData.otherAchievement"
                    type="textarea"
                    placeholder="请填写其他工作成效"
                    :autosize="{ minRows: 4, maxRows: 4 }"
                    :style="{ width: '100%' }"
                  />
                </el-form-item>
              </el-col>
            </el-form>
          </el-row>
        </BlockCard>
        <BlockCard
          title="附件列表"
        >
          <FileUpload
            v-if="formData.id!=''&&formData.businessTable!=''"
            :edit='edit'
            :key="problemId||formData.id||formData.businessTable"
            :problemId="problemId"
            :relevantTableId="formData.id"
            :busiTable = "formData.businessTable"
            :relevantTableName="formData.businessTable"
            flowType="VIOL_DAILY"
            problemStatus="6"
            flowKey = "SupervisionDailyReport"
            ref="file"
          ></FileUpload>

        </BlockCard>
        <!--修改记录-->
        <el-dialog :visible.sync="visibleModify" width="80%"  append-to-body title="修改记录">
          <modifyRecord v-if="visibleModify"
                        ref="modify"
                        edit="true"
                        :key="problemId||relevantTableId||relevantTableName"
                        :problemId="problemId"
                        :relevantTableId="relevantTableId"
                        :relevantTableName="relevantTableName"
                        :problemStatus="6"
                        @modifySave="modifySave"
          ></modifyRecord>
          <div slot="footer" class="dialog-footer">
            <el-button size="mini" type="primary" @click="modifyClose">保存</el-button>
          </div>
        </el-dialog>
      </div>
    <el-dialog :visible.sync="VisibleCheckTree" width="60%" append-to-body title="整改责任单位">
      <Check2Tree
        v-if="VisibleCheckTree"
        :key="selectTree"
        ref="checkTree2"
        :url="url1"
        :selectTree="selectTree"
        :params="{
        unitName:'',
        problemId:problemId,
        businessId:formData.id
        }"
        @list="persList"
      />
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="savePers">保存</el-button>
      </div>
    </el-dialog>

    <el-dialog :visible.sync="VisibleCheckTree2" width="60%" append-to-body title="整改责任人">
      <CheckTree
        v-if="VisibleCheckTree2"
        :key="selectTree"
        ref="checkTree"
        :url="url2"
        :selectTree="selectTree"
        :params="{
        userName:'',
        problemId:problemId,
        businessId:formData.id
        }"
        @list="persLists"
      />
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="savePer">保存</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import BlockCard from '@/components/BlockCard'
import Remind from './../../components/remind';
import FileUpload from './../../components/fileUpload';//附件
import Check2Tree from './../tree/check2Tree'// checkTree
import CheckTree from './../tree/check2Tree'// checkTree
import { queryDailyCheckInfo,temporarySaveViolationReformRecord,saveViolationReformRecord,saveReformResponsibleUnit,saveReformResponsiblePerson,deleteReformResponsibleUnit,deleteReformResponsiblePerson } from '@/api/daily/process/rectificationRecord'
import {
  //生成情形范围修改记录
  generateSituationRangeModifyRecord
  //生成业务数据修改记录
  ,generateBusinessModifyRecord} from "@/api/daily/modifyRecord/modifyRecord";//修改记录js方法
import PersList from './../tree/persList';//tree
import modifyRecord from './../modifyRecord';//修改记录
export default {
  components: {
    BlockCard,
    Remind,
    Check2Tree,
    CheckTree,FileUpload,modifyRecord,PersList
  },
  dicts: ['VIOLD_DAILY_SPEC', 'VIOLD_ADVER_EFFECT_DES'],
  props: {
    problemId: {
      type: String
    }
  },
  data() {
    return {
      relevantTableId:'',
      relevantTableName:'',
      visibleModify:false,
      rules: {
        reformMeasure: [
          { min: 1, max: 2048, message: '长度在 1 到 2048 个字符', trigger: 'blur' }
        ],
        perfectSystemName: [
          { min: 1, max: 255, message: '长度在 1 到 255 个字符', trigger: 'blur' }
        ],
      },
      persEdit:false,
      edit:true,
      url1:'/colligate/violationDailyReform/responsibleUnitCheckedTree',
      url2:'/colligate/violationDailyReform/nationalOrganizationPersonTree',
      flag: false,
      visible: false,
      visibleTree: false,
      VisibleCheckTree:false,
      VisibleCheckTree2:false,
      formData: {
        accountabilityFlag: '',
        actualFlag: '',
        appealFlag: '',
        appealResult: '',
        appealResultList: [],
        auditCode: '',
        createBy: '',
        createByOrg: '',
        createLoginName: '',
        createPostId: '',
        createTime: '',
        dealPostIdPre: '',
        deletedFlag: '',
        expandFileTypeOptions: [],
        id: '',
        investigateTime: '',
        isCompleted: '',
        problemAreaCode: '',
        problemAreaCodePre: '',
        problemAreaName: '',
        problemAreaNamePre: '',
        problemCode: '',
        problemDescribe: '',
        problemId: '',
        problemProvCode: '',
        problemProvCodePre: '',
        problemProvName: '',
        problemProvNamePre: '',
        problemTitle: '',
        relevantTableName: '',
        relevorgList: [],
        reviewerList: [],
        specSelectedList: [],
        unInvestigateReason: '',
        updateBy: '',
        updateByOrg: '',
        updateLoginName: '',
        updatePostId: '',
        updateTime: ''
      },

      specList: [],

      seriousAdverseEffectsFlagOptions: [
        {
          label: '是',
          value: 1
        },
        {
          label: '否',
          value: 0
        }
      ],
    //整改完成时间选择处理
    pickerOptions: {
        disabledDate: (time) => {
          // 禁用所有早于 investigateTime 责任追究时间的日期（不包括责任追究时间）
          const investigateTimestamp = new Date(this.formData.investigateTime);
          investigateTimestamp.setHours(0, 0, 0, 0);
          return time.getTime() < investigateTimestamp
        }
      },
    }
  },
  computed: {},
  watch: {
    "formData.retrieveLossAmount": {
      handler(captchaType) {
        if(captchaType>this.formData.lossAmount){
          this.formData.retrieveLossAmount = ''
          this.$message.error('【挽回损失金额(万元)】需小于【预估损失金额（万元）】');
        }
      },
    },
    "formData.reduceLossRisk": {
      handler(captchaType) {
        if(captchaType>this.formData.lossRisk){
          this.formData.reduceLossRisk = ''
          this.$message.error('【降低损失风险(万元)】需小于【预估损失风险（万元）】');
        }
      },
    }
  },
  created() {
    this.queryDailyCheckInfo();
  },
  mounted() {},
  methods: {
    //保存数据
    savePers(){
      this.$refs.checkTree2.list();
    },
    //数据整改责任单位
    persList(data){
      let list=[];
      if(!data.length)
        return false;
      for (let i = 0; i < data.length; i++) {
        list.push(data[i].id);
      }
      let query = {
        id: this.formData.id,
        problemId:this.problemId,
        waitSaveReformResponsibleCodes:list
      };
      saveReformResponsibleUnit(query).then(
        response => {
          const { code, data } = response;
          if (code === 200) {
            this.VisibleCheckTree = false;
            this.queryDailyCheckInfoPer();
          }
        }
      )
    },
    //保存数据
    savePer(){
      this.$refs.checkTree.list();
    },
    //数据整改责任人
    persLists(data){
      let list=[];
      if(!data.length)
        return false;
      for (let i = 0; i < data.length; i++) {
        list.push(data[i].id);
      }
      let query = {
        id: this.formData.id,
        problemId:this.problemId,
        waitSaveReformResponsibleCodes:list
      };
      saveReformResponsiblePerson(query).then(
        response => {
          const { code, data } = response;
          if (code === 200) {
            this.VisibleCheckTree2 = false;
            this.queryDailyCheckInfoPer();
          }
        }
      )
    },
    /**初始化数据*/
    queryDailyCheckInfo () {
      this.loading = true;
      let array = [];
      queryDailyCheckInfo(this.problemId).then(
        response => {
          let specSelectedList = response.data.involveProfessionalLines;
          this.formData = {...this.formData, ...response.data};
          for (let i = 0, len = specSelectedList.length; i < len; i++) {
            array.push(specSelectedList[i].specCode);
          }
          this.actualFlag = response.data.actualFlag;
          this.lossRiskTypeOptions = response.data.lossRiskTypeOptions;
          this.formData.specLists = array;
          this.specList = specSelectedList;
          this.relevantTableId = response.data.id;
          this.problemSourceList = response.data.problemSourceList;
          this.relevantTableName = response.data.businessTable;
          this.loading = false;
          this.$nextTick(() => {
            // this.$refs.pers.DueryDepartmentSelectInfo();
            this.$refs.pers.DueryDepartmentSelectInfo();
            this.$refs.file.ViolationFileItems();
          });
          this.$emit('closeLoading');
        }
      );
    },
    /**初始化整改责任单位与整改责任人*/
    queryDailyCheckInfoPer () {
      queryDailyCheckInfo(this.problemId).then(
        response => {
          this.formData.reformResponsibleUnits = response.data.reformResponsibleUnits;
          this.formData.reformResponsiblePersons = response.data.reformResponsiblePersons;
        }
      );
    },
    /** 提交数据*/
    submitForm() {
      this.$refs['elForm'].validate((valid) => {
        if (!valid) return
        // TODO 提交表单
      })
    },
    nextStep() {
      this.$refs['elForm1'].validate(valid => {
        if(valid){
          saveViolationReformRecord(this.formData).then(
            response => {
              if(response.code==200)
                this.$modal.msgSuccess('保存成功')
                this.Modify();
            }
          );
        }
      })
    },
    /** 保存数据*/
    publicSave() {
      this.$refs['elForm1'].validate(valid => {
        if(valid){
          temporarySaveViolationReformRecord(this.formData).then((response) => {
            this.$modal.msgSuccess('保存成功')
          })
        }
      });
    },

    resetForm() {
      this.$refs['elForm'].resetFields();
      this.$refs['elForm1'].resetFields();
    },
    // 打开弹窗
    show() {
      this.visible = true
    },
    addReformResponsibleUnits() {
      this.selectTree = [];
      this.selectTree = this.formData.reformResponsibleUnits;
      for(let i = 0;i<this.selectTree.length;i++){
        this.selectTree[i].id = this.selectTree[i].reformUnitCode;
        this.selectTree[i].name = this.selectTree[i].reformUnitName;
      }
      this.VisibleCheckTree = true;
    },
    addReformResponsiblePersons() {
      this.selectTree = [];
      this.selectTree = this.formData.reformResponsiblePersons;
      for(let i = 0;i<this.selectTree.length;i++){
        this.selectTree[i].id = this.selectTree[i].reformPersonPost+"";
        this.selectTree[i].name = this.selectTree[i].reformPersonName;
      }
      this.VisibleCheckTree2 = true;
    },
    //删除单位
    deletetReformResponsibleUnits(item,index){
      deleteReformResponsibleUnit(item.id).then((response) => {
        this.queryDailyCheckInfoPer();
      })
    },
    //删除责任人人
    deletetReformResponsiblePersons(item,index){
      deleteReformResponsiblePerson(item.id).then((response) => {
        this.queryDailyCheckInfoPer();
      })
    },
    //调用修改记录保存
    modifyClose(){
      this.$refs.modify.save();
    },
    //修改记录保存后
    modifySave(type){
      if(type){
        this.visibleModify=false;
        this.$emit('handle',1);
      }else{
        this.visibleModify=false;
      }
    },
    //修改记录
    Modify(){
      //生成情形范围修改记录
      generateSituationRangeModifyRecord(this.problemId,this.relevantTableId);
      //生成业务数据修改记录
      generateBusinessModifyRecord(this.problemId,this.relevantTableId,this.relevantTableName).then(
        response => {
          let isExistDifferenceField = response.data.isExistDifferenceField
          console.info(isExistDifferenceField);
          if(isExistDifferenceField){
            this.visibleModify=true;
          }else{
            this.$emit('handle',1);
          }
        }
      )
    }

  }
}
</script>
<style scoped lang="scss">
.input-btn {
  ::v-deep .el-form-item__content {
    display: flex;
    button {
      margin-left: 8px;
      height: 35px;
    }
  }
}
.float-right {
  float: right;
}
.edit-span {
  white-space: normal;
  overflow-y: auto;
  overflow-wrap: break-word;
  word-break: normal;
  height: 61px;
  line-height: 30px;
  text-align: left;
  padding: 0px 10px;
  display: block;
}
::v-deep .editStyle {
  padding: 0px !important;
}
::v-deep .editStyle div.cell {
  padding: 0px !important;
}

::v-deep .editStyle .el-input--mini .el-input__inner {
  height: 56px;
  line-height: 56px;
  border: 0px;
}
.list1 {
  overflow: hidden;
  .list1-one {
    background-color: #e6f7ff;
    color: #40a9ff;
    margin: 0 10px 10px 10px;
    float: left;
    height: 30px;
    line-height: 30px;
    padding: 0 12px 0 12px;
    border-radius: 2px;
    .close {
      padding: 8px;
      cursor: pointer;
    }
  }
}
.list2 {
  display: flex;
  align-items: center;
  .list2-one {
    width: 250px;
    height: 30px;
    line-height: 30px;
    margin: 0 0 12px 0;
    display: inline-block;
    padding: 0 6px 0 12px;
    border-radius: 2px;
    box-sizing: border-box;
    position: relative;
    margin-right: 35px;
    span {
      text-align: left;
      display: inline-block;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      -webkit-line-clamp: 1;
      -webkit-box-orient: vertical;
      word-break: break-all;
      max-width: 100%;
      padding-right: 6px;
      box-sizing: border-box;
    }
  }
  .list2-one-bg {
    background: #f4f4f4;
    border-radius: 4px;
    color: #000;
    width: 250px;
    height: 30px;
    line-height: 30px;
    margin: 0 0 12px 0;
    display: inline-block;
    padding: 0 6px 0 12px;
    border-radius: 2px;
    box-sizing: border-box;
    position: relative;
    margin-right: 35px;

    span {
      text-align: left;
      display: inline-block;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      -webkit-line-clamp: 1;
      -webkit-box-orient: vertical;
      word-break: break-all;
      max-width: 100%;
      padding-right: 6px;
      box-sizing: border-box;
    }
    &::before {
      position: absolute;
      content: "";
      right: -30px;
      top: 15px;
      width: 25px;
      height: 1px;
      background: #d9d9d9;
    }
  }
}
.bottom-line {
  padding: 10px 0;
  text-align: center;
  border-top: 1px solid #d9d9d9;
  color: #f5222d !important;
}
</style>
