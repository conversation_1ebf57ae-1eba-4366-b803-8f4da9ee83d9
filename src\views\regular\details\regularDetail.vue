<!--上报详情-->
<template>
  <div class="regular">
    <el-dialog  :visible.sync="visible" @open="regularHandlerFillData"  append-to-body  :title="title" width="90%">
        <el-form ref="detailForm" :model="formData" :rules="rules" size="medium" label-width="118px">
          <el-row>
          <!--基本信息-->
          <el-col :span="24">
            <ReportBaseInfo
              v-if="baseInfoFlag"
              :key="index"
              :regularReportId="regularReportId"
              ref="baseInfo"
            ></ReportBaseInfo>
          </el-col>
          <el-col :span="24">
            <el-form-item   label="上报单位">
              <el-tabs v-model="type" @tab-click="queryReportDepartmentList">
                <el-tab-pane :label="tabName0" name = "0"></el-tab-pane>
                <el-tab-pane :label="tabName2" name = "2"></el-tab-pane>
                <el-tab-pane :label="tabName1" name = "1"></el-tab-pane>
              </el-tabs>
              <!--全部-->
              <el-form v-show="table0ShowFlag">
                <el-button
                  size="mini"
                  type="primary"
                  icon="el-icon-plus"
                  @click="violDailyAccept"
                  v-show="status!='2'"
                  class="float-right margin-top-50"
                  plain
                >补录上报单位
                </el-button>
                <div style="height:calc(71vh - 280px);min-height:440px">
                <el-table v-loading="loading" :data="tableList" height="100%" :key="table0ShowFlag" >
                  <el-table-column label="序号" type="index" min-width="4%" align="center"/>
                  <el-table-column label="上报单位" prop="reportUnitName" align="center" min-width="30%"/>
                  <el-table-column label="接口人" prop="interfaceUserName" align="center" min-width="15%"/>
                  <el-table-column label="邮箱" prop="interfaceUserMail"  align="center" min-width="20%"/>
                  <el-table-column label="联系电话" prop="interfaceUserPhone" align="center" min-width="20%"/>
                  <el-table-column label="退回次数" prop="backNum" align="center" min-width="10%">
                    <template slot-scope="scope">
                      <el-button
                        size="mini"
                        type="text"
                        icon="el-icon-show"
                        @click="showHistoryInfo(scope.row)"
                        v-if="scope.row.backNum>0"
                      >{{scope.row.backNum}}
                      </el-button>
                      <span v-if="scope.row.backNum == 0">{{scope.row.backNum}}</span>
                    </template>
                  </el-table-column>
                  <el-table-column label="状态" prop="statusName" align="center" min-width="6%"/>
                </el-table>
                </div>
              </el-form>

              <!--进行中-->
              <el-form v-show="table1ShowFlag">
                <el-button
                  size="mini"
                  type="primary"
                  icon="el-icon-bell"
                  @click="urgeAll"
                  class="float-right margin-top-50"
                  plain
                >批量催办
                </el-button>
                <div style="height:calc(71vh - 280px);min-height:440px">
                <el-table v-loading="loading" :key="table1ShowFlag" :data="tableList" height="100%"  @selection-change="handleSelectionChange">
                  <el-table-column
                    type="selection"
                    min-width="4%">
                  </el-table-column>
                  <el-table-column label="序号" type="index" min-width="4%" align="center"/>
                  <el-table-column label="上报单位" prop="reportUnitName" align="center" min-width="20%"/>
                  <el-table-column label="接口人" prop="interfaceUserName" align="center" min-width="15%"/>
                  <el-table-column label="邮箱" prop="interfaceUserMail"  align="center" min-width="10%"/>
                  <el-table-column label="联系电话" prop="interfaceUserPhone" align="center" min-width="10%"/>
                  <el-table-column label="所处阶段" prop="procFlowName" align="center" min-width="15%"/>
                  <el-table-column label="退回次数" prop="backNum" align="center" min-width="10%">
                    <template slot-scope="scope">
                      <el-button
                        size="mini"
                        type="text"
                        icon="el-icon-show"
                        @click="showHistoryInfo(scope.row)"
                        v-if="scope.row.backNum>0"
                      >{{scope.row.backNum}}
                      </el-button>
                      <span v-if="scope.row.backNum == 0">{{scope.row.backNum}}</span>
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" width="100px" align="center">
                    <template slot-scope="scope">
                      <el-button
                        size="mini"
                        type="text"
                        icon="el-icon-bell"
                        title="提醒"
                        @click="toRemind(scope.row)"
                      >
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
                </div>
              </el-form>
              <!--已完成-->

              <el-form  v-show="table2ShowFlag">
                <el-button
                  size="mini"
                  type="primary"
                  icon="el-icon-bottom"
                  @click="handleExport"
                  class="float-right margin-top-50"
                  plain
                >批量下载附件</el-button>
                <div style="height:calc(71vh - 280px);min-height:440px">
                <el-table v-loading="loading" :key="table2ShowFlag" :data="tableList"  height="100%"  @selection-change="handleSelectionChangeFile">
                  <el-table-column
                    type="selection"
                    min-width="4%">
                  </el-table-column>
                  <el-table-column label="序号" type="index" min-width="4%" align="center"/>
                  <el-table-column label="上报单位" prop="reportUnitName" align="center" min-width="20%"/>
                  <el-table-column label="接口人" prop="interfaceUserName" align="center" min-width="15%"/>
                  <el-table-column label="邮箱" prop="interfaceUserMail"  align="center" min-width="10%"/>
                  <el-table-column label="联系电话" prop="interfaceUserPhone" align="center" min-width="10%"/>
                  <el-table-column label="完成时间" prop="procEndTime" align="center" min-width="10%"/>
                  <el-table-column label="退回次数" prop="backNum" align="center" min-width="5%">
                    <template slot-scope="scope">
                        <el-button
                          size="mini"
                          type="text"
                          icon="el-icon-show"
                          @click="showHistoryInfo(scope.row)"
                          v-if="scope.row.backNum>0"
                        >{{scope.row.backNum}}
                        </el-button>
                        <span v-if="scope.row.backNum == 0">{{scope.row.backNum}}</span>
                    </template>
                  </el-table-column>
                  <el-table-column label="操作"  width="100px" align="center">
                    <template slot-scope="scope">
                      <el-button
                        size="mini"
                        type="text"
                        icon="el-icon-search"
                        title="查看"
                        @click="detail(scope.row)"
                      >
                      </el-button>
                      <el-button
                        size="mini"
                        type="text"
                        icon="el-icon-sort"
                        title="退回"
                        @click="backUnitDetail(scope.row)"
                      >
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
                </div>
              </el-form>

            </el-form-item>

          </el-col>
          </el-row>
        </el-form>
    </el-dialog>
    <ViolDailyAccept
      :key="regularReportId"
      ref="add"
      :regularReportId="regularReportId"
      @list="regularHandlerFillData"
    ></ViolDailyAccept>

    <showDetail
      key="reportUnitId"
      ref="showDetail"
      :reportUnitId="reportUnitId"
      :regularReportId="regularReportId"
      :procInsId="procInsId"
      :check="check"
    >
    </showDetail>
    <showHistory
      :key="reportUnitId"
      ref="showHistory"
      :reportUnitCode="reportUnitCode"
      :regularReportId="regularReportId"
      :orgGrade="orgGrade"
    >
    </showHistory>
  </div>
</template>

<script>
  import {
    selectReportInfo//加载数据
    ,backUnitDetail//退回
  } from '@/api/regular/add/addAndEditRegular.js';
  import ReportBaseInfo from './regularBaseDetail';//基本数据
  import ViolDailyAccept from './../add/addRegularSupplementary';//补录
  import showDetail from './provHandlerDetail';//已完成详情
  import showHistory from './showHistoryList';//显示历史数据
  import {
    listLength0
    ,listLength1
    ,listLength2
    ,selectReportDetailList//查询数据列表
    ,regularReportUrge//单条催办
    ,regularReportUrgeAll//批量催办
  } from '@/api/regular/details/regularDetail';//详情js
    export default {
        name: "regularDetail"
      ,components:{
        ReportBaseInfo,
        ViolDailyAccept//补录
        ,showDetail//已完成详情
        ,showHistory//显示历史版本数据
      }
      ,props:{
        regularReportId:{
          type:String,
          default:''
        }
        ,status:{
          type:String,
          default:''
        }
        ,title:{
          type:String,
          default:''
        }
        ,procInsId:{
          type:String,
          default:''
        }
      }
      ,data(){
          return {
            visible:false//弹窗
            ,loading:false
            ,baseInfoFlag:false//加载基本信息
            ,type:'0'//页签类型 0：全部；1：进行中；2：已完成
            ,tabName0:'全部'
            ,tabName1:'进行中'
            ,tabName2:'已完成'
            ,table0ShowFlag:true
            ,table1ShowFlag:false
            ,table2ShowFlag:false
            ,tableList: []//表格数据
            ,selection:[]//表格选中数据
            ,selectionInfoId:[]//表格选中需要下载的数据
            ,violDailyAcceptFlag:false//显示补录弹窗
            ,reportUnitId:undefined
            ,detailList:[]
            ,reportUnitCode:''
            ,orgGrade:''
            , check:false //已完成-查看页面是否显示校验按钮
          }
      }
      ,methods:{
        // 显示弹框
        show() {
          this.visible = true;
          this.regularHandlerFillData();
        },
        //补录
        violDailyAccept(){
          this.$refs.add.show();
        },
          //加载基本信息数据
        regularHandlerFillData(){
          selectReportInfo(this.regularReportId).then(
            response => {
              if(response.code === 200){
                this.formData = response.data;
                this.formData.reportTime = [];
                this.formData.reportTime[0] = this.formData.reportStartTime;
                this.formData.reportTime[1] = this.formData.reportEndTime;
                this.baseInfoFlag = true;
                this.orgGrade = this.formData.orgGrade;
                this.$nextTick(()=>{
                  this.$refs.baseInfo.showInfo(this.formData);
                })
                this.check = this.formData.checkFlag;
                this.queryReportTypeCount();
              }else{
                this.$modal.msgError(response.msg);
              }
            }
          )
        },
        //加载页签各类型数量
        queryReportTypeCount(){
          listLength0(this.regularReportId).then(
            response => {
              this.tabName0 = "全部（"+response.data.length+"）";
            }
          );
          listLength1(this.regularReportId).then(
            response => {
              this.tabName1 = "进行中（"+response.data.length+"）";
            }
          );
          listLength2(this.regularReportId).then(
            response => {
              this.tabName2 = "已完成（"+response.data.length+"）";
            }
          )
          //加载上报单位列表
          this.queryList();
        },
        //加载各页签下上报单位列表
        queryReportDepartmentList(obj){
          this.type = obj.name;
          if(this.type === '0'){
            this.table0ShowFlag = true;
            this.table1ShowFlag = false;
            this.table2ShowFlag = false;
          }else if(this.type === '1'){
            this.table0ShowFlag = false;
            this.table1ShowFlag = true;
            this.table2ShowFlag = false;
          }else if(this.type === '2'){
            this.table0ShowFlag = false;
            this.table1ShowFlag = false;
            this.table2ShowFlag = true;
          }
          this.$nextTick(()=>{
            this.queryList();
            }
          )
        },
        //加载列表
        queryList(){
          this.loading = true;
          selectReportDetailList(this.regularReportId,this.type).then(
            response => {
              this.loading = false;
              this.tableList = response.data;
            }
          )
        },
        //单条催办
        toRemind(data){
          this.$modal.confirm("是否催办该单位？").then(() =>{
            regularReportUrge(this.regularReportId,data.reportUnitId).then(
              response => {
                if (response.code === 200) {
                  this.$modal.msgSuccess("催办成功！");
                  this.queryList();
                } else {
                  this.$modal.msgError(response,msg);
                }
              }
            )
          })
        },
        //进行中表格选中数据
        handleSelectionChange(selection){
          this.selection = selection;
        },
        //已完成 选中需要批量下载的数据
        handleSelectionChangeFile(selection){
          this.selectionInfoId = [];
          for(var i = 0;i<selection.length;i++){
            this.selectionInfoId[i] = selection[i].reportUnitId;
          }
        },
        /** 批量导出按钮操作 */
        handleExport() {
          if (this.selectionInfoId.length == 0) {
            this.$modal.msgError("请选择需要下载的上报单位！");
            return false;
          }
          const zipName = this.formData.reportYear+"年"+this.formData.reportTitle+"【"+(this.formData.reportType=='1'?'定期报告':'其他报告')+"】附件.zip"
          this.download('/regular/download/batchDownLoadRegularFiles', {
            reportUnitIds:this.selectionInfoId
            ,orgGrade:this.orgGrade
            ,regularReportId:this.regularReportId
            ,zipName:zipName
          }, zipName)
        },
        //批量催办
        urgeAll(){
          let idList = [];
          for(let i = 0;i<this.selection.length;i++){
            idList.push(this.selection[i].reportUnitId);
          }
          if (idList.length == 0) {
            this.$modal.msgError("请选择需要催办的上报单位！");
            return false;
          }
          this.$modal.confirm("批量催办？").then(()=>{
            regularReportUrgeAll(this.regularReportId,idList).then(
              response => {
                if (response.code === 200) {
                  this.$modal.msgSuccess("催办成功！");
                  this.queryList();
                } else {
                  this.$modal.msgError(response.msg);
                }
              }
            )
          })
        },
        //打开补录窗口
        showViolDailyAccept(){
          this.violDailyAcceptFlag = true;
        },
        //已完成详情
        detail(row){
          this.procInsId = row.procInsId;
          this.reportUnitId = row.reportUnitId;
          this.$refs.showDetail.show();
        },
        //退回
        backUnitDetail(row){
          this.detailList = [];
          this.detailList[0] = row;
          this.$modal.confirm('是否确定将'+row.reportUnitName+'的上报数据退回？').then(()=> {
            backUnitDetail(this.regularReportId,this.detailList).then(
              response => {
                if (response.code === 200) {
                  this.$modal.msgSuccess("退回成功！");
                  var obj = {};
                  obj.name = '2';
                  this.queryReportTypeCount();
                  this.queryReportDepartmentList(obj);
                } else {
                  this.$modal.msgError(response.msg);
                }
              }
            )
          }).catch(() => {});
        },
        //显示历史数据+本条数据
        showHistoryInfo(row){
          this.reportUnitCode = row.reportUnitCode;
          this.$refs.showHistory.show();
        }
      }
    }
</script>

<style lang="scss">
  .margin-top-50{
   position: absolute;
    right: 0;
    top: 0;
  }
  .regular{
  .el-dialog__body{
    background: #fff !important;
  }
  }
</style>
