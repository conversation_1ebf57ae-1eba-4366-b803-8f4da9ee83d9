<template>
  <div class="navbar" :style="{ backgroundColor: settings.theme}">
    <!--<hamburger id="hamburger-container" :is-active="sidebar.opened" class="hamburger-container" @toggleClick="toggleSideBar" />-->
    <logo v-if="showLogo" :collapse="isCollapse"/>
    <!--<breadcrumb id="breadcrumb-container" class="breadcrumb-container" v-if="!topNav"/>-->
    <!--<top-nav id="topmenu-container" class="topmenu-container" v-if="topNav"/>-->

    <div class="right-menu">
      <el-button type="text">
        <div class="avatar-wrapper margin-right16" style="color:#fff;">
          <span @click="fileDownload()"  style="color:#fff;" class="el-icon-s-comment">操作手册</span>
        </div>
      </el-button>
      <el-button type="text">
        <div class="avatar-wrapper margin-right16" style="color:#fff;">
          <span @click="openHelpDoc()"  style="color:#fff;" class="el-icon-s-help">支撑渠道</span>
        </div>
      </el-button>
      <el-dropdown  @command="handleCommand" trigger="click">
        <div class="avatar-wrapper margin-right16"
             :style="{ color: sideTheme === 'theme-dark' ? variables.logoTitleColor : variables.logoTitleColor }">
          {{orgName}}
          <i class="el-icon-caret-bottom"/>
        </div>
        <el-dropdown-menu>
          <el-dropdown-item v-for="item in depts" :command="item.userId">{{item.orgName}}</el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
      <el-dropdown class="avatar-container right-menu-item hover-effect" trigger="click">
        <div class="avatar-wrapper"
             :style="{ color: sideTheme === 'theme-dark' ? variables.logoTitleColor : variables.logoTitleColor }">
          {{nickName}}
          <!--<i class="el-icon-caret-bottom"/>-->
        </div>
        <el-dropdown-menu slot="dropdown">
          <router-link to="/user/profile">
            <el-dropdown-item>个人中心</el-dropdown-item>
          </router-link>
          <el-dropdown-item @click.native="setting = true">
            <span>布局设置</span>
          </el-dropdown-item>
          <el-dropdown-item divided @click.native="logout">
            <span>退出登录</span>
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>
    <Navbar ref="navbar"/>
  </div>
</template>

<script>
  import {mapGetters, mapState} from 'vuex'
  import Breadcrumb from '@/components/Breadcrumb'
  import TopNav from '@/components/TopNav'
  import Hamburger from '@/components/Hamburger'
  import Screenfull from '@/components/Screenfull'
  import SizeSelect from '@/components/SizeSelect'
  import Search from '@/components/HeaderSearch'
  import RuoYiGit from '@/components/RuoYi/Git'
  import RuoYiDoc from '@/components/RuoYi/Doc'
  import Logo from './Sidebar/Logo'
  import variables from '@/assets/styles/variables.scss'
  import { getRouters } from '@/api/menu'
  import {getOperateManualFileInfo} from "@/api/system/manual";
  import Navbar from "@/layout/components/Navbar/index";
  export default {
    components: {
      Breadcrumb,
      TopNav,
      Hamburger,
      Screenfull,
      SizeSelect,
      Search,
      RuoYiGit,
      RuoYiDoc,
      Logo,
      Navbar
    },
    computed: {
      ...mapState(["settings"]),
      ...mapGetters([
        'sidebar',
        'avatar',
        'device',
        'nickName',
        'orgName',
        'depts',
        'userId'
      ]),
      showLogo() {
        return this.$store.state.settings.sidebarLogo;
      },
      variables() {
        return variables;
      },
      isCollapse() {
        return !this.sidebar.opened;
      },
      sideTheme() {
        return this.$store.state.settings.sideTheme
      },
      setting: {
        get() {
          return this.$store.state.settings.showSettings
        },
        set(val) {
          this.$store.dispatch('settings/changeSetting', {
            key: 'showSettings',
            value: val
          })
        }
      },
      topNav: {
        get() {
          return this.$store.state.settings.topNav
        }
      }
    },
    data() {
      return {
        fileInfo: {}, //基本信息
      };
    },
    created() {
      this.getOperateManualFileInfo();
    },
    methods: {
      openHelpDoc(){
        this.$refs.navbar.open()
      },
      toggleSideBar() {
        this.$store.dispatch('app/toggleSideBar')
      },
      /**获取操作文件信息*/
      getOperateManualFileInfo(){
        getOperateManualFileInfo().then((res)=>{
          this.fileInfo = res.data;
        })
      },
      /**下载文件*/
      fileDownload() {
        this.download(
          "/sys/attachment/downloadSysAttachment/" + this.fileInfo.attachmentId,
          {},
          this.fileInfo.fileName
        );
      },
      // 岗位切换
      handleCommand(userId) {
        if(userId == this.userId){
          //部门相同无需切换
        }else{
          this.$store.dispatch('PostSwitch', userId).then(() => {
            this.$router.push({ path: '/', query: {} });
            this.$router.go(0);
          })
        }
      },
      async logout() {
        this.$confirm('确定注销并退出系统吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.$store.dispatch('LogOut').then(() => {
            location.href = '/index';
          })
        }).catch(() => {
        });
      }
    }
  }
</script>

<style lang="scss" scoped>
  .margin-right16{
    margin-right:16px;
  }
  .navbar {
    height: 68px;
    overflow: hidden;
    position: relative;
    -webkit-box-shadow: 0 1px 4px rgb(0 21 41 / 8%);
    box-shadow: 0 1px 4px rgb(0 21 41 / 8%);
    background:url("../../assets/images/header.png") 50% 0 no-repeat;

    .hamburger-container {
      line-height: 32px;
      height: 32px;
      background: #fff;
      margin-top: 3px;
      float: left;
      cursor: pointer;
      transition: background .3s;
      -webkit-tap-highlight-color: transparent;

      &:hover {
        background: rgba(0, 0, 0, .025)
      }
    }

    .breadcrumb-container {
      /*float: left;*/
    }

    .topmenu-container {
      position: absolute;
      left: 50px;
    }

    .errLog-container {
      display: inline-block;
      vertical-align: top;
    }

    .right-menu {
      float: right;
      height: 100%;
      line-height: 68px;

      &:focus {
        outline: none;
      }

      .avatar-container {
        margin-right: 30px;
        .avatar-wrapper {
          margin-top: 0;
          font-size: 14px;
          position: relative;

          .user-avatar {
            cursor: pointer;
            width: 40px;
            height: 40px;
            border-radius: 10px;
          }

          .el-icon-caret-bottom {
            cursor: pointer;
            position: absolute;
            right: -20px;
            top: 20px;
            font-size: 12px;
          }
        }
      }
    }
  }
</style>
