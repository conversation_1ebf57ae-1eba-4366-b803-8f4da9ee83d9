<template>
  <div
    :id="id"
    ref="chart"
    :class="className"
    :style="{ height: height, width: width }"
  />
</template>
<script>
import pubSub from 'pubsub-js'
import echarts from 'echarts'
import { fontSizeEchars } from './mixins/fontSizeEchars'
import { formatNum } from '@/utils/index'
import resize from './mixins/resize'
require('echarts/theme/macarons')

export default {
  mixins: [resize],
  props: {
    charsData: {
      type: null,
      default: () => {}
    },
    id: {
      type: String,
      default: 'myChart'
    },
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '100%'
    }
  },
  data() {
    return {
      chart: null
    }
  },
  watch: {
    charsData: {
      handler(val, oldVal) {
        this.chart.clear()
        setTimeout(() => {
          this.initChart()
        }, 1000)
      },
      deep: true
    }
  },
  mounted() {
    this.initChart()
  },
  beforeD<PERSON>roy() {
    if (!this.chart) {
      return
    }
    this.chart.clear()
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    initChart() {
      var color = [
        '#FCED85',
        '#FDAF82',
        '#FC8586',
        '#8DA6FC',
        '#8ECFFD',
        '#3ABE7B',
        '#90FAFE',
        '#A2D72D'
      ]

      this.chart = echarts.init(this.$refs.chart, 'macarons')
      var option = {
        title: {
          text:
            '{a|' +
            this.charsData.statisticsList[0].name.slice(0, 6) +
            // '\n}{a1|' +
            // this.charsData.statisticsList[0].name.slice(6, this.charsData.statisticsList[0].name.length) +
            '\n}{b|' +
            this.charsData.statisticsList[0].value +
            '\n}{c|' +
            formatNum(this.charsData.percent) +
            '%}',
          triggerEvent: true, // 是否触发事件
          textStyle: {
            rich: {
              a: {
                fontSize: fontSizeEchars(0.13),
                color: '#A9B0B4',
                padding: [-28, 0, 0, 0]
              },
              b: {
                fontSize: fontSizeEchars(0.13),
                color: '#A9B0B4',
                lineHeight: '30',
                padding: [-36, 0, 0, 0]
              },
              c: {
                fontSize: fontSizeEchars(0.3),
                color: '#181818',
                lineHeight: 65,
                fontWeight: '500'

              },
              d: {
                fontSize: fontSizeEchars(0.2),
                color: '#A9B0B4',
                margin: [0, 0, 0, 18]
              }
            }
          },
          x: 'center',
          y: 'center'
        },

        color: color,
        series: [
          {
            type: 'pie',
            clockwise: true, // 饼图的扇区是否是顺时针排布
            hoverAnimation: true,
            minAngle: 25, // 最小的扇区角度（0 ~ 360）
            radius: ['50%', '65%'],
            center: ['50%', '50%'],
            avoidLabelOverlap: false,
            itemStyle: {
              // 图形样式
              normal: {
                borderColor: '#ffffff',
                borderWidth: 2
              }
            },
            label: {
              fontSize: fontSizeEchars(0.13),
              color: '#000',
              formatter: function(v) {
                let text = v.name
                if (text.length <= 6) {
                  return text
                } else if (text.length > 6 && text.length <= 12) {
                  return text = `${text.slice(0, 6)}\n${text.slice(6)}`
                } else if (text.length > 12 && text.length <= 18) {
                  return text = `${text.slice(0, 6)}\n${text.slice(6, 12)}\n${text.slice(18)}`
                } else if (text.length > 18 && text.length <= 24) {
                  return text = `${text.slice(0, 6)}\n${text.slice(6, 12)}\n${text.slice(12, 18)}\n${text.slice(18)}`
                } else if (text.length > 24) {
                  return text = `${text.slice(0, 6)}\n${text.slice(6, 12)}\n${text.slice(12, 18)}\n${text.slice(18, 24)}\n${text.slice(24)}`
                }
              },
              textStyle: {
                align: 'center', // 水平对齐方式可选left，right，center
                baseline: 'top'
              }
            },
            labelLine: {
              show: true,
              normal: {
                length: fontSizeEchars(0.1), // 第一段长度
                length2: fontSizeEchars(0.1), // 第二段长度 设置0不显示第二段
                lineStyle: {
                  width: fontSizeEchars(0.01)
                }
              }

            },

            emphasis: {
              label: {
                color: '#181818',
                fontWeight: 800,
                fontSize: fontSizeEchars(0.14)
              },
              labelLine: {
                length: fontSizeEchars(0.15),
                length2: fontSizeEchars(0.1),
                lineStyle: {
                  color: '#DEDEDE'
                }
              }
            },
            data: this.charsData.statisticsList
          }
        ]
      }
      this.chart.setOption(option, true)
      this.$nextTick(() => {
        setTimeout(() => {
          this.chart.on('mouseover', (e) => {
            if (e.name) {
              option.title.text =
              '{a|' + e.name.slice(0, 6) + '\n}{b|' + e.name.slice(6, e.name.length) + '\n}{c|' + e.value + '\n}{d|' + e.percent + '%}'
            }

            this.chart.setOption(option, true)
            for (var i = 0; i < this.charsData.statisticsList.length; i++) {
              this.chart.dispatchAction({
                type: e.dataIndex == i ? 'highlight' : 'downplay',
                seriesIndex: 0,
                dataIndex: i
              })
            }
          })
          this.chart.on('mouseout', (e) => {
            this.chart.dispatchAction({
              type: 'highlight',
              seriesIndex: 0,
              dataIndex: e.dataIndex
            })
          })
          this.chart.dispatchAction({
            type: 'highlight',
            seriesIndex: 0,
            dataIndex: 0
          })

          this.chart.getZr().on('click', params => {
            if (params.target.eventData.componentType === 'title') {
              const values = params.target.style.text.split('}')
              let value = ''
              let code = ''
              values.forEach(ele => {
                const eleE = ele.substr(1, 1)
                if (eleE == 'a' || eleE == 'b') {
                  value += ele.substr(3)
                }
              })
              value = value.replace(/\n/g, '')

              this.charsData.statisticsList.forEach(element => {
                if (value == element.name) {
                  code = element.code
                }
              })
              pubSub.publish('echars_huan', { type: 'wttj', code: code })
            }
          })
        }, 1000)
      })
    }
  }
}
</script>
<style lang="scss" scoped>
#myChart {
  width: 100%;
  height: 100%;
  div {
    width: 100%;
    height: 100%;
  }
}
</style>
