import request from '@/utils/request'

// 范围情形获取
export function queryRangeList(data) {
  return request({
    url: '/colligate/violateRangeDaily/queryRangeList',
    method: 'post',
    data:data
  })
}

// 范围情形选择页面
export function queryAspectSituateList(data) {
  return request({
    url: '/colligate/violateRangeDaily/queryAspectSituateList',
    method: 'post',
    data:data
  })
}

// 范围情形方面下拉
export function queryRangeAspectList() {
  return request({
    url: '/colligate/violateRangeDaily/queryRangeAspectList',
    method: 'post'
  })
}

// 保存范围情形
export function saveAspectSituate(data) {
  return request({
    url: '/colligate/violateRangeDaily/saveAspectSituate',
    method: 'post',
    data: data
  })
}

// 删除范围情形
export function delRangeById(data) {
  return request({
    url: '/colligate/violateRangeDaily/delRangeById',
    method: 'post',
    data: data
  })
}
