<template>
  <div>
    <!--<el-form :model="query" ref="queryForm"  :inline="true" label-width="75px">-->
      <!--<el-form-item label="人员姓名">-->
        <!--<el-input v-model="query.selectName" placeholder="人员姓名"  :style="{width: '100%'}">-->
        <!--</el-input>-->
      <!--</el-form-item>-->
      <!--<el-form-item>-->
        <!--<el-button type="primary" icon="el-icon-search" size="mini" @click="treeQuery">搜索</el-button>-->
      <!--</el-form-item>-->
    <!--</el-form>-->
    <el-row>
      <el-col :span="16" class="tree-box">
        <el-tree ref="tree"
                 :data="data"
                 lazy
                 node-key="id"
                 @check-change="checkChange"
                 :load="loadnode"
                 :props="defaultProps"
                 @node-click="nodeclick">
        </el-tree>
      </el-col>
      <el-col :span="8" class="tree-box">
        <TreeSelect
          :selectTree="selectTree"
          type="radio"
        >
        </TreeSelect>
      </el-col>
    </el-row>
  </div>

</template>

<script>
  import {userTree,treeUrl} from "@/api/components/index";
  import TreeSelect from '@/components/TreeSelect/personnel';

  export default {
    components: {
      TreeSelect
    },
    props: {
      selectTree:[],
      url:''
    },
    data() {
      return {
        data:[],
        defaultTree:[],
        query:{
          name:'',
          areaCode:'',
          isParent:'',
          provCode:'',
          checked:'',
          id:'',
          pId:'',
          isAll:false,
          open:false,
          nocheck:'',
          userId:'',
          selectName:'',
        },
        defaultProps: {//树对象属性对应关系
          children: 'children',
          label: 'name',
          isLeaf:function(data, node){
            return !data.isParent
          },
          disabled:function(data, node){
            return data.isParent
          }
        }
      }
    },
    methods: {
      //查询人员姓名
      treeQuery(){
        treeUrl(this.url,this.query).then(
          response => {
            this.data = response
          }
        );
      },
      loadnode(node,resolve){
        //如果展开第一级节点，从后台加载一级节点列表
        if(node.level==0)
        {
          this.loadfirstnode(resolve);
        }
        //如果展开其他级节点，动态从后台加载下一级节点列表
        if(node.level>=1)
        {
          this.loadchildnode(node,resolve);
        }
      },
      //加载第一级节点
      loadfirstnode(resolve){
        treeUrl(this.url,this.query).then(
          response => {
            resolve(response);
          }
        );
      },
      //加载节点的子节点集合
      loadchildnode(node,resolve){
        treeUrl(this.url,node.data).then(
          response => {
            resolve(response);
          }
        );
      },
      //点击节点上触发的事件，传递三个参数，数据对象使用第一个参数
      nodeclick(data,dataObj,self)
      {
        if(!data.isParent){//人员
        this.selectTree=[data];
        }
      },
      //修改状态
      checkChange(node,type){

      },
      //返回数据
      save(){
        this.$emit('accept',this.selectTree)
      }
    }
  }
</script>
<style  rel="stylesheet/scss" scoped lang="scss">
  .is-disabled{
    display: none !important;
  }
  .tree-box{
    height: calc(100vh - 400px);
    overflow: auto;

  }
</style>
