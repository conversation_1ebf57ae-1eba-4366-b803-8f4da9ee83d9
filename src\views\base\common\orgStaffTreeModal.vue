<template>
  <el-dialog
    :title="modalTitle"
    :visible.sync="dialogVisible"
    width="80%"
    :before-close="handleClose">
    <el-row :gutter="20">
      <el-col :span="9">
        <el-card class="box-card" style=" height:64vh">
          <div slot="header" class="clearfix">
            <el-row :gutter="10">
              <el-col :span="18">
                <el-input
                  placeholder="输入关键字进行过滤"
                  v-model="filterText">
                </el-input>
              </el-col>
              <el-col :span="4" style="padding-top:2px;">
                <el-button  type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
              </el-col>
            </el-row>
          </div>
          <checkeboxTree ref="tree" v-on:selectNode="selectNode" :treeList="treeList"
                         :checkedList="selectList" :personInfo="personInfo"
                         :selectType="selectType">
          </checkeboxTree>
        </el-card>
      </el-col>
      <el-col :span="15">
        <el-card class="box-card" shadow="never" style="height: 64vh">
          <div v-for="(staff, index) in checkedList">
            <div v-if="staff.personTypeText==modalTitle && staff.personId">
              <!--<el-tag-->
                <!--:key="staff.personId"-->
                <!--:closable="selectType == 'more'"-->
                <!--@close="handleCloseOne(staff, index)">-->
                <!--{{staff.personName}}-->
              <!--</el-tag>-->
              <div class="treeSelection margin-t5">
                <span class="float-left">{{staff.personName}}</span>
                <i v-if="selectType == 'more'" class="el-icon-close icon iconfont"  @click="handleCloseOne(staff, index)"></i>
              </div>
              <el-form label-width="75px">
                <el-row>
                  <el-col :span="8">
                    <el-form-item label="类别">
                      <el-input v-model="staff.personTypeText" placeholder="类别" readonly></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="职务">
                      <el-input v-model="staff.personDuty" placeholder="职务"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="办公电话">
                      <el-input v-model="staff.personPhone" placeholder="办公电话"></el-input>
                    </el-form-item>
                  </el-col>
                  </el-row>
                <el-row>
                  <el-col :span="8">
                    <el-form-item label="电子邮箱">
                      <el-input v-model="staff.personEmail" placeholder="电子邮箱"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="传真">
                      <el-input v-model="staff.personFax" placeholder="传真"></el-input>
                    </el-form-item>
                  </el-col>

                  <el-col :span="8">
                    <el-form-item label="手机">
                      <el-input type="number" maxlength="11"  v-model="staff.personMobilePhone"  @input="handleInput($event,index)"  placeholder="手机"></el-input>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="8">
                    <el-form-item label="地址">
                      <el-input v-model="staff.personAdress" placeholder="地址"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="邮编">
                      <el-input v-model="staff.personPostalCode" placeholder="邮编"></el-input>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    <span slot="footer" class="dialog-footer">
      <el-button size="mini" @click="handleClose()">取 消</el-button>
      <el-button size="mini" type="primary" @click="sub()">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script lang="ts">
  import checkeboxTree from "./checkeboxTree";

  export default {
    name: "orgStaffTreeModal",
    components: {checkeboxTree},
    props: {
      treeList: {
        type: Array,
        default: () => {
          return []
        }
      },
      dialogVisible: {
        type: Boolean,
        default: true
      },
      modalTitle: {
        type: String,
        default: ''
      },
      selectList: {//默认选中列表
        type: Array,
        default: () => {
          return []
        }
      },
      personInfo: {//人员类型信息
        type: Object,
        default: () => {
          return {}
        }
      },
      selectType: {//标识多选还是单选
        type: String,
        default: 'more'
      },
    },
    data() {
      return {
        filterText: '',
        defaultProps: {
          children: 'children',
          label: 'label'
        },
        checkedList: [],
      };
    },
    created() {
      this.checkedList = JSON.parse(JSON.stringify(this.selectList))
    },
    methods: {
      //校验手机号
      handleInput(value,index) {
        if (value.length > 11) {
          this.checkedList[index].personMobilePhone = value.slice(0, 11);
          return;
        }
      },
      /**关闭模态框*/
      handleClose() {
        this.$emit("closeModal");

      },
      /* 已选人员删除 */
      handleCloseOne(staff, index) {
        this.checkedList.splice(index, 1);
        this.$refs.tree.deleteTree(staff.personId,this.checkedList);
      },
      /**选择节点*/
      selectNode(data) {
        this.checkedList = data
      },
      handleQuery() {
        this.$emit("getStaffTreeData", this.filterText);
      },
      sub() {
        const phoneRegex = /^1[3-9]\d{9}$/;
        const perRegex = /^(0\d{2,3}-)?\d{7,8}$/;
        let noPersonTypeName = '';
        let personMobilePhone='';
        let personPhone = '';
        let dataVerification = [];
        this.checkedList.forEach((item,index) => {
          if(this.personInfo.personType=='fun_dept_leader'
            ||this.personInfo.personType=='fun_dept_monitor_leader'
            ||this.personInfo.personType=='fun_office_worker'
            ||this.personInfo.personType=='fun_office_leader'
          ){//部门主要负责人/分管监督追责工作负责人/工作人员/职能处室负责人  校验职务、办公电话、手机
            if((item.personDuty || '').trim()==''||item.personPhone==''||item.personMobilePhone==''){
              dataVerification.push(
                {
                  personName:item.personName,
                  personDuty:(item.personDuty || '').trim()=='',
                  personPhone:item.personPhone=='',
                  personMobilePhone:item.personMobilePhone=='',
                }
              )
            }
          }
          if(this.personInfo.personType=='contacts'){//联系人  校验职务、电子邮箱、地址、邮编
            if((item.personDuty || '').trim()==''||item.personEmail==''||item.personAdress==''||item.personPostalCode==''){
              dataVerification.push(
                {
                  personName:item.personName,
                  personDuty:(item.personDuty || '').trim()=='',
                  personEmail:item.personEmail=='',
                  personAdress:item.personAdress=='',
                  personPostalCode:item.personPostalCode=='',
                }
              )
            }
          }
          if(this.personInfo.personType=='info_admin'){//"信息系统管理员  电子邮箱、传真
            if(item.personEmail==''||item.personFax==''){
              dataVerification.push(
                {
                  personName:item.personName,
                  personEmail:item.personEmail=='',
                  personFax:item.personFax=='',
                }
              )
            }
          }
          if (!phoneRegex.test(item.personMobilePhone)&&item.personMobilePhone!='') {
            personMobilePhone+= item.personName+'、'
          }
          if (!perRegex.test(item.personPhone)&&item.personPhone) {
            personPhone+= item.personName+'、'
          }
          if(!item.personType){
            noPersonTypeName += item.personName+'、'
          }
        })
        // 去掉最后一个 "、"
        noPersonTypeName = noPersonTypeName.slice(0, -1);
        personMobilePhone = personMobilePhone.slice(0, -1);
        personPhone = personPhone.slice(0, -1);
        let verification = '';
        dataVerification.forEach(item=>{
          let verificationOne = item.personName+'的';
          if(item.personDuty){
            verificationOne += '【职务】'
          }
          if(item.personPhone){
            verificationOne += '【办公电话】'
          }
          if(item.personMobilePhone){
            verificationOne += '【手机】'
          }
          if (item.personEmail){
            verificationOne += '【电子邮箱】'
          }
          if (item.personAdress){
            verificationOne += '【地址】'
          }
          if (item.personPostalCode){
            verificationOne += '【邮编】'
          }
          if (item.personFax){
            verificationOne += '【传真】'
          }
          verificationOne += '不能为空；';
          verification+=verificationOne;
        })
        if(dataVerification.length){
          this.$message({showClose: true,message: verification,type: 'error',duration: '8000'});
        }else if(noPersonTypeName!=''){
          this.$message({showClose: true,message: '请选择【类别】！',type: 'error',duration: '8000'});
        }else if(personMobilePhone!=''){
          this.$message({showClose: true,message: '请填写正确格式的【手机】！',type: 'error',duration: '8000'});
        }else if (this.selectType == 'more' && this.checkedList.length < this.personInfo.minLength) {
          this.$message({showClose: true,message: '请至少选择一个人！',type: 'error',duration: '8000'});
        } else {
          this.$emit("getOrg", this.checkedList);
          this.handleClose();
        }
      }
    }
  };
</script>

<style scoped lang="scss">
  .text {
    font-size: 14px;
  }

  .item {
    margin-bottom: 18px;
  }

  .clearfix:before,
  .clearfix:after {
    display: table;
    content: "";
  }

  .clearfix:after {
    clear: both
  }


  .is-never-shadow {
    ::v-deep .el-card__body {
      height: 100% !important;
      overflow: auto;
      padding-top: 0px !important;
    }
  }

  .el-tag {
    width: 100%;
    margin-top: 10px;
    margin-bottom: 10px;
  }

  .el-icon-close {
    float: right;
    margin-top: 6px;
  }

  .el-tag--medium {
    height: 39px;
    line-height: 39px;
  }
</style>
