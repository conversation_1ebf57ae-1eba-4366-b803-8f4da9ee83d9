<template>
  <div class="todo">
      <div class="todo-content">
        <div class="todo-header">
          <el-radio-group v-model="tabPosition">
            <el-radio-button label="1">业务信息</el-radio-button>
            <el-radio-button label="2">流程历史</el-radio-button>
            <el-radio-button label="3">流程图</el-radio-button>
          </el-radio-group>
          <Opinion
            :activities="activities"
          ></Opinion>
        </div>
      </div>
    <el-scrollbar style="height:calc(100vh - 70px);overflow-x: hidden">
      <div class="todo-data" v-show="tabPosition==='1'">
        <Daily
          v-if="type==='daily'"
          :key="index"
          ref="todo"
          :selectValue="selectValue"
          :centerVariable="centerVariable"
          @handle="handle"
          @openLoading="openLoading"
          @closeLoading="closeLoading"
        ></Daily>
        <Regular
          v-if="type==='regular'"
          :key="index"
          ref="todo"
          :selectValue="selectValue"
          :centerVariable="centerVariable"
          @handle="handle"
          @openLoading="openLoading"
          @closeLoading="closeLoading"
        ></Regular>
        <Actual
          v-if="type==='actual'||type==='actualEdit'"
          :key="index"
          ref="todo"
          type="actual"
          :selectValue="selectValue"
          :centerVariable="centerVariable"
          @handle="handle"
          @openLoading="openLoading"
          @closeLoading="closeLoading"
        ></Actual>
        <component
          v-if="type!=='actual'&&type!=='actualEdit'&&type!='regular'&&type!='daily'"
          :is="businessUrl"
          :key="index"
          ref="todo"
          :selectValue="selectValue"
          :centerVariable="centerVariable"
          @handle="handle"
          @openLoading="openLoading"
          @closeLoading="closeLoading"
        />
      </div>
      <div class="todo-data" v-show="tabPosition==='2'">
        <History
          :activities="activities"
        ></History>
      </div>
      <div class="todo-data" v-show="tabPosition==='3'">
        <FlowChart
          :key="selectValue"
          :selectValue="selectValue"
        ></FlowChart>
      </div>
    </el-scrollbar>
    <div  style="text-align: right;padding:0 10px">
      <Hasdone
        slot="footer"
        :key="centerVariable"
        ref="process"
        :tabFlag="tabFlag"
        :selectValue="selectValue"
        :centerVariable="centerVariable"
        :withdrawFlag="withdrawFlag"
        @close="close"
      ></Hasdone>
    </div>
  </div>
</template>
<script>
  import Opinion from "./../common/opinion";
  import Hasdone from "@/components/Process/hasdone";
  import FlowChart from "./../common/flowChart";
  import Daily from "@/views/daily/dailyHasdone";//日常
  import Actual from "@/views/actual/flow";//实时
  import History from  "./../common/history";
  import Regular from "@/views/regular/flow/taskHastonAreaHandler";//定期
  import { tasktodopath,taburls,taskhasdonepath,histoicflow } from "@/api/components/process";
  import {Loading} from "element-ui";

  export default {
    inheritAttrs: false,
    components: {
      Opinion,
      Hasdone,
      FlowChart,
      Daily,
      History,
      Regular,
      Actual,

    },
    props: {
      selectValue: {
        type: Object
      },
      tabFlag: {
        type: String
      },
    },
    data() {
      return {
        index:1,
        businessUrl: '',
        centerVariable:{},
        withdrawFlag:0,
        visible:false,//弹框
        tabPosition: '1',
        processType:1,
        type:'',
        activities: [],
        loadingInstance: ''
      }
    },
    computed: {},
    watch: {},
    created() {
      this.selectValue={
        linkKey:this.$route.query.linkKey,
        processInstanceId:this.$route.query.processInstanceId,
        readLinkId:this.$route.query.readLinkId,
        taskId:this.$route.query.taskId,
        typeId:this.$route.query.typeId,
        flowKey:this.$route.query.flowKey
      };
      this.show();
    },
    mounted() {},
    methods: {
      //打开这招
      openLoading() {
        this.loadingInstance = Loading.service({
          target: document.querySelector('#todo'),
          background:'rgba(255,255,255,0)',
          spinner: 'el-icon-loading', // 自定义加载图标类名
          text: '正在加载...', // 显示在加载图标下方的加载文案
          lock: false // lock的修改符--默认是false
        })
        return this.loadingInstance
      },
      //关闭这招
      closeLoading(){
        this.loadingInstance.close()
      },
      /** 点开弹窗 */
      show(){
        this.visible = true;
        this.Tasktodopath();
        this.Histoicflow();
      },
      /** 关闭弹窗 */
      close() {
        window.opener=null;
        window.open('','_self');
        window.close();
      },
      /**主要数据*/
      Tasktodopath(){
        taskhasdonepath(this.selectValue).then(
          response => {
            this.centerVariable = response.data.dataRows[0].centerVariable;
            this.withdrawFlag = response.data.dataRows[0].withdrawFlag;
            this.flowCfgLink = response.data.dataRows[0].flowCfgLink;
            this.type=response.data.dataRows[0].url;
            this.businessUrl = (resolve) =>
              require([`@/views/${response.data.dataRows[0].url}`], resolve);
            this.Taburls();
            this.index++;
            this.visible = true;
          }
        );
      },
      /**主要数据*/
      Histoicflow(){
        histoicflow(this.selectValue.processInstanceId).then(
          response => {
            this.activities = response;
          }
        );
      },
      /**根据所在环节查询需展现的自定义标签*/
      Taburls(){
        taburls(this.centerVariable.flowKey,this.selectValue.linkKey,this.tabFlag).then(
          response => {

          }
        );
      },
    }
  }

</script>
<style scoped lang="scss">
  .todo{
    .todo-header{
      ::v-deep.el-radio-button__inner{
        border-radius: 0 !important;
        border-color: #f4f4f4 !important;
        box-shadow:0 0 0 0 #f5222d !important;
        width: 120px;
      }
    }
    .todo-content{
      background: #F4F4F4;
    }
    .todo-data{
      background: #fff;
      margin-top:8px;
      overflow: auto;
      height: calc(100% - 10px);
    }
    ::v-deep.el-scrollbar__view{
      height: calc(100% - 10px);
    }
    ::v-deep.el-scrollbar__wrap {
      overflow-x: hidden !important;
    }
    ::v-deep.el-dialog__body{
      border-top: 2px solid #E9E8E8;
      padding:0 20px 10px;
      background: #F4F4F4;
      height: 70vh;
      overflow: auto;
    }
  }
</style>

