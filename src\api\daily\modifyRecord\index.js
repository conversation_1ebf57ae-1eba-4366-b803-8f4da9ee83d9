import request from '@/utils/request'

// 查询本次修改
export function thisStageModifyRecord(problemId,businessId,businessTable) {
  return request({
    url: '/colligate/violDailyModifyRecord/thisStageModifyRecord',
    method: 'post'
    , data: JSON.stringify({
      problemId: problemId
      , businessId: businessId
      , businessTable: businessTable
    })
  })
}

//查询涉及单位、涉及人员
export function queryInvolveCompanyAndPerson(problemId,relevantTableId) {
  return request({
    url: '/colligate/supervisionInvolve/queryInvolveCompanyAndPerson',
    method: 'post'
    , data: JSON.stringify({
      problemId: problemId
      , relevantTableId: relevantTableId
    })
  })
}

//查询修改历史
export function violDailyModifyHistory(problemId){
  return request({
    url: '/colligate/violDailyModifyRecord/violDailyModifyHistory/'+problemId,
    method: 'post'
  })
}

//保存修改原因
export function saveModifyReason(problemId,businessId,businessTable,modifyReasonText,initialBusinessId){
  return request({
    url: '/colligate/violDailyModifyRecord/saveModifyReason',
    method: 'post'
    , data: JSON.stringify({
      problemId: problemId
      , businessId: businessId
      , businessTable: businessTable
      , modifyReason: modifyReasonText
      , initialBusinessId: initialBusinessId
    })
  })
}



