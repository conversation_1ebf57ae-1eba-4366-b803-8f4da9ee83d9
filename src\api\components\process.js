import request from '@/utils/request'

// 下一环节名称
export function processLinkData(processDefinitionKey,data) {
  return request({
    url: '/workflowRestController/tasklinkforstart/'+processDefinitionKey,
    method: 'get',
    params: data?data:{}
  })
}


// 下一环节处理人
export function refreshNextAssignee(data) {
  return request({
    url: '/businessController/refreshNextAssignee',
    method: 'post',
    data: data
  })
}

// 通过或者退回下一环节名称
export function tasklink(processInstanceId,linkKey,processDefinitionKey,flowKeyReV,handleType,data) {
  return request({
    url: '/workflowRestController/tasklink/'+processInstanceId+'/'+linkKey+'/'+processDefinitionKey+'/'+flowKeyReV+'/'+handleType,
    method: 'get',
    params:  data?data:{}
  })
}


// 退回下一环节处理人
export function backAssignee(data) {
  return request({
    url: '/workflowRestController/refreshBackAssignee',
    method: 'post',
    data: data
  })
}

// 转派下一环节处理人
export function refreshTurnAssignee(data) {
  return request({
    url: '/workflowRestController/refreshTurnAssignee',
    method: 'post',
    data: data
  })
}


// 流程启动并送审
export function startAndSubmitProcess(data) {
  return request({
    url: '/businessController/startAndSubmitProcess',
    method: 'post',
    data: data
  })
}

// 流程推进
export function pushProcess(data) {
  return request({
    url: '/businessController/pushProcess',
    method: 'post',
    data: data
  })
}

// 退回
export function backProcess(data) {
  return request({
    url: '/businessController/backProcess',
    method: 'post',
    data: data
  })
}

// 已办撤回
export function withdrawProcess(data) {
  return request({
    url: '/workflowRestController/withdrawProcess',
    method: 'post',
    data: data
  })
}

// 转派
export function transferProcess(data) {
  return request({
    url: '/workflowRestController/transferProcess',
    method: 'post',
    data: data
  })
}


// 待阅点击已阅
export function read(readerId) {
  return request({
    url: '/workflowRestController/rebackSendRound',
    method: 'post',
    data: readerId
  })
}

// 根据所在环节查询需展现的自定义标签
export function taburls(processDefinitionId,taskDefinitionKey,tabFlag) {
  return request({
    url: '/workflowRestController/taburls/'+processDefinitionId+'/'+taskDefinitionKey+'/'+tabFlag,
    method: 'get'
  })
}

// 获取待办页面业务主页面及参数
export function tasktodopath(data) {
  return request({
    url: '/workflowRestController/tasktodopath/'+data.processInstanceId+'/'+data.linkKey+'/'+data.taskId+'/'+data.typeId,
    method: 'get'
  })
}

// 获取流程发起时参数
export function flowParams() {
  return request({
    url: '/colligate/violDaily/flowParams',
    method: 'get'
  })
}

// 获取已办页面业务主页面及参数
export function taskhasdonepath(data) {
  return request({
    url: '/workflowRestController/taskhasdonepath/'+data.processInstanceId+'/'+data.linkKey+'/'+data.taskId+'/'+data.typeId,
    method: 'get'
  })
}

// 获取待阅、已阅页面业务主页面及参数
export function findRecordPath(data) {
  return request({
    url: '/workflowRestController/findRecordPath',
    method: 'post',
    data: data
  })
}

// 获取流程图地址
export function flowChatData(data) {
  return request({
    url: '/workflowRestController/getProcessChartByProcInstId/'+data.processInstanceId,
    method: 'get'
  })
}

// 获取流转历史
export function histoicflow(processInstanceId) {
  return request({
    url: '/workflowRestController/histoicflow/'+processInstanceId,
    method: 'get'
  })
}
