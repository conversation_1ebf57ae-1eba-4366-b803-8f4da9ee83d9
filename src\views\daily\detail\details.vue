<template>
  <div>
    <el-tabs v-model="activeName">
      <el-tab-pane label="日常报送" name="0">
        <div class="process-x" data-code="2">
          <dailyDetails
            :key="dailyProblemId||dailyProblemStatus"
            v-if="dailyProblemId||dailyProblemStatus"
            :dailyProblemId="dailyProblemId"
            :dailyProblemStatus="dailyProblemStatus"
            :selectValue="selectValue"
            :procInsId="procInsId"
          ></dailyDetails>
        </div>
      </el-tab-pane>
      <el-tab-pane label="实时报送" name="1" v-if="selectValue.actualFlag==='1'">
        <div class="process-x">
          <actualDetails
            :actualProblemId="actualProblemId"
            :actualProblemStatus="actualProblemStatus"
            v-if="actualProblemId&&actualProblemStatus"
            :selectValue="selectValue"
            :procInsId="procInsId"
          ></actualDetails>
        </div>
      </el-tab-pane>
    </el-tabs>
    <!--<opinion-->
    <!--:processInstanceId="procInsId"-->
    <!--:isShow="isShow"-->
    <!--/>-->
  </div>
</template>

<script>
import actualDetails from '@/views/actual/detail';
import {problemStatus} from '@/api/daily/index';
import opinion from '@/views/daily/modifyRecord/opinion';

export default {
  name: "details",
  props: {
    selectValue: {
      // type:Object
    },
    procInsId:{
      type:String
    },
    activeName:{
      type:String
    }
  },
  components: {
    dailyDetails: (d) => import("@/views/daily/detail/index"), //解决递归加载组件报错
    actualDetails,
    opinion
  },
  data() {
    return {
      dailyProblemId:'',
      dailyProblemStatus:'',
      actualProblemId:'',
      actualProblemStatus:'',
      isShow:'1'
    }
  },
  mounted() {
    this.ProblemStatus();
  },
  methods: {
    //获取流程环节
    ProblemStatus(){
      problemStatus({dailyProblemId: this.selectValue.id}).then(response => {
        this.dailyProblemId = response.data.dailyProblemId;
        // this.dailyProblemStatus = response.data.dailyProblemStatus;
        if(response.data.dailyProblemStatus=='10'){
          this.dailyProblemStatus = '2';
        }else if(response.data.dailyProblemStatus=='9'){
          this.dailyProblemStatus ='1';
        }else if(response.data.dailyProblemStatus=='8'){
          this.dailyProblemStatus ='7';
        }else{
          this.dailyProblemStatus = response.data.dailyProblemStatus;
        }
        this.actualProblemId = response.data.actualProblemId;
        this.actualProblemStatus = response.data.actualProblemStatus?response.data.actualProblemStatus:'1';
      });
    }
  }
}
</script>

<style scoped lang="scss">
.process-x {
  /*overflow: hidden;*/
  height: calc(80vh - 165px);
}
::v-deep .el-tabs__content{
  overflow: inherit;
}
</style>
