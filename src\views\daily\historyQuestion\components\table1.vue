<!--企业基本信息-->
<template>
  <div class="app-areaList padding_b10">
    <div class="search-top">
      <el-form ref="form" :model="queryParams" label-width="120px">
        <el-row>
          <el-col :span="5">
            <el-form-item label="年度" prop="problemYear">
              <el-date-picker
                style="width: 100%"
                v-model="queryParams.problemYear"
                type="year"
                size="small"
                value-format="yyyy"
                format="yyyy"
                placeholder="请选择年度"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item label="问题涉及单位" prop="problemInvolvesProvinces">
              <el-select
                filterable
                v-model="queryParams.problemProvinceCode"
                placeholder="请选择问题涉及单位"
                clearable
                size="small"
              >
                 <el-option
                  v-for="province in provinces"
                  :key="province.deptId"
                  :label="province.deptName"
                  :value="province.deptId"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item label="问题描述" prop="questionsDes">
              <el-input
                size="small"
                placeholder="请输入问题描述"
                v-model="queryParams.problemDescription"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>

          <el-col :span="9">
            <div class="flex">
              <div class="flex-1"></div>
              <el-form-item>
                <el-button
                  type="primary"
                  icon="el-icon-search"
                  size="mini"
                  @click="handleQuery"
                  >搜索</el-button
                >
                <el-button
                  icon="el-icon-refresh"
                  size="mini"
                  class="el-refresh-btn"
                  @click="resetQuery"
                  >重置</el-button
                >
              </el-form-item>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </div>
   <div style="height: calc(100vh - 185px)">
     <el-table
       v-loading="loading"
       :data="tableList"
       height="100%"
       :header-cell-style="{
        background: '#F4F8FC',
        color: '#606266',
        'text-align': 'center',
      }"
       :cell-style="{ 'text-align': 'center' }"
     >
       <el-table-column prop="problemYear" label="年度" width="80"> </el-table-column>
       <el-table-column prop="involveCompany" label="涉及企业名称" width="150" align="left" show-overflow-tooltip>
       </el-table-column>
       <el-table-column prop="companyLevel" label="涉及企业级次" width="80">
       </el-table-column>
       <el-table-column prop="isListedCompany" label="是否上市公司" width="80">
       </el-table-column>
       <el-table-column label="问题受理">
         <el-table-column
           prop="problemSource"
           label="问题线索来源"
           width="200"
           align="left" show-overflow-tooltip
         ></el-table-column>
         <el-table-column
           prop="acceptTime"
           label="问题受理时间"
           width="100"
         ></el-table-column>
         <el-table-column
           prop="problemBriefDescription"
           label="问题简要描述"
           width="220"
           align="left" show-overflow-tooltip
         ></el-table-column>
         <el-table-column
           prop="problemType"
           label="问题类别"
           width="150"
         ></el-table-column>
         <el-table-column
           prop="occurrenceTime"
           label="问题发生时间"
           width="100"
         ></el-table-column>
         <el-table-column
           prop="domesticOrForeign"
           label="境内（外）"
           width="100"
         >
           <template slot-scope="scope">
             <div>{{ scope.row.domesticAndForeign || "-" }}</div>
           </template>
         </el-table-column>

         <el-table-column
           prop="estimatedRiskLoss"
           label="预估损失风险（万元）"
           width="100"
         >
           <template slot-scope="scope">
             <div style="text-align: right">
               {{ scope.row.estimatedRiskLoss || "-" }}
             </div>
           </template>
         </el-table-column>

         <el-table-column
           prop="takeSteps"
           label="采取的应对措施"
           width="300"
           align="left" show-overflow-tooltip
         ></el-table-column>
       </el-table-column>
       <el-table-column label="检查情况">
         <el-table-column
           prop="isCheckCompleted"
           label="是否完成核查"
           width="100"
         ></el-table-column>

         <el-table-column
           prop="checkSubject"
           label="核查主体"
           width="150"
         ></el-table-column>

         <el-table-column
           prop="checkEndTime"
           label="核查结束时间"
           width="120"
         ></el-table-column>
       </el-table-column>

       <el-table-column label="违规事实">
         <el-table-column
           prop="checkResultDescription"
           label="核查结果简要描述"
           width="280"
           align="left" show-overflow-tooltip
         ></el-table-column>

         <el-table-column
           prop="involvePerson"
           label="涉及人员"
           width="200"
         ></el-table-column>

         <el-table-column
           prop="violationRegulation"
           label="违反具体规定"
           width="200"
           align="left" show-overflow-tooltip
         ></el-table-column>

         <el-table-column
           prop="regulationType"
           label="违反规定类别"
           width="150"
         ></el-table-column>
       </el-table-column>

       <el-table-column label="损失认定">
         <el-table-column
           prop="lossDeterminationTime"
           label="损失认定时间"
           width="100"
         ></el-table-column>
         <el-table-column prop="lossAmount" label="损失金额（万元）" width="150">
           <template slot-scope="scope">
             <div style="text-align: right">
               {{ scope.row.lossMoney || "-" }}
             </div>
           </template>
         </el-table-column>

         <el-table-column
           prop="involveLossRisk"
           label="损失风险金额（万元）"
           width="150"
         >
           <template slot-scope="scope">
             <div style="text-align: right">
               {{ scope.row.lossRiskMoney || "-" }}
             </div>
           </template>
         </el-table-column>

         <el-table-column
           prop="lossDeterminationType"
           label="损失认定类别"
           width="150"
         ></el-table-column>
       </el-table-column>

       <el-table-column label="责任认定">
         <el-table-column
           prop="responsibilityTime"
           label="责任认定时间"
           width="120"
         ></el-table-column>
         <el-table-column label="总计（人数）">
           <el-table-column
             prop="responsibilityTotalCount"
             label="总人数"
             width="100"
           ></el-table-column>
           <el-table-column label="其中">
             <el-table-column
               prop="responsibilityMiddleCount"
               label="中管干部"
               width="150"
             ></el-table-column>
             <el-table-column
               prop="responsibilityCommissionedCount"
               label="委管干部"
               width="150"
             ></el-table-column>

             <el-table-column
               prop="responsibilityEnterpriseCount"
               label="企业管理干部"
               width="150"
             ></el-table-column>
           </el-table-column>
         </el-table-column>

         <el-table-column label="总计（人次）">
           <el-table-column
             prop="responsibilityTotalTimes"
             label="总人次"
             width="100"
           ></el-table-column>
           <el-table-column label="其中">
             <el-table-column
               prop="responsibilityImportantTimes"
               label="重要领导责任"
               width="150"
             ></el-table-column>
             <el-table-column
               prop="responsibilityChargeTimes"
               label="分管领导责任"
               width="150"
             ></el-table-column>

             <el-table-column
               prop="responsibilityLeaderTimes"
               label="领导责任"
               width="150"
             ></el-table-column>

             <el-table-column
               prop="responsibilityDirectorTimes"
               label="主管责任"
               width="150"
             ></el-table-column>

             <el-table-column
               prop="responsibilityDirectTimes"
               label="直接责任"
               width="150"
             ></el-table-column>
           </el-table-column>
         </el-table-column>
       </el-table-column>

       <el-table-column label="责任追究">
         <el-table-column
           prop="accountabilityTime"
           label="责任追究时间"
           width="120"
         ></el-table-column>
         <el-table-column label="总计（人数）">
           <el-table-column
             prop="accountabilityTotalCount"
             label="总人数"
             width="100"
           ></el-table-column>
           <el-table-column label="其中">
             <el-table-column
               prop="accountabilityMiddleCount"
               label="中管干部"
               width="150"
             ></el-table-column>
             <el-table-column
               prop="accountabilityCommissionedCount"
               label="委管干部"
               width="150"
             ></el-table-column>

             <el-table-column
               prop="accountabilityEnterpriseCount"
               label="企业管理干部"
               width="150"
             ></el-table-column>
           </el-table-column>
         </el-table-column>
         <el-table-column label="总计（人次）">
           <el-table-column
             prop="accountabilityTotalTimes"
             label="总人次"
             width="100"
           ></el-table-column>
           <el-table-column label="组织处理">
             <el-table-column
               prop="organizationProcessing"
               label="人次"
               width="100"
             ></el-table-column>

             <el-table-column label="其中">
               <el-table-column
                 prop="educationTimes"
                 label="批评教育"
                 width="120"
               ></el-table-column>
               <el-table-column
                 prop="writtenInspectionTimes"
                 label="责令书面检查"
                 width="120"
               ></el-table-column>

               <el-table-column
                 prop="notificationTimes"
                 label="通报批评"
                 width="120"
               ></el-table-column>
               <el-table-column
                 prop="persuadeTimes"
                 label="诫勉"
                 width="120"
               ></el-table-column>
               <el-table-column
                 prop="suspensionTimes"
                 label="停职"
                 width="120"
               ></el-table-column>
               <el-table-column
                 prop="alienationTimes"
                 label="调离工作岗位"
                 width="120"
               ></el-table-column>
               <el-table-column
                 prop="demotionTimes"
                 label="降职"
                 width="120"
               ></el-table-column>
               <el-table-column
                 prop="nonLeaderTimes"
                 label="改任非领导职务"
                 width="120"
               ></el-table-column>
               <el-table-column
                 prop="resignationTimes"
                 label="责令辞职"
                 width="120"
               ></el-table-column>
               <el-table-column
                 prop="dismissalTimes"
                 label="免职"
                 width="120"
               ></el-table-column>
             </el-table-column>
           </el-table-column>

           <el-table-column label="扣减薪酬">
             <el-table-column
               prop="salaryDeductionTimes"
               label="人次"
               width="120"
             ></el-table-column>
             <el-table-column
               prop="salaryDeductionAmount"
               label="扣减金额（万元，分别列示）"
               width="200"
             ></el-table-column>
           </el-table-column>

           <el-table-column label="禁入限制">
             <el-table-column
               prop="restrictedTimes"
               label="人次"
               width="120"
             ></el-table-column>
             <el-table-column
               prop="restrictedTime"
               label="禁入时间（年，分别列示）"
               width="200"
             ></el-table-column>
           </el-table-column>

           <el-table-column label="纪律处分">
             <el-table-column
               prop="disciplinaryTimes"
               label="人次"
               width="120"
             ></el-table-column>
             <el-table-column label="其中">
               <el-table-column
                 prop="warningTimes"
                 label="警告"
                 width="120"
               ></el-table-column>

               <el-table-column
                 prop="criticalWarningTimes"
                 label="严重警告"
                 width="120"
               ></el-table-column>

               <el-table-column
                 prop="partyPostTimes"
                 label="撤销党内职务"
                 width="120"
               ></el-table-column>

               <el-table-column
                 prop="stayPartyTimes"
                 label="留党察看"
                 width="120"
               ></el-table-column>

               <el-table-column
                 prop="expulsionPartyTimes"
                 label="开除党籍"
                 width="120"
               ></el-table-column>
             </el-table-column>
           </el-table-column>

           <el-table-column
             prop="transferredTimes"
             label="移送国家监察机关或司法机关人次"
             width="150"
           ></el-table-column>
         </el-table-column>
       </el-table-column>
       <el-table-column label="整改落实">
         <el-table-column label="是否完成整改">
           <el-table-column
             prop="isReformCompleted"
             label="是否完成"
             width="100"
           ></el-table-column>

           <el-table-column
             prop="incompleteReformReason"
             label="未完成原因"
             width="100"
           ></el-table-column>
         </el-table-column>

         <el-table-column label="挽回、减少损失或损失风险金额">
           <el-table-column prop="recoveredLossAmount" label="损失(万元)" width="100">
             <template slot-scope="scope">
               <div style="text-aligin: right">
                 {{ scope.row.lossMoney || "-" }}
               </div>
             </template>
           </el-table-column>

           <el-table-column
             prop="recoveredLossRisk"
             label="损失风险(万元)"
             width="100"
           >
             <template slot-scope="scope">
               <div style="text-aligin: right">
                 {{ scope.row.lossRiskMoney || "-" }}
               </div>
             </template>
           </el-table-column>
         </el-table-column>

         <el-table-column label="完善制度情况（制订或修订）">
           <el-table-column prop="improvedSystemCount" label="个数" width="100">
           </el-table-column>

           <el-table-column
             prop="systemName"
             label="制度名称及出台时间（分别列示）"
             width="150"
           >
             <template slot-scope="scope">
               <div>
                 {{ scope.row.systemNameIntroductionTime || "-" }}
               </div>
             </template>
           </el-table-column>
         </el-table-column>
       </el-table-column>

       <el-table-column
         prop="remark"
         label="备注"
         width="180"
         align="left" show-overflow-tooltip
       ></el-table-column>
       <el-table-column
         prop="problemDescription"
         label="问题描述"
         width="250"
         align="left" show-overflow-tooltip
       ></el-table-column>
       <el-table-column
         prop="involvePersonName"
         label="涉及人员姓名"
         width="200"
         align="left" show-overflow-tooltip
       ></el-table-column>

       <el-table-column
         prop="involvePersonPost"
         label="及人员职务"
         width="200"
         align="left" show-overflow-tooltip
       ></el-table-column>
       <el-table-column
         prop="adverseEffect"
         label="造成的不良影响"
         width="250"
         align="left" show-overflow-tooltip
       ></el-table-column>

       <el-table-column
         prop="evidentialMaterial"
         label="相关证据材料目录"
         width="250"
         align="left" show-overflow-tooltip
       ></el-table-column>

       <el-table-column prop="accordLevel" label="符合情况" width="250" fixed="right">
         <template slot-scope="scope">
           <el-select
             v-model="scope.row.accordLevel"
             placeholder="请选择符合情况"
             clearable
             size="mini"
             @change="fullyCompliantChange($event, scope.row.id)"
           >
             <el-option
               v-for="dict in selectList"
               :key="dict.value"
               :label="dict.label"
               :value="dict.value"
             />
           </el-select>
         </template>
       </el-table-column>
     </el-table>
   </div>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="params.pageNum"
      :limit.sync="params.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import {dailyHistoryProblemBefore2019, saveDailyHistoryProblemBefore2019AccordLevel, allProvinces} from "@/api/daily/historyQuestion/historyProblem"
export default {
  name: "Table",
  components: {},
  dicts: [],
  data() {
    return {
      provinces: [],
      // 总条数
      total: 0,
      // 表格数据
      tableList: [{}],
      //参数
      queryParams: {
        problemYear: '2019',
        problemProvinceCode: "",
        problemDescription: "",
      },
      // 表格页码
      params: {
        pageNum: 1,
        pageSize: 10,
      },
      loading: false,
      selectList: [
        {
          value: "1",
          label: "完全符合",
        },
        {
          value: "2",
          label: "部分符合",
        },
      ],
    };
  },
  created() {
    this.getProvince();
    this.getList();
  },
  methods: {
    getProvince() {
      allProvinces().then(res => {
        this.provinces = res.data;
      });
    },
    /**查询企業基本信息列表*/
    getList() {
      this.loading = true;
      dailyHistoryProblemBefore2019(this.queryParams,this.params).then(
        response => {
          this.tableList = response.rows;
          this.total = response.total;
          this.loading = false;
        }
      );
    },
    /** 搜索按钮操作*/
    handleQuery() {
      this.getList();
    },
    /**重置按钮操作*/
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        problemYear: "",
        problemInvolvesProvinces: "",
        questionsDes: "",
      };
      this.getList();
    },
    fullyCompliantChange(val, id) {
      if (val) {
        saveDailyHistoryProblemBefore2019AccordLevel({id: id, accordLevel: val}).then(res => {
            if(res.code== 200){
            this.$message.success(res.msg)
          }
        })
      }
    },
  },
};
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
.flex {
  display: flex;
}
.flex-1 {
  flex: 1;
}
</style>







