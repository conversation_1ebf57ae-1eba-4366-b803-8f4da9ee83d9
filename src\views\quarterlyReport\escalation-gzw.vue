<!-- 上报国资委查询 -->

<template>
  <div class="wai-container new-height-40" style="background-color: #fff">
    <div class="layui-row width height">
      <div class="width height">
        <div class="common-wai-box" style="height: 100%">
          <div class="common-in-box" style="height: auto; min-height: 100%">
            <div class="top-search" style="margin-bottom: 10px">
              <el-col :span="6" class="height">
                <div class="layui-form">
                  <div class="layui-form-left">上报年度</div>
                  <el-date-picker
                    format="yyyy"
                    value-format="yyyy"
                    v-model="searchData.reportYear"
                    type="year"
                    placeholder="请选择"
                  >
                  </el-date-picker>
                </div>
              </el-col>

              <el-col :span="6" class="height">
                <div class="layui-form">
                  <div class="layui-form-left">上报季度</div>

                  <el-select
                    v-model="searchData.reportQuarter"
                    placeholder="请选择"
                  >
                    <el-option
                      v-for="(item, index) in dict.type.REPORT_QUARTER"
                      :key="index"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </div>
              </el-col>

              <el-col :span="12" class="height">
                <div class="flex">
                  <div class="flex-1"></div>
                  <el-button type="primary"  icon="el-icon-plus" size="mini" @click="addItem">新增</el-button>
                  <el-button type="primary" icon="el-icon-search" size="mini" @click="tableList">搜索</el-button>
                  <el-button icon="el-icon-refresh" size="mini" @click="clearInfo">重置</el-button>

                </div>
              </el-col>
            </div>

            <div class="tables tables_1" style="height: calc(100vh - 300px)">
              <el-table
                :data="tableData"
                border
                height="100%"
                v-loading="tableLoading"
                style="width: 100%"
              >
              <el-table-column
          type="index"
          label="序号"
          align="center"
          sortable
          min-width="5%"
          :index="table_index"
        />
                <el-table-column label="年度" prop="reportYear" min-width="5%" align="center"/>
                <el-table-column label="季度" prop="reportQuarter" min-width="8%" align="center"> <template slot-scope="scope">
                  {{
                    scope.row.reportQuarter
                      | fromatComon(dict.type.REPORT_QUARTER)
                  }}
                </template>
                </el-table-column>
                <el-table-column label="公司名称" prop="companyName" min-width="20%" align="center" show-overflow-tooltip/>
                <el-table-column label="所属行业" prop="industryName" min-width="15%" align="center" show-overflow-tooltip/>
                <el-table-column label="保存时间" prop="updateTime" min-width="10%" align="center" show-overflow-tooltip/>
                <el-table-column
                  label="操作"
                  min-width="10%"
                  align="center"
                  class-name="small-padding fixed-width"
                >
                  <template slot-scope="scope">
                    <el-button
                      v-if="orgGrade == 'G' && scope.row.editable == '1'"
                      type="text"
                      title="编辑"
                      size="small"
                      icon="el-icon-edit"
                      @click="editItem(scope.row)"
                    ></el-button>
                    <el-button
                      size="mini"
                      type="text"
                      title="查看"
                      lay-event="detail"
                      icon="el-icon-search"
                      @click="openViewItem(scope.row)"
                    />
                    <el-button
                      type="text"
                      v-if="orgGrade == 'G' && scope.row.editable == '1'"
                      size="small"
                      title="删除"
                      icon="el-icon-delete"
                      @click="deleteItem(scope.row)"
                    ></el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
            <pagination
              v-show="total > 0"
              :total="total"
              :page.sync="queryParams.pageNum"
              :limit.sync="queryParams.pageSize"
              @pagination="tableList"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 新增 -->


    <el-dialog
      class="commons_popup"
      v-bind="$attrs"
      :visible.sync="addvisible"
      width="90%"
      title="新增"
      v-if="addvisible"
      append-to-body
    >
      <div slot="title" class="el-popup-header-title">
        <svg-icon icon-class="edit_file" />
        <span class="el-dialog-header-name">{{ "新增" }}</span>
      </div>
      <escalationGzwAdd editType="add" :closeBtn="closeBtnAdd" />
    </el-dialog>


        <!-- 修改 -->


        <el-dialog
      class="commons_popup"
      v-bind="$attrs"
      :visible.sync="editvisible"
      width="90%"
      title="修改"
      v-if="editvisible"
      append-to-body
    >
      <div slot="title" class="el-popup-header-title">
        <svg-icon icon-class="edit_file" />
        <span class="el-dialog-header-name">{{ "修改" }}</span>
      </div>
      <escalationGzwAdd editType="edit" :closeBtn="closeBtnEdit" :rowData = "rowData"/>
    </el-dialog>

    <!-- 查看 -->


    <el-dialog
      class="commons_popup"
      v-bind="$attrs"
      :visible.sync="viewvisible"
      width="90%"
      title="查看"
      v-if="viewvisible"
      append-to-body
    >
      <div slot="title" class="el-popup-header-title">
        <svg-icon icon-class="edit_file" />
        <span class="el-dialog-header-name">{{ "查看" }}</span>
      </div>
      <escalationGzwView  :rowData = "rowData"/>
    </el-dialog>


  </div>
</template>
    <script>
// import {} from '@/api/views/quarterly-report'
import {
  getSasacQuarterList, deleteSasacQuarterInfo, queryQuarterReportParam
} from "@/api/quarterly-report/escalation-gzw";
import escalationGzwAdd from "@/views/quarterlyReport/escalation-gzw-add";
import escalationGzwView from "@/views/quarterlyReport/escalation-gzw-view";
export default {
  name:'Escalation-gzw',
  components: {escalationGzwAdd,escalationGzwView},
  props: {},
  dicts: ["REPORT_QUARTER", "REPORT_STATUS"],
  data() {
    return {
      orgGrade: "",
      //右侧查询条件
      searchData: {
        reportYear: "",
        reportQuarter: "",
      },
      //表格页码
      total: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      tableLoading: false, //表格loading
      tableData: [], //附件列表
      addvisible:false, //新增弹窗
      editvisible:false, //修改弹窗
      viewvisible:false, //查看
      rowData:{} // 行点击
    };
  },
  created() {
    this.orgGrade = this.$store.getters.orgGrade;
    this.tableList();
  },
  methods: {
    table_index(index) {
      return (this.queryParams.pageNum - 1) * this.queryParams.pageSize + index + 1
    },
    tableList() {
      this.tableLoading = true;
      getSasacQuarterList(this.searchData).then((response) => {
        this.tableData = response.rows;
        this.total = response.total;
        this.tableLoading = false;
      });
    },
    //重置
    clearInfo() {
      this.queryParams.pageNum = 1;
      this.searchData.reportYear = "";
      this.searchData.reportQuarter = "";
      this.tableList();
    },
    //表格删除
    deleteItem(row) {
      this.$modal
        .confirm("是否确定删除该数据？")
        .then(function () {
          return deleteSasacQuarterInfo({ sasacQuarterReportId: row.id });
        })
        .then(() => {
          this.$modal.msgSuccess("删除成功");
          this.tableList();
        })
        .catch(() => {});
    },
    //查看
    openViewItem(row){
        this.rowData = row
        this.viewvisible = true
    },
    // 新增
    addItem(){
      queryQuarterReportParam().then((response) => {
        if( response.code===200){
          this.addvisible = true
        }
      });


    },
    //表格修改
    editItem(row){
        this.rowData = row
        this.editvisible = true
    },
    closeBtnAdd(){
        this.addvisible = false
        this.tableList()
    },
    closeBtnEdit(){
        this.editvisible = false
        this.tableList()
    }
  },
};
</script>
    <style lang="scss" scoped>

@import "~@/assets/styles/quarterly-report/index.css";

</style>
