<template>
<div>
  发起实例
  <el-button type="primary"  @click="nextStep" size="mini">发起</el-button>
  <el-button type="primary"  @click="publicSave" size="mini">保存</el-button>
  <Process
    :key="busiKey"
    ref="process"
    :refreshAssigneeUrl = "refreshAssigneeUrl"
    :save-btn-type="saveBtnType"
    :tab-flag="tabFlag"
    :selectValue="{
      busiKey:busiKey,
      title:title
    }"
    :center-variable="{}"
  />
</div>
</template>

<script>
  import Process from "@/components/Process/index";
    export default {
        name: "demoLaunch",
        components: {Process},
      data() {
        return {
          busiKey:'',//业务中获取
          title:'',//业务中获取
          saveBtnType:true,//是否需要保存按钮
          tabFlag:true,//表明是业务发起环节
          refreshAssigneeUrl:'/colligate/violDaily',//自定义业务url
        }
      },
      computed: {},
      watch: {},
      created() {
      },
      mounted() {
      },
      methods: {
          //保存
        publicSave(){
          this.$modal.msgSuccess('保存成功！')
        },
        //关闭页面或者弹框根据业务而定
        close(){
          this.$modal.msgSuccess('关闭业务！')
        },
        //弹出流程选择
        nextStep(){
          // 额外参数
          const loadProcessData = {
            a:1
          };
          // 业务操作完成后调用
          this.$refs.process.handle(1, loadProcessData);
        }
      }
    }
</script>

<style scoped>

</style>
