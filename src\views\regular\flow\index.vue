<template>
    <div>
      <TaskTodoAreaHandler
        v-if="selectValue.linkKey=='a001'"
        ref="todo"
      :selectValue="selectValue"
      :centerVariable="centerVariable"
      @handle="handle"
      ></TaskTodoAreaHandler>
      <TaskHastonAreaHandler
        v-else
        ref="todo"
        :selectValue="selectValue"
        :centerVariable="centerVariable"
        @handle="handle"
      ></TaskHastonAreaHandler>
    </div>
</template>

<script>
  import TaskTodoAreaHandler from './taskTodoAreaHandler';
  import TaskHastonAreaHandler from './taskHastonAreaHandler';
    export default {
      name: "index",
      props: {
        selectValue: {
          type: Object
        },
        centerVariable: {
          type: Object
        },
      },
      components: {
        TaskTodoAreaHandler,TaskHastonAreaHandler
      },
      mounted(){
        if(this.selectValue.linkKey==='a001'){
          this.$emit('saveBtn',true);
        }else{
          this.$emit('saveBtn',false);
        }
      },
      methods:{
        //保存
        publicSave(){
          this.$refs.todo.save();
        },
        //流程提交
        nextStep() {
          this.$refs.todo.nextStep();
        },
        //流程提交
        handle(type,data) {
          this.$emit('handle',type,data);
        },
      }
    }
</script>

<style scoped>

</style>
