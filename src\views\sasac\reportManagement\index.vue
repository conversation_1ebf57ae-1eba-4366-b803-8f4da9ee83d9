<!--提交国资委  上报管理-->
<template>
  <div class="grayBackground padding4 sasac">
    <el-row>
      <el-col :span="8">
        <BlockCard
          title="上报总览"
        >
          <div class="ry-common-card-content" style="height: 82px;">
            <div class="early-warning-data">
              <div class="early-warning-data-li">
                  <el-row>
                    <el-col :span="4" class="ewd-li ewd-li-6">
                      <el-row class="ewd-li-box padding10_0">
                        <el-col :span="24" class="ewd-li-5-right padding0">
                          <div class="ewd-li-6-num font-weight text-center margin-t10 text-red"  :title="sasacStatisticsData.reportYear">{{sasacStatisticsData.reportYear}}</div>
                          <p class="ewd-li-6-title text-center margin-t5 margin-b0 ovflowHidden">上报年度</p>
                        </el-col>
                      </el-row>
                    </el-col>
                    <el-col :span="20" class="padding-left10">
                      <div class="width100">
                        <el-row class="ewd-li-box  padding-top-bottom10">
                          <el-col :span="8" class="ewd-li-5-left">
                            <div class="ewd-li-6-num font-weight text-center margin-t10 ovflowHidden" :title="sasacStatisticsData.thisYearReportNumber">{{sasacStatisticsData.thisYearReportNumber}}</div>
                            <p class="ewd-li-6-title text-center margin-t5 margin-b0 ovflowHidden" title="本年上报次数">本年上报次数</p>
                          </el-col>
                          <el-col :span="8" class="ewd-li-5-left">
                            <div class="ewd-li-6-num font-weight text-center margin-t10 ovflowHidden" :title="sasacStatisticsData.cumulativeReportNumber">{{sasacStatisticsData.cumulativeReportNumber}}</div>
                            <p class="ewd-li-6-title text-center margin-t5 margin-b0 ovflowHidden" title="累计上报次数">
                              累计上报次数</p>
                          </el-col>
                          <el-col :span="8" class="ewd-li-5-right">
                            <div class="ewd-li-6-num font-weight text-center margin-t10 ovflowHidden" :title="sasacStatisticsData.lastReportTime" style="height: 24px;">{{sasacStatisticsData.lastReportTime}}</div>
                            <p class="ewd-li-6-title text-center margin-t5 margin-b0 ovflowHidden" title="最新一次上报时间">最新一次上报时间</p>
                          </el-col>
                        </el-row>
                      </div>
                    </el-col>
                  </el-row>
              </div>
            </div>
          </div>
        </BlockCard>
        <BlockCard
          title="上报类型统计"
        >
          <el-table v-loading="loading" :data="sasacStatisticsData.reportContentStatistics" height="352">
            <el-table-column label="上报内容" prop="reportContent" min-width="25%"/>
            <el-table-column label="累计上报次数" prop="cumulativeReportNumber" min-width="25%"/>
            <el-table-column label="最新一次上报时间" prop="lastReportTime" min-width="25%"/>
            <el-table-column label="最新一次上报数量" prop="reportItemNumber" min-width="25%"/>
          </el-table>
        </BlockCard>
        <BlockCard
          title="上报次数"
        >
          <el-form>
            <div class="position">
              <div id="canvas" class="canvas"></div>
            </div>
          </el-form>
        </BlockCard>
      </el-col>
      <el-col :span="16">
        <BlockCard
          title="上报问题列表"
          :height="822"
        >

          <el-form :model="queryParams" size="medium" ref="elForm"
                   label-width="105px">
            <el-row>
              <el-col :span="16">
                <el-form-item label="上报标题">
                  <el-input v-model="queryParams.reportTitle" placeholder="上报标题"  :style="{width: '100%'}">
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="上报年度">
                  <el-date-picker
                    :style="{width: '100%'}"
                    v-model="queryParams.reportYear"
                    type="year"
                    value-format="yyyy"
                    placeholder="上报年度">
                  </el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="16">
                <el-form-item label="上报内容">
                  <el-select v-model="queryParams.reportContents"
                             multiple
                             :style="{width: '100%'}"
                             >
                    <el-option :label="item.codeText" :value="item.code" v-for="(item,index) in queryDicListByTypeList"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="上报状态">
                  <el-select v-model="queryParams.reportStatus"
                             :style="{width: '100%'}"
                             placeholder="请选择"
                             clearable>
                    <el-option label="待上报" value="0">待上报</el-option>
                    <el-option label="上报审批中" value="2">上报审批中</el-option>
                    <el-option label="已上报" value="1">已上报</el-option>
                    <el-option label="上报失败" value="3">上报失败</el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <div class="float-right">
                  <el-button
                    type="primary"

                    icon="el-icon-plus"
                    size="mini"
                    @click="handleAdd"
                  >新增</el-button>
                  <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                  <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
                </div>
              </el-col>
            </el-row>
          </el-form>
          <el-form style="height:calc(100% - 190px);margin-top:5px">
            <el-table v-loading="loading" :data="tableList" height="100%">
              <el-table-column label="序号" type="index" width="50px" align="center">
                <template slot-scope="scope">
                  <table-index
                    :index="scope.$index"
                    :page-num="queryParams.pageNum"
                    :page-size="queryParams.pageSize"
                  />
                </template>
              </el-table-column>
              <el-table-column label="上报年度" show-overflow-tooltip prop="reportYear" width="100px"/>
              <el-table-column label="上报标题" show-overflow-tooltip prop="reportTitle" width="350px" />
              <el-table-column label="上报内容" show-overflow-tooltip prop="reportContent" width="350px"/>
              <el-table-column label="上报时间" show-overflow-tooltip prop="reportTime" width="100px"/>
              <el-table-column label="上报人" show-overflow-tooltip prop="reportPerson" width="100px"/>
              <el-table-column
                prop="reportStatus"
                label="上报状态"
                width="100px"
                min-width="10%">
                <template slot-scope="scope">
                  <span v-if = "scope.row.reportStatus == '2' && scope.row.procStatus == '1'">上报审批中</span>
                  <span v-else-if = "scope.row.reportStatus == '0'">待上报</span>
                  <span v-else-if = "scope.row.reportStatus == '1'">已上报</span>
                  <span v-else-if = "scope.row.reportStatus == '3'">上报失败</span>
                </template>
              </el-table-column>
              <el-table-column label="操作" fixed="right" width="100px" min-width="10%" align="center"
                               class-name="small-padding fixed-width">
                <template slot-scope="scope">
                  <el-button
                    v-if="scope.row.procStatus == '0' && scope.row.isNewData == '1'"
                    type="text"
                    title="编辑"
                    size="small"
                    icon="el-icon-edit"
                    @click="handleEditTable(scope.row)"
                  ></el-button>
                  <el-button
                    type="text"
                    v-if="scope.row.procStatus == '0' && scope.row.isNewData == '1'"
                    size="small"
                    title="删除"
                    icon="el-icon-delete"
                    @click="handleDelete(scope.row)"
                  ></el-button>
                  <el-button
                    type="text"
                    v-if="scope.row.procStatus == '2' && scope.row.isNewData == '1'"
                    size="small"
                    title="再次发起"
                    icon="el-icon-s-promotion"
                    @click="submitAgain(scope.row)"
                  ></el-button>
                  <el-button
                    size="mini"
                    type="text"
                    v-if="scope.row.procStatus != '0'"
                    title="查看"
                    icon="el-icon-search"
                    @click="handleDetail(scope.row)"
                  >
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-form>
          <pagination
            style="margin-top:0"
            v-show="total>0"
            :total="total"
            :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize"
            @pagination="ReportSasacRecords"
          />
        </BlockCard>
      </el-col>
    </el-row>
    <!--新增-->
    <ADD
      ref="add"
      :key="index"
      :list="queryDicListByTypeList"
      :length="sasacStatisticsData.cumulativeReportNumber"
      @addReport="addReport"
    ></ADD>
    <!--编辑-->
    <el-dialog class="sasac-dialog" :visible.sync="visible"  width="90%" @close="closeEdit" :modal-append-to-body="false" title="上报预览">
      <Edit
        ref="edit"
        :key="visible||problemId"
        :problemId="problemId"
        @closeEdit="closeEdit"
      >
      </Edit>
      <div slot="footer">
        <el-button size="mini" @click="closeEdit">取消</el-button>
        <el-button size="mini" type="primary" @click="editSubmit">提交</el-button>
      </div>
    </el-dialog>

    <!--详情-->
    <el-dialog  class="sasac-dialog" :visible.sync="visibleD"  width="90%" :modal-append-to-body="false" title="上报预览">
      <Detail
        ref="detail"
        :key="visibleD||problemId"
        :problemId="problemId"
        @closeEdit="closeDetail"
      >
      </Detail>
      <div slot="footer">
        <el-button size="mini" @click="closeD">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import echarts from 'echarts';
  import resize from '@/components/echars/mixins/resize'
  import ADD from './add';
  import Edit from './edit/index';
  import Detail from './edit/detail';
  import {reportSasacRecords, reportSasacStatisticsData
    , deleteReportRecord, queryDicListByType
    ,copySasacReportInfos} from "@/api/sasac/reportManagement/index";
  import BlockCard from '@/components/BlockCard';

  export default {
    mixins: [resize],
    name: "ReportManagement",
    components: {
      BlockCard,
      ADD,
      Edit,
      Detail
    },
    dicts: [],
    data() {
      return {
        problemId:'',//编辑与新增
        index:0,
        visible:false,
        loading:false,
        rows: {},
        total:0,
        echartName: '',
        ehcartNum: '',
        echartPercent: '',
        chart: null,
        sasacStatisticsData:{},
        queryDicListByTypeList:[],
        tableList:[],
        visibleD:false,
        queryParams: {
          pageNum: 1,
          pageSize: 10,
          reportContents: [],
          reportStatus: "",
          reportYear: "",
        },
      };
    },
    created() {
      this.QueryDicListByType();
      this.ReportSasacRecords();
    },
    mounted() {
      this.ReportSasacStatisticsData();
    },
    beforeDestroy() {
      if (!this.chart) {
        return
      }
      this.chart.dispose()
      this.chart = null
    },
    filters: {},
    methods: {
      close() {
        this.visible = false;
        this.ReportSasacRecords();
      },
      closeD(){
        this.visibleD=false;
        this.ReportSasacRecords();
      },
      /**查询上报问题列表*/
      ReportSasacRecords() {
        reportSasacRecords(this.queryParams).then(response => {
          this.tableList = response.rows;
          this.total = response.total;
        });
      },
      /** 搜索按钮操作*/
      handleQuery() {
        this.queryParams.pageNum = 1;
        this.ReportSasacRecords();
      },
      /**重置按钮操作*/
      resetQuery() {
        this.queryParams = {
          pageNum: 1,
          pageSize: 10,
          reportContents: [],
          reportStatus: "",
          reportYear: "",
        };
        this.ReportSasacRecords();
      },

      /** 删除按钮操作 */
      handleDelete(row) {
        this.$modal.confirm('是否删除该条上报吗？').then(() => {
          this.deleteReportRecord(row.id);
        }).catch(() => {
        });
      },
      /** 查看操作 */
      handleDetail(row) {
        this.problemId = row.id;
        this.visibleD = true;
        this.$nextTick(()=>{
          this.$refs.detail.ReportDetailBasicData(row.procInsId);
        });
      },
      /** 编辑 */
      handleEditTable(row) {
        this.problemId = row.id;
        this.visible = true;
        this.$nextTick(()=>{
          this.$refs.edit.ReportDetailBasicData();
        });
      },
      closeEdit(){
        this.visible = false;
        this.ReportSasacRecords();
      },
      //提交
      editSubmit(){
        this.$refs.edit.submitReport();
      },
      //新增上报成功
      addReport(problemId){
        this.problemId = problemId;
        this.visible = true;
        this.$nextTick(()=>{
          this.$refs.edit.ReportDetailBasicData();
        });
      },
      //新增
      handleAdd(){
        this.index++;
        this.$nextTick(()=>{
          this.$refs.add.show();
        });
      },
      deleteReportRecord(id) {
        deleteReportRecord(id).then(response => {
          if (200 === response.code) {
            this.$modal.msgSuccess("删除成功");
            this.ReportSasacRecords();
          } else {
            this.$modal.alertError(response.msg);
          }
        });
      },
      //上报总览、上报类型统计与上报次数数据
      ReportSasacStatisticsData() {
        reportSasacStatisticsData().then(response => {
          this.sasacStatisticsData = response.data;
          this.QueryDicListByType();
          this.EchartList();
        });
      },
      //上报类型统计
      QueryDicListByType() {
        queryDicListByType().then(response => {
          let dataArray = response.data;
          let dictList = [];
          dataArray.forEach(function (item) {
            dictList.push({code: item.dictValue, codeText: item.dictLabel})
          });
          this.queryDicListByTypeList = dictList;
        });
      },
      //echarts问题分布统计
      EchartList() {
        this.chart = echarts.init(document.getElementById("canvas"))
        this.chart.clear();
            // const chartDom = document.getElementById("canvas");
            // const myChart = echarts.init(chartDom);
            let title = []; //x轴值
            let taskNumber = []; //任务
            for (var i = 0; i < this.sasacStatisticsData.reportRecordYearDistribution.length; i++) {
              title.push(this.sasacStatisticsData.reportRecordYearDistribution[i].reportYear);
              taskNumber.push(this.sasacStatisticsData.reportRecordYearDistribution[i].thisYearReportNumber)
            }
            let option = {
              color: ['#A0BCFC'],
              tooltip: {
                trigger: 'axis',
                axisPointer: {
                  type: 'cross',
                  crossStyle: {
                    color: '#999'
                  }
                }
              },
              grid: {
                left: '2%',
                right: '2%',
                bottom: '6%',
                containLabel: true
              },
              xAxis: [{
                type: 'category',
                data: title,
                key: 'codeText',
                axisPointer: {
                  type: 'shadow'
                }
              }],
              yAxis: [{
                type: 'value',
                name: '次',
                axisLabel: {
                  formatter: '{value}'
                }
              }],
              series: [{
                name: '上报次数',
                type: 'bar',
                barWidth: '25',
                data: taskNumber,
                label: {
                  normal: {
                    show: true,
                    position: 'top',
                    textStyle: {
                      color: '#000'
                    }
                  }
                }
              }
              ]
            };
            this.chart.setOption(option, true);
      },

      //再次发起，弹出提示，确认，则后端将该数据涉及到的所有表数据重新复制一份，将原数据置为非最新数据；返回最新id，打开编辑窗口
      submitAgain(rowData){
        this.$modal.confirm('是否确认对【' + rowData.reportTitle + '】再次发起上报国资委？').then( ()=> {
          copySasacReportInfos(rowData.id).then(res=>{
            if (200 === res.code) {
              rowData.id = res.msg;
              //打开编辑窗口
              this.handleEditTable(rowData);
            } else {
              this.$modal.alertError(response.msg);
            }
          })
        }).catch(function () {});
      },
    }
  };
</script>
<style rel="stylesheet/scss" scoped lang="scss">

  .sasac {
    .el-dialog__body {
      height: 71vh;
    }
    .padding10_0{
      padding:10px 0 !important;
    }

  .question-ul {
    height: 156px;
    overflow: hidden;
  }

  .scope-chao-shi {
    margin-right: 5px;
    margin-bottom: 0;
  }

  .canvas {
    width: 100%;
    height: 194px;
    z-index: 999;
  }

  .echart-text-box {
    position: absolute;
    width: calc((100% - 40px) / 2);
    height: 100px;
    top: 56px;
    z-index: 9;
    text-align: center;
    p {
      text-align: center;
      line-height: 32px;
    }
    .echart-text-p {
      font-size: 12px;
    }
  }

  .early-warning-data {
    margin-bottom: 10px;
  }

  .early-warning-data-li {
    width: 100%;
    margin: 4px 0;
  }

  .ewd-li {
    float: left;
    padding: 4px;
    box-sizing: border-box;
  }

  .ewd-li-box {
    box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    border: solid 1px #eeeeee;
    height: 100%;
    padding: 8px;
    box-sizing: border-box;
  }

  .ewd-li-1{
    width: 83px;
    .ewd-li-1-circular {
      width: 68px;
      height: 68px;
      display: inline-block;
      line-height: 68px;
      border-radius: 50%;
      text-align: center;
      font-size: 18px;
      color: #ffffff;
      margin-top: 8px;
    }
  }

  .circular-1 {
    background-image: linear-gradient(-30deg,
      #ff4d4e 0%,
      #ffa39d 100%);
  }

  .circular-2 {
    background-image: linear-gradient(-30deg,
      #40a9ff 0%,
      #91d5ff 100%);
  }

  .ewd-li-right {
    float: right;
    width: calc(100% - 83px);
  }

  .ewd-li-2 {
    width: 30%;
  }

  .ewd-li-3 {
    width: 50%;
  }

  .ewd-li-4 {
    width: 20%;
  }

  .ewd-li-5 {
    width: 100%;
    padding: 0;
  }

  .ewd-li-6 {
    padding: 0;
  }

  .ewd-li-5 .ewd-li-box, .ewd-li-6 .ewd-li-box {
    padding: 10px 20px;
  }

  .ewd-li-5-left {
    height: 100%;
    border-right: 1px solid #d9d9d9;
  }

  .ewd-li-5-right {
    height: 100%;
    box-sizing: border-box;
  }

  .ewd-li-5-top {
    width: 100%;
    font-size: 14px;
    line-height: 24px;
    color: #73777a;
    margin-bottom: 10px;
  }

  .ewd-li-5-num {
    font-size: 20px;
    line-height: 24px;
    color: #181818;
  }

  .ewd-li-6-num {
    font-size: 18px;
    line-height: 24px;
    color: #333333;
    width: 100%;
    text-align: center;
  }

  .ewd-li-6-title {
    width: 100%;
    font-size: 14px;
    line-height: 24px;
    color: #898D8F;
    margin-bottom: 10px;
    text-align: center;
  }

  .ewd-li-5-box-img {
    margin-right: 10px;
  }

  .ewd-li-5-box-b {
    padding-left: 10px;
    font-size: 18px;
  }

  .ewd-li-top {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    height: 40px;
  }

  .ewd-li-5-box {
    line-height: 24px;
  }

  .ewd-li-5-percentage {
    float: left;
    font-size: 14px;
  }

  .ewd-li-5-per {
    float: left;
    width: 26%;
    margin: 3px 10px 0 10px;
    height: 18px;
    background-color: #f5f5f5;
    border-radius: 2px;
  }

  .ewd-li-5-span {
    height: 18px;
    display: inline-block;
    border-radius: 2px;
  }

  .ewd-li-5-span-1 {
    background-color: #ff4d4e;
  }

  .ewd-li-5-span-2 {
    background-color: #ffa940;
  }

  .ewd-li-5-span-3 {
    background-color: #ff8787;
  }

  .ewd-li-5-span-4 {
    background-color: #8da2fe;
  }
  .ewd-li-top-left {
    display: flex;
    align-items: center;
  }

  .nodara {
    width: 100%;
    height: 100%;
    min-height: 130px;
    color: #b5b5b5;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .ewd-li-top{
    .ewd-li-top-left{
      .iconfont {
        font-size: 20px;
      }
      .icon-processed {
        font-size: 16px;
      }
      span {
        font-size: 14px;
        color: #000;
        margin-left: 4px;
        line-height: 14px;
      }
    }
  }

  .ewm-conter-li-box {
    display: flex;
    border-radius: 2px;
    overflow: hidden;
  }

  .ewd-li-box .ry-form-radio, .early-warning-model .ry-form-radio {
    margin: 0;
  }

  .ry-form-radioed > i, .ry-form-radio > i:hover {
    color: #f5222d
  }

  .right-btn-box .ry-form-radio {
    margin-top: 0;
    padding: 0;
  }

  .ewd-li-top-right span {
    font-size: 22px;
    font-weight: bold;
    color: #333333;
  }

  .ewd-li-bottom {
    width: 100%;
    display: flex;
    height: 28px;
    justify-content: space-between;
    align-items: center;
  }

  .ewd-li-bottom {
    .ewd-li-bottom-li-label {
      font-size: 12px;
      color: #888888;
    }
    .ewd-li-bottom-li-num {
      font-size: 14px;
      color: #f5212d;
    }
    .ewd-li-bottom-li-model {
      font-size: 12px;
      background-color: #eeeeee;
      border-radius: 4px;
      border: solid 1px #cccccc;
      color: #888888;
      padding: 2px 12px;
    }
    .ewd-li-bottom-li-value {
      font-size: 14px;
      color: #03ac2b;
    }
  }
  .ry-quarantine {
    width: 100%;
    height: 13px;
    background-color: #eeeeee;
  }

  .early-warning-model-box {
    width: 100%;
    height: 466px;
    position: relative;
  }

  .early-warning-model-list {
    width: 100%;
    height: 466px;
    position: relative;
  }

  .early-warning-model-box:before {
    position: absolute;
    content: '\7cbe\51c6\5ea6';
    height: calc(100% - 20px);
    left: 50%;
    top: 10px;
    width: 1px;
    background: #cccccc;
    display: flex;
    justify-content: center;
    padding-top: 30px;
    box-sizing: border-box;
    color: #888888;
  }

  .early-warning-model-box:after {
    position: absolute;
    content: "\91cd\8981\6027";
    width: calc(100% - 20px);
    top: 50%;
    left: 10px;
    height: 1px;
    background: #cccccc;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding-right: 30px;
    box-sizing: border-box;
    color: #888888;
  }

  .ewm-box-ul-li {
    height: 233px;
    box-sizing: border-box;
    position: relative;
  }

  .ewm-box-ul-li1 {
    padding: 20px 0 20px 20px;
  }

  .ewm-box-ul-li2 {
    padding: 20px 10px 20px 0;
    position: relative;
  }

  .ewm-box-ul-li3 {
    padding: 35px 0 20px 20px;
  }

  .ewm-box-ul-li4 {
    padding: 35px 10px 20px 0;
  }

  .ewm-box-ul-li1:after {
    content: "";
    position: absolute;
    left: -7px;
    top: 5px;
    width: 0;
    height: 0;
    border-width: 0 8px 8px;
    border-style: solid;
    border-color: transparent transparent #888888;
  }

  .ewm-box-ul-li2:after {
    content: "";
    width: 8px;
    height: 8px;
    background: #fff;
    display: inline-block;
    position: absolute;
    left: 0;
    bottom: -5px;
    border-radius: 50%;
    border: 1px solid #888888;
  }

  .ewm-box-ul-li3:after {
    content: "";
    position: absolute;
    right: -6px;
    top: -7px;
    width: 0;
    height: 0;
    border-width: 8px 8px 8px;
    border-style: solid;
    border-color: transparent transparent transparent #888888;
  }

  .ewm-box-ul-li4:after {
    content: "";
    width: 8px;
    height: 8px;
    background: #fff;
    display: inline-block;
    position: absolute;
    right: -5px;
    bottom: 0;
    border-radius: 50%;
    border: 1px solid #888888;
  }

  .ewm-conter-list {
    height: 180px;
    overflow: auto;
    padding-right: 10px;
  }

  .ewm-conter-li {
    width: 100%;
    height: 20px;
    margin: 10px 0;
    cursor: pointer;
  }

  .ewm-conter-li.active {
    .ewm-conter-li-title {
      color: #f5222d;
    }
  }

  .ewm-conter-li-title {
    padding: 0;
    float: left;
    line-height: 20px;
    width: 32%;
    font-size: 13px;
  }

  .ewm-conter-li-right {
    float: right;
    width: 68%;
  }
  .ewm-conter-li-data {
    width: 46px;
    float: left;
    line-height: 20px;
    padding-left: 2px;
    box-sizing: border-box;
    .iconfont {
      font-size: 14px;
      vertical-align: middle;
    }
  }

  .ewm-conter-li-num {
    font-size: 14px;
    color: #333333;
    vertical-align: top;
  }

  .ewm-conter-li-speed {
    float: right;
    width: calc(100% - 46px);
  }

  .ewm-conter-li-value {
    box-sizing: border-box;
    float: left;
    height: 20px;
    color: #ffffff;
    font-size: 14px;
    line-height: 20px;
  }

  /*******************  */
  .ewm-conter-li-value1 {
    text-align: center;
    background-image: linear-gradient(90deg,
      rgba(3, 172, 43, 0.8) 0%,
      rgba(3, 172, 43, 0.3) 100%);
    border-radius: 2px 0px 0px 2px;
  }

  .ewm-conter-li-value2 {
    text-align: center;
    background-image: linear-gradient(90deg,
      rgba(250, 139, 22, 0.8) 0%,
      rgba(250, 139, 22, 0.3) 100%);
    border-radius: 0px 2px 2px 0px;
  }

  .drill-model-list {
  }

  .drill-model-li {
    display: flex;
    align-items: center;
    height: 38px;
  }

  .drill-model-text {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 40px;
  }

  .drill-model-span1 {
    color: #73777a;
  }

  .drill-model-span2 {
    color: #181818;
    margin-right: 8px;
  }

  .ewm-conter-li-values1 {
    text-align: left;
    background: #ffb180;
    padding-left: 6px;
  }

  .ewm-conter-li-values2 {
    padding-left: 8px;
    text-align: left;
    background: #5ad8a6;
  }

  /*******************  */
  .ewm-conter-fixed {
    position: absolute;
  }

  .position-select {
    position: absolute;
    width: 100%;
    text-align: right;
    top: -54px;
  }

  .ewm-conter-fixed-title {
    width: 24px;
    height: 24px;
    line-height: 24px;
    text-align: center;
    background-color: #555555;
    border: solid 2px #cccccc;
    display: inline-block;
    border-radius: 50%;
    font-size: 14px;
    color: #ffffff;
    position: relative;
    z-index: 999;
  }

  .ewm-conter-fixed:hover {
    .ewm-conter-fixed-data {
      display: inline-block;
    }
  }

  .ewm-conter-fixed-data {
    display: none;
    height: 20px;
    background-color: #888888;
    border-radius: 10px 9px 9px 10px;
    border: solid 1px #cccccc;
    width: 110px;
    text-align: center;
    position: absolute;
    right: 15px;
    top: 4px;
    z-index: 0;
    line-height: 20px;
  }


  .ewm-conter-fixed1 {
    left: 10px;
    bottom: 10px;
    .ewm-conter-fixed-data {
      left: 15px;
      top: 4px;
    }
  }


  .ewm-conter-fixed2 {
    right: 10px;
    bottom: 10px;
    .ewm-conter-fixed-data {
      right: 15px;
      top: 4px;
    }
  }

  .ewm-conter-fixed3 {
    left: 10px;
    top: 10px;
  }

  .ewm-conter-fixed3 .ewm-conter-fixed-data {
    left: 15px;
    top: 4px;
  }

  .ewm-conter-fixed4 {
    right: 10px;
    top: 10px;
  }

  .ewm-conter-fixed4 .ewm-conter-fixed-data {
    right: 15px;
    top: 4px;
  }

  .ewm-conter-fixed-data .iconfont {
    color: #fff;
    font-size: 10px;
    margin-left: 5px;
  }

  .right-btn-box .ry-form-switch {
    margin-top: -2px;
  }

  .ewm-conter-fixed-data span {
    color: #fff;
  }

  .question-li {
    height: 32px;
    line-height: 32px;
    border-bottom: 1px dashed #cccccc;
  }

  .question-li-1 {
    float: left;
    width: 62%;
  }

  .question-li-2 {
    float: left;
    width: 14%;
    display: flex;
    align-items: center;
  }

  .question-li-3 {
    float: left;
    width: 24%;
  }

  .question-li-4 {
    float: right;
    width: 24%;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .question-li-ranking {
    width: 16px;
    font-size: 12px;
    height: 16px;
    line-height: 16px;
    color: #fff;
    display: inline-block;
    border-radius: 50%;
    text-align: center;
  }

  .question-li-ranking1 {
    background-color: #73777a;
  }

  .question-li-ranking2 {
    background-color: #a9b0b4;
  }

  .question-li-text {
    font-size: 14px;
    margin-left: 10px;
    color: #555555;
  }

  .question-li-2{
    .iconfont {
      color: #ff4e4f;
      font-size: 18px;
    }
  }

  .question-li-3 .iconfont, .question-li-4 .iconfont {
    color: #ffa940;
    font-size: 18px;
  }

  .padding-left10 {
    padding-left: 10px;
  }
  }
  .sasac-dialog ::v-deep.el-dialog__body{
    padding:2px 20px;
  }
</style>














