import request from '@/utils/request'

export function queryProvDataAuditInfo(params){
  return request({
    url: '/dataaudit/query/queryProvDataAuditInfo',
    method: 'post',
    data: params
  })
}

//查询数据质量列表--系统实时数据
export function queryProvRealList(query) {
  // const pageParams = { pageNum: query.pageNum, pageSize: query.pageSize }
  return request({
    url: '/dataaudit/query/queryProvRealList',
    method: 'post',
    data: query
    // ,params: pageParams
  })
}

//查询数据质量列表--系统实时数据-已办
export function queryProvSaveRealList(query) {
  return request({
    url: '/dataaudit/query/queryProvSaveRealList',
    method: 'post',
    data: query
    // ,params: pageParams
  })
}

//查询数据质量列表--稽核数据
export function queryProvAuditList(query) {
  return request({
    url: '/dataaudit/query/queryProvAuditList',
    method: 'post',
    data: query
    // ,params: pageParams
  })
}

//保存不一致原因
export function saveDifferentReason(params) {
  return request({
    url: '/dataaudit/edit/saveDifferentReason',
    method: 'post',
    data: params
  })
}

//流程提交校验
export function validateAuditInfo(auditId){
  return request({
    url: '/dataaudit/edit/validateAuditInfo/'+auditId,
    method: 'post'
  })
}


//查询数据质量列表
export function queryAuditTableList(query) {
 const pageParams = { pageNum: query.pageNum, pageSize: query.pageSize }
  return request({
    url: '/dataaudit/query/queryAuditTableList',
    method: 'post',
    data: query
     ,params: pageParams
  })
}
//催办
export function urging(id) {
  return request({
    url: '/dataaudit/edit/toUrge/'+id,
    method: 'post',
    data:{
      'auditId':id
    }
  })
}

//团查看根据稽核年度、季度查询稽核信息
export function queryDataAuditInfo(query,pageParams) {
  return request({
    url: '/dataaudit/query/queryDataAuditInfo',
    method: 'post',
    data: query
    ,params: pageParams
  })
}

//集团查看查询系统业务实时数据(一致、不一致)
export function queryAllRealList(query) {
  return request({
    url: '/dataaudit/query/queryAllRealList',
    method: 'post',
    data: query
  })
}

//集团查看查询稽核汇总数据(一致、不一致)
export function queryAllAuditList(query) {
  return request({
    url: '/dataaudit/query/queryAllAuditList',
    method: 'post',
    data: query
  })
}
