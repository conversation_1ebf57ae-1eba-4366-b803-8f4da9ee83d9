<template>
  <div class="home app-container">
    <!--公告-->
    <div class="notice-container-box" v-show="open">
      <div class="notice-container">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>系统公告</span>
            <el-button style="float: right; padding: 3px 0" type="text" @click="closeNotice">关闭</el-button>
          </div>
          <el-descriptions :title="form.noticeTitle" direction="vertical">
            <el-descriptions-item label="公告内容">
              <editor v-model="form.noticeContent" height="350" readOnly="true"/>
            </el-descriptions-item>
          </el-descriptions>
        </el-card>
      </div>
    </div>
    <div v-if="isClock">
      <HomeIndex v-if="isHomeIndex"/>
      <PerSonal v-if="!isHomeIndex"/>
    </div>
  </div>
</template>

<script>
  import HomeIndex from './home'
  import PerSonal from './personal'
  import { showNotice, addCloseNotice } from "@/api/system/notice";
  import {queryHomeRole} from '@/api/home/<USER>'

  '@/components/trig-tag'
  export default {
    name: 'Index',
    components: {
      HomeIndex,
      PerSonal
    },
    data() {
      return {
        // 系统公告表单
        form: {},
        open: false,
        // 版本号
        version: '3.7.0',
        isHomeIndex: '',
        isClock: ''
      }
    },
    created() {
      console.log('进入jdzz')
      this.queryHomeRole();
       this.showNotice();
    },
    methods: {
      queryHomeRole() {
        queryHomeRole({}).then((response) => {
          const {code, data} = response
          if (code === 200) {
            this.isHomeIndex = data
            this.isClock = '1'
          }
        })
      },
      showNotice() {
        this.loading = true;
        showNotice().then(response => {
          if (response.data.length > 1){
            this.$message.error("存在多条系统公告，无法弹出!");
          }else if (response.data.length == 1){
            this.open = true;
            this.form = response.data[0];
          }
        });
      },
      closeNotice(){
        addCloseNotice({noticeId:this.form.noticeId}).then(response => {
          this.open = false;
          this.form = {};
        });
      }
    }
  }
</script>

<style scoped lang="scss">

  .home {
    .notice-container-box{
      width: 100%;
      height: 100vh;
      position: fixed;
      display: flex;
      align-items: center;
      background: rgba(0,0,0,0.3);
      left:0;
      top:0;
      z-index: 9999;
      .notice-container{
        width: 55%;
        margin: 0 auto ;
      }
    }
    blockquote {
      padding: 10px 20px;
      margin: 0 0 20px;
      font-size: 17.5px;
      border-left: 5px solid #eee;
    }
    hr {
      margin-top: 20px;
      margin-bottom: 20px;
      border: 0;
      border-top: 1px solid #eee;
    }
    .col-item {
      margin-bottom: 20px;
    }

    ul {
      padding: 0;
      margin: 0;
    }

    font-family: "open sans", "Helvetica Neue", Helvetica, Arial, sans-serif;
    font-size: 13px;
    color: #676a6c;
    overflow-x: hidden;

    ul {
      list-style-type: none;
    }

    h4 {
      margin-top: 0px;
    }

    h2 {
      margin-top: 10px;
      font-size: 26px;
      font-weight: 100;
    }

    p {
      margin-top: 10px;

      b {
        font-weight: 700;
      }
    }

    .update-log {
      ol {
        display: block;
        list-style-type: decimal;
        margin-block-start: 1em;
        margin-block-end: 1em;
        margin-inline-start: 0;
        margin-inline-end: 0;
        padding-inline-start: 40px;
      }
    }
  }
</style>
