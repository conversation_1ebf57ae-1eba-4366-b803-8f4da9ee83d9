<template>
  <div>
    <el-dialog v-bind="$attrs" :visible.sync="visible" width="90%" :title="title" @close="close">
      <div class="process-x">
        <div class="process-top">
          <div class="process-box">
            <div class="process-li" v-for="(item,index) in processBox" :class="
        processIndex==item.statusName?problemStatus==item.statusName?'active select process-li':'green select process-li':
        problemStatus==item.statusName?'active green process-li':problemStatus<item.statusName?'process-li':'green process-li'"
                 @click="iframeUrl(item.statusName)">
              <span class="process-number">{{ item.statusName }}</span>
              <span class="process-name">{{ item.problemStatus }}</span>
            </div>
          </div>
        </div>
        <div class="process-content" v-show="FillIn">
          <URL1
            @openLoading="openLoading"
            @closeLoading="closeLoading"
            v-if="problemStatus==='1'"
            :key="id"
            ref="accept"
            :edit='edit'
            :problemId="id"
            @hisNext="QueryProblemStatus"
            :relevantTableId="relevantTableId"
            :relevantTableName="relevantTableName"
            @handle="handle"
          ></URL1>
          <URL2
            @openLoading="openLoading"
            @closeLoading="closeLoading"
            v-if="problemStatus==='2'"
            :key="id"
            ref="accept"
            :edit='edit'
            :problemId="id"
            @hisNext="QueryProblemStatus"
            :relevantTableIds="relevantTableId"
            :relevantTableNames="relevantTableName"
            @handle="handle"
          ></URL2>
          <URL3
            @openLoading="openLoading"
            @closeLoading="closeLoading"
            v-if="problemStatus==='3'"
            :key="id"
            ref="accept"
            :edit='edit'
            :problemId="id"
            @hisNext="QueryProblemStatus"
            :relevantTableIds="relevantTableId"
            :relevantTableNames="relevantTableName"
            @handle="handle"
          ></URL3>

          <URL4
            @openLoading="openLoading"
            @closeLoading="closeLoading"
            v-if="problemStatus==='4'"
            :key="id"
            ref="accept"
            :edit='edit'
            :problemId="id"
            @hisNext="QueryProblemStatus"
            :relevantTableIds="relevantTableId"
            :relevantTableNames="relevantTableName"
            @handle="handle"
          ></URL4>
          <URL5
            @openLoading="openLoading"
            @closeLoading="closeLoading"
            v-if="problemStatus==='5'"
            :key="id"
            ref="accept"
            :edit='edit'
            :problemId="id"
            @hisNext="QueryProblemStatus"
            :relevantTableIds="relevantTableId"
            :relevantTableNames="relevantTableName"
            @handle="handle"
          ></URL5>
          <URL6
            @openLoading="openLoading"
            @closeLoading="closeLoading"
            v-if="problemStatus==='6'"
            :key="id"
            ref="accept"
            :edit='edit'
            :problemId="id"
            @hisNext="QueryProblemStatus"
            :relevantTableIds="relevantTableId"
            :relevantTableNames="relevantTableName"
            @handle="handle"
          ></URL6>
          <URL7
            @openLoading="openLoading"
            @closeLoading="closeLoading"
            v-if="problemStatus==='7'"
            :key="id"
            ref="accept"
            :edit='edit'
            :problemId="id"
            @hisNext="close"
            :relevantTableIds="relevantTableId"
            :relevantTableNames="relevantTableName"
            @handle="handle"
          ></URL7>
        </div>
        <div class="process-content" v-show="!FillIn">
          <URL1S
            @openLoading="openLoading"
            @closeLoading="closeLoading"
            v-if="!FillIn&&processIndex=='1'"
            :key="id"
            ref="accepts"
            :edit='edit'
            :problemId="id"
            :relevantTableIds="relevantTableId"
            :relevantTableNames="relevantTableName"
            @handle="handle"
          ></URL1S>
          <URL2S
            @openLoading="openLoading"
            @closeLoading="closeLoading"
            v-if="!FillIn&&processIndex=='2'"
            :key="id"
            ref="accepts"
            :edit='edit'
            :problemId="id"
            :relevantTableIds="relevantTableId"
            :relevantTableNames="relevantTableName"
            @handle="handle"
          ></URL2S>
          <URL3S
            @openLoading="openLoading"
            @closeLoading="closeLoading"
            v-if="!FillIn&&processIndex=='3'"
            :key="id"
            ref="accepts"
            :edit='edit'
            :problemId="id"
            :relevantTableIds="relevantTableId"
            :relevantTableNames="relevantTableName"
            @handle="handle"
          ></URL3S>
          <URL4BS
            @openLoading="openLoading"
            @closeLoading="closeLoading"
            v-if="!FillIn&&processIndex=='4'&&linkKey == 'a033'"
            :key="id"
            ref="accepts"
            :edit='edit'
            :problemId="id"
            :relevantTableIds="relevantTableId"
            :relevantTableNames="relevantTableName"
            @handle="handle"
          ></URL4BS>
          <URL4S
            @openLoading="openLoading"
            @closeLoading="closeLoading"
            v-if="!FillIn&&processIndex=='4' && linkKey != 'a033'"
            :key="id"
            ref="accepts"
            :edit='edit'
            :problemId="id"
            :relevantTableIds="relevantTableId"
            :relevantTableNames="relevantTableName"
            @handle="handle"
          ></URL4S>
          <URL5S
            @openLoading="openLoading"
            @closeLoading="closeLoading"
            v-if="!FillIn&&processIndex=='5'"
            :key="id"
            ref="accepts"
            :edit='edit'
            :problemId="id"
            :relevantTableIds="relevantTableId"
            :relevantTableNames="relevantTableName"
            @handle="handle"
          ></URL5S>
          <URL6S
            @openLoading="openLoading"
            @closeLoading="closeLoading"
            v-if="!FillIn&&processIndex=='6'"
            :key="id"
            ref="accepts"
            :edit='edit'
            :problemId="id"
            :relevantTableIds="relevantTableId"
            :relevantTableNames="relevantTableName"
            @handle="handle"
          ></URL6S>
          <URL7S
            @openLoading="openLoading"
            @closeLoading="closeLoading"
            v-if="!FillIn&&processIndex=='7'"
            :key="id"
            ref="accepts"
            :edit='edit'
            :problemId="id"
            :relevantTableIds="relevantTableId"
            :relevantTableNames="relevantTableName"
            @handle="handle"
          ></URL7S>
        </div>

        <div class="text-right" style="margin-top:10px;">
          <el-button type="primary" v-show="FillIn&&processIndex!='7'" icon="el-icon-tickets" size="mini" @click="publicSave" v-preventReClick plain>保存</el-button>
          <el-button type="primary" v-show="FillIn" icon="el-icon-check" size="mini" @click="nextStep" plain>提交</el-button>
          <el-button  icon="el-icon-close" size="mini" @click="close">关闭</el-button>
        </div>

      </div>
    </el-dialog>
  </div>
</template>

<script>
import URL1 from './process/taskTodoViewAccept';
import URL1S from './process/taskTodoViewAcceptProess';
import URL2 from './process/taskTodoViewVerify';
import URL2S from './process/taskTodoViewVerifyProess';
import URL3 from './process/taskTodoViewDisposal';
import URL3S from './process/taskTodoViewDisposalProess';
import URL4 from './process/verificationRecord';
import URL4BS from './process/verificationRecordDetailBranch';
import URL4S from './process/verificationRecordDetail';
import URL5 from './process/handlingAppealRecords';
import URL5S from './process/handlingAppealRecordsDetail';
import URL6 from './process/rectificationRecord';
import URL6S from './process/rectificationRecordProess';
import URL7 from './process/taskTodoViewFileReview';
import URL7S from './process/taskTodoViewFileReview';
import {selectDailyTopFlowInfo} from "@/api/components/daily";
import {queryProblemStatus} from "@/api/daily/historyQuestionEnter/index";

export default {
  name: "dailyBox",
  props: {
    title:{
      type:String,
      default:'编辑'
    },
    id: {
      type: String
    },
    relevantTableId: {
      type: String
    },
    relevantTableName: {
      type: String
    }
  },
  components: {
    URL1, URL1S, URL2, URL2S, URL3, URL3S, URL4, URL4BS, URL4S, URL5, URL5S, URL6, URL6S, URL7, URL7S
  },
  data() {
    return {
      FillIn: true,
      processIndex: 1,
      edit: false,
      problemStatus: '',
      processBox: [],
      linkKey: this.id,
    }
  },
  mounted() {
    this.QueryProblemStatus();
  },
  methods: {
    closeLoading() {
      this.$emit('closeLoading');
    },
    openLoading() {
      this.$emit('openLoading');
    },
    //打开弹窗
    show() {
      this.visible = true
      this.$forceUpdate();
    },
    // 关闭
    close() {
      this.visible = false
      this.$forceUpdate();
      this.$emit('close')
    },
    iframeUrl(index) {
      if (index > this.problemStatus) {
        return false;
      } else if (index == this.problemStatus) {
        this.processIndex = index;
        this.FillIn = true;
      } else {
        this.processIndex = index;
        this.FillIn = false;
      }
    },
    //保存
    publicSave() {
      this.$refs.accept.publicSave();
    },
    //下一步
    nextStep() {
      this.$refs.accept.nextStep();
    },
    //下一步回调
    handle(type, object) {
      this.$emit('handle', type, object);
    },
    //获取环节页面
    QueryProblemStatus() {
      if(this.id){
        queryProblemStatus(this.id).then(
          response => {
            this.problemStatus = response.data.dailyProblemStatus;
            this.processIndex = response.data.dailyProblemStatus;
            this.SelectDailyFlowInfo();
            this.$emit('openLoading');
          }
        )
      }
    },
    //环节名称
    SelectDailyFlowInfo() {
      selectDailyTopFlowInfo({procInsId: ''}).then(
        response => {
          this.processBox = response.data;
        }
      )
    }
  }
}
</script>

<style scoped lang="scss">
.position-top {
  position: fixed;
  top: 0;
}

.process-box {
  display: flex;
  position: relative;
  height: 64px;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #d9d9d9;
}

.process-box:before {
  position: absolute;
  content: '';
  left: 0;
  top: 31px;
  width: 100%;
  z-index: 10;
  height: 1px;
  border-bottom: 1px solid #d9d9d9;
}

.process-box .process-li {
  padding: 0 16px;
  background: #fff;
  z-index: 999;
  position: relative;
}

.process-li .process-number {
  width: 32px;
  height: 32px;
  background-color: #d9d9d9;
  display: inline-block;
  text-align: center;
  line-height: 32px;
  border-radius: 50%;
  color: #73777a;
  font-size: 14px;
  margin-right: 16px;
}

.process-li .process-name {
  letter-spacing: 1px;
  color: #a9b0b4;
  font-size: 14px;
}

.process-li.green .process-number {
  background-color: #ffe2e4;
  color: #f5222d;
  cursor: pointer;
}

.process-li.green .process-name {
  color: #f5222d;
  cursor: pointer;
}

.process-li.select:before {
  position: absolute;
  content: '';
  bottom: -16px;
  width: 100%;
  left: 0;
  height: 4px;
  background-color: #f5222d;
}

.process-li.active .process-number {
  background-color: #f5222d;
  color: #fff;
}

.process-li.active .process-name {
  color: #f5222d;
}

.verify-top-title {
  color: #a9b0b4;
  padding: 10px 0;
  text-align: center;
  border-bottom: 1px solid #d9d9d9;
}

.verify-bottom-title {
  color: #a9b0b4;
  padding: 10px 0;
  text-align: center;
  border-top: 1px solid #d9d9d9;
}

.process-x {
  position: relative;
  overflow: hidden;
  height: 100%;

  .process-box {
    height: 64px;
  }

  .process-content {
    height: calc(100% - 105px);
    overflow: auto;
  }
}
::v-deep .el-dialog__body{
  height: 80vh;
  padding:10px 20px 20px;
}
</style>
