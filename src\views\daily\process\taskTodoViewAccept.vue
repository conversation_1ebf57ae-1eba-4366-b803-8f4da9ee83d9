<!--1：问题受理-发起流程-->
<template>
  <div>
    <el-dialog v-bind="$attrs" :visible.sync="visible" width="90%" title="受理填报" @open="QueryViolateInfo"  @close="close">
      <div>
        <el-form ref="elForm" :model="formData" size="medium" label-width="138px">
          <BlockCard
            title="基本信息"
          >
            <el-row>

              <el-col :span="6">
                <el-form-item label="发现日期" prop="findTime">
                  <el-date-picker
                    v-model="formData.findTime"
                    format="yyyy-MM-dd"
                    :disabled="!edit"
                    value-format="yyyy-MM-dd"
                    :style="{width: '100%'}"
                    placeholder="请选择发现日期"
                    clearable
                  />
                </el-form-item>
              </el-col>
              <!-- <el-col :span="6">
                <el-form-item label="受理日期" prop="acceptTime">
                  <el-date-picker
                    v-model="formData.acceptTime"
                    format="yyyy-MM-dd"
                     :disabled="!edit"
                    value-format="yyyy-MM-dd"
                    :style="{width: '100%'}"
                    placeholder="请选择受理日期"
                    clearable
                  />
                </el-form-item>
              </el-col> -->
              <el-col :span="12">
                <el-form-item label="问题线索来源" prop="problemSource">
                  <el-select
                    v-model="formData.problemSource"
                    :disabled="!edit"
                    placeholder="请选择问题线索来源"
                    clearable
                    :style="{width: '100%'}"
                    @change="problemSourceChanged"
                  >
                    <el-option
                      v-for="(item, index) in problemSourceList"
                      :key="index"
                      :label="item.dictLabel"
                      :value="item.dictValue"
                    >{{ item.dictLabel }}</el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="spLedgerShow?12:24">
                <el-form-item label="违规事项" prop="problemTitle">
                  <el-input
                    v-model="formData.problemTitle"
                    :readonly="!edit"
                    placeholder="请输入违规事项"
                    clearable
                    :style="{width: '100%'}"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12" v-if="spLedgerShow">
                <SpLedger
                  :key="spLedgerKey"
                   ref="scopesp"
                  :edit="edit"
                  :problem-id="problemId"
                  :saveFlag="saveFlag"
                  @spLedgerSaveInfo="spLedgerSaveInfo"
                />
              </el-col>
              <el-col :span="24">
                <el-form-item
                  label="问题线索描述"
                  prop="problemDescribe"
                  :rules="[
                    { min: 1, max: 1024, message: '长度在 1 到 1024 个字符', trigger: 'blur' }
                  ]"
                >
                  <el-input
                    v-model="formData.problemDescribe"
                    type="textarea"
                    placeholder="请输入问题线索描述"
                    :autosize="{minRows: 4, maxRows: 4}"
                    :style="{width: '100%'}"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="涉及专业线" prop="specLists">
                  <el-checkbox-group :key="formData.specLists" v-model="formData.specLists" size="medium"  :disabled="!edit">
                    <el-checkbox
                      v-for="(item, index) in specList"
                      :key="item.dictValue"
                      border
                      :label="item.dictValue"
                    >{{ item.dictLabel }}</el-checkbox>
                  </el-checkbox-group>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="涉及单位/部门/人员" prop="field107">
                  <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="treeOpen" v-if="edit">添加部门人员</el-button>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item>
                  <PersList
                    ref="pers"
                    :edit="edit"
                    :problem-id="problemId"
                    :relevant-table-id="relevantTableId"
                    :relevant-table-name="relevantTableName"
                  />
                </el-form-item>
              </el-col>

            </el-row>
          </BlockCard>

          <BlockCard
            title="造成的损失风险"
          >
            <el-row>

              <el-col :span="24">
                <el-form-item label="是否产生资产损失" prop="lossStateAssetsFlag">
                  <el-radio-group v-model="formData.lossStateAssetsFlag" size="medium" :disabled="!edit">
                    <el-radio v-for="(item, index) in whetherLossOptions" :key="index" :label="item.value" @change="lossStateAssetsChanged">{{item.label}}</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="预估损失金额（万元）" prop="lossAmount">
                  <el-input-number v-model="formData.lossAmount" :style="{width: '100%'}" :min="0" :precision="2" placeholder="损失金额（万元）" controls-position="right" :disabled="lossAmountDisabled||!edit" />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="预估损失风险（万元）" prop="lossRisk">
                  <el-input-number v-model="formData.lossRisk" :style="{width: '100%'}" :min="0" :precision="2" placeholder="损失风险（万元）" controls-position="right" :disabled="!edit"/>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item
                  label="损失形成主要原因"
                  prop="lossReason"

                  :rules="[
                    { min: 1, max: 1024, message: '长度在 1 到 1024 个字符', trigger: 'blur' }
                  ]"
                >
                  <el-input
                     :readonly="!edit"
                    v-model="formData.lossReason"
                    type="textarea"
                    placeholder="请输入损失形成主要原因"
                    :autosize="{minRows: 4, maxRows: 4}"
                    :style="{width: '100%'}"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="是否产生不良影响" prop="isAdverseEffects">
                  <el-radio-group v-model="formData.isAdverseEffect" size="medium" :disabled="!edit">
                    <el-radio v-for="(item, index) in whetherEffectOptions" :key="index" :label="item.value" @change="radioEffectChanged"
                              :disabled="item.disabled">{{item.label}}</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="对应不良影响" prop="correspondingAdverseEffects" v-show="formData.isAdverseEffect" >
                  <el-select v-model="formData.correspondingAdverseEffects" :style="{width: '100%'}" clearable="clearable" multiple="multiple" value="" :disabled="!edit">
                    <el-option v-for="(item, index) in dict.type.corresponding_adverse_effect" :key="index" :label="item.label" :value="item.value"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="造成的不良影响" prop="adverseEffects">
                  <el-input
                  :readonly="!edit"
                    v-model="formData.adverseEffects"
                    type="textarea"
                    placeholder="请输入造成的不良影响"
                    :autosize="{minRows: 4, maxRows: 4}"
                    :style="{width: '100%'}"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="是否产生严重不良影响" prop="seriousAdverseEffectsFlag" v-show="formData.isAdverseEffect">
                  <el-radio-group v-model="formData.seriousAdverseEffectsFlag" size="medium" :disabled="!edit">
                    <el-radio
                      v-for="(item, index) in seriousAdverseEffectsFlagOptions"
                      :key="index"
                      :label="item.value"
                      :disabled="item.disabled"
                    >{{ item.label }}</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col v-show="formData.seriousAdverseEffectsFlag && formData.isAdverseEffect" :span="18">
                <el-form-item label="严重不良影响描述" prop="seriousAdverseEffectsDesc">
                  <el-select
                  :disabled="!edit"
                    v-model="formData.seriousAdverseEffectsDesc"
                    placeholder="请选择严重不良影响描述"
                    clearable
                    :style="{width: '100%'}"
                    value="formData.seriousAdverseEffectsDesc"
                  >
                    <el-option
                      v-for="(item, index) in dict.type.VIOLD_ADVER_EFFECT_DES"
                      :key="index"
                      :label="item.label"
                      :value="item.value"
                      :disabled="item.disabled"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="存在风险或涉嫌的违法违纪行为" prop="illegalActivities">
                  <el-input
                   :readonly="!edit"
                    v-model="formData.illegalActivities"
                    type="textarea"
                    placeholder="请输入存在风险或涉嫌的违法违纪行为"
                    :autosize="{minRows: 4, maxRows: 4}"
                    :style="{width: '100%'}"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <ScopeSituation
                  :key="problemId"
                  ref="scope"
                  :edit="edit"
                  :problem-id="problemId"
                  :relevant-table-id="relevantTableId"
                  :relevant-table-name="relevantTableName"
                />
              </el-col>
              <el-col :span="24">
                <el-form-item label="其他说明事项" prop="otherNotes">
                  <el-input
                   :readonly="!edit"
                    v-model="formData.otherNotes"
                    type="textarea"
                    placeholder="请输入其他说明事项"
                    :autosize="{minRows: 4, maxRows: 4}"
                    :style="{width: '100%'}"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </BlockCard>
        </el-form>
        <BlockCard
          title="附件列表"
        >
          <FileUpload
            v-if="relevantTableId!=''&&relevantTableName!=''"
            ref="file"
            :edit="edit"
            :problem-id="problemId"
            :relevant-table-id="relevantTableId"
            :relevant-table-name="relevantTableName"
            flow-type="VIOL_DAILY"
            problem-status="1"
            link-key="a001"
            flow-key="SupervisionDailyReport"
          />
          <el-dialog class="tree-body-dialog" :visible.sync="visibleTree" width="90%" :before-close="saveY" append-to-body title="人员选择">
            <Tree
              v-if="visibleTree"
              ref="persTree"
              :problem-id="problemId"
              :relevant-table-id="relevantTableId"
              :relevant-table-name="relevantTableName"
              @save="saveY"
            />
            <div slot="footer" class="dialog-footer">
              <el-button size="mini" type="primary" @click="closeTree">保存</el-button>
            </div>
          </el-dialog>

        </BlockCard>
      </div>
      <Process
        v-if="edit"
        slot="footer"
        ref="process"
        type="parent"
        tab-flag="6"
        :select-value="{
          busiKey:problemId,
          title:formData.problemTitle,
        }"
        flow-params-url="/colligate/violDaily/flowParams"
        @publicSave="publicSave"
        @close="close"
        @nextStep="submitForm"
      />
    </el-dialog>
  </div>
</template>
<script>
import { queryViolateInfo, saveViolateInfo, checkAndSaveViolateInfo } from '@/api/daily/process/taskTodoViewAccept'
import { generateInvolveItemModifyRecord } from '@/api/components/index'
import BlockCard from '@/components/BlockCard'
import ScopeSituation from './../scopeSituation/scopeSituationData'// 范围情形展示
import { checkInvolve } from '@/api/components/index'
import FileUpload from './../../components/fileUpload'// 附件
import PersList from './../tree/persList'// tree
import Tree from './../tree'// tree
import TaskTodoViewAccept from './../process/taskTodoViewAccept'// tree
import Process from '@/components/Process/daily'
import SpLedger from '@/views/daily/spledger/spLedgerData';//专项报告台账
import {deleteSpLedgerById} from "@/api/daily/spledger/index";

export default {
  components: { BlockCard, ScopeSituation, FileUpload, Tree, PersList, Process, TaskTodoViewAccept ,SpLedger},
  dicts: ['VIOLD_DAILY_SPEC', 'VIOLD_ADVER_EFFECT_DES', 'corresponding_adverse_effect'],
  props: {
    edit: {
      type: Boolean,
      default: false
    },
    problemId: {
      type: String
    },
    relevantTableId: {
      type: String
    },
    relevantTableName: {
      type: String
    }
  },
  data() {
    return {
      lossAmountDisabled: false,
      showAdverseEffectFlag: false,
      flag: false,
      visible: false,
      visibleTree: false,
      formData: {
        findTime: null,
        acceptTime: null,
        problemSource: null,
        problemTitle: null,
        problemDescribe: undefined,
        field107: undefined,
        lossAmount: undefined,
        lossRisk: undefined,
        lossReason: undefined,
        adverseEffects: undefined,
        seriousAdverseEffectsFlag: 1,
        seriousAdverseEffectsDesc: undefined,
        illegalActivities: undefined,
        otherNotes: undefined,
        specLists: [],
        correspondingAdverseEffects: [],
      },
      specList: [],
      rules: {},
      seriousAdverseEffectsFlagOptions: [{
        'label': '是',
        'value': 1
      }, {
        'label': '否',
        'value': 0
      }],
      whetherLossOptions: [{
        "label": "是",
        "value": 1
      }, {
        "label": "否",
        "value": 0
      }],
      whetherEffectOptions: [{
        "label": "是",
        "value": 1
      }, {
        "label": "否",
        "value": 0
      }],
      problemSourceList: [],
      spLedgerKey:0,
      spLedgerShow:false,//关联追责台账是否显示
      saveFlag:'1',//是否保存 0：未保存；1：已保存
    }
  },
  computed: {},
  watch: {},
  created() {
  },
  mounted() {},
  methods: {
    lossStateAssetsChanged() {
      if (this.formData.lossStateAssetsFlag !== undefined && this.formData.isAdverseEffect !== undefined) {
        if (!this.formData.lossStateAssetsFlag && !this.formData.isAdverseEffect) {
          this.$message.error("【是否产生资产损失】与【是否产生不良影响】全部选择“否”不符合填报规则！");
          this.formData.lossStateAssetsFlag = 1;
        }
      }
      this.lossAmountDisabled = !this.formData.lossStateAssetsFlag;
      if (!this.formData.lossStateAssetsFlag) {
        this.formData.lossAmount = 0
      }
    },
    radioEffectChanged() {
      if (this.formData.lossStateAssetsFlag !== undefined && this.formData.isAdverseEffect !== undefined) {
        if (!this.formData.lossStateAssetsFlag && !this.formData.isAdverseEffect) {
          this.$message.error("【是否产生资产损失】与【是否产生不良影响】全部选择“否”不符合填报规则！");
          this.formData.isAdverseEffect = 1;
        }
      }
      this.showAdverseEffectFlag = this.formData.isAdverseEffect;
    },
    //问题线索来源切换事件
    problemSourceChanged(problemSource){
      this.saveFlag = '0';
      //循环数组，取对应值的remark
      let remark = this.problemSourceList[this.problemSourceList.findIndex(item => item.dictValue === problemSource)].remark;
      if(remark.indexOf("LEDGER")>-1){
        this.spLedgerKey++
        this.spLedgerShow = true;
      }else{
        this.spLedgerShow = false;
        //删除关联的台账
        let data={
          problemId:this.problemId
        };
        deleteSpLedgerById(data);
      }
    },
    // 关闭
    close() {
      this.visible = false
      this.$emit('close')
    },
    /** 初始化数据*/
    QueryViolateInfo() {
      this.loading = true
      const array = []
      queryViolateInfo({ problemId: this.problemId, relevantTableId: this.relevantTableId }).then(
        response => {
          const specSelectedList = response.data.specSelectedList
          this.formData = { ...this.formData, ...response.data.acceptEntity }
          for (let i = 0, len = specSelectedList.length; i < len; i++) {
            array.push(specSelectedList[i].specCode)
          }
          this.lossAmountDisabled = !this.formData.lossStateAssetsFlag;
          this.formData.specLists = array
          this.specList = response.data.specList
          this.formData.relevantTableId = this.relevantTableId
          this.formData.correspondingAdverseEffects = response.data.correspondingAdverseEffects;
          this.problemSourceList = response.data.problemSourceList
          this.relevantTableName = response.data.acceptEntity.relevantTableName
          //判断是否显示关联台账
          if(this.formData.problemSource){
            this.problemSourceChanged(this.formData.problemSource)
          }
          this.saveFlag = '1';
          this.loading = false
          this.$refs.pers.DueryDepartmentSelectInfo()
          this.$refs.file.ViolationFileItems()
          this.$emit('closeLoading')
        }
      )
    },
    /** 提交数据*/
    submitForm() {
      const volve = this.CheckInvolve()
      if (!volve) {
        return false
      }
      this.$refs['elForm'].validate(valid => {
        if (!valid) return
        // TODO 提交表单
        const array = []
        const specSelectedList = this.formData.specLists
        const specList = this.specList
        for (let i = 0, len = specSelectedList.length; i < len; i++) {
          for (let j = 0, leng = specList.length; j < leng; j++) {
            if (specList[j].dictValue == specSelectedList[i]) {
              specList[j].specCode = specList[j].dictValue
              specList[j].specName = specList[j].dictLabel
              array.push(specList[j])
            }
          }
        }
        this.formData.specList = array
        checkAndSaveViolateInfo(this.formData).then(
          response => {
            const data = response.data
            if (!data.validateFlag) {
              if(data.validateType == 'LEDGER'){
                this.$confirm("此问题需要关联专项追责台账，当前未关联，是否返回关联？关联途径有：<br>1、返回“受理填报”页面进行关联追责台账；<br>" +
                  "2、“专项报告录入”-“项目问题台账”功能下关联问题。", '提示', {
                  confirmButtonText: '直接提交',
                  cancelButtonText: '返回关联',
                  type: 'warning',
                  dangerouslyUseHTMLString:true
                }).then(()=> {
                  //继续提交
                  this.$refs.process.handle(1)
                }).catch(() => {});
              }else{
                this.$modal.msgError(data.validateMsg)
              }
            } else {
              this.$modal.msgSuccess('保存成功')
              this.$refs.process.handle(1)
            }
          }
        )
      })
    },
    // 校验单位、部门、人员
    CheckInvolve() {
      let final = true;
      if (!this.formData.lossStateAssetsFlag && !this.formData.isAdverseEffect) {
        this.$message.error("【是否产生资产损失】与【是否造成不良影响】全部选择“否”不符合填报规则，请重新选择！");
        return false;
      }
      checkInvolve(this.problemId).then(
        response => {
          if (response.code == 200) {
            let companyString = ''; let // 单位列表
              deptString = ''// 部门列表
            if (response.data.resultCode == 'false') {
              this.$message.error('请选择单位！')
              final = false
            } else if (response.data.resultCode == 'company') {
              companyString = ''
              for (let i = 0; i < response.data.listCompany.length; i++) {
                companyString += '【' + response.data.listCompany[i].INVOL_COMPANY_NAME + '】'
                if (i + 1 != response.data.listCompany.length) {
                  companyString += '、'
                }
              }
              companyString = '' + companyString + '下未选择涉及部门！'
              this.$message.error(companyString)
              final = false
            } else if (response.data.resultCode == 'double') {
              companyString = ''
              deptString = ''
              for (let i = 0; i < response.data.listCompany.length; i++) {
                companyString += '【' + response.data.listCompany[i].INVOL_COMPANY_NAME + '】'
                if (i + 1 != response.data.listCompany.length) {
                  companyString += '、'
                }
              }
              for (let i = 0; i < response.data.listDept.length; i++) {
                deptString += '【' + response.data.listDept[i].INVOL_ORG_NAME + '】'
                if (i + 1 != response.data.listDept.length) {
                  deptString += '、'
                }
              }
              companyString = '1、' + companyString + '下未选择涉及部门！<br/>'
              deptString = '2、' + deptString + '下未选择涉及人员！'
              this.$message.error(companyString + deptString)
              final = false
            } else if (response.data.resultCode == 'dept') {
              deptString = ''
              for (let i = 0; i < response.data.listDept.length; i++) {
                deptString += '【' + response.data.listDept[i].INVOL_ORG_NAME + '】'
                if (i + 1 != response.data.listDept.length) {
                  deptString += '、'
                }
              }
              deptString = deptString + '下未选择涉及人员！'
              this.$message.error(deptString)
              final = false
            } else {
              generateInvolveItemModifyRecord({ problemId: this.problemId, businessId: this.relevantTableId }).then(response => {})
              final = true
            }
          }
        }
      )
      return final
    },
    /** 保存数据*/
    publicSave() {
      // this.formData.specList=[];
      const array = []
      const specSelectedList = this.formData.specLists
      const specList = this.specList
      for (let i = 0, len = specSelectedList.length; i < len; i++) {
        for (let j = 0, leng = specList.length; j < leng; j++) {
          if (specList[j].dictValue == specSelectedList[i]) {
            specList[j].specCode = specList[j].dictValue
            specList[j].specName = specList[j].dictLabel
            array.push(specList[j])
          }
        }
      }
      this.formData.specList = array
      saveViolateInfo(this.formData).then(
        response => {
          this.$modal.msgSuccess('保存成功')
        }
      )
    },
    resetForm() {
      this.$refs['elForm'].resetFields()
    },
    // 打开弹窗
    show() {
      this.visible = true
    },
    // 关闭弹窗
    closeTree() {
      this.$refs.persTree.CheckInvolve()// 校验
    },
    saveY() {
      this.visibleTree = false

      this.$refs.pers.DueryDepartmentSelectInfo()
    },
    // 选择人员
    treeOpen() {
      this.flag = !this.flag
      this.visibleTree = true
    },

    //关联台账前默认保存数据
    spLedgerSaveInfo(queryType){
      const loading = this.$loading({
        spinner: 'el-icon-loading', // 自定义加载图标类名
        text: '正在加载...', // 显示在加载图标下方的加载文案
        lock: false, // lock的修改符--默认是false
      });
      const array = []
      const specSelectedList = this.formData.specLists
      const specList = this.specList
      for (let i = 0, len = specSelectedList.length; i < len; i++) {
        for (let j = 0, leng = specList.length; j < leng; j++) {
          if (specList[j].dictValue == specSelectedList[i]) {
            specList[j].specCode = specList[j].dictValue
            specList[j].specName = specList[j].dictLabel
            array.push(specList[j])
          }
        }
      }
      this.formData.specList = array
      saveViolateInfo(this.formData).then(
        response => {
          loading.close();
          this.saveFlag='1';
          this.$refs.scopesp.queryLedgerList(queryType)
        }
      )
    },
  }
}

</script>
<style scoped>
.tree-body-dialog ::v-deep.el-dialog__body{
  padding:0 20px;
}
</style>
