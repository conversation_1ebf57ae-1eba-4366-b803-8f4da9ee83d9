<template>
  <div
    :id="id"
    ref="chart"
    :class="className"
    :style="{ height: height, width: width }"
  />
</template>
<script>
import echarts from 'echarts'
import { fontSizeEchars } from './mixins/fontSizeEchars'
import resize from './mixins/resize'
require('echarts/theme/macarons')

export default {
  mixins: [resize],
  props: {
    charsData: {
      type: null,
      default: () => {}
    },
    id: {
      type: String,
      default: 'myChart'
    },
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '100%'
    }
  },
  data() {
    return {
      chart: null
    }
  },
  watch: {
    charsData: {
      handler(val, oldVal) {
        this.chart.clear()
        setTimeout(() => {
          this.initChart()
        }, 1000)
      },
      deep: true
    }
  },
  mounted() {
    this.initChart()
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.clear()
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$refs.chart, 'macarons')
      this.chart.setOption(
        {
          title: {
            text: '{a|超时占比}{c|\n}{c|' + this.charsData.warnRate + '%}',
            textStyle: {
              color: '#fff',
              fontSize: fontSizeEchars(0.32),
              fontWeight: '100',
              rich: {
                a: {
                  fontSize: fontSizeEchars(0.15),
                  color: '#A6A6A6',
                  padding: [-fontSizeEchars(0.3), 0, 0, 0],
                  fontWeight: '500'
                },

                c: {
                  fontSize: fontSizeEchars(0.20),
                  lineHeight: fontSizeEchars(0.75),
                  color: '#0B0B0B',
                  fontWeight: '600',
                  padding: [0, 0, fontSizeEchars(0.3), 0]
                }
              }
            },
            x: 'center',
            y: 'center'
          },
          angleAxis: {
            max: this.charsData.totalNumbers,
            clockwise: true,
            axisLine: {
              show: false
            },
            axisTick: {
              show: false
            },
            axisLabel: {
              show: false
            },
            splitLine: {
              show: false
            }
          },
          radiusAxis: {
            type: 'category',
            axisLine: {
              show: false
            },
            axisTick: {
              show: false
            },
            axisLabel: {
              show: false
            },
            splitLine: {
              show: false
            }
          },
          polar: {
            center: ['50%', '50%'],
            radius: '160%'
          },
          series: [{
            type: 'bar',
            startAngle: 180,
            data: [{
              name: '超时占比',
              value: this.charsData.warnNumbers,
              itemStyle: {
                normal: {
                  color: '#FEBE9A'
                }
              }
            }],
            coordinateSystem: 'polar',
            roundCap: true,
            barWidth: fontSizeEchars(0.25),
            barGap: '-100%',
            radius: ['49%', '52%'],
            z: 1
          },
          {
            type: 'bar',
            data: [{
              value: this.charsData.totalNumbers,
              itemStyle: {
                color: '#F0F1F5'

              }
            }],
            coordinateSystem: 'polar',
            roundCap: true,
            barWidth: fontSizeEchars(0.25),
            barGap: '-110%',
            radius: ['48%', '53%'],
            z: 0
          }

          ]
        },
        true
      )
    }
  }
}
</script>
<style lang="scss" scoped>
#myChart {
  width: 100%;
  height: 100%;
  div {
    width: 100%;
    height: 100%;
  }
}
</style>
