<!--附件列表-->
<template>
  <div>
    <div class="vio-file-box">
      <el-row class="vio-file-div" v-for="item in fieldList">
        <el-col :span="4" class="vio-file-type flex vio-file-border">
          <span class="text-red" v-show="item.attachmentType=='CHBG'">*</span>
          <span>{{item.attachmentTypeName}}</span>
        </el-col>
        <el-col :span="edit?4:0" class="vio-file-download flex vio-file-border">
          <div class="mr5">
            <el-button  size="mini" type="primary" v-if="item.attachmentType=='CHBG'&&edit" @click="handleDownload()"  title="下载" plain>模板下载</el-button>
          </div>
          <FileUpload
            v-if="edit"
            :key="index"
            :isShowTip=showTip
            :fileUrl="uploadUrl"
            btnTitle="上传附件"
            :param="{
                     attachmentType: item.attachmentType,
                     projectId:projectId
                     }"
            @handleUploadSuccess="handleUploadSuccess"
          >
            文件上传
          </FileUpload>
        </el-col>
        <el-col :span="edit?16:20" class="vio-file-content">
          <ul class="vio-file-list">
            <el-row class="vio-file-li ry-row flex" v-for="obj in item.attachmentList">
              <el-col :span="12"  class="vio-file-name">
                <i class="el-icon-tickets"></i>
                <span>{{obj.fileName}}</span>
              </el-col>
              <el-col :span="2" class="vio-file-user icon-grey">
                <span>{{obj.uploadNickName}}</span>
              </el-col>
              <el-col :span="6" class="vio-file-time layui-col-md3 layui-col-sm3 icon-grey">{{obj.createTime}}</el-col>
              <el-col :span="4" class="vio-file-del layui-col-md2 layui-col-sm2 text-center">
                <a href="javascript:void(0);" @click="fileDownload(obj)" class="table-btn tip-edit" title="下载">
                  <i class="el-icon-bottom"></i>
                </a>
                <a href="javascript:void(0);" class="table-btn tip-edit" v-if="edit" title="删除" @click="delFile(obj)">
                  <i class="el-icon-delete"></i>
                </a>
              </el-col>
            </el-row>
          </ul>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
import {
  queryReportFileList
  ,delSprAttachmentById
} from '@/api/special-report'

export default {
  name: "fileUpload",
  components: {
  },
  props: {
    edit: {
      type: Boolean,
      default:false
    },
    projectId: {
      type: String,
      default: ''
    }
  },
  data(){
    return{
      type:false,
      status:'',
      index:0,
      showTip:false,
      fieldList:[],
      uploadUrl: '/spr/attachment/uploadSprFile',
      downloadUrl:'/jtauditwo/files/downLoad/',//下载地址
    }
  },
  created(){
    this.fieldListFun()
  },
  methods:{
    /** 获取数据*/
    fieldListFun() {
      //根据主键查询初核专项报告附件信息
      queryReportFileList(this.projectId).then((res)=>{
        //附件信息
        this.fieldList = res.data;
        this.$forceUpdate();
      })
    },

    /** 删除操作 */
    delFile(obj) {
      let  title = '确认删除该附件吗？';
      this.$modal.confirm(title).then(()=> {
        let params = {
          id:obj.id
          ,attachmentId:obj.attachmentId
        }
        return delSprAttachmentById(params);
      }).then(() => {
        this.$modal.msgSuccess("删除成功");
        this.fieldListFun();
      }).catch(() => {});
    },
    // 上传成功回调
    handleUploadSuccess(res, file) {
      this.fieldListFun();
    },
    //下载模板
    handleDownload(){
      this.download('/spr/attachment/downLoadSpReportAttachment/D_PRO_SP_PROJECT_INFO_SPR', {}, "专项初步核实报告（模板）.docx");
    },
    /**下载文件*/
    fileDownload(obj){
      this.download('/sys/attachment/downloadSysAttachment/'+obj.attachmentId, {
      },obj.fileName)
    },
  }
}
</script>

<style scoped lang="scss">
.flex{
  display: flex;
  align-items: center;
}
.vio-file-box{
  border: 1px solid #d9d9d9;
  .vio-file-div{
    display: flex;
    width: 100%;
    border-bottom: 1px solid #d9d9d9;
    .vio-file-border{
      border-right: 1px solid #d9d9d9;
    }
    .vio-file-type{
      background-color: #F4F8FC;
      color: #73777a;
      min-height: 48px;
      padding: 0 10px;
      box-sizing: border-box;
      .text-red{
        color: #f5222d !important;
      }
    }
    .vio-file-download{
      justify-content: center;
      .vio-file-down{
        padding: 0 4px;
        border-right: 1px solid #d9d9d9;
      }
      i{
        color: #f5222d;
      }
      .vio-file-down:last-child{
        border-right-width: 0;
      }
    }

    .vio-file-content{
      min-height: 48px;
      .vio-file-list{
        padding:0;
        margin:0;
        .vio-file-li{
          padding-left: 10px;
          box-sizing: border-box;
          border-bottom: 1px solid #d9d9d9;
          min-height: 48px;
          .vio-file-name{
            i{
              margin-right:6px;
            }
          }
          .vio-file-user,.vio-file-time{
            height: 48px;
            display: flex;
            align-items: center;
            color: #a9b0b4;
          }
          .vio-file-del{
            text-align: center;
            i{
              color:#f5222d;
              margin:0 6px;
            }
          }
        }
        .vio-file-li:last-child {
          border-bottom-width: 0;
        }
      }
    }
  }
  .vio-file-div:last-child {
    border-bottom-width: 0;
  }
  ::v-deep.upload-file-uploader{
    margin-bottom: 0;
  }
}
</style>
