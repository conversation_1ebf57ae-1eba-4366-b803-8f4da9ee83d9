
/**
* 数组格式转树状结构
* @param   {array}     array
* @return  {Array}
*/
export function arrayToTree(array) {
 let data = JSON.parse(JSON.stringify(array));
 let result = [];
 let hash = {};
 data.forEach((item) => {
   hash[item['id']] = item;
 });

 data.forEach(item => {
   let hashVP = hash[item['pId']];
   if (hashVP) {
     !hashVP['children'] && (hashVP['children'] = []);
     if (item['id']) {
       item.key = item['id'];
       item.id = item['id'];
     }
     if (item['name']) {
       item.label = item['name'];
     }
     hashVP['children'].push(item);
   } else {// 根节点直接放入结果集
     if (item['id']) {
       item.key = item['id'];
       item.id = item['id'];
     }
     if (item['name']) {
       item.label = item['name'];
     }
     result.push(item);
   }
 });
 return result;
}