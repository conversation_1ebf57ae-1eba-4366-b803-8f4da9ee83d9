<template>
  <div class="todo">
    <el-dialog v-bind="$attrs" :visible.sync="visible" width="90%" title="已阅详情">
      <div class="todo-content">
        <div class="todo-header">
          <el-radio-group  v-model="tabPosition">
            <el-radio-button label="1">业务信息</el-radio-button>
          </el-radio-group>
        </div>
      </div>
      <div class="todo-data">
        <Daily
          v-if="type==='daily'"
          :key="index"
          ref="todo"
          :selectValue="selectValue"
          :centerVariable="centerVariable"
          @handle="handle"
        ></Daily>
        <Regular
          v-if="type==='regular'"
          :key="index"
          ref="todo"
          :selectValue="selectValue"
          :centerVariable="centerVariable"
          @handle="handle"
        ></Regular>
        <Actual
          v-if="type==='actual1'||type==='actual2'"
          :key="index"
          ref="todo"
          :type="type"
          :selectValue="selectValue"
          :centerVariable="centerVariable"
          @handle="handle"
        ></Actual>

        <component
           v-if="type!=='actual'&&type!=='actual2'&&type!=='actualEdit'&&type!='regular'&&type!='daily'"
          :is="businessUrl"
          :key="index"
          ref="todo"
          :center-variable="centerVariable"
          @collocation = "collocation"
          @openLoading="openLoading"
          @closeLoading="closeLoading"
        />
        <!-- <component
          :is="businessUrl"
          :key="index"
          ref="todo"
          :center-variable="centerVariable"
          @collocation = "collocation"
          @openLoading="openLoading"
          @closeLoading="closeLoading"
        /> -->
      </div>
      <Read :params="centerVariable"
            slot="footer"
            :key="centerVariable"
            ref="process"
            :tabFlag="tabFlag"
            :selectValue="selectValue"
            :centerVariable="centerVariable"
            :flowCfgLink="flowCfgLink"
            :edit="edit"
            @close="close">

      </Read>
    </el-dialog>
  </div>
</template>
<script>
  window.componentsConfig = {    //左边是key值，右边是组件的引入路径
    'daily': '@/components/Process/read'
  };
  import Opinion from "./opinion";
  import daily from "@/views/index";
  import Read from "@/components/Process/read";
  import { findRecordPath } from "@/api/components/process";
  import Daily from "@/views/daily/dailyHasdone";//日常
  import Actual from "@/views/actual/flow/toRead";//实时
  import Regular from "@/views/regular/flow/taskHastonAreaHandler";//定期

  export default {
    inheritAttrs: false,
    components: {
      Opinion,
      daily,
      Read,
      Daily,
      Actual,
      Regular
    },
    props: {
      selectValue: {
        type: Object
      }
    },
    data() {
      return {
        businessUrl:'',
        edit:false,
        targetComponent:'daily',
        currentTabComponent:false,
        tabPosition:"1",
        centerVariable:{},
        flowCfgLink:{},
        type:'',
        index:0,
        visible:false,//弹框
      }
    },
    watch: {},
    created() {},
    mounted() {},
    computed:{
      NextTickName: function (){
        let map = window.componentsConfig;
        if(this.targetComponent){
          let k = this.targetComponent;       // 组件映射关系key值
          // let p = map[k];        // 通知k值读取到路径信息
          // let c = () => import(`${p}`);          // 动态组件
          return k
        }
      }
    },
    methods: {
      /** 点开弹窗 */
      show(){
        this.visible = true;
        this.FindRecordPath();
      },
      /** 关闭弹窗 */
      close() {
        this.visible = false;
        this.$emit('refresh');
      },
      /**主要数据*/
      FindRecordPath(){
        findRecordPath(this.selectValue).then(
          response => {
            this.centerVariable = {
              busiKey:this.selectValue.busiId
            };
            this.flowCfgLink = response.data.dataRows[0].flowCfgLink;
            this.type=response.data.dataRows[0].url;
            this.businessUrl = (resolve) => require([`@/views/${response.data.dataRows[0].url}`], resolve);
            this.index++;
            this.$nextTick(()=>{
              this.visible = true;
            })
          }
        );
      },
    }
  }

</script>
<style scoped lang="scss">
  .todo{
    .todo-header{
      ::v-deep.el-radio-button__inner{
        border-radius: 0 !important;
        border-color: #f4f4f4 !important;
        box-shadow:0 0 0 0 #f5222d !important;
        width: 120px;
      }
    }
    .todo-data{
      background: #fff;
      margin-top:8px;
      overflow: auto;
      height: calc(100% - 46px);
    }
    ::v-deep.el-dialog__body{
      border-top: 2px solid #E9E8E8;
      padding:0 20px 10px;
      background: #F4F4F4;
      height: calc(90vh - 110px);
      overflow: auto;
    }
    ::v-deep.el-dialog{
      margin-bottom: 0;
    }
  }
</style>

