<template>
  <div class="j-scrollbar">
    <el-scrollbar :style="{'height':height?height:''}">
      <slot></slot>
    </el-scrollbar>
  </div>
</template>

<script>
  export default {
    name: "maxHeight",
    props: {
      height: {
        type: String,
        default: 0
      },
    }
  }
</script>

<style rel="stylesheet/scss" lang="scss">
  .j-scrollbar{
    .el-scrollbar__wrap {
      overflow-x: hidden !important;
    }
    .el-scrollbar__view{
      height: 90%;
    }
  }
</style>
