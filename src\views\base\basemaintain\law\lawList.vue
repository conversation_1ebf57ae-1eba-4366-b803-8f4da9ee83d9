<!--规章制度-->
<template>
  <div class="padding_b10 app-lawList">
    <SearchList :searchList="searchList" :buttonGroup="buttonGroup" @on-search="handleQuery" @on-reset="resetQuery"></SearchList>
    <el-table :data="tableList" border :header-cell-style="{background:'#F4F8FC',color:'#606266','text-align':'center'}">
      <el-table-column label="序号" type="index" min-width="4%" align="center">
        <template slot-scope="scope">
          <table-index
          :index="scope.$index"
          :pageNum="queryParams.pageNum"
          :pageSize="queryParams.pageSize"
          />
        </template>
      </el-table-column>
      <el-table-column label="单位名称" prop="involProvName" min-width="8%" align="center"/>
      <el-table-column label="标题" prop="title"  min-width="25%" show-overflow-tooltip align="left"/>
      <el-table-column label="文号" prop="issueCode"  min-width="15%" show-overflow-tooltip align="center"/>
      <el-table-column label="印发日期" prop="publishDate" :formatter="dateFormat" min-width="10%" align="center"/>
      <el-table-column label="施行日期" prop="implementDate" :formatter="dateFormat" min-width="10%" align="center"/>
      <el-table-column label="制度类型" prop="classifyText" min-width="8%" align="center"/>
      <el-table-column label="类别" prop="categoryText" min-width="8%" align="center"/>
      <el-table-column label="制度文件" prop="lawNum" min-width="5%" align="center"/>
      <el-table-column label="状态" prop="commitFlag" :formatter="formatText" width="100" align="center"/>
      <el-table-column label="操作" fixed="right" width="100" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit-outline"
            title="编辑"
            @click="openAdd(scope.row)"
          ></el-button>
          <el-button
            v-if="scope.row.dispatchStatus != 0"
            size="mini"
            type="text"
            title="提交"
            icon="el-icon-finished"
            @click="subLaw(scope.row)"
          ></el-button>
          <el-button
            v-if="scope.row.dispatchStatus != 0"
            size="mini"
            type="text"
            title="删除"
            icon="el-icon-delete"
            @click="delLaw(scope.row)"
          ></el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="lawBaseInfo"
    />
    <lawAdd v-if="dialogVisible" v-on:closeModal="closeModal" :id="id"></lawAdd>
  </div>
</template>

<script>
  import {getBaseLawList, saveBaseLaw, delBaseLaw} from "@/api/base/law";
  import lawAdd from "./lawAdd";
  import moment from "moment"
  import SearchList from "../../common/SearchList";

  export default {
    name: "lawList",
    components: { lawAdd, SearchList },
    data() {
      return {
        // 总条数
        total: 0,
        // 表格数据
        tableList: [],
        // 是否显示弹出层
        dialogVisible: false,
        id: null,
        //新增主键
        //日常问题查询 参数
        queryParams: {
          pageNum: 1,
          pageSize: 10,
          involProvName:'',
          title:'',
        },
        searchList: [
          {
            label: "单位名称",
            name: "involProvName",
            value: null,
            type: "Input"
          },
          {
            label: "标题",
            name: "title",
            value: null,
            type: "Input"
          }
        ],
        buttonGroup: [{
          label: "新增",
          icon: "el-icon-plus",
          onClickHandle: ()=>this.openAdd(null)
        },]
      };
    },
    created() {
      this.lawBaseInfo();
    },
    methods: {
      /**查询企業基本信息列表*/
      lawBaseInfo() {
       //this.loading = true;
        getBaseLawList(this.queryParams).then(
          response => {
            this.tableList = response.rows;
            this.total = response.total;
            //this.loading = false;
          }
        );
      },
      /** 搜索按钮操作*/
      handleQuery(params) {
        this.queryParams={
          ...this.queryParams,
          ...params,
          pageNum: 1,
        }
        this.lawBaseInfo();
      },
      /**重置按钮操作*/
      resetQuery() {
        this.queryParams = {
          pageNum: 1,
          pageSize: 10,
        };
        this.lawBaseInfo();
      },
      /**打开新增编辑框*/
      openAdd(row) {
        this.dialogVisible = !this.dialogVisible;
        if(row){//编辑
          this.id = row.id;
        }else{//新增
          this.id = null;
        }
      },
      /* 提交数据 */
      subLaw(row){
        this.$confirm('确认提交该条规章制度？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          const params = {
            ...row,
            commitFlag: "1",
          };
          saveBaseLaw(params).then(
            response => {
              if(response.code === 200){
                this.$message({
                  message: response.msg,
                  type: 'success'
                });
                this.lawBaseInfo();
              }else{
                this.$message.error(response.msg);
              }
            }
          );
        })
      },
      /* 删除行 */
      delLaw(row){
        this.$confirm('确认删除该条规章制度？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          const params = {
            id: row.id,
            uniqueCode: row.uniqueCode,
          };
          delBaseLaw(params).then(
            response => {
              if(response.code === 200){
                this.$message({
                  message: response.msg,
                  type: 'success'
                });
                this.lawBaseInfo();
              }else{
                this.$message.error(response.msg);
              }
            }
          );
        })
      },
      /**关闭模态框*/
      closeModal(){
        this.dialogVisible = !this.dialogVisible;
        this.lawBaseInfo();
      },
      /*日期处理*/
      dateFormat:function(row,column){
        var date = row[column.property];
        if(date === undefined){
          return ''
        }
        return moment(date).format("YYYY-MM-DD")
      },
      // 内容数据转换
      formatText(row){
          return row.commitFlag == 1 ? "已提交" : "未提交";
      },
    }
  };
</script>
<style rel="stylesheet/scss" lang="scss">
</style>







