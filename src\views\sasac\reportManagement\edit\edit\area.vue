<!--企业基本信息-->
<template>
  <el-container>
    <el-dialog  :visible.sync="visible" width="90%" append-to-body :modal-append-to-body="false" @close="close" title="企业基本信息">
      <el-main>
        <div style=" margin-bottom: 20px;">
          <span style="color: #589DDE; float: right; margin-left: 10px;" v-show="dataDetails.commitFlag=='1'">【已提交】</span>
          <span style="color: #F5222D; float: right; margin-left: 10px;" v-show="dataDetails.commitFlag=='0'">【待提交】</span>
          <span style="float: right; background: #FEF6F6;"><i class="el-icon-info" style="color: #FBA651; margin-right: 8px"></i>温馨提示：状态为已提交时，其他人员才能查询到</span>
        </div>
        <el-form ref="dataDetails" :model="dataDetails" label-width="120px" style="padding: 20px 300px; ">
          <el-form-item label="企业名称">
            <el-input v-model="dataDetails.involOrgName"></el-input>
          </el-form-item>
          <el-form-item label="企业简称">
            <el-input v-model="dataDetails.involOrgNameBak"></el-input>
          </el-form-item>
          <el-form-item label="社会信用代码">
            <el-input v-model="dataDetails.socialCreditCode"></el-input>
          </el-form-item>
          <el-form-item label="所属行业名称">
            <el-input v-model="dataDetails.industryName"></el-input>
          </el-form-item>
          <el-form-item label="行业代码">
            <el-input v-model="dataDetails.industryCode"></el-input>
          </el-form-item>
        </el-form>
      </el-main>
      <div slot="footer">
          <el-button size="mini" @click="saveAreaBaseInfo(0)">保存</el-button>
          <el-button size="mini" type="primary" @click="saveAreaBaseInfo(1)">提交</el-button>
      </div>
    </el-dialog>
  </el-container>
</template>

<script lang="ts">
  import {getAreaBaseInfoOne, addAreaBaseInfo } from "@/api/base/area";

  export default {
    name: "area",
    data() {
      return {
        dataDetails: {},
        visible:false,
        commitFlag: 0,
      };
    },
    created() {
    },
    methods: {
      /**查询企业基本信息详情*/
      onShow() {
        this.visible = true;
        //this.loading = true;
        getAreaBaseInfoOne().then(
          response => {
            if(response.code == 200 && response.data){
              this.dataDetails = response.data;
            }
          }
        );
      },
      //关闭弹框
      close(){
        this.visible = false;
        this.$emit('editClose', this.commitFlag);
        this.commitFlag = 0;
      },
      /**保存或提交*/
      saveAreaBaseInfo(commitFlag){
        this.commitFlag = commitFlag;
        const params = {
          ...this.dataDetails,
          commitFlag
        };
        addAreaBaseInfo(params).then(
          response => {
            if(response.code === 200){
              this.$message({
                message: response.msg,
                type: 'success'
              });
              if(commitFlag){
               this.close();
              }
            }else{
              this.$message.error(response.msg);
            }
          }
        );
      }
    }
  };
</script>

<style>
  .text-red {
    color: #f5222d;
  }

  .icon-orange {
    color: #fa8b16;
  }
</style>
