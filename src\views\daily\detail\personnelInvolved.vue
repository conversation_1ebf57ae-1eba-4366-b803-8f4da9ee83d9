<!--涉及人员列表-->
<template>
  <div class="app-container app-report" style="height: 100%">
    <el-form  style="height: calc(100% - 25px)">
      <el-table :data="tableData" ref="table"   height="100%">
        <el-table-column
          label="序号"
          type="index"
          width="60"
          align="center"
          fixed
        />
        <el-table-column
          label="涉及人员"
          prop="userName"
          show-overflow-tooltip
          width="90"
          align="center"
          fixed
        />
        <el-table-column
          label="部门"
          prop="involOrgName"
          show-overflow-tooltip
          width="200"
          align="center"
          fixed
        />

        <el-table-column
          label="职务"
          prop="postName"
          show-overflow-tooltip
          width="150"
          align="center"
          fixed
        />

        <el-table-column
          label="追责总人次"
          prop="accountabilitySum"
          width="100"
          align="center"
          fixed
        />

        <el-table-column
          align="center"
          prop="enddate"
          label="处理方式"
          min-width="1100"
        >
          <template slot-scope="scope">
            <el-checkbox-group
              class="checkbox-group-specLists"
              v-if="scope.row.userName !== '合计'"
              :key="scope.row.specLists"
              v-model="scope.row.specLists"
              size="medium"
              disabled="true"
            >
              <el-checkbox
                v-for="item in scope.row.detailWidth"
                :key="item.dictValue"
                border
                :label="item.dictValue"
              >{{ item.dictLabel }}</el-checkbox>
            </el-checkbox-group>


            <el-checkbox-group
              class="checkbox-group-specLists checkbox-group-processingOther"
              v-if="scope.row.userName !== '合计'"
              :key="scope.row.processingOther"
              v-model="scope.row.processingOther"
              size="medium"
              disabled="true"
            >
              <el-checkbox
                v-for="dict in dict.type.other_handler_way"
                :key="dict.value"
                border
                :label="dict.value"
              >{{ dict.label }}</el-checkbox>
            </el-checkbox-group>
          </template>
        </el-table-column>

        <el-table-column
          :show-overflow-tooltip="true"
          align="center"
          prop="enddate"
          label="扣减金额（万元）"
          width="250"
        >
          <template
            slot-scope="scope"
          >
            <span>{{ scope.row.deductionSalary }}</span>
          </template>
        </el-table-column>

        <el-table-column
          label="组织处理+扣减薪酬"
          type="handleDeductionCount"
          width="180"
          align="center"
        >

          <template
            slot-scope="scope"
          >
            <span>{{ scope.row.userName == '合计'?scope.row.handleDeductionCount:scope.row.handleDeductionCount=='1'?'是':'否' }}</span>
          </template>
        </el-table-column>

        <el-table-column
          label="政务处分+扣减薪酬"
          type="governmentDeductionCount"
          width="180"
          align="center"
        >

          <template
            slot-scope="scope"
          >
            <span>{{ scope.row.userName == '合计'?scope.row.governmentDeductionCount:scope.row.governmentDeductionCount=='1'?'是':'否' }}</span>
          </template>
        </el-table-column>

        <el-table-column
          label="党纪处分+扣减薪酬"
          type="partyDeductionCount"
          width="180"
          align="center"
        >
          <template
            slot-scope="scope"
          >
            <span>{{ scope.row.userName == '合计'?scope.row.partyDeductionCount:scope.row.partyDeductionCount=='1'?'是':'否' }}</span>
          </template>
        </el-table-column>
      </el-table>
    </el-form>
  </div>
</template>

<script>
import {queryInvolPersonListByProblemId} from '@/api/daily/process/handlingAppealRecords'

export default {
  name: "personnelInvolved",
  dicts: ['other_handler_way'],
  data() {
    return {
      loading:false,
      // 显示搜索条件
      showSearch: false,
      // 总条数
      total: 0,
      // 表格数据
      tableData: [],
      //新增主键
      queryParams: {
        dailyProblemId:''
      },
    };
  },
  created() {
  },
  activated() {
    this.$nextTick(() => {
      this.$refs.table.doLayout(); //解决表格错位
    });
  },
  methods: {
    /**查询*/
    queryInvolPersonList() {
      this.loading = true;
      queryInvolPersonListByProblemId(this.queryParams).then((response) => {
        const { code, data } = response
        if (code === 200) {
          this.loading = false;
          if (data.length > 0) {
            data.forEach((item) => {
              if (item.userName !== '合计') {
                item.specLists = []
                if (item.orgHandleFlag == '1') {
                  item.specLists.push('1')
                }
                if (item.deductionSalaryFlag == '1') {
                  item.specLists.push('2')
                }
                if (item.prohibitFlag == '1') {
                  item.specLists.push('3')
                }
                if (item.governmentPunishmentFlag == '1') {
                  item.specLists.push('4')
                }
                if (item.partyPunishmentFlag == '1') {
                  item.specLists.push('5')
                }
                if (item.transferAuthorityFlag == '1') {
                  item.specLists.push('6')
                }
                item.detailWidth = [
                  {
                    dictValue: '1',
                    dictLabel: '组织处理'
                  },
                  {
                    dictValue: '2',
                    dictLabel: '扣减薪酬'
                  },
                  {
                    dictValue: '3',
                    dictLabel: '禁入限制'
                  },
                  {
                    dictValue: '4',
                    dictLabel: '政务处分'
                  },
                  {
                    dictValue: '5',
                    dictLabel: '党纪处分'
                  },
                  {
                    dictValue: '6',
                    dictLabel: '移送监察/司法机关'
                  }
                ]
              }
            })
          }
          this.tableData = data
          this.$nextTick(() => {
            this.$refs.table.doLayout(); //解决表格错位
          });
        }
      })
    },
    /**调用查询*/
    queryList(id) {
      this.queryParams = {
        dailyProblemId:id
      };
      this.queryInvolPersonList();
    },

  }
};
</script>
<style>
.checkbox-group-specLists{
  display: inline-block;
}
.checkbox-group-processingOther{
  margin-left:30px;
}
</style>




