<template>
  <div>
    <el-scrollbar style="height:100%">
      <div class="history-box">
        <ul class="history-ul">
          <li :class="index==0?'history-li history-first':'history-li'" v-for="(item,index) in activities">
            <div class="history-top">
              <span class="history-top-type">{{item.linkName}}</span>
              <span class="history-top-title" :title="item.comment">处理意见：{{item.comment}}</span>
            </div>
            <div class="history-bottom">
              <el-row>
                <el-col :span="8">
                  <div class="history-bottom-left">
                    <div class="history-img-1 float-left">
                      <img src="@/assets/images/head.png">
                    </div>
                    <div class="history-left-content float-left">
                      <div class="history-per text-red">{{item.performerName}}（{{item.handleTypeName}}）</div>
                      <div class="history-type">{{item.deptName}}</div>
                    </div>
                  </div>
                </el-col>
                <el-col :span="8" class="text-center">
                  <div class="history-bottom-center">
                    <div class="history-center-time">接受时间：{{item.senderDateStr}}</div>
                    <div class="history-center-time">办理时间：{{item.completedDateStr}}</div>
                  </div>
                </el-col>
                <el-col :span="8">
                  <div class="history-bottom-right">
                    <div class="history-card">
                      <div class="history-right-icon">
                        <img src="@/assets/images/time.png">
                      </div>
                      <div class="history-right-time" v-if="item.handTime!=null&&item.handTime!='null'">
                        {{(item.handTime/60/60).toFixed(2)}}小时/{{(item.handTime/60/60/24).toFixed(2)}}天
                      </div>
                      <div class="history-right-time" v-else>
                        {{(0/60/60).toFixed(2)}}小时/{{(0/60/60/24).toFixed(2)}}天
                      </div>
                    </div>
                  </div>
                </el-col>
              </el-row>
            </div>
          </li>
        </ul>
      </div>
    </el-scrollbar>
  </div>
</template>

<script>
  export default {
    name: "history",
    props: {
      activities: {
        type: Array
      },
    }
  }
</script>

<style scoped lang="scss">
  .history-box {
    .history-ul:before {
      content: "";
      display: block;
      width: 1px;
      height: calc(100% - 40px);
      border-left: 2px solid #C6CCD1;
      position: absolute;
      margin: auto;
      left: 23px;
      top: 32px;
    }
    .history-ul {
      position: relative;
      padding: 10px 20px 10px 50px;
      box-sizing: border-box;
      .history-li {
        margin-bottom:20px;
        position: relative;
        width: 100%;
        padding: 10px 16px;
        background: #F6F7FC;
        .history-top {
          .history-top-type {
            padding: 4px 16px;
            display: inline-block;
            background: #F6747B;
            border-radius: 2px;
            font-size: 14px;
            font-weight: 400;
            color: #FFFFFF;
          }
          .history-top-title {
            padding-left: 10px
          }
        }
        .history-bottom {
          margin-top: 10px;
          .history-bottom-left {
            .history-img-1 {
              margin-right: 10px;
              img {
              }
            }
            .history-left-content {
              .history-per {
                line-height: 26px;
              }
              .history-type {
                line-height: 26px;
              }
            }
          }
          .history-bottom-center {
            width: 210px;
            display: inline-block;
            text-align: left;
            .history-center-time {
              line-height: 26px;
            }
          }
          .history-bottom-right {
            text-align: right;
            .history-card {
              width: 140px;
              display: inline-block;
              text-align: center;
              .history-right-icon {

              }
              .history-right-time {
                line-height: 26px;
              }
            }

          }
        }
      }
      .history-li:before {
        content: "";
        display: block;
        width: 16px;
        height: 16px;
        background: #fff;
        border: 2px solid #F5222D;
        border-radius: 50%;
        opacity: 1;
        position: absolute;
        margin: auto;
        left: -34px;
        top: 16px;
        z-index: 9999;
      }
      .history-li.history-first:after {
        content: "";
        display: block;
        width: 8px;
        height: 8px;
        background: #f5222d;
        border-radius: 50%;
        opacity: 1;
        position: absolute;
        margin: auto;
        left: -30px;
        top: 20px;
        z-index: 9999;
      }
    }
  }
</style>
