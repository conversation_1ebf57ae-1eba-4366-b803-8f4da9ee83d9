import request from '@/utils/request'

// 查询页面信息
export function queryVerifyRecord(problemId) {
  return request({
    url: '/colligate/violationVerify/findViolationVerifyRecord/'+problemId,
    method: 'post'
  })
}

// 保存
export function saveViewVerrify(data) {
  return request({
    url: '/colligate/violationVerify/temporarySaveVerifyRecord',
    method: 'post',
    data:data
  })
}

// 提交前的校验
export function saveViolationVerifyRecord(data) {
  return request({
    url: '/colligate/violationVerify/saveViolationVerifyRecord',
    method: 'post',
    data:data
  })
}



