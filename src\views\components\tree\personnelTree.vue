<template>
    <div>
        <!--<el-form :model="query" ref="queryForm"  :inline="true" label-width="75px">-->
          <!--<el-form-item label="人员姓名">-->
            <!--<el-input v-model="query.selectName" placeholder="人员姓名"  :style="{width: '100%'}">-->
            <!--</el-input>-->
          <!--</el-form-item>-->
          <!--<el-form-item>-->
            <!--<el-button type="primary" icon="el-icon-search" size="mini" @click="treeQuery">搜索</el-button>-->
          <!--</el-form-item>-->
        <!--</el-form>-->
      <el-row>
        <el-col :span="16" class="tree-box">
          <el-tree ref="tree"
                   :data="data"
                   lazy
                   show-checkbox
                   :default-checked-keys="defaultTree"
                   node-key="id"
                   check-strictly
                   @check-change="checkChange"
                   :load="loadnode"
                   :props="defaultProps"
                   @node-click="nodeclick">
          </el-tree>
        </el-col>
        <el-col :span="8" class="tree-box">
          <TreeSelect
          :selectTree="selectTree"
          @noCheck="noCheck"
          >
          </TreeSelect>
        </el-col>
      </el-row>
    </div>

</template>

<script>
  import {userTree} from "@/api/components/index";
  import TreeSelect from '@/components/TreeSelect';

  export default {
    components: {
      TreeSelect
    },
    data() {
      return {
        data:[],
        selectTree:[
          {id: "227",name: "刘晓明",pid: "00692804686"},
          {id: "266",name: "刘霞凤",pid: "00692804686"},
          {id: "345",name: "马亮",pid: "00692804686"}
        ],
        defaultTree:['266','227','345'],
        query:{
          name:'',
          areaCode:'',
          isParent:'',
          provCode:'',
          checked:'',
          id:'',
          pId:'',
          isAll:false,
          open:false,
          nocheck:'',
          userId:'',
          selectName:'',
        },
        defaultProps: {//树对象属性对应关系
          children: 'children',
          label: 'name',
          isLeaf:function(data, node){
            return !data.isParent
          },
          disabled:function(data, node){
            return data.isParent
          }
        }
      }
    },
    methods: {
      //查询人员姓名
      treeQuery(){
        userTree(this.query).then(
          response => {
             this.data =response.data
          }
        );
      },
      loadnode(node,resolve){
        //如果展开第一级节点，从后台加载一级节点列表
        if(node.level==0)
        {
          this.loadfirstnode(resolve);
        }
        //如果展开其他级节点，动态从后台加载下一级节点列表
        if(node.level>=1)
        {
          this.loadchildnode(node,resolve);
        }
      },
      //加载第一级节点
      loadfirstnode(resolve){
        userTree(this.query).then(
          response => {
            resolve(response.data);
          }
        );
      },
      //加载节点的子节点集合
      loadchildnode(node,resolve){
        userTree(node.data).then(
          response => {
            resolve(response.data);
          }
        );
      },
      //点击节点上触发的事件，传递三个参数，数据对象使用第一个参数
      nodeclick(data,dataObj,self)
      {},
      //修改状态
      checkChange(node,type){
        if(type){//选中
          this.selectTree.push(node);
          this.defaultTree.push(node.id);
        }else{
          this.selectTree.splice(this.selectTree.findIndex(item => item.id === node.id), 1);
          this.defaultTree.splice(this.defaultTree.findIndex(item => item === node.id), 1)
        }
      },
      //删除某节点
      noCheck(item){
          this.$refs.tree.setChecked(item.id,false);
      }
    }
  }
</script>
<style  rel="stylesheet/scss" scoped lang="scss">
  .is-disabled{
    display: none !important;
  }
  .tree-box{
    height: calc(100vh - 400px);
    overflow: auto;

  }
</style>
