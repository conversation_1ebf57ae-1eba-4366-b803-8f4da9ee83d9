<template>
  <div>
    <div class="vio-file-box">
      <el-row class="vio-file-div" v-for="item in fileList">
        <el-col :span="4" class="vio-file-type flex vio-file-border">
          <span class="text-red" v-show="edit&&item.fileTypeCode!=='0'">*</span>
          <span>{{item.fileTypeName}}</span>
        </el-col>
        <el-col :span="edit?4:0" class="vio-file-download flex vio-file-border">
          <div class="mr5">
            <el-button  size="mini" type="primary" v-if="item.fileTemplate&&edit" @click="handleDownload(item.fileTemplate)"  title="下载" plain>模板下载</el-button>
          </div>
          <FileUpload
            v-if="edit"
            :isShowTip=showTip
            :fileUrl="uploadUrl"
            btnTitle="上传附件"
            :param='{
                    "reportSasacId":problemId,
                    "reportBusinessId":id,
                    "uploadType":item.fileTypeCode
                     }'
            @handleUploadSuccess="handleUploadSuccess"
          >
            文件上传
          </FileUpload>
        </el-col>
        <el-col :span="edit?16:20" class="vio-file-content">
          <ul class="vio-file-list">
            <el-row class="vio-file-li ry-row flex" v-for="obj in item.businessAttachments">
              <el-col :span="12"  class="vio-file-name">
                <i class="el-icon-tickets"></i>
                <span>{{obj.attachmentName}}</span>
              </el-col>
              <el-col :span="8" class="vio-file-user icon-grey">
                <span>{{obj.uploaderName}}</span>
              </el-col>
              <el-col :span="4" class="vio-file-del layui-col-md2 layui-col-sm2 text-center">
                <a href="javascript:void(0);" @click="fileDownload(obj)" class="table-btn tip-edit" title="下载">
                  <i class="el-icon-bottom"></i>
                </a>
                <!--<a href="javascript:void(0);" class="table-btn tip-edit" title="预览" @click="openPreView('6cfe8d7314ba4929adb6cf7eabbcaa70','logo_看图王.png')">-->
                <!--<i class="el-icon-view"></i>-->
                <!--</a>-->
                <a href="javascript:void(0);" class="table-btn tip-edit" v-if="edit" title="删除" @click="delFile(obj.id, obj.attachmentUuid)">
                  <i class="el-icon-delete"></i>
                </a>
              </el-col>
            </el-row>
          </ul>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
  import {regularReportData, downloadFilledDataTemplate, deleteRegularPartUploadedFile} from "@/api/sasac/reportManagement/edit/detail/index";

  export default {
    name: "periodic",
    components: {
    },
    props: {
      problemId:{
        type: String
      },
      edit: {
        type: Boolean,
        default:false
      },
      fileList: {
        type: Array
      },
      id: {
        type: String
      },
    },
    data(){
      return{
        type:false,
        status:'',
        showTip:false,
        uploadUrl: '/colligate/violSasacRegularReport/uploadRegularAttachment',
        downloadUrl:'/jtauditwo/files/downLoad/',//下载地址
      }
    },
    methods:{
      /** 获取数据*/
      ViolationFileItems() {
        regularReportData(this.problemId).then(response => {
          this.fileList = response.data.regularAttachmentItems;
        });
      },

      /** 删除操作 */
      delFile(id, attachmentUuid) {
        let  title = '确认删除该附件吗？';
        this.$modal.confirm(title).then(() => {
          deleteRegularPartUploadedFile({id: id, attachmentUuid: attachmentUuid}).then(response => {
            if (200 === response.code) {
              this.$modal.msgSuccess("删除成功");
              this.ViolationFileItems();
            } else {
              this.$modal.alertError(response.msg);
            }
          });
        }).catch(() => {});
      },
      // 上传成功回调
      handleUploadSuccess(res, file) {
        this.ViolationFileItems();
      },
      /**下载模板*/
      handleDownload(fileTemplate){
        this.$emit('fileDown', {fileTemplate: fileTemplate});
      },
      /**下载文件*/
      fileDownload(attachment){
        this.download('/sys/attachment/downloadSysAttachment/' + attachment.attachmentUuid, {}, attachment.attachmentName);
      },
    }
  }
</script>

<style scoped lang="scss">
  .flex{
    display: flex;
    align-items: center;
  }
  .vio-file-box{
    border: 1px solid #d9d9d9;
    .vio-file-div{
      display: flex;
      width: 100%;
      border-bottom: 1px solid #d9d9d9;
      .vio-file-border{
        border-right: 1px solid #d9d9d9;
      }
      .vio-file-type{
        background-color: #F4F8FC;
        color: #73777a;
        min-height: 48px;
        padding: 0 10px;
        box-sizing: border-box;
        .text-red{
          color: #f5222d !important;
        }
      }
      .vio-file-download{
        justify-content: center;
        .vio-file-down{
          padding: 0 4px;
          border-right: 1px solid #d9d9d9;
        }
        i{
          color: #f5222d;
        }
        .vio-file-down:last-child{
          border-right-width: 0;
        }
      }

      .vio-file-content{
        min-height: 48px;
        .vio-file-list{
          padding:0;
          margin:0;
          .vio-file-li{
            padding-left: 10px;
            box-sizing: border-box;
            border-bottom: 1px solid #d9d9d9;
            min-height: 48px;
            .vio-file-name{
              i{
                margin-right:6px;
              }
            }
            .vio-file-user,.vio-file-time{
              height: 48px;
              display: flex;
              align-items: center;
              color: #a9b0b4;
            }
            .vio-file-del{
              text-align: center;
              i{
                color:#f5222d;
                margin:0 6px;
              }
            }
          }
          .vio-file-li:last-child {
            border-bottom-width: 0;
          }
        }
      }
    }
    .vio-file-div:last-child {
      border-bottom-width: 0;
    }
    ::v-deep.upload-file-uploader{
      margin-bottom: 0;
    }
  }
</style>
