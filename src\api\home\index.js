import request from '@/utils/request'

// 监督追责问题总览
export function violationProblemOverviewData(data) {
  return request({
    url: '/colligate/violLeaderHomepage/violationProblemOverviewData',
    method: 'post',
    data: data
  })
}

// 问题分布统计
export function violationLeaderHomePage(data, url) {
  return request({
    url: '/colligate/violLeaderHomepage/' + url,
    method: 'post',
    data: data
  })
}

// 涉及单位统计（31省）-中国地图
export function chinaMapStatisticsData(data) {
  return request({
    url: '/colligate/violLeaderHomepage/chinaMapStatisticsData',
    method: 'post',
    data: data
  })
}

// 31省分统计
export function provinceTotalData(data) {
  return request({
    url: '/colligate/violLeaderHomepage/provinceTotalData',
    method: 'post',
    data: data
  })
}

// 年度趋势变化
export function yearTrendChangeData(data) {
  return request({
    url: '/colligate/violLeaderHomepage/yearTrendChangeData',
    method: 'post',
    data: data
  })
}

// 涉及单位统计子公司

export function involveSubsidiaryStatisticsData(data) {
  return request({
    url: '/colligate/violLeaderHomepage/involveSubsidiaryStatisticsData',
    method: 'post',
    data: data
  })
}

// 我的工作台

export function userTaskCount(data) {
  return request({
    url: '/colligate/violLeaderHomepage/queryQuantity',
    method: 'post',
    data: data
  })
}

// 超时提醒
export function selectWarnData(data) {
  return request({
    url: '/colligate/violLeaderHomepage/selectWarnData',
    method: 'post',
    data: data
  })
}

// 判断是否领导还是个人

export function queryHomeRole() {
  return request({
    url: '/colligate/violLeaderHomepage/queryHomeRole',
    method: 'post'
  })
}

// 判断是否为子公司

export function querySubsidiary() {
  return request({
    url: '/colligate/violLeaderHomepage/querySubsidiary',
    method: 'post'
  })
}
