import request from '@/utils/request'

// 查询页面信息
export function queryVerifyRecord(problemId) {
  return request({
    url: '/colligate/violationDisposal/findViolationDisposalRecord/'+problemId,
    method: 'post'
  })
}

// 保存
export function saveViewVerrify(data) {
  return request({
    url: '/colligate/violationDisposal/temporarySaveViolationDisposalRecord',
    method: 'post',
    data:data
  })
}

// 提交前的校验
export function saveViolationVerifyRecord(data) {
  return request({
    url: '/colligate/violationDisposal/saveViolationDisposalRecord',
    method: 'post',
    data:data
  })
}

// 保存移交二级单位记录
export function saveHandoverSecondaryRecord(data) {
  return request({
    url: '/colligate/violationDisposal/saveHandoverSecondaryRecord',
    method: 'post',
    data:data
  })
}

// 保存移交纪检部门记录
export function saveHandoverInspectionRecord(data) {
  return request({
    url: '/colligate/violationDisposal/saveHandoverInspectionRecord',
    method: 'post',
    data:data
  })
}


// 删除交二级单位记录
export function deleteDailyHandoverRecord(id) {
  return request({
    url: '/colligate/violationDisposal/deleteDailyHandoverRecord/'+id,
    method: 'post'
  })
}


