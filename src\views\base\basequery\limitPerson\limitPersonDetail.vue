<template>
    <el-dialog
        title="禁入限制人员详情"
        :visible.sync="dialogVisible"
        width="70%"
        :before-close="handleClose">
      <el-form class="common-card padding10_0" size="medium"  label-width="130px">
        <el-row>
          <el-col :span="8">
            <el-form-item label="禁入人姓名"><span>{{dataDetails.userName}}</span></el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="性别"><span>{{dataDetails.sexFlag}}</span></el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="身份证号"><span>{{dataDetails.idCard}}</span></el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="所在企业"><span>{{dataDetails.involAreaName}}</span></el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="企业层级"><span>{{dataDetails.orgGradeName}}</span></el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="处理前职位"><span>{{dataDetails.postName}}</span></el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="干部类别"><span>{{dataDetails.cadreCategory}}</span></el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="禁入限制期间开始"><span>{{dateFormat(dataDetails.limitStartTime)}}</span></el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="禁入限制期间结束"><span>{{dateFormat(dataDetails.limitEndTime)}}</span></el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="责任追究处理部门"><span>{{dataDetails.accountabDepartment}}</span></el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="责任处理联系人"><span>{{dataDetails.contactsName}}</span></el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="责任处理联系方式"><span>{{dataDetails.contactInformation}}</span></el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="工作简历"><span>{{dataDetails.workResume}}</span></el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="违规问题"><span>{{dataDetails.violationsProblem}}</span></el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="不良后果"><span>{{dataDetails.adverseConsequences}}</span></el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="责任认定情况"><span>{{dataDetails.responIdenty}}</span></el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="责任追究处理情况"><span>{{dataDetails.responIdenty}}</span></el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="备注"><span>{{dataDetails.remark}}</span></el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <BlockCard
        title="附件列表"
      >
        <el-table
          border
          :data="dataDetails.files"
          ref="table2"
          :show-header="false"
          :cell-class-name="rowClass"
        >
          <el-table-column
            fixed
            align="center"
            label="序号"
            type="index"
            min-width="10%">
          </el-table-column>
          <el-table-column label="文档名称" prop="fileName" min-width="60%"/>
          <el-table-column label="上传人" prop="createLoginName" min-width="15%"/>
          <el-table-column label="上传时间" prop="time" :formatter="dateFormat" min-width="13%"/>
          <el-table-column label="操作" prop="del" min-width="15%" fixed="right"
                           align="center"
          >
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                icon="el-icon-bottom"
                title="下载"
                @click="downloadFile(scope.row)"
              >
              </el-button>
              <!-- <el-button
                size="mini"
                type="text"
                icon="el-icon-search"
              >预览
              </el-button> -->
            </template>
          </el-table-column>
        </el-table>
      </BlockCard>
    </el-dialog>
</template>

<script lang="ts">
  import {queryLimitedPersonInfoById} from "@/api/base/limitPerson";
  import moment from "moment";
  import BlockCard from '@/components/BlockCard';

  export default {
    components:{BlockCard},
    name: "limitPersonDetail",
    props: {
      dialogVisible: {
        type: Boolean,
        default: true
      },
      id: {
        type: String,
        default: ''
      },
    },
    data() {
      return {
        dataDetails: {},
      };
    },
    created() {
      this.limitPersonInfo();
    },
    methods: {
      /**查询企业基本信息详情*/
      limitPersonInfo() {
       //this.loading = true;
        queryLimitedPersonInfoById({id: this.id}).then(
          response => {
            this.dataDetails = response.data;
            //this.loading = false;
          }
        );
      },
      /** 修改附件表样式 */
      rowClass ({ row, column, rowIndex, columnIndex }) {
        if (columnIndex === 1 || columnIndex === 2) {
          return 'no-right-border'
        }else if(columnIndex === 0){
          return 'cell-color'
        }
      },
      /**关闭模态框*/
      handleClose() {
        this.$emit("closeModal");
      },
      /*日期处理*/
      dateFormat:function(date){
        if(date === undefined){
          return ''
        }
        return moment(date).format("YYYY-MM-DD")
      },
      /** 下载附件 */
      downloadFile(row) {
        this.download('/sys/documentTemplate/downloadByAttachmentId', { id: row.id }, row.fileName)
      },
    }
  };
</script>
