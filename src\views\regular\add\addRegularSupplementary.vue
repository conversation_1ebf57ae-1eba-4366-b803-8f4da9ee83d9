<!--补录定期上报-->
<template>
  <div>
    <el-dialog  :visible.sync="visible"  :modal-append-to-body="false" append-to-body title="补录上报单位" width="90%">
      <el-form ref="elForm" :model="formData" :rules="rules" size="medium" label-width="118px">
        <el-col :span="6">
          <el-form-item label="报告类型" prop="reportType">
           <span>{{formData.reportType==='1'?'定期上报':'其他上报'}}</span>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="上报年度" prop="reportYear">
            <span>{{formData.reportYear}}</span>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="上报区间" prop="reportTime">
            <span>{{formData.reportStartTime}}  -  {{formData.reportEndTime}}</span>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="上报截止日期" prop="reportCloseTime">
            <span>{{formData.reportCloseTime}}</span>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="标题" prop="reportTitle">
            <span>{{formData.reportTitle}}</span>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="上报要求" prop="reportRequire">
            <span>{{formData.reportRequire}}</span>
          </el-form-item>
        </el-col>
        <!--新增上报单位-->
        <el-col :span="24">
          <ReportDepartment
            v-if="showDepartment"
            :key="index"
            :edit="true"
            :regularReportId="regularReportId"
            v-on:toSetUnitList = "toSetUnitList"
            ref="reportDepartment"
          ></ReportDepartment>
        </el-col>
      </el-form>
      <div slot="footer">
        <el-button size="mini" type="primary" @click="submitForm">提交</el-button>
        <el-button size="mini" @click="onClose">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import ReportDepartment from "./reportDepartmentSup";//上报单位
  import Process from "@/components/Process/regular";//流程按钮
  import {
    selectReportInfo//编辑加载数据
    ,saveRegularSupplementary
    ,checkReportData
    ,submitRegularReport} from "@/api/regular/add/addAndEditRegular";//新增js
  export default {
    inheritAttrs: false,
    name: "addAndEditRegular"
    ,components: {
      ReportDepartment
      ,Process
    }
    ,props: {
      regularReportId:{
        type:String,
        default:''
      }
    },
    data() {
      return {
        edit:true,
        visible:false,//弹框
        formData:{
          id:''
          ,reportType:undefined//报告类型 1:定期  2：其他
          ,reportYear:undefined//上报年度
          ,reportTime:[]//上报区间
          ,reportStartTime:undefined//上报区间-开始
          ,reportEndTime:undefined//上报区间-结束
          ,reportCloseTime:undefined//上报截止日期
          ,reportTitle:undefined//标题
          ,reportRequire:undefined//上报要求
          ,reportProvCode:undefined
          ,reportProvName:undefined
          ,orgGrade:undefined
        }
        ,showDepartment:false//上报单位是否加载
        ,unitList:[]//上报单位
        ,reportType:''//1：定期 2：其他
        ,reportYear:''//上报年度
      }
    }
    ,created(){
    }
    ,methods:{
      // 显示弹框
      show() {
        this.visible = true;
        this.GetRegularReport();
      },
      //直接关闭
      onClose(){
        this.$modal.confirm('关闭后将无法发起上报流程，确认是否关闭？').then(()=> {
            this.visible = false;
          }
        );
      },
      //关闭弹窗
      close(){
        this.visible = false;
        this.$emit("list");
        this.$refs.reportDepartment.GetunitList();
        this.$refs.reportDepartment.SelectReportDetailList();
      },
      //初始化数据
      GetRegularReport(){
        selectReportInfo(this.regularReportId).then(
          response => {
            this.formData = response.data;
            this.reportType = this.formData.reportType;
            this.$nextTick(()=>{
              //加载上报单位
              this.showDepartment = true;
            })
          }
        )
      },
      //获取上报单位
      toSetUnitList(data){
        this.unitList = data;
      },
      //提交
      submitForm() {
         saveRegularSupplementary(this.regularReportId,this.unitList).then(
          response => {
            if (response.code == "200") {
              this.toCheckReportData();
            } else {
              this.$modal.alert(res.msg);
            }
          }
        )
      },
      //校验
      toCheckReportData(){
        if (!this.formData.reportType) {
          this.$modal.msgError("请选择【报告类型】！");
          return false;
        } else if (!this.formData.reportYear) {
          this.$modal.msgError("请选择【上报年度】！");
          return false;
        } else if (!this.formData.reportStartTime) {
          this.$modal.msgError("请选择【上报区间】！");
          return false;
        } else if (!this.formData.reportCloseTime) {
          this.$modal.msgError("请选择【上报截止日期】！");
          return false;
        } else if (!this.formData.reportTitle) {
          this.$modal.msgError("【标题】不能为空！");
          return false;
        } else if (!this.formData.reportRequire) {
          this.$modal.msgError("【上报要求】不能为空！");
          return false;
        } else if (this.unitList.length === 0) {
          this.$modal.msgError("请选择【上报单位】！");
          return false;
        }
        //后端校验
        checkReportData(this.regularReportId).then(
          response => {
            if (response.code == "200") {
              if (response.data.codeFlag) {
                this.todoSubmit();
              } else {
                this.$modal.msgError(response.data.codeText);
              }
            } else {
              this.$modal.msgError(response.msg);
            }
          }
        )
      },
      //调用提交
      todoSubmit(){
        this.$modal.confirm('是否确认提交上报？').then(
          ()=> {
            const loading = this.$loading({
              spinner: 'el-icon-loading', // 自定义加载图标类名
              text: '正在加载...', // 显示在加载图标下方的加载文案
              lock: false, // lock的修改符--默认是false
            });
            submitRegularReport(this.regularReportId).then(
              response => {
                loading.close();
                if(response.code === 200){
                  this.$modal.msgSuccess(response.msg);
                  this.close();
                }else {
                  this.$modal.msgError(response.msg);
                }
              }
            ).catch(err => {
              loading.close();
            })
          }
        )
      },
    }
  }
</script>

<style scoped>

</style>
