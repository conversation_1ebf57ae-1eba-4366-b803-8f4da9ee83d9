<template>
  <el-row class="height">
    <el-col :span="24" class="tree-box">
      <div class="unit-list">
        <el-form ref="elForm" size="medium" label-width="0">
          <el-checkbox-group class="unit-list" v-model="data" @change="DeptChange" size="medium">
            <el-checkbox  v-for="item in Dpids" checked :key="item.involOrgId" border :label="item.involOrgId" :value="item.involOrgId"
            >{{item.involOrgName}}
            </el-checkbox>
          </el-checkbox-group>
        </el-form>
      </div>
      <div class="flex border-top">
        <el-input
          v-model="name"
          placeholder="请输入姓名"
          clearable
          size="small"
          style="width: 200px;margin-right:10px;"
        />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="QueryPersonInvolved">搜索</el-button>

        <!--<p>当前部门人员共{{resultList.length}}人(已选 <span>{{selectTree.length}}</span> 人)</p>-->
      </div>
      <div class="persList">
        <div :class="item.checked?'com-checkbox checked':'com-checkbox'" @click="persChange(item,index)"  v-for="(item,index) in resultList">
          <span class="com-con">{{item.name}}</span>
        </div>
      </div>
    </el-col>
    <el-col :span="24" class="tree-bottom">
      <div class="flex border-top">
        <p>已选人员</p>
        <p>已选<span>{{selectTree.length}}</span>个</p>
      </div>
      <TreeSelect
        :key="selectTree"
        type="pres"
        :selectTree="selectTree"
        @noCheck="noCheck"
      >
      </TreeSelect>
    </el-col>
  </el-row>
</template>

<script>
  import {queryPersonInvolved,savePersonInvolved,delPersonInvolved} from "@/api/daily/tree";
  import TreeSelect from '@/components/TreeSelect';

  export default {
    components: {
      TreeSelect
    },
    props: {
      Dpids: {
        type: Array
      },
      defaultTree: [],
      problemId: {
        type: String
      },
      relevantTableId: {
        type: String
      },
      relevantTableName: {
        type: String
      },
    },
    data() {
      return {
        height: 0,
        index: 0,
        data: [],
        name:'',
        resultList: [],
        selectTree: [],
      }
    },
    methods: {
      refresh() {
        this.index++;
        this.QueryPersonInvolved();
      },
      //查询人员
      QueryPersonInvolved() {
        let query = {
          problemId: this.problemId,
          relevantTableId: this.relevantTableId,
          relevantTableName: this.relevantTableName,
          pIds: this.data,
          personList: [],
          personName: this.name
        };
        queryPersonInvolved(query).then(
          response => {
            this.resultList = response.data.resultList;
            this.selectTree = response.data.selectedList;
          }
        );
      },
      //部门选择
      DeptChange(){
        this.QueryPersonInvolved();
      },
      //保存人员
      SavePersonInvolved(item){
        let query = {
          problemId: this.problemId,
          relevantTableId: this.relevantTableId,
          relevantTableName: this.relevantTableName,
          personList: [item]
        };
        savePersonInvolved(query).then(
          response => {
            this.QueryPersonInvolved();
          }
        );
      },
      //删除人员
      DelPersonInvolved(item){
        let query = {
          problemId: this.problemId,
          relevantTableId: this.relevantTableId,
          relevantTableName: this.relevantTableName,
          personList: [item]
        };
        delPersonInvolved(query).then(
          response => {
            this.QueryPersonInvolved();
          }
        );
      },
      //删除已选人员
      noCheck(item){
        this.DelPersonInvolved({id:item.postId});
        this.QueryPersonInvolved();
      },
      //人员选择
      persChange(item,index) {
        this.resultList[index].checked=!this.resultList[index].checked;
        if(!item.checked){//删除
          this.DelPersonInvolved(item);
        }else{//保存
          this.SavePersonInvolved(item);
        }
      }
    }
  }
</script>
<style rel="stylesheet/scss" scoped lang="scss">
  .is-disabled {
    display: none !important;
  }
  .com-checkbox {
    height: 30px;
    line-height: 30px;
    margin: 5px 10px 5px 0;
    display: inline-block;
    border-radius: 2px;
    box-sizing: border-box;
    padding: 0 12px;
    border: solid 1px #d9d9d9;
    cursor: pointer;
    .com-con {

    }
  }
  .com-checkbox.checked{
    color: #f5222d;
    border: solid 1px #f5222d;
     }
  .height {
    height: 100%;
  }

  .el-checkbox.is-bordered.el-checkbox--medium {
    margin-bottom: 10px;
  }

  .tree-body {
    .unit-list {
      height: 50%;
      overflow: auto;
    }
    .persList {
      height: calc(50% - 44px);
      overflow: auto;
    }
    height: 100%;
    .public-box-content {
      height: 60vh !important;
    }
    .tree-height {
      height: calc(100% - 40px);
      .tree-box {
        height: calc(100% - 100px);
        overflow: auto;
      }
      .tree-bottom {
        height: 140px;
        overflow: auto;
      }
    }
  }
</style>
