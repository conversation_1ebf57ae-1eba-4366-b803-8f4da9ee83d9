<!--企业联系人-->
<template>
  <div  style="height:calc(70vh - 254px);">
    <el-table
      border
      :data="tableList"
      ref="table"
      height="100%"
    >
      <el-table-column
        align="center"
        type="index"
        width="50">
      </el-table-column>
      <el-table-column label="组织机构分级名称" prop="orgClassText" width="250" show-overflow-tooltip/>
      <el-table-column label="类别" align="center" prop="personTypeText" width="200" show-overflow-tooltip/>
      <el-table-column label="部门名称" prop="deptName" width="200" show-overflow-tooltip/>
      <el-table-column label="姓名" align="center" prop="personName" width="250" show-overflow-tooltip/>
      <el-table-column label="职务" align="center" prop="personDuty" width="250" show-overflow-tooltip/>
      <el-table-column label="办公电话" align="center" prop="personPhone" width="250" show-overflow-tooltip/>
      <el-table-column label="手机" align="center" prop="personMobilePhone" width="250" show-overflow-tooltip/>
      <el-table-column label="传真" prop="personFax" width="250" show-overflow-tooltip/>
      <el-table-column label="电子邮箱" prop="personEmail" width="250" show-overflow-tooltip/>
      <el-table-column label="邮编" prop="personPostalCode" width="250" show-overflow-tooltip/>
      <el-table-column label="地址" prop="personAdress" width="250" show-overflow-tooltip/>
      <el-table-column label="排序" align="center" prop="sort" width="50" show-overflow-tooltip/>
      <el-table-column label="状态" align="center" prop="del" width="60">
        <template slot-scope="scope">
          {{ scope.row.del==1?'已删除': scope.row.del==2?'新增':'编辑'}}
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
  import {getReportAreaPerson,getReportedAreaPerson} from "@/api/sasac/reportManagement/edit/detail/index";

  export default {
    name: "enterpriseContact",
    props: {
      problemId: {
        type: String
      },
      height:{
        type: String
      }
    },
    data() {
      return {
        tableList: [],
        total: 0,
        hasSelectList:[],//选中的值
        params: {
          pageNum: 1,
          pageSize: 10,
        }
      }
    },
    created() {
      this.GetReportAreaPerson();
    },
    mounted() {
    },
    methods: {
      // 获取查询页数据
      GetReportAreaPerson() {
        getReportedAreaPerson({ reportId:this.problemId,...this.params}).then(response => {
          this.tableList = response.data[0].allList;
        });
      },
      // 获取刷新数据
      onRefresh() {
        this.GetReportAreaPerson();
      }
    }
  }
</script>

<style scoped>

</style>
