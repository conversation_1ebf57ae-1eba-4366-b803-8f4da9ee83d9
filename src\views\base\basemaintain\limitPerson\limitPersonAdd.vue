<template>
  <el-dialog
    title="禁入限制人员编辑"
    :visible.sync="dialogVisible"
    width="80%"
    :before-close="handleClose"
  >

    <div style=" margin-bottom: 30px;">
      <span v-show="dataDetails.commitFlag=='1'" class="submit-s">【已提交】</span>
      <span v-show="dataDetails.commitFlag=='0'" class="submit-n">【待提交】</span>
      <span class="submit-w"><i class="el-icon-info submit-i" />温馨提示：状态为已提交时，其他人员才能查询到</span>
    </div>
    <div>
      <el-form ref="dataDetails" :model="dataDetails" label-width="150px" style="margin-top: 20px">
        <el-row>
          <el-col :span="8">
            <el-form-item label="禁止人姓名">
              <span>{{ dataDetails.userName }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="性别">
              <span>{{ dataDetails.sexFlag=='1'?'男':'女' }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="身份证件号">
              <span>{{ dataDetails.idCard }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="所在企业">
              <span>{{ dataDetails.involAreaName }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="企业层级">
              <span>{{ dataDetails.orgGradeName }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="处理前职位">
              <el-input v-model="dataDetails.postName" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="干部类别">
              <el-select v-model="dataDetails.cadreCategory" placeholder="请选择" style="width: 100%">
                <el-option
                  v-for="item in dataDetails.cadreCategoryList"
                  :key="item.dictValue"
                  :label="item.dictLabel"
                  :value="item.dictValue"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="禁入限制期间开始">
              <el-date-picker v-model="dataDetails.limitStartTime" type="date" placeholder="选择禁入限制期间开始" style="width: 100%;" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="禁入限制期间结束">
              <el-date-picker v-model="dataDetails.limitEndTime" type="date" placeholder="选择禁入限制期间结束" style="width: 100%;" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="责任追究处理部门">
              <el-input v-model="dataDetails.accountabDepartment" readonly @click.native="openOrgTree('orgTree')" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="责任处理联系人">
              <el-input v-model="dataDetails.contactsName" readonly @click.native="openOrgTree('staffTree')" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="责任处理联系方式">
              <el-input type="number" v-model="dataDetails.contactInformation" @input="handleInput($event)"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="工作简历">
              <el-input v-model="dataDetails.workResume" type="textarea" :rows="2" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="违规问题">
              <el-input v-model="dataDetails.violationsProblem" type="textarea" :rows="2" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="不良后果">
              <el-input v-model="dataDetails.adverseConsequences" type="textarea" :rows="2" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="责任认定情况">
              <el-input v-model="dataDetails.responIdenty" type="textarea" :rows="2" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="责任追究处理情况">
              <el-input v-model="dataDetails.accountabHandle" type="textarea" :rows="2" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="备注">
              <el-input v-model="dataDetails.remark" type="textarea" :rows="2" />
            </el-form-item>
          </el-col>
        </el-row>

        <div style="height: 36px; line-height: 36px; margin-bottom: 20px">
          <span class="file-title">附件列表</span>
          <div style="float: right; padding: 3px 0; white-space:nowrap" type="text">
            <el-upload
              class="upload-demo"
              accept=".pdf"
              :headers="headers"
              :data="fileParams"
              :action="url"
              :show-file-list="false"
              :before-upload="handlePreview"
              style="display: inline; margin-left: 8px;"
              :on-success="handleFileSuccess"
            >
              <el-button size="small" type="primary">处理决定书扫描件.PDF</el-button>
            </el-upload>
          </div>
        </div>
        <div>
          <el-table :data="dataDetails.files" max-height="250" style="width: 100%" border :show-header="false" :cell-class-name="rowClass">
            <el-table-column label="序号" type="index" min-width="10%" align="center" />
            <el-table-column label="文档名称" prop="fileName" min-width="55%" />
            <el-table-column label="上传人" prop="createLoginName" min-width="13%" />
            <el-table-column label="上传时间" prop="createTime" :formatter="dateFormat" min-width="13%" />
            <el-table-column label="操作" fixed="right" min-width="9%" align="center" class-name="small-padding fixed-width">
              <template slot-scope="scope">
                <i class="el-icon-delete" style="color: red" @click="deleteFile(scope.row)" />
                <i class="el-icon-download" style="color: red;margin-left:15px;    cursor: pointer;" @click="downLoad(scope.row)" />
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-form>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button size="mini" :loading="buttonLoading=='save'" @click="savelimitPersonBaseInfo(0)">保存</el-button>
      <el-button size="mini" type="primary" :loading="buttonLoading=='sub'" @click="savelimitPersonBaseInfo(1)">提交</el-button>
    </span>
    <treeModal
      v-if="treeVisible=='orgTree' || treeVisible=='staffTree'"
      :node-data="nodeData"
      :modal-title="modalTitle"
      :tree-list="treeList"
      :only-child="onlyChild"
      @closeModal="closeModal"
      @getTreeData="getTreeData"
      @getOrg="getOrg"
    />
  </el-dialog>
</template>

<script lang="ts">
import { queryLimitedPersonInfoById, saveLimitPerson, getBaseLimitPersonNewVerson } from '@/api/base/limitPerson'
import { deleteViolFile } from '@/api/base/sendBox'
import moment from 'moment'
import { getToken } from '@/utils/auth'
import treeModal from './treeModal'
import { getOrgTree, getOrgTreeWithPerson } from '@/api/base/person'

export default {
  name: 'LimitPersonAdd',
  components: { treeModal },
  props: {
    dialogVisible: {
      type: Boolean,
      default: true
    },
    id: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      dataDetails: {},
      // 设置上传的请求头部
      headers: { Authorization: 'Bearer ' + getToken() },
      url: process.env.VUE_APP_BASE_API + '/colligate/baseInfo/uploadViolFile',
      fileParams: {
        busiId: '',
        busiTableName: 'T_COL_VIOL_BASE_LIMIT_PERSON'
      },
      modalTitle: '责任追究处理部门',
      treeVisible: '',
      treeList: [],
      nodeData: {}, // 组织树选中的数据
      onlyChild: false, // 是否只能选择叶子节点
      buttonLoading: null
    }
  },
  created() {
    this.limitPersonInfo()
  },
  methods: {
    //校验手机号
    handleInput(value) {
      if (value.length > 11) {
        this.dataDetails.contactInformation = value.slice(0, 11);
        return;
      }
      const phoneRegex = /^1[2-9]\d{9}$/;
      if (!phoneRegex.test(value) && value.length === 11) {
        this.dataDetails.contactInformation= value.slice(0, -1);
      }
    },
    /** 查询禁入限制人员详情*/
    limitPersonInfo() {
      queryLimitedPersonInfoById({ id: this.id }).then(
        response => {
          this.dataDetails = {
            ...response.data
            //  limitStartTime: this.dateFormat(response.data.limitStartTime),
            //  limitEndTime: this.dateFormat(response.data.limitEndTime),
          }
          this.fileParams = {
            ...this.fileParams,
            busiId: response.data.id
          }
        }
      )
    },
    savelimitPersonBaseInfo(commitFlag) {

      const phoneRegex = /^1[2-9]\d{9}$/;

      if(!phoneRegex.test(this.dataDetails.contactInformation)){
        this.$message.error('请输入正确的【责任处理联系方式】')
        return false;
      }else{
        if (commitFlag == 1) {
          this.buttonLoading = 'sub'
        } else {
          this.buttonLoading = 'save'
        }
      }
      const params = {
        ...this.dataDetails,
        commitFlag
      }
      saveLimitPerson(params).then(
        response => {
          if (response.code === 200) {
            this.$message({
              message: response.msg,
              type: 'success'
            })
            if (commitFlag) {
              this.handleClose()
            } else {
              this.handleFileSuccess()
            }
          }
        }
      ).catch(err => {
        this.buttonLoading = null
      })
    },

    downLoad(obj) {
      console.log(obj)
      this.download('/sys/attachment/downloadSysAttachment/' + obj.id, {
      }, obj.fileName)
    },
    /** 关闭模态框*/
    handleClose() {
      this.$emit('closeModal')
      this.buttonLoading = null
    },
    /** 关闭模态框*/
    closeModal() {
      this.treeVisible = ''
    },
    /* 日期处理*/
    dateFormat: function(date) {
      if (date === undefined) {
        return ''
      }
      return moment(date).format('YYYY-MM-DD')
    },
    /* 删除附件*/
    deleteFile: function(row) {
      this.$confirm('确认删除附件【' + row.fileName + '】吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteViolFile({
          busiTableName: 'T_COL_VIOL_BASE_LIMIT_PERSON',
          busiId: this.dataDetails.id,
          id: row.id
        }).then(response => {
          if (response.code === 200) {
            this.handleFileSuccess()
          } else {
            this.$message.error(response.msg)
          }
        })
      })
    },
    /** 附件上传成功*/
    handleFileSuccess() {
      getBaseLimitPersonNewVerson({ uniqueCode: this.dataDetails.uniqueCode }).then(
        response => {
          this.buttonLoading = null
          this.dataDetails = {
            ...this.dataDetails,
            id: response.data.id,
            files: response.data.files,
            version: response.data.version,
            commitFlag: response.data.commitFlag
          }
          this.fileParams = {
            ...this.fileParams,
            busiId: response.data.id
          }
        }
      )
    },
    /* 附件上传之前*/
    handlePreview: function(file) {
      if (file.size / 1024 / 1024 > 100) {
        this.$message.error('附件大小不能超过 100MB!')
        return false
      }
    },
    /** 修改附件表样式 */
    rowClass({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 1 || columnIndex === 2 || columnIndex === 3) {
        return 'no-right-border'
      } else if (columnIndex === 0) {
        return 'cell-color'
      }
    },
    // 打开组织树选择框
    openOrgTree(treeVisible) {
      // var staffOrgList = getOrgTreeWithPerson({});
      this.treeVisible = treeVisible
      if (treeVisible == 'orgTree') {
        this.onlyChild = false
        this.getTreeData(null)
        this.nodeData = {
          id: this.dataDetails.accountabDepartmentId,
          name: this.dataDetails.accountabDepartment
        }
      } else if (treeVisible == 'staffTree') {
        this.onlyChild = true
        this.getStaffTreeData(null)
        this.nodeData = {
          id: this.dataDetails.contactsPostId,
          name: this.dataDetails.contactsName
        }
      }
    },
    // 查询组织树数据
    getTreeData(name) {
      getOrgTree({ name }).then(
        response => {
          this.treeList = this.arrayToTree(response.data)
        }
      )
    },
    arrayToTree(array) {
      const data = JSON.parse(JSON.stringify(array))
      const result = []
      const hash = {}
      data.forEach((item) => {
        hash[item['id']] = item
      })
      data.forEach(item => {
        const hashVP = hash[item['pId']]
        if (hashVP) {
          !hashVP['children'] && (hashVP['children'] = [])
          if (item['id']) {
            item.key = item['id']
            item.id = item['id']
          }
          if (item['name']) {
            item.label = item['name']
          }
          hashVP['children'].push(item)
        } else { // 根节点直接放入结果集
          if (item['id']) {
            item.key = item['id']
            item.id = item['id']
          }
          if (item['name']) {
            item.label = item['name']
          }
          result.push(item)
        }
      })
      return result
    },
    // 回显选择信息
    getOrg(nodeData) {
      if (this.treeVisible == 'orgTree') {
        this.dataDetails.accountabDepartmentId = nodeData.id
        this.dataDetails.accountabDepartment = nodeData.name
      } else if (this.treeVisible == 'staffTree') {
        this.dataDetails.contactsPostId = nodeData.id
        this.dataDetails.contactsName = nodeData.name
      }
    },
    // 查询人员组织树数据
    getStaffTreeData(name) {
      getOrgTreeWithPerson({ name }).then(
        response => {
          this.treeList = this.arrayToTree(response.data)
        }
      )
    }

  }
}
</script>
<style>
  @import "../../common/common.css";
</style>
