<template>
  <div
    :id="id"
    ref="chart"
    :class="className"
    :style="{ height: height, width: width }"
  />
</template>
<script>
import echarts from 'echarts'
import { fontSizeEchars } from './mixins/fontSizeEchars'
import resize from './mixins/resize'
require('echarts/theme/macarons')

export default {
  mixins: [resize],
  props: {
    charsData: {
      type: null,
      default: () => {}
    },
    id: {
      type: String,
      default: 'myChart'
    },
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '100%'
    }
  },
  data() {
    return {
      chart: null
    }
  },
  watch: {
    charsData: {
      handler(val, oldVal) {
        this.chart.clear()
        setTimeout(() => {
          this.initChart()
        }, 1000)
      },
      deep: true
    }
  },
  mounted() {
    this.initChart()
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.clear()
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$refs.chart, 'macarons')
      this.chart.setOption(
        {
          title: [
            {
              text: this.charsData + '%',
              x: '48%',
              y: '40%',
              textAlign: 'center',
              textStyle: {
                fontSize: fontSizeEchars(0.16),
                fontWeight: '500',
                color: '#373D41',
                textAlign: 'center'
              }
            }
          ],

          angleAxis: {
            max: 100,
            clockwise: true,
            axisLine: {
              show: false
            },
            axisTick: {
              show: false
            },
            axisLabel: {
              show: false
            },
            splitLine: {
              show: false
            }
          },
          radiusAxis: {
            type: 'category',
            axisLine: {
              show: false
            },
            axisTick: {
              show: false
            },
            axisLabel: {
              show: false
            },
            splitLine: {
              show: false
            }
          },
          polar: {
            center: ['50%', '50%'],
            radius: '150.1%'
          },
          series: [
            {
              type: 'bar',
              startAngle: 180,
              data: [
                {
                  name: '',
                  value: this.charsData,
                  itemStyle: {
                    normal: {
                      color: '#8DA6FC'
                    }
                  }
                }
              ],
              coordinateSystem: 'polar',
              roundCap: true,
              barWidth: 10,
              barGap: '-100%',
              radius: ['49%', '52%'],
              z: 1
            },
            {
              type: 'bar',
              data: [
                {
                  value: 100,
                  itemStyle: {
                    color: '#F0F1F5'
                  }
                }
              ],
              coordinateSystem: 'polar',
              roundCap: true,
              barWidth: 10,
              barGap: '-110%',
              radius: ['48%', '53%'],
              z: 0
            }
          ]
        },
        true
      )
    }
  }
}
</script>
<style lang="scss" scoped>
#myChart {
  width: 100%;
  height: 100%;
  div {
    width: 100%;
    height: 100%;
  }
}
</style>
