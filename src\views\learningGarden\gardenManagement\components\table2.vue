
<!-- 政策理论 -->
<template>
  <div class="wai-container">
    <div class="layui-row width height">
      <el-col class="height" style="border-left:1px solid #eee">
        <div class="common-in-box new-common-in-box">
          <div class="common-in-box-content">
            <div class="top-search" style="margin-bottom: 10px">
              <el-col :span="6" class="height">
                <div class="layui-form">
                  <div class="layui-form-left">信息标题</div>
                  <el-input
                    size="mini"
                    placeholder="请输入信息标题"
                    v-model="searchData.informationTitle"
                    clearable
                  ></el-input>
                </div>
              </el-col>

              <el-col :span="6" class="height">
                <div class="layui-form">
                  <div class="layui-form-left">信息类型</div>

                  <el-select
                    v-model="searchData.informationType"
                    placeholder="请选择"
                    clearable
                  >
                    <el-option
                      v-for="(item, index) in dict.type.INFORMATION_TYPE"
                      :key="index"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </div>
              </el-col>
              <el-col :span="12" class="height  float-right text-right">
                <el-button type="primary" icon="el-icon-s-promotion" size="mini" @click="batchPublishInformation">批量发布</el-button>
                <el-button type="primary"  icon="el-icon-plus" size="mini" @click="openAdd">新增</el-button>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="getInformationList">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="clearInfo">重置</el-button>
              </el-col>
            </div>
            <div>
              <el-col  class="height">
                <div class="layui-form">
                  <el-radio-group v-model="searchData.isPublish" @change="isPublishChange">
                    <el-radio-button  label="" size="mini" border>全部</el-radio-button>
                    <el-radio-button  :label="1" size="mini" border>已发布</el-radio-button>
                    <el-radio-button  :label="0" size="mini" border>未发布</el-radio-button>
                  </el-radio-group>
                </div>
              </el-col>
            </div>

            <div class="tables tables_1">
              <el-table
                :data="tableData"
                border
                v-loading="tableLoading"
                style="width: 100%"
                height="610"
                @selection-change="handleSelectionChange"
              >
                <el-table-column
                  type="selection"
                  align="center"
                  width="50">
                </el-table-column>
                <el-table-column
                  type="index"
                  label="序号"
                  align="center"
                  sortable
                  min-width="5%"
                  :index="table_index"
                />
                <el-table-column
                  label="信息标题"
                  prop="informationTitle"
                  min-width="30%"
                  align="left"
                >
                </el-table-column>
                <el-table-column
                  label="信息类型"
                  prop="informationType"
                  min-width="10%"
                  align="center"
                >
                  <template slot-scope="scope">
                    {{
                      scope.row.informationType
                        | fromatComon(dict.type.INFORMATION_TYPE)
                    }}
                  </template>
                </el-table-column>
                <el-table-column
                  label="文号"
                  align="center"
                  prop="informationNum"
                  min-width="15%"
                >
                </el-table-column>
                <el-table-column
                  label="信息创建时间"
                  prop="createTime"
                  min-width="16%"
                  align="center"
                >
                </el-table-column>
                <el-table-column
                  label="是否发布"
                  prop="isPublish"
                  min-width="10%"
                  align="center"
                >
                  <template slot-scope="scope">
                    {{
                      scope.row.isPublish
                        | fromatComon(dict.type.IS_PUBLISH)
                    }}
                  </template>
                </el-table-column>
                <el-table-column
                  label="发布人"
                  prop="publishUserName"
                  min-width="10%"
                  align="center"
                >
                </el-table-column>
                <el-table-column
                  label="系统发布时间"
                  prop="publishTime"
                  min-width="16%"
                  align="center"
                >
                </el-table-column>
                <el-table-column
                  label="操作"
                  min-width="20%"
                  width="200"
                  align="center"
                  class-name="small-padding fixed-width"
                  fixed="right"
                >
                  <template slot-scope="scope">
                    <el-button
                      v-if="scope.row.isPublish ==0"
                      size="mini"
                      type="text"
                      title="发布"
                      icon="el-icon-finished"
                      @click="publishInformation(scope.row,1)"
                    >
                    </el-button>

                    <el-button
                      v-if="scope.row.isPublish ==1"
                      size="mini"
                      type="text"
                      title="取消发布"
                      icon="el-icon-sort"
                      @click="publishInformation(scope.row,0)"
                    >
                    </el-button>


                    <el-button
                      v-if="scope.row.isPublish ==0"
                      size="medium"
                      type="text"
                      icon="el-icon-edit"
                      title="编辑"
                      @click="openAdd(scope.row)"
                    >
                    </el-button>


                    <el-button
                      size="mini"
                      type="text"
                      title="查看"
                      icon="el-icon-search"
                      @click="openView(scope.row)"
                    ></el-button>

                    <el-button
                      size="medium"
                      type="text"
                      icon="el-icon-delete"
                      title="删除"
                      @click="delInformation(scope.row)"
                    ></el-button>
                  </template>
                </el-table-column>
              </el-table>
              <pagination
                v-show="total > 0"
                :total="total"
                :page.sync="queryParams.pageNum"
                :limit.sync="queryParams.pageSize"
                @pagination="getInformationList"
              />
            </div>
          </div>
        </div>
      </el-col>
    </div>
    <table2Add v-if="dialogVisible" :editType = "editType" v-on:closeModal="closeModal" :informationId="id"></table2Add>


  </div>
</template>

<script>


import {
  batchPublishArticle,
  getInformationList,
  delInformation,
  publishInformation, batchPublishInformation
} from "@/api/learningGarden/gardenManagement/gardenManagement";
import table2Add from "@/views/learningGarden/gardenManagement/components/table2Add";
export default {
  name: "InformationPublish",
  components: {
    table2Add
  },
  dicts: ["IS_PUBLISH","INFORMATION_TYPE"],
  data() {
    return {
      id:'',
      // 是否显示弹出层
      dialogVisible: false,
      editType:'',
      // 信息id
      informationId:'',
      //右侧查询条件
      searchData: {
        informationTitle:'',
        informationType:'',
        isPublish:''
      },
      radio: '',
      showFlag:false,
      siteCodeOption:[],
      siteCodeOption1:[],
      moduleTreeNodeOption:[],
      multipleSelection: [], // 存储选中的行
      //表格数据
      tableLoading: false, //表格loading
      tableData: [],
      //表格页码
      total: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
    };
  },
  created() {
    this.getInformationList()
  },
  methods: {
    delInformation(row){
      this.$confirm('确认删除该信息吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'})
        .then(() => {
          delInformation({id:row.id}).then(
            response => {
              if(response.code == 200){
                this.$message({
                  message: response.msg,
                  type: 'success'
                });
                this.getInformationList();
              }else {
                this.$message.error(response.msg)
              }
            }
          )
        })
    },
    /**关闭模态框*/
    closeModal(){
      this.dialogVisible = !this.dialogVisible;
      this.getInformationList();
    },
    /**打开新增编辑框*/
    openAdd(row) {
      this.dialogVisible = !this.dialogVisible;
      console.log(row)
      if(row.id){//编辑
        this.editType = 'edit'
        this.id = row.id;
      }else{//新增
        this.editType = 'add'
        this.id = null;
      }
    },
    /**打开查看框*/
    openView(row) {
      this.dialogVisible = !this.dialogVisible;
      this.id = row.id;
      this.editType = 'view'
    },



    isPublishChange(val){
      this.getInformationList()
    },
    handleSelectionChange(val) {
      this.multipleSelection = val; // 当表格的选项发生变化时，更新选中项数组
    },
    batchPublishInformation(){
      if (this.multipleSelection.length == 0){
        this.$message.error("请选择要批量发布的信息！")
        return false;
      }
      let lists = []
      let isJump = false
      this.multipleSelection.forEach((item) => {
        if (item.isPublish == 1){
          isJump = true
        }
        let article = {
          id: item.id,
          isPublish: 1,
        }
        lists.push(article)
      })
      if (isJump){
        this.$message.error("部分信息已发布，请重新选择！")
        return false;
      }
      this.$confirm('确认发布这'+lists.length+'条信息吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'})
        .then(() => {
          batchPublishInformation(lists).then((response) => {
            if (response.code === 200) {
              this.$message.success(response.msg)
              this.getInformationList()
            }else {
              this.$message.error(response.msg)
            }
          })
        })
    },

    getInformationList(){
      getInformationList(this.queryParams,this.searchData).then((response) => {
        if (response.code === 200) {
          this.tableData = response.rows
          this.total = response.total
        }else {
          this.$message.error(response.msg)
        }
      })
    },
    publishInformation(row, type){
      //type为1为发布，type为0为取消发布
      var data = {
        "id": row.id,
        "isPublish": type,
      }
      let msg = type == 1 ? '发布' : '取消发布'
      this.$confirm('确认'+msg+'该信息吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'})
        .then(() => {
          publishInformation(data).then((response) => {
            if (response.code === 200) {
              this.$message.success(response.msg)
              this.getInformationList()
            } else {
              this.$message.error(response.msg)
            }
          })
        })
    },
    table_index(index) {
      return (this.queryParams.pageNum - 1) * this.queryParams.pageSize + index + 1
    },
    //重置
    clearInfo() {
      this.searchData = {
        siteCode:'',
        title:'',
        moduleId:'',
        isPublish:''
      }
      this.getInformationList();
    },

  },
};
</script>
<style rel="stylesheet/scss"  lang="scss">
@import "~@/assets/styles/quarterly-report/index.css";
</style>







