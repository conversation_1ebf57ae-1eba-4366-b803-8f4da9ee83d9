<template>
  <ul>
    <li v-for="item in selectTree" class="depart_li">
      <span class="float-left">{{type=='radio'?item.name:item.name}}</span>
    </li>
  </ul>
</template>

<script>
  export default {
    name: "personne;",
    props: {
      selectTree: {
        type: Array,
        default: []
      },
      type:{
        type: String,
        default: ''
      }
    },
    data() {
      return {}
    },
    methods: {
      /** 删除操作 */
      noCheck(item) {
        this.$emit('noCheck', item);
      },
    }
  }
</script>

<style scoped lang="scss">
  .depart_li {
    min-width: 84px;
    height: auto;
    position: relative;
    background-color: #e6f7ff;
    color: #40a9ff;
    line-height: 30px;
    margin: 0 6px 12px;
    display: inline-block;
    padding: 0 30px 0 12px;
    border-radius: 2px;
    box-sizing: border-box;
    .icon {
      float: right;
      cursor: pointer;
      position: absolute;
      right: 8px;
      top: 6px;
      font-size: 16px;
    }
  }
</style>
