<!--企业联系人-->
<template>
  <el-container>
    <el-dialog  :visible.sync="visible" width="90%" append-to-body  @close="close" title="企业联系人">
     <div style="height:calc(70vh - 100px)">
       <el-table
         border
         :data="tableList.length?tableList[0].allList:[]"
         ref="table"
         height="100%"
         @selection-change="handleSelectionChange"
       >
         <el-table-column
           fixed
           align="center"visible
           type="selection"
           width="40">
         </el-table-column>
         <el-table-column
           fixed
           align="center"
           label="序号"
           type="index"
           width="50">
         </el-table-column>
         <el-table-column label="组织机构分级名称" prop="orgClassText" width="250"/>
         <el-table-column label="类别" prop="personTypeText" width="200"/>
         <el-table-column label="部门名称" prop="deptName" width="200"/>
         <el-table-column label="姓名" prop="personName" width="250"/>
         <el-table-column label="职务" prop="personDuty" width="250"/>
         <el-table-column label="办公电话" prop="personPhone" width="250"/>
         <el-table-column label="手机" prop="personMobilePhone" width="250"/>
         <el-table-column label="传真" prop="personFax" width="250"/>
         <el-table-column label="电子邮箱" prop="personEmail" width="250"/>
         <el-table-column label="邮编" prop="personPostalCode" width="250"/>
         <el-table-column label="地址" prop="personAdress" width="250"/>
         <el-table-column label="排序" prop="sort" width="50"/>
         <el-table-column label="状态" prop="del" width="140" fixed="right"
                          align="center"
         >
           <template slot-scope="scope">
             <span v-if="scope.row.del=='2'">新增</span>
             <div v-else>
               <el-select v-model="scope.row.del"
                          :style="{width: '100%'}"
               >
                 <el-option label="编辑" key="3" :value="3"></el-option>
                 <el-option label="删除" key="1" :value="1"></el-option>
               </el-select>
             </div>
           </template>
         </el-table-column>
       </el-table>
     </div>
      <div slot="footer">
        <el-button size="mini" @click="close()">取消</el-button>
        <el-button size="mini" type="primary" @click="save()">保存</el-button>
      </div>
    </el-dialog>
  </el-container>
</template>

<script>
  import {getReportAreaPersonAll, saveReportedAreaPerson} from "@/api/sasac/reportManagement/edit/detail/index";

  export default {
    name: "person",
    props: {
      problemId: {
        type: String
      }
    },
    data() {
      return {
        hasSelectList:[],//选中的值
        tableList:[],
        visible:false,
        commitFlag: 0,
        personData: [],//企业联系人信息
      };
    },
    created() {
      // this.onShow();
    },
    methods: {
      /**查询企业联系人*/
      onShow() {
        // debugger;
        this.visible = true;
        //this.loading = true;
        //接口：colligate/baseInfo/report/getReportAreaPersonAll
        getReportAreaPersonAll({reportId: this.problemId}).then(
          response => {
            this.tableList = response.data;
            this.personData = response.data[0];
            let tableChecked = this.tableList[0].allList;
            this.hasSelectList = [];
            this.$nextTick(()=> {
              tableChecked.forEach(row => {
                if (row.checked) {
                  this.hasSelectList.push(row);
                  this.$refs.table.toggleRowSelection(row, true);
                }
              });
            });
            // this.loading = false;
          }
        );
      },
      //关闭弹框
      close(){
        this.visible = false;
        this.$emit('editClose', this.commitFlag);
        this.commitFlag = 0;
      },
      //选中的值
      handleSelectionChange(val){
        let selectList = [];
        val.forEach(function (item) {
          selectList.push(item)
        });
        this.hasSelectList = selectList;
      },
      /**保存或提交*/
      save(){
        this.commitFlag = 1;
        const params = {
          reportId:this.problemId,
          personList:[{
            ...this.personData,
            allList: this.hasSelectList,
          }]
        };
        saveReportedAreaPerson(params).then(
          response => {
            if(response.code === 200){
              this.$modal.msgSuccess('保存成功！');
              this.close();
            }else{
              this.$message.error(response.msg);
            }
          }
        );
      }
    }
  };
</script>

<style>
  .text-red {
    color: #f5222d;
  }

  .icon-orange {
    color: #fa8b16;
  }
</style>
