<!--下级单位数据上报-->
<template>
  <div class="scope">
    <el-dialog class="subordinateReport" :visible.sync="visible" @close="close" :modal-append-to-body="false"
               append-to-body title="下级单位上报" width="90%">
      <BlockCard title="基本信息">
        <el-form ref="elForm" :model="infoData" size="medium" label-width="100px">
          <el-row>
            <el-col :span="8">
              <el-form-item label="上报年度">
                {{ infoData.reportYear }}
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="上报季度">
                {{ infoData.reportQuarter | fromatComon(dict.type.REPORT_QUARTER) }}
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="上报截止日期">
                <el-date-picker
                  format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd"
                  v-model="infoData.reportCloseTime"
                  type="datetime"
                  placeholder=""
                  readonly
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="上报标题">
                {{ infoData.reportTitle }}
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="上报要求">
                {{ infoData.reportRequire }}
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </BlockCard>
      <BlockCard title="附件列表">
        <el-table
          :data="filesData"
          border
          v-loading="tableLoading"
          style="width: 100%"
        >
          <el-table-column
            label="序号"
            type="index"
            min-width="5%"
            align="center"
          />
          <el-table-column label="文件名" prop="fileName" min-width="50%">
            <template slot-scope="scope">
              <div
                style="text-align: left"
                class="overflowHidden-1"
                :title="scope.row.fileName"
              >
                {{ scope.row.fileName || "" }}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            label="上传人"
            prop="createUserName"
            min-width="10%"
            align="center"
          />
          <el-table-column
            label="上传时间"
            prop="createTime"
            min-width="20%"
            align="center"
          />

          <el-table-column
            label="操作"
            fixed="right"
            min-width="15%"
            align="center"
            class-name="small-padding fixed-width"
          >
            <template slot-scope="scope">
              <a @click="fileDownload(scope.row)" class="table-btn">下载</a>
            </template>
          </el-table-column>
        </el-table>
      </BlockCard>
      <BlockCard title="下级单位上报情况" v-show="subordinateReportType=='0'">
        <div class="sub-report-box">

          <div class="sub-report-detail">
            <div class="sub-report-type">进行中</div>
              <div class="sub-report-num" :class="jxzList.length==0?'border-right':''">{{
                jxzCount||0
                }}</div>
              <div
                class="sub-report-li"
                v-for="(item, index) in jxzList"
                :key="index"
                :title="item"
              >
                {{ item }}
              </div>
              <div class="sub-report-core" v-if="jxzList.length>0" @click="subordinateReportClick('2')">更多</div>
            </div>

          <div class="sub-report-detail">
            <div class="sub-report-type">已完成</div>
            <div class="sub-report-num" :class="ywcList.length==0?'border-right':''">{{
              ywcCount||0
              }}</div>
            <div
              class="one-province"
              v-for="(item, index) in ywcList"
              :key="index"
              :title="item"
            >
              {{ item }}
            </div>
            <div class="sub-report-core" v-if="ywcList.length>0"  @click="subordinateReportClick('3')">更多</div>
          </div>
        </div>
      </BlockCard>

      <BlockCard title="下级单位上报确认" v-show="subordinateReportType=='1'">
        <el-form>
          <el-table v-loading="loading" :data="tableList1">
            <el-table-column label="序号" type="index" min-width="4%" align="center"/>
            <el-table-column label="上报单位" prop="reportUnitName" show-overflow-tooltip align="center" min-width="30%"/>
            <el-table-column label="接口人" prop="interfaceUserName" show-overflow-tooltip align="center" min-width="20%"/>
            <el-table-column label="邮箱" prop="interfaceUserMail" show-overflow-tooltip align="center" min-width="20%"/>
            <el-table-column label="联系电话" prop="interfaceUserPhone" show-overflow-tooltip align="center"
                             min-width="20%"/>
            <el-table-column label="操作" fixed="right" width="200" align="center"
                             class-name="small-padding fixed-width">
              <template slot-scope="scope">
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-edit"
                  @click="clickPostName(scope.row)"
                >编辑
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-form>
      </BlockCard>

      <BlockCard title="下级单位上报情况（进行中）" v-show="subordinateReportType=='2'">
        <template v-slot:right>
          <el-button
            size="mini"
            type="primary"
            @click="urgeAll"
            icon="el-icon-bell"
            class="float-right margin-top-8"
            plain
          >批量催办
          </el-button>
        </template>
        <el-table v-loading="loading" :data="tableList2" @selection-change="handleSelectionChange">
          <el-table-column
            type="selection"
            min-width="4%">
          </el-table-column>
          <el-table-column label="序号" type="index" min-width="4%" align="center"/>
          <el-table-column label="上报单位" show-overflow-tooltip prop="reportUnitName" align="center" min-width="20%"/>
          <el-table-column label="接口人" show-overflow-tooltip prop="nickName" align="center" min-width="15%"/>
          <el-table-column label="邮箱" show-overflow-tooltip prop="email" align="center" min-width="10%"/>
          <el-table-column label="联系电话" show-overflow-tooltip prop="phoneNumber" align="center" min-width="10%"/>
          <el-table-column label="所处阶段" show-overflow-tooltip prop="linkName" align="center" min-width="15%"/>
          <el-table-column label="处理人" show-overflow-tooltip prop="handleUserName" align="center" min-width="10%"/>
          <el-table-column label="操作" width="100px" align="center">
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                icon="el-icon-bell"
                @click="toRemind(scope.row)"
              >催办
              </el-button>
            </template>
          </el-table-column>
        </el-table>

      </BlockCard>

      <BlockCard title="下级单位上报情况（已完成）" v-show="subordinateReportType=='3'">
        <template v-slot:right>
          <el-button
            size="mini"
            type="primary"
            icon="el-icon-download"
            class="float-right margin-top-8"
            @click="detailedExport"
            plain
          >导出
          </el-button>
        </template>
        <el-table ref="table" border v-loading="loading" :data="tableList3">
          <el-table-column label="序号" type="index" width="100" align="center">
            <template slot-scope="scope">
              <table-index
                :index="scope.$index"
                :page-num="queryParams.pageNum"
                :page-size="queryParams.pageSize"
              />
            </template>
          </el-table-column>
          <el-table-column label="基本信息" show-overflow-tooltip>
            <el-table-column label="公司名称" prop="reportUnitName" width="200" show-overflow-tooltip/>
            <el-table-column label="所属行业" prop="industryType" width="130" show-overflow-tooltip/>
            <el-table-column label="填报季度" prop="reportYearQuarter" align="center" width="130" show-overflow-tooltip>
              <template slot-scope="scope">
                {{ scope.row.reportYear }}年{{ scope.row.reportQuarterName }}
              </template>
            </el-table-column>
          </el-table-column>

          <el-table-column label="工作部署情况" show-overflow-tooltip>
            <el-table-column label="季度数据" show-overflow-tooltip>
              <el-table-column label="本季度召开领导小组会议（次）" align="center" prop="quarterTeamMeetingTime" width="130"
                               show-overflow-tooltip/>
              <el-table-column label="本季度召开领导小组办公室会议（次）" align="center" prop="quarterTeamOfficeMeetingTime" width="130"
                               show-overflow-tooltip/>
              <el-table-column label="本季度召开专题会议（次）" align="center" prop="quarterSpecialMeetingTime" width="130"
                               show-overflow-tooltip/>
            </el-table-column>
            <el-table-column label="当年累计数据" show-overflow-tooltip>
              <el-table-column label="当年累计召开领导小组会议（次）" align="center" prop="totalLeaderTeamMeetingTime" width="130"
                               show-overflow-tooltip/>
              <el-table-column label="当年累计召开领导小组办公室会议（次）" align="center" prop="totalTeamOfficeMeetingTime" width="130"
                               show-overflow-tooltip/>
              <el-table-column label="当年累计召开专题会议（次）" align="center" prop="totalSpecialMeetingTime" width="130"
                               show-overflow-tooltip/>
            </el-table-column>
          </el-table-column>
          <el-table-column label="累计印发责任追究相关制度数量（项）" align="center" prop="totalAccountabilitySystemNumber" width="130"
                           show-overflow-tooltip/>
          <el-table-column label="体系建设情况" show-overflow-tooltip>
            <el-table-column label="当年累计新增配套制度（项）" align="center" prop="totalNewSupportingSystem" width="130"
                             show-overflow-tooltip/>
            <el-table-column label="新增配套制度名称" prop="newSupportingName" width="200" show-overflow-tooltip/>
            <el-table-column label="当年累计新增工作机制（项）" align="center" prop="totalNewWorkSystem" width="130"
                             show-overflow-tooltip/>
            <el-table-column label="新增工作机制名称" prop="newWorkName" width="200" show-overflow-tooltip/>
            <el-table-column label="累计专职人员数量（人）" align="center" prop="totalProfessionalNumber" width="130"
                             show-overflow-tooltip/>
            <el-table-column label="当年累计新增专职人员数量（人）" align="center" prop="totalNewSpecialPersonNumber" width="130"
                             show-overflow-tooltip/>
            <el-table-column label="主责部门" prop="groupMainDept" width="130" show-overflow-tooltip/>
          </el-table-column>

          <el-table-column label="违规问题线索查办情况" show-overflow-tooltip>
            <el-table-column label="全级次企业" show-overflow-tooltip>
              <el-table-column label="新受理问题线索情况（季度数据）" show-overflow-tooltip>
                <el-table-column label="本季度新受理问题线索数量（件）" prop="quarterNewProblemNumber" align="center" width="130"
                                 show-overflow-tooltip/>
                <el-table-column label="本季度涉及资产损失（万元）" prop="lossAmount" align="right" width="130" show-overflow-tooltip>
                  <template slot-scope="scope">
                    {{ scope.row.lossAmount | filterNum }}
                  </template>
                </el-table-column>
                <el-table-column label="本季度涉及资产损失风险（万元）" prop="lossRisk" align="right" width="130" show-overflow-tooltip>
                  <template slot-scope="scope">
                    {{ scope.row.lossRisk | filterNum }}
                  </template>
                </el-table-column>
              </el-table-column>
              <el-table-column label="受理问题线索办理进展情况（当年累计数据）" show-overflow-tooltip>
                <el-table-column label="当年累计受理问题线索数量（件）" align="center" prop="totalProblemSourceNumber" width="130"
                                 show-overflow-tooltip/>
                <el-table-column label="上年结转问题线索数量（件）" align="center" prop="lastYearProblemSourceNumber" width="130"
                                 show-overflow-tooltip/>
                <el-table-column label="其中" show-overflow-tooltip>
                  <el-table-column label="未启动核查（件）" align="center" prop="checkNoStartedNumber" width="200"
                                   show-overflow-tooltip/>
                  <el-table-column label="正在核查（件）" align="center" prop="checkInProcessNumber" width="200"
                                   show-overflow-tooltip/>
                  <el-table-column label="完成核查（件）" align="center" prop="checkCompletedNumber" width="200"
                                   show-overflow-tooltip/>
                </el-table-column>
              </el-table-column>
            </el-table-column>
          </el-table-column>

          <el-table-column label="追责整改工作成效" show-overflow-tooltip>
            <el-table-column label="当年累计完成追责问题数量（件）" align="center" prop="totalCompletedProblemNumber" width="130"
                             show-overflow-tooltip/>
            <el-table-column label="当年累计追责总人数（人）" align="center" prop="totalAccountabilityPersonNumber" width="130"
                             show-overflow-tooltip/>
            <el-table-column label="其中" show-overflow-tooltip>
              <el-table-column label="中央企业负责人（人）" align="center" prop="enterpriseManagementNumber" width="130"
                               show-overflow-tooltip/>
              <el-table-column label="集团管理干部（人）" align="center" prop="groupManagementNumber" width="130"
                               show-overflow-tooltip/>
              <el-table-column label="子企业管理干部（人）" align="center" prop="subManagementNumber" width="130"
                               show-overflow-tooltip/>
            </el-table-column>
            <el-table-column label="当年累计追责总人次（人次）" align="center" prop="totalAccountabilityPersonTime" width="130"
                             show-overflow-tooltip/>
            <el-table-column label="其中" show-overflow-tooltip>
              <el-table-column label="组织处理（人次）" align="center" prop="orgHandleTime" width="130" show-overflow-tooltip/>
              <el-table-column label="扣减薪酬（人次）" align="center" prop="deductionSalaryTime" width="130"
                               show-overflow-tooltip/>
              <el-table-column label="党纪处分（人次）" align="center" prop="partyPunishmentTime" width="130"
                               show-overflow-tooltip/>
              <el-table-column label="政务处分（人次）" align="center" prop="governmentPunishmentTime" width="130"
                               show-overflow-tooltip/>
              <el-table-column label="禁入限制（人次）" align="center" prop="prohibitTime" width="130" show-overflow-tooltip/>
              <el-table-column label="移送监察机关或司法机关（人次）" align="center" prop="transferAuthorityTime" width="130"
                               show-overflow-tooltip/>
              <el-table-column label="其他（人次）" align="center" prop="accountabilityOtherTime" width="130"
                               show-overflow-tooltip/>
            </el-table-column>
            <el-table-column label="当年累计扣减薪酬金额（万元）" prop="totalDeductionSalary" align="right" width="130"
                             show-overflow-tooltip>
              <template slot-scope="scope">
                {{ scope.row.totalDeductionSalary | filterNum }}
              </template>
            </el-table-column>
            <el-table-column label="责任约谈" show-overflow-tooltip>
              <el-table-column label="当年累计责任约谈次数（次）" align="center" prop="dutyInterviewNumber" width="130"
                               show-overflow-tooltip/>
              <el-table-column label="当年累计责任约谈总人次（人次）" align="center" prop="dutyInterviewPersonTime" width="130"
                               show-overflow-tooltip/>
            </el-table-column>
            <el-table-column label="当年累计挽回资产损失（万元）" prop="totalRetrieveLossAmount" align="right" width="130"
                             show-overflow-tooltip>
              <template slot-scope="scope">
                {{ scope.row.totalRetrieveLossAmount | filterNum }}
              </template>
            </el-table-column>
            <el-table-column label="当年累计降低损失风险（万元）" prop="totalReduceLossRisk" align="right" width="130"
                             show-overflow-tooltip>
              <template slot-scope="scope">
                {{ scope.row.totalReduceLossRisk | filterNum }}
              </template>
            </el-table-column>
            <el-table-column label="当年累计制修订管理制度（项）" align="center" prop="totalPerfectSystemNumber" width="130"
                             show-overflow-tooltip/>
            <el-table-column label="其他工作成效" align="center" prop="otherAchievement" width="300" show-overflow-tooltip/>
          </el-table-column>


          <el-table-column label="备注" align="left" prop="remark" width="200" show-overflow-tooltip/>
          <el-table-column label="追责部门填报人" align="center" prop="informantName" width="130" show-overflow-tooltip/>
          <el-table-column label="联系电话" align="center" prop="informantPhone" width="130" show-overflow-tooltip/>

        </el-table>
        <pagination
          v-show="total>0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="queryQuarterReportProvData"
        />
      </BlockCard>

      <div slot="footer">
        <!--        <el-button size="mini" type="primary"  plain>保存</el-button>-->
        <!--        <el-button size="mini" type="primary" >提交</el-button>-->

        <el-button size="mini" type="primary" @click="initiateReportingFun" v-show="subordinateReportType=='1'">发起上报
        </el-button>
        <el-button size="mini" @click="close" v-show="subordinateReportType=='0'||subordinateReportType=='1'">取消
        </el-button>
        <el-button size="mini" @click="subordinateReportClick('0')"
                   v-show="subordinateReportType!='0'&&subordinateReportType!='1'">返回
        </el-button>
      </div>
    </el-dialog>
    <el-dialog
      v-if="memberOpen"
      :visible.sync="memberOpen"
      width="80%"
      class="changePeoples"
      title="上报单位接口人"
      append-to-body
    >
      <personTree :closeBtn="cancelPeople" :rowData="params"/>
    </el-dialog>
  </div>
</template>

<script>
import BlockCard from '@/components/BlockCard';
import {queryQuarterReportFileList, queryQuarterReportInfo} from '@/api/quarterly-report'
import personTree from '@/views/quarterlyReport/subordinateReport/components/personTree' // 上报单位接口人
import {
  saveAndStartProcess,
  queryQuarterReportProvSummary,
  regularReportUrge,
  regularReportUrgeAll

  ,queryReportConfirmList
  ,reportLowerUnitValidateInfo
} from '@/api/quarterly-report/subordinateReport'

import {
  queryProvList,
  getReportUnitUserData
} from "@/api/quarterly-report/view";

export default {
  name: "specialReportEdit",
  components: {BlockCard, personTree},
  props: {},
  dicts: ['REPORT_QUARTER'],
  data() {
    return {
      reportQuarterList:[],
      openLoading: {},
      id: '',
      //上报单位接口人信息
      memberOpen: false,
      params: {},


      loading: false,
      visible: false,
      infoData: {},
      filesData: [],
      tableLoading: false,
      // areaSelect: [],//查询下级单位
      // areaProgress: [],//进行中
      // areaCompleted: [],//已完成
      //已完成
      ywcList: [],
      // 进行中
      jxzList: [],
      //未提交
      wtjList: [],
      wtjCount: "",
      jxzCount: "",
      ywcCount: "",
      subordinateReportType: '0',//0已发起下级单位上报 1：未发起下级单位上报 2：下级单位上报情况（进行中）3：下级单位上报情况（已完成）
      tableList1: [],//下级单位上报确认 表格
      tableList2: [],//下级单位上报(进行中) 表格
      selection: [],//需要提醒的数据
      tableList3: [],//下级单位上报(已完成) 表格
      //参数
      queryParams: {
        pageNum: 1,
        pageSize: 10
      },
      queryData: {},
      total: 0,
      reportYear: '',//导出附件使用
      reportQuarterName: '',//导出附件使用
      reportProvCode:'',//下级单位所属省分
      reportLowerFlag:'1',//是否发起下级单位上报
    }
  }
  , created() {
  }
  , methods: {
    // 显示弹框
    open(id) {
      this.id = id;
      this.queryQuarterReportInfo()
      this.visible = true;
    },
    //关闭弹窗
    close() {
      this.visible = false;
      this.$emit("close",'edit');
    },
    //打开loading
    getLoading() {
      this.openLoading = this.$loading({
        lock: true,//lock的修改符--默认是false
        text: '提交中',//显示在加载图标下方的加载文案
        spinner: 'el-icon-loading',//自定义加载图标类名
        background: 'rgba(0, 0, 0, 0.7)',//遮罩层颜色
        target: document.querySelector('#table')//loadin覆盖的dom元素节点
      });
    },
    //关闭loading
    closeLoading() {
      this.openLoading.close();
    },
    //查询上报内容
    queryQuarterReportInfo() {
      let params = {
        reportProvId: this.id,
        operationType: 'edit'
      }
      queryQuarterReportInfo(params).then((response) => {
        this.infoData = response.data.reportProvInfo;
        this.reportProvCode = this.infoData.reportUnitCode;
        this.reportLowerFlag = this.infoData.reportLowerFlag;
        if(this.reportLowerFlag != '1'){
          //未发起下级单位上报
          this.subordinateReportType = '1';
          //下级单位发起确认列表
          this.getunitData();
        }else{
          //已发起下级单位上报
          this.subordinateReportType = '0';
          //下级单位上报情况查询
          this.areaSelectData();
        }
        //查询附件列表
        this.queryFileList();
      })
    },
    //查询附件列表
    queryFileList() {
      this.tableLoading = true
      queryQuarterReportFileList(this.infoData.id).then((response) => {
        this.filesData = response.data;
        this.tableLoading = false
      })
    },
    /**下载文件*/
    fileDownload(obj) {
      this.download(
        "/sys/attachment/downloadSysAttachment/" + obj.attachmentId,
        {},
        obj.fileName
      );
    },
    //模块切换
    subordinateReportClick(type) {
      this.subordinateReportType = type;
      if (this.subordinateReportType == '0') {//返回下级单位上报情况
        this.areaSelectData();
      } else if (this.subordinateReportType == '1') {//下级单位上报确认
        this.getunitData();
      } else if (this.subordinateReportType == '2') {//下级单位上报情况（进行中）
        this.selectReportDetail();
      } else if (this.subordinateReportType == '3') {//下级单位上报情况（已完成）
        this.queryParams.pageNum = 1;
        this.queryQuarterReportProvData();
      }
    },
    /****************下级单位上报情况--开始**************/
    // 下级单位上报情况 接口areaSelectList查询areaSelect areaProgress areaCompleted列表
    areaSelectData() {
      let params = {
        quarterAreaProvId  : this.id,
        reportUnitCode:this.reportProvCode
      }
      queryProvList(params).then((response)=>{

        const sumProvData = response.data;
        sumProvData.forEach((ele) => {
          if (ele.procStatus === "0") {
            this.wtjList = ele.unitNames.split(",");
            this.wtjCount = ele.provCount;
          }
          if (ele.procStatus === "1") {
            this.jxzList = ele.unitNames.split(",");
            this.jxzCount = ele.provCount;
          }
          if (ele.procStatus === "2") {
            this.ywcList = ele.unitNames.split(",");
            this.ywcCount = ele.provCount;
          }
        });

      // this.areaSelect = response.data.areaSelect ? response.data.areaSelect : [];//未选择
      // this.areaProgress = response.data.areaProgress ? response.data.areaProgress : [];//进行中
      // this.areaCompleted = response.data.areaCompleted ? response.data.areaCompleted : [];//已完成
      // if (this.areaProgress.length == 0 && this.areaCompleted.length == 0) {//从未发起过
      //   this.subordinateReportType = '1';
      //   this.getunitData();
      // } else {//已发起过
      //   this.subordinateReportType = '0';
      // }
      })
    },

    //上报单位确认按钮点击
    areaSelectSaveFun() {
      if (this.checkboxGroup.length == 0) {
        this.$message.error("请选择需要上报的单位");
        return false;
      }
      this.subordinateReportClick('1');
    },

    /****************下级单位上报情况--结束**************/

    /****************下级单位上报确认--开始**************/
    //下级单位上报情况初始化查询列表
    getunitData() {
      let params = {
        reportProvId: this.id,
        reportProvCode: this.reportProvCode
      }
      queryReportConfirmList(params).then((response)=>{
        this.tableList1 = response.data;
      })
    },

    //选择上报单位接口人
    clickPostName(data) {
      this.memberOpen = true;
      this.params.id = data.id;
      this.params.reportUnitCode = data.reportUnitCode;
    },

    //保存上报单位接口人
    toSavePersonById() {
      //获取选中信息
      this.$refs.regularPersonTree.list();
    },

    // 人员选择完成
    cancelPeople(detail) {
      this.memberOpen = false;
      //刷新上报单位列表
      this.getunitData();
    },

    //发起上报按钮
    initiateReportingFun() {
      if (this.tableList1.length == 0) {
        this.$message.error("请选择需要上报的单位");
        return false;
      }
      // 发起上报按钮 提示
      this.$confirm('是否确认发起上报？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        //发起前校验
        let params = {
          reportProvId: this.id,
          reportProvCode: this.reportProvCode
        }
        reportLowerUnitValidateInfo(params).then(res => {
          if(res.code == 200){
            this.getLoading()
            saveAndStartProcess({
              reportProvId: this.id,
              reportProvCode: this.reportProvCode
            }).then((response) => {
              if (response.code == 200) {
                this.$message({message: '发起上报成功', type: 'success'});
                //发起上报成功 关闭当前页面
                this.closeLoading()
                this.close()
              }
            }).catch(() => {
              this.closeLoading()
            })
          }else{
            this.$message.error(res.msg);
          }
        })
      }).catch(() => {
      });
    },
    /****************下级单位上报确认--结束**************/


    /****************下级单位上报情况（进行中）--开始**************/
    //下级单位上报情况（进行中） 列表
    selectReportDetail() {
      this.selection = [];
      let params = {
        quarterAreaProvId : this.id,
        status : '1'
      }
      getReportUnitUserData(params).then(response => {
        this.tableList2 = response.rows;
       })
    },

    //进行中表格选中数据
    handleSelectionChange(selection) {
      this.selection = selection;
    },
    //批量催办
    urgeAll() {
      let idList = [];
      for (let i = 0; i < this.selection.length; i++) {
        idList.push(this.selection[i].reportUnitId);
      }
      if (idList.length == 0) {
        this.$modal.msgError("请选择需要催办的上报单位！");
        return false;
      }
      this.$modal.confirm("批量催办？").then(() => {
        regularReportUrgeAll({regularReportId: this.id, list: idList}).then(
          response => {
            if (response.code === 200) {
              this.$modal.msgSuccess("催办成功！");
              this.selectReportDetail();
            } else {
              this.$modal.msgError(response.msg);
            }
          }
        )
      })
    },

    //单条催办
    toRemind(data) {
      this.$modal.confirm("是否催办该单位？").then(() => {
        regularReportUrge(this.regularReportId, data.reportUnitId).then(
          response => {
            if (response.code === 200) {
              this.$modal.msgSuccess("催办成功！");
              this.queryList();
            } else {
              this.$modal.msgError(response, msg);
            }
          }
        )
      })
    },
    /****************下级单位上报情况（进行中）--结束**************/

    /****************下级单位上报情况（已完成）--开始**************/
    queryQuarterReportProvData() {
      this.loading = true;
      let params = {
        quarterAreaProvId : this.id,
        status : '2'
      }
      getReportUnitUserData(params).then(response => {
        this.tableList3 = response.rows;
        this.loading = false;
        this.total = response.total;
      })
      // queryQuarterReportProvSummary({id: this.id}, this.queryParams).then(response => {
      //   this.tableList3 = response.rows;
      //   this.loading = false;
      //   this.total = response.total;
      // })
    },
    //导出
    detailedExport() {
      this.download(
        "/quarterReport/getSumDataDetailExport",
        {quarterReportId: this.quarterReportId},
        this.getFileName()
      );
    },
    getFileName() {
      // 时间戳
      let date = new Date().getTime()
      let filename = '下级单位上报情况' + date + '.xlsx'
      return filename;
    },
    /****************下级单位上报情况（已完成）--结束**************/
  }
}
</script>

<style lang="scss" scoped>
.subordinateReport {
  .margin-top-8 {
    margin-top: 8px;
  }

  ::v-deep .el-dialog__body {
    height: 70vh;
    padding: 10px;
    overflow: auto;
  }

  .sub-report-box {
    display: inline-block;
    width: 100%;
    .sub-report-detail {
      margin-bottom:10px;
      display: flex;
      justify-content: left;
      flex-wrap: wrap;
      .sub-report-type{
        text-align: center;
        width: 70px;
        height: 35px;
        font-size: 12px;
        border:1px solid #ddd;
        line-height: 35px;
        border-radius: 4px 0 0 4px;
      }

      .sub-report-num{
        text-align: center;
        width: 70px;
        height: 35px;
        font-size: 12px;
        border:1px solid #ddd;
        line-height: 35px;
        background-color: #f5f8fc;
      }
      .border-right{
        border-radius: 0 4px 4px 0;
        border-right: 1px solid #ddd;
      }

        .sub-report-li{
          text-align: center;
          padding:0 6px;
          min-width: 70px;
          height: 35px;
          font-size: 12px;
          border:1px solid #ddd;
          line-height: 35px;
        }

      .sub-report-core{
        cursor: pointer;
        text-align: center;
        width: 70px;
        color:#f5222d;
        height: 35px;
        font-size: 12px;
        border:1px solid #ddd;
        line-height: 35px;
        border-radius: 0 4px 4px 0;
      }
    }
  }
}
</style>
