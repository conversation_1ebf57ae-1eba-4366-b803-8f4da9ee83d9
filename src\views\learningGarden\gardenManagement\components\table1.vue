
<!-- 政策理论 -->
<template>
  <div class="wai-container">
    <div class="layui-row width height">
      <el-col class="height" style="border-left:1px solid #eee">
        <div class="common-in-box new-common-in-box">
          <div class="common-in-box-content">
            <div class="top-search" style="margin-bottom: 10px">
              <el-col :span="6" class="height">
                <div class="layui-form">
                  <div class="layui-form-left">文章标题</div>
                  <el-input
                    size="mini"
                    placeholder="请输入文章标题"
                    v-model="searchData.title"
                    clearable
                  ></el-input>
                </div>
              </el-col>

              <el-col :span="6" class="height">
                <div class="layui-form">
                  <div class="layui-form-left">来源站点</div>
                  <el-select v-model="searchData.siteCode" placeholder="请选择" @change="siteChange" clearable>
                    <el-option
                      v-for="(item, index) in siteCodeOption"
                      :key="index"
                      :label="item.siteName"
                      :value="item.siteCode"
                    ></el-option>
                  </el-select>
                </div>
              </el-col>

              <el-col :span="6" class="height">
                <div class="layui-form">
                  <div class="layui-form-left">所属模块</div>

                  <el-select
                    v-model="searchData.moduleId"
                    placeholder="请选择"
                    clearable
                  >
                    <el-option
                      v-for="(item, index) in moduleTreeNodeOption"
                      :key="index"
                      :label="item.moduleName"
                      :value="item.id"
                    ></el-option>
                  </el-select>
                </div>
              </el-col>
              <el-col :span="6" class="height float-right text-right">
                <el-button type="primary" icon="el-icon-search" size="mini" @click="getArticleList">搜索</el-button>
                <el-button type="primary" icon="el-icon-s-promotion" size="mini" @click="batchPublishArticle">批量发布</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="clearInfo">重置</el-button>
              </el-col>
            </div>
            <div>
              <el-col  class="height">
                <div class="layui-form">
                  <el-radio-group v-model="searchData.isPublish" @change="isPublishChange">
                    <el-radio-button  label="" size="mini" >全部</el-radio-button>
                    <el-radio-button  :label="1" size="mini" >已发布</el-radio-button>
                    <el-radio-button  :label="0" size="mini" >未发布</el-radio-button>
                  </el-radio-group>
                </div>
              </el-col>
            </div>

            <div class="tables tables_1">
              <el-table
                :data="tableData"
                border
                v-loading="tableLoading"
                style="width: 100%"
                @selection-change="handleSelectionChange"
              >
                <el-table-column
                  type="selection"
                  align="center"
                  width="55">
                </el-table-column>
                <el-table-column
                  type="index"
                  label="序号"
                  align="center"
                  sortable
                  width="80"
                  min-width="5%"
                  :index="table_index"
                />
                <el-table-column
                  label="文章标题"
                  prop="title"
                  width="350px"
                  min-width="35%"
                  align="left"
                >
                  <template slot-scope="scope">
                    <el-link
                      :underline="false"
                      type="primary"
                      @click="goToDetail(scope.row)"
                    >{{ scope.row.title }}</el-link>
                  </template>
                </el-table-column>
                <el-table-column
                  label="来源站点"
                  prop="siteCode"
                  width="300px"
                  min-width="10%"
                  align="center"
                >
                  <template slot-scope="scope">
                    {{
                      scope.row.siteCode
                        | fromatComon(siteCodeOption1)
                    }}
                  </template>
                </el-table-column>
                <el-table-column
                  label="所属模块"
                  width="100px"
                  align="center"
                  prop="moduleName"
                  min-width="10%"
                >
                </el-table-column>
                <el-table-column
                  label="来源模块"
                  prop="tree"
                  width="250px"
                  min-width="10%"
                  align="center"
                >
                </el-table-column>
                <el-table-column
                  label="文章发布时间"
                  prop="publishDate"
                  width="100px"
                  min-width="10%"
                  align="center"
                >
                </el-table-column>
                <el-table-column
                  label="是否发布"
                  prop="isPublish"
                  width="100px"
                  min-width="10%"
                  align="center"
                >
                  <template slot-scope="scope">
                    {{
                      scope.row.isPublish
                        | fromatComon(dict.type.IS_PUBLISH)
                    }}
                  </template>
                </el-table-column>
                <el-table-column
                  label="发布人"
                  prop="publishUserName"
                  width="100px"
                  min-width="10%"
                  align="center"
                >
                </el-table-column>
                <el-table-column
                  label="系统发布时间"
                  prop="publishTime"
                  width="200px"
                  min-width="10%"
                  align="center"
                >
                </el-table-column>
                <el-table-column
                  label="操作"
                  min-width="20%"
                  width="150px"
                  align="center"
                  class-name="small-padding fixed-width"
                  fixed="right"
                >
                  <template slot-scope="scope">
                    <el-button
                      v-if="scope.row.isPublish ==0"
                      size="mini"
                      type="text"
                      title="发布"
                      icon="el-icon-finished"
                      @click="publishArticle(scope.row,1)"
                    >
                    </el-button>

                    <el-button
                      v-if="scope.row.isPublish ==1"
                      size="mini"
                      type="text"
                      title="取消发布"
                      icon="el-icon-sort"
                      @click="publishArticle(scope.row,0)"
                    >
                    </el-button>
                    <el-button
                      size="mini"
                      type="text"
                      title="查看"
                      icon="el-icon-search"
                      @click="goToDetail(scope.row)"
                    >
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
              <pagination
                v-show="total > 0"
                :total="total"
                :page.sync="queryParams.pageNum"
                :limit.sync="queryParams.pageSize"
                @pagination="getArticleList"
              />
            </div>
          </div>
        </div>
      </el-col>
    </div>
    <!-- 集团-新增 -->

<!--    <el-dialog
      class="commons_popup"
      v-bind="$attrs"
      :visible.sync="addvisible"
      width="90%"
      title="新增"
      v-if="addvisible"
      append-to-body
    >
      <div slot="title" class="el-popup-header-title">
        <svg-icon icon-class="edit_file" />
        <span class="el-dialog-header-name">{{ "新增" }}</span>
      </div>

     <addProvince editType="add" :closeBtn="closeBtnAdd" editUnitType="group"/>
    </el-dialog>-->


  </div>
</template>

<script>

import { isExternalUrlOpen } from '@/utils/index'
import {
  batchPublishArticle,
  getArticleList,
  getModuleTreeNodeVo,
  getSiteData,
  publishArticle
} from "@/api/learningGarden/gardenManagement/gardenManagement";
export default {
  name: "articleList",
  components: {

  },
  dicts: ["IS_PUBLISH"],
  data() {
    return {
      open:false,//是否能打开外链
      //右侧查询条件
      searchData: {
        siteCode:'',
        title:'',
        moduleId:'',
        isPublish:''
      },
      radio: '',
      siteCodeOption:[],
      siteCodeOption1:[],
      moduleTreeNodeOption:[],
      multipleSelection: [], // 存储选中的行
      //表格数据
      tableLoading: false, //表格loading
      tableData: [],
      //表格页码
      total: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
    };
  },
  created() {
    this.getSiteData()
    this.getArticleList()
    // 判断
    isExternalUrlOpen('http://www.sasac.gov.cn').then((res)=>{
      this.open = true;
    }).catch((res)=>{
    })
  },
  methods: {
    goToDetail(row){
      if(this.open&&row.url){
        this.openNewTab(row.url)
      }else{
        this.$message.error('该网址为外网地址，请确认是否联网。');
      }
    },
    //打开页签
    openNewTab (url) {
      window.open(url)
    },
    isPublishChange(val){
      this.getArticleList()
    },
    handleSelectionChange(val) {
      this.multipleSelection = val; // 当表格的选项发生变化时，更新选中项数组
    },
    siteChange(value){
      this.searchData.moduleId = ''
      if (value){
        this.getModuleTreeNodeVo(value)
      }
    },
    batchPublishArticle(){
      if (this.multipleSelection.length == 0){
        this.$message.error("请选择要批量发布的文章！")
        return false;
      }
      let lists = []
      let isJump = false
      this.multipleSelection.forEach((item) => {
        if (item.isPublish == 1){
          isJump = true
        }
        let article = {
          articleId: item.id,
          isPublish: 1,
          publishId: item.publishId
        }
        lists.push(article)
      })
      if (isJump){
        this.$message.error("部分文章已发布，请重新选择！")
        return false;
      }
      this.$confirm('确认发布这'+lists.length+'篇文章吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'})
        .then(() => {
          batchPublishArticle(lists).then((response) => {
            if (response.code === 200) {
              this.$message.success(response.msg)
              this.getArticleList()
            }else {
              this.$message.error(response.msg)
            }
          })
        })
    },
    getSiteData(){
      getSiteData().then((response) => {
        if (response.code === 200) {
          this.siteCodeOption = response.data
          for (let i = 0; i < this.siteCodeOption.length; i++){
            this.siteCodeOption1.push({label: this.siteCodeOption[i].siteName,value: this.siteCodeOption[i].siteCode})
          }
        }else {
          this.$message.error(response.msg)
        }
      })
    },
    getModuleTreeNodeVo(siteCode){
      getModuleTreeNodeVo({siteCode: siteCode}).then((response) => {
        if (response.code === 200) {
          this.moduleTreeNodeOption = response.data
        }else {
          this.$message.error(response.msg)
        }
      })
    },
    getArticleList(){
      getArticleList(this.queryParams,this.searchData).then((response) => {
        if (response.code === 200) {
          this.tableData = response.rows
          this.total = response.total
        }else {
          this.$message.error(response.msg)
        }
      })
    },
    publishArticle(row, type){
      //type为1为发布，type为0为取消发布
      var data = {
        "articleId": row.id,
        "isPublish": type,
        "publishId": row.publishId
      }
      let msg = type == 1 ? '发布' : '取消发布'
      this.$confirm('确认'+msg+'该文章吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'})
        .then(() => {
          publishArticle(data).then((response) => {
            if (response.code === 200) {
              this.$message.success(response.msg)
              this.getArticleList()
            } else {
              this.$message.error(response.msg)
            }
          })
        })
    },
    table_index(index) {
      return (this.queryParams.pageNum - 1) * this.queryParams.pageSize + index + 1
    },
    //重置
    clearInfo() {
      this.searchData = {
        siteCode:'',
        title:'',
        moduleId:'',
        isPublish:''
      }
      this.getArticleList();
    },

  },
};
</script>
<style rel="stylesheet/scss"  lang="scss">
@import "~@/assets/styles/quarterly-report/index.css";
</style>







