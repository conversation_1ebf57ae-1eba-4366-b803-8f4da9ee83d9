<!--基础数据查询-->
<template>
    <div id="basequery" class="padding10_20">
        <el-tabs v-model="activeName" @tab-click="handleClick">
            <el-tab-pane label="企业基本信息" name="area"><areaList v-if="activeName=='area'"></areaList></el-tab-pane>
            <el-tab-pane label="企业联系人" name="person"><personList v-if="activeName=='person'"></personList></el-tab-pane>
            <el-tab-pane label="企业规章制度" name="law"><lawList v-if="activeName=='law'"></lawList></el-tab-pane>
            <el-tab-pane label="禁入限制人员" name="limit"><limitPersonList v-if="activeName=='limit'"></limitPersonList></el-tab-pane>
            <el-tab-pane label="发件箱" name="sendBox"><sendBoxList v-if="activeName=='sendBox'"></sendBoxList></el-tab-pane>
        </el-tabs>
    </div>
</template>

<script>
  import areaList from "./area/areaList";
  import lawList from "./law/lawList";
  import personList from "./person/personList";
  import limitPersonList from "./limitPerson/limitPersonList";
  import sendBoxList from "./sendBox/sendBoxList";

  export default {
    name: "Basequery",
    components: { areaList, lawList, personList, limitPersonList, sendBoxList },
    data() {
        return {
            activeName: 'area',
        }
    },
    methods: {
        handleClick(tab, event) {
            this.activeName = tab.name
        },
    },
  };
</script>
<style rel="stylesheet/scss" lang="scss">
</style>







