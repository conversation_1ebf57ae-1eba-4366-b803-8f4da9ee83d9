<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="insId" prop="insId">
        <el-input
          v-model="queryParams.insId"
          placeholder="流程实例id"
          clearable
          size="small"
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="登录名" prop="loginName">
        <el-input
          v-model="queryParams.loginName"
          placeholder="流程中止人登录名"
          clearable
          size="small"
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button size="small" type="primary" @click="submitFill">确定</el-button>
      </el-form-item>
    </el-form>

  </div>
</template>

<script>
  import { breakProcess} from "@/api/system/workflow/index";

  export default {
    name: "WorkFlow",
    data() {
      return {
        // 显示搜索条件
        showSearch: true,
        // 查询参数
        queryParams: {
          insId: undefined,
          loginName:undefined
        },

        // 表单参数
        form: {},
      };
    },
    methods: {
      /**  */
      breakProcess() {
        breakProcess(this.queryParams).then(res => {
          this.$modal.msgSuccess("流程中止成功");

        });
      },



      /** 搜索按钮操作 */
      submitFill() {
        if(!this.queryParams.insId){
          this.$message.error('流程实例Id为空')
        }
        if(!this.queryParams.loginName){
          this.$message.error('流程中止人登录名为空')
        }
        this.breakProcess();
      },



    }
  };
</script>
