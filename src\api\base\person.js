import request from '@/utils/request'

// 基础数据查询--企业联系人列表查询
export function getAreaPersonBaseInfoPage(query) {
  return request({
    url: '/colligate/baseInfo/getAreaPersonBaseInfoPage',
    method: 'post',
    data: query
  })
}


// 基础数据查询--企业联系人详情查询
export function getAreaPersonBaseInfoById(query) {
  return request({
    url: '/colligate/baseInfo/getAreaPersonBaseInfoById',
    method: 'post',
    data: query
  })
}

// 基础数据维护--查询最新版本的企业联系人信息，若有待提交的，则查询待提交的
export function getAreaPersonBaseInfo() {
  return request({
    url: '/colligate/baseInfo/getAreaPersonBaseInfo',
    method: 'post',
  })
}


// 基础数据维护--保存或提交
export function saveAreaPeraonBaseInfo(query) {
  return request({
    url: '/colligate/baseInfo/saveAreaPeraonBaseInfo',
    method: 'post',
    data: query
  })
}

// 查询组织树
export function getOrgTree(query) {
  return request({
    url: '/colligate/baseInfo/getOrgTree',
    method: 'post',
    data: query
  })
}

// 查询人员组织树
export function getOrgTreeWithPerson(query) {
  return request({
    url: '/colligate/baseInfo/getOrgTreeWithPerson',
    method: 'post',
    data: query
  })
}

//查询人员类型
export function queryPerSonType(data){
  return request({
    url: '/colligate/baseInfo/getPersonTypeList',
    method: 'post',
    data: JSON.stringify(data)
  })
}
