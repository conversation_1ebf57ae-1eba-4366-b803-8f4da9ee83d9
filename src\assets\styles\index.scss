@import './variables.scss';
@import './mixin.scss';
@import './transition.scss';
@import './element-ui.scss';
@import './sidebar.scss';
@import './btn.scss';

body {
  height: 100%;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Arial, sans-serif;
}

label {
  font-weight: 700;
}

html {
  height: 100%;
  box-sizing: border-box;
}

#app {
  height: 100%;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

.no-padding {
  padding: 0px !important;
}

.padding-content {
  padding: 4px 0;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

div:focus {
  outline: none;
}

.fr {
  float: right;
}

.fl {
  float: left;
}

.pr-5 {
  padding-right: 5px;
}

.pl-5 {
  padding-left: 5px;
}

.block {
  display: block;
}

.pointer {
  cursor: pointer;
}

.cursor {
  cursor: pointer;
}

.underline{
text-decoration: underline
}

.inlineBlock {
  display: block;
}

.clearfix {
  &:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: " ";
    clear: both;
    height: 0;
  }
}

aside {
  background: #eef1f6;
  padding: 8px 24px;
  margin-bottom: 20px;
  border-radius: 2px;
  display: block;
  line-height: 32px;
  font-size: 16px;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
  color: #2c3e50;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  a {
    color: #337ab7;
    cursor: pointer;

    &:hover {
      color: rgb(32, 160, 255);
    }
  }
}

//main-container全局样式
.app-container {
  padding: 20px;
}

.components-container {
  margin: 30px 50px;
  position: relative;
}

.pagination-container {
  margin-top: 30px;
}

.text-center {
  text-align: center !important;
}

.sub-navbar {
  height: 50px;
  line-height: 50px;
  position: relative;
  width: 100%;
  text-align: right;
  padding-right: 20px;
  transition: 600ms ease position;
  background: linear-gradient(90deg, rgba(32, 182, 249, 1) 0%, rgba(32, 182, 249, 1) 0%, rgba(33, 120, 241, 1) 100%, rgba(33, 120, 241, 1) 100%);

  .subtitle {
    font-size: 20px;
    color: #fff;
  }

  &.draft {
    background: #d0d0d0;
  }

  &.deleted {
    background: #d0d0d0;
  }
}

.link-type,
.link-type:focus {
  color: #337ab7;
  cursor: pointer;

  &:hover {
    color: rgb(32, 160, 255);
  }
}

.filter-container {
  padding-bottom: 10px;

  .filter-item {
    display: inline-block;
    vertical-align: middle;
    margin-bottom: 10px;
  }
}

.el-tree-node {
  min-width: max-content;
}

//refine vue-multiselect plugin
.multiselect {
  line-height: 16px;
}

.multiselect--active {
  z-index: 1000 !important;
}

.el-tree-node {
  min-width: max-content;
}

/*灰色背景*/
.grayBackground {
  box-sizing: border-box;
  min-height: calc(100vh - 129px);
  background: #EEE;
}
.height_vh_100{
  height: calc(100vh - 102px);
}

body, div, ul, ol, dl, dt, dd, li, dl, h1, h2, h3, h4, p {
  margin: 0;
  padding: 0;
}

ol, ul, li {
  list-style: none;
}

img {
  border: 0;
  vertical-align: middle;
}

.clear {
  clear: both;
  height: 1px;
  width: 100%;
  overflow: hidden;
  margin-top: -1px;
}
.font-weight{
  font-weight: bold;
}
.ovflowHidden {
  text-align: left;
  display: inline-block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  word-break: break-all;
  max-width: 100%;
  padding-right: 6px;
  box-sizing: border-box;
}

.table-text-left{
  display: inline-block;
  width: 100%;
  text-align: left;
}

//.el-table--scrollable-x .el-table__body-wrapper {
//  z-index: 2;
//}
//.el-table__fixed, .el-table__fixed-right{
//  z-index: 4;
//}
//.el-table__fixed-right {
//  height:auto !important;
//  bottom:17px !important;
//}

.border0 {
  border-width: 0 !important;
}

.bottom0 {
  margin-bottom: 0 !important;
}

.position {
  position: relative;
}

/*英文换行*/
.word-break-all {
  word-break: break-all;
}

/*文字不被选中*/
.noSelect {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/*便于标签识别换行情况*/
.white-space {
  white-space: pre-line;
}

.margin-t5{
  margin-top:5px;
}

.margin-l10{
  margin-left:10px;
}

.margin-t10{
  margin-top:10px;
}

.margin-b0{
  margin-bottom:0 !important;
}

.margin-b10{
  margin-bottom:10px !important;
}

.padding-left10{
  padding-left:10px;
  box-sizing: border-box;
}

.padding0{
  padding:0 !important;
}

.padding10{
  padding: 10px !important;
  box-sizing: border-box;
}

.padding10_20{
  padding: 10px 20px !important;
  box-sizing: border-box;
}

.padding10_0{
  padding:10px 0 !important;
}

.padding_b10{
  padding-bottom:10px;
}

.padding_b60{
  padding-bottom:60px;
}


.cursor {
  cursor: pointer;
}

.float-left {
  float: left;
}

.float-right {
  float: right;
}

.height100{
  height: 100%;
}

.flex {
  display: flex;
}

.flex-end {
  justify-content: flex-end;
}

.flex-between {
  justify-content: space-between;
}

.item-center {
  align-items: center;
}

.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.text-red{
  color:#f5222d;
}

.text-blue{
  color:#1890ff;
}

.box-sizing {
  box-sizing: border-box;
}

.padding4 {
  padding: 4px;
  box-sizing: border-box;
}

.modifyRecord {
  position: absolute;
  right: 15px;
  top: 38px;
  z-index: 9999;
}

.treeSelection{
  width: 100%;
  line-height: 30px;
  background: #E6F7FF;
  position: relative;
  color: #333333;
  margin-bottom: 12px;
  display: inline-block;
  padding: 4px 30px 4px 12px;
  border-radius: 2px;
  box-sizing: border-box;
  .icon {
    float: right;
    cursor: pointer;
    position: absolute;
    right: 8px;
    top: 6px;
    font-size: 16px;
  }
}

.base-warning{
  float: right;
  background: #FEF6F6;
  padding: 4px 10px;
  font-size: 14px;
  display: inline-block;
  .el-icon-info{
    color: #FBA651;
    margin-right: 8px
  }
}

.select-list{
  overflow: hidden;
  .list-li {
    background-color: #e6f7ff;
    color: #40a9ff;
    margin: 0 20px 4px 0;
    float: left;
    height: 30px;
    line-height: 30px;
    padding: 0 12px 0 12px;
    border-radius: 2px;
    .close {
      padding: 8px;
      cursor: pointer;
    }
  }
}

.text-center{
  text-align: center;
}
.border0{
  border-width: 0 !important;
}
.flex{
  display: flex;
}
.flex-1{
  flex:1
}
.align-items-center{
  align-items: center;
}
.flex-dir-col-jus-cen{
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.flex-dir-col-jus-cen-algin-center{
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.just-spc-between{
  justify-content: space-between;
}

.just-spc-aro{
  justify-content: space-around;
}
.just-con-cen{
  justify-content: center;
}

.no-drop{
  cursor: no-drop;
}

.width{
  width: 100%!important;
}
.width-50{
  width: 50%!important;
}
.height{
  height: 100%;
}
.relactive{
  position: relative;
}

.text-right {
  text-align: right;
}
.block{
  display: block;
}
.overflow-x-y{
  overflow: hidden;
  overflow-y: auto;

}
.border-0{
  border: 0px!important;
}

.ovflowHidden {
  text-align: left;
  display: inline-block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  word-break: break-all;
  max-width: 100%;
  padding-right: 0;
  box-sizing: border-box;
}
// 带阴影的块

.el-common-card{
    background-color: transparent;
    border-radius: 4px;
    box-shadow: 0px 0px 10px 0px rgb(155 11 9 / 10%);
}


// 带竖杠的标题
.el-common-header{
  padding: 0 20px;
  box-sizing: border-box;
}
.el-common-card-header{
  height: 45px;
    line-height: 45px;
    padding: 0 0 0 18px;
    font-size: 16px;
    position: relative;
    border-bottom: 1px solid #EEEEEE;
    text-align: center;
}

.el-common-card-header:before{
  position: absolute;
  content: '';
  height: 15px;
  left: 0;
  top: 15px;
  width: 5px;
  border-radius: 4px;
  background: #f5222d;
}

.el-card-header-title{
  font-size: 16px;
  display: inline-block;
  float: left;
  font-weight: bold;
}

.el-content{
  height: calc(100% - 45px);
  padding: 0 20px;
  box-sizing: border-box;
}


// 上面是数字 下面是文字

.el-row-one-num{
  padding: 0;
  width: 30%;
  position: relative;
}

.el-bottom-nums {
  font-size: 20px;
  font-family: Microsoft YaHei;
  font-weight: bold;
  color: #373d41;
  text-align: center;
}

.el-top-text {
  font-size: 14px;
  font-family: Microsoft YaHei;
  font-weight: 400;
  color: #73777a;
  text-align: center;
  white-space: nowrap;
  margin-top: 10px
}

.el-after-line::after{
  position: absolute;
  top: 0px !important;
  right: 0px !important;
  display: block;
  content: "";
  height: 100% !important;
  width: 1px !important;
  background-color: #d9d9d9 !important;
}
.one-after-line::after{

  position: absolute;
  top: 0px !important;
  right: 0px !important;
  display: block;
  content: "";
  height: 100% !important;
  width: 1px !important;
  background-color: #d9d9d9 !important;

}

//

.graphic_alphanumeric .one_graphic i{
  font-size: 14px;
  margin-right: 8px;
  color: #FD8487 !important;
}
.el-one_graphic_alphanumeric{
  padding: 0 10px;
}

.el-graphic_text{
  font-size: 14px;
  font-family: Microsoft YaHei;
  font-weight: 400;
  color: #73777a;
  margin-right: 15px;
  white-space: nowrap;
}
.el-graphic_num{
  font-size: 20px;
  font-family: Microsoft YaHei;
  font-weight: bold;
  color: #373d41;
}


//

.el-progress{
  border-radius: 4px;
}

.el-progress-big, .el-progress-big .el-progress-bar{
  height: 18px;
  line-height: 18px;
}


  .el-progress .el-progress-bar__outer{
    height: 18px!important;
    border-radius: 4px;
  }

  .el-progress .el-progress-bar__outer .el-progress-bar__inner {
    border-radius: 4px;
  }


  //
.el-one_graphic  i {
  font-size: 14px;
  margin-right: 8px;
  color: #FEC84C !important;
}
 .el-graphic_text {
  font-size: 14px;
  font-family: Microsoft YaHei;
  font-weight: 400;
  color: #73777a;
  margin-right: 15px;
  white-space: nowrap;
  }
  .el-graphic_num{
    font-size: 20px;
    font-family: Microsoft YaHei;
    font-weight: bold;
    color: #373d41;
  }

  // 滑动页签
.el-card-header-select {
  font-size: 16px;
  font-family: Microsoft YaHei;
  font-weight: 400;
  color: #73777a;
  min-width: 60px;
  display: inline-block;
  float: left;
  position: relative;
  margin-right: 40px;
  cursor: pointer;
}
  .el-common-card-headers{
    border-color: #d9d9d9;
    padding-bottom: 10px;
    padding: 0;
    height: 45px;
    line-height: 45px;
    font-size: 16px;
    position: relative;
    border-bottom: 1px solid #EEEEEE;
    text-align: center;
  }

  .el-card-header-select.active{
    font-size: 16px;
    font-family: Microsoft YaHei;
    font-weight: bold;
    color: #181818;
    min-width: 60px;
    display: inline-block;
    float: left;
    position: relative;
    margin-right: 40px;
    cursor: pointer
  }

  .el-card-header-select.active:before{
    position: absolute;
    content: '';
    bottom: 0;
    width: 60%;
    height: 6px;
    left: 20%;
    border-radius: 2px;
    background: #f5222d;
  }
  .el-common-card-content{
    height: calc(100% - 45px);
    padding: 15px;
    padding-bottom: 0px;
  }

  .el-form-x-y{
    position: absolute;
    z-index: 1002;
    top: 10px;
    left:20px;
  }




  .echars-right-block .titles{
    font-size: 18px;
    font-family: Microsoft YaHei;
    font-weight: bold;
    color: #373d41;
    margin-bottom: 20px;
  }

  .echars-right-block .echars-right-block_con{
    border: 1px solid #d9d9d9;
    background-color: #f7f8fa;
    border-radius: 0px 16px 16px 16px;
    height: 80%;
    width: 100%;
    padding: 30px 20px;
    box-sizing: border-box;
  }

  .echars-right-block .echars-right-block_con .one-echars-right-block i{
    color: #a9b0b4;
    font-size: 18px;
  }


  .echars-right-block .echars-right-block_con .one-echars-right-block span:last-child {
    font-size: 20px;
    font-family: Microsoft YaHei;
    font-weight: bold;
    color: #373d41;
  }

  .one-echars-right-block{
    height: 35px;
    line-height: 35px;
  }

  //

  .top-echars-con{
    height: 60%;
    width: 100%;
    display: flex;
    align-items: center;
  }

  .bottom-echars-con{
    height: 40%;
    width: 100%;
    padding-top: 15px;
    padding-left: 15px;

  }
.msg-item{
  line-height: 28px;
  display: block!important;
}

  //

  .echars-right-con .titles{
    font-size: 14px;
    font-family: Microsoft YaHei;
    font-weight: 400;
    color: #73777A;
    margin-bottom: 12px;
  }

  .echars-right-con .red-block{
    background: #FFF1F0;
    border: 1px solid #FC473C;
    border-radius: 5px 5px 5px 0px;
    height: 24px;
    line-height: 24px;
    width: 70px;
    text-align: center;
    margin-left: 30px;
    font-size: 14px;
    font-family: Microsoft YaHei;
    font-weight: 400;
    color: #FC473C;
  }

  .echars-right-con .black-num{
    font-family: Microsoft YaHei;
    font-weight: bold;
    color: #373D41;
    margin-bottom: 20px;
    font-size: 20px;
  }

  .scroll-box{
    width: 100%;
    height: 50px;
  }


  // 对齐的文字行
.el-alignment-row{
  height: 40px;
  line-height: 40px;
  border-bottom: 1px solid #f0f0f0;
}
  .el-left-point{
    width: 10px;
    height: 10px;
    background: #C5C5C5;
    border-radius: 50%;
    margin-right: 15px;
  }

  .el-mid-content{
    font-size: 14px;
    font-family: Source Han Sans CN;
    font-weight: 400;
    color: #555555;
    position: relative;
    padding-right: 8px;
    cursor: pointer;
    box-sizing: border-box;
  }

  .el-last-content .iconfont{
    font-size: 18px;
    color: #ff4d4e;
    margin-right: 8px;
  }

  .el-last-content-text{
    font-size: 14px;
    font-family: Source Han Sans CN;
    font-weight: 400;
    color: #73777a;
  }

  .el-last-2-content{
    margin-right: 8px;
  }

  .el-last-2-content .iconfont{
    font-size: 14px;
    margin-right: 8px;
  }

  .el-last-2-content  .el-last-2-content-text{
    font-size: 14px;
    font-family: Source Han Sans CN;
    font-weight: 400;
    color: #a9b0b4;
  }

  .el-one-top-img{
    display: flex;
    align-items: center;
    flex-direction: column;
  }
  .el-one-top-img.prohibit-btn{
    cursor: no-drop;
    opacity: 0.6;
  }
  .el-one-top-img img{
    width: 58px;
    height: 58px;
    margin-bottom: 8px;
  }

  .el-one-top-img-text{
    font-size: 14px;
    font-family: Microsoft YaHei;
    font-weight: 400;
    color: #595959;
    text-align: center;
  }



.custom_date_class span{

    background: #F7E6E4;
    color: #FF4B3B;
    border-radius: 50%;



}


.custom_date_class span:hover {
  background-color: #F7E6E4!important;
}


.custom_date_class-2 span{

  background: #E5EBF7;
  color: #2E74FF;
  border-radius: 50%;



}


.custom_date_class-2 span:hover {
  background: #E5EBF7!important;
}


//
.time-choose .one-time-choose {
  margin-right: 15px;
}
.time-choose .left-time-choose{
  font-size: 12px;
font-family: Microsoft YaHei;
font-weight: 400;
color: #A9B0B4;
margin-right: 5px;
}

.time-choose .one-time-choose .one-time-choose-bg{
  width: 15px;
  height: 15px;
  border-radius: 5px;
  margin-right: 5px;
  font-size: 12px;
  font-family: Microsoft YaHei;
  font-weight: 400;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4px;
}


.time-choose .one-time-choose .one-time-choose-tip{
  font-size: 12px;
  font-family: Microsoft YaHei;
  font-weight: 400;
  color: #73777A
}

//

.el-process_con_block {
  background: #f5f5f5;
  border-radius: 2px;
  margin-top: 8px;
  padding: 8px 12px;
}


.el-top-ciycle{
    width: 22px;
    height: 22px;
    background: #d9d9d9;
    border: 1px solid #d9d9d9;
    border-radius: 50%;
    font-size: 12px;
    font-family: Source Han Sans CN;
    font-weight: 400;
    color: #73777a;
}

.el-process_style_color_1{
  background: #58c22d !important;
  border-color: #58c22d !important;
  color: #FFFFFF!important;
}
.el-process_style_color_2 {
  background: #e2f0de !important;
  border-color: #58c22d !important;
  color: #5cc431 !important;
}
.el-process-point_style_color_2 {
  background:#58c22d !important;
  border-color: #58c22d !important;

}
.el-peocess-bottom-text{
  font-size: 12px;
  font-family: Source Han Sans CN;
  font-weight: 400;
  color: #a9b0b4;
  white-space: nowrap;
}

.el-process-points{
  width: 7px;
  height: 7px;
  background: #d9d9d9;
  border-radius: 50%;
  margin-left: 20px;
}

.el-one-process-diat:last-child .el-process-points{
  display: none;
}



// el-date 修改样式

// 修改 --- 固定列的遮挡底部滚动条

.el-table__fixed, .el-table__fixed-right{
  //height: auto!important;
  bottom: 17px;
  position: absolute;
  top: 0px;
}







// ********** 公共弹窗样式  **********//
.commons_popup .el-dialog__header {
  padding: 0px !important;
    border-bottom: 1px solid #eee;
}
.el-popup-header-title{
  padding-left: 27px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 100%;
  height: 45px;
  background: #f4f4f4;
  opacity: 1;
  border-radius: 4px 4px 0px 0px;
}
.el-popup-header-title svg{
  margin-right: 12px;
  color: #ff4d4e;
  width: 1.2em !important;
  height: 1.2em !important;
}
.el-dialog-header-name{
  font-size: 16px;
  font-family: PingFang SC;
  font-weight: bold;
  color:  #ff4d4e;
}

.bottom-btn{
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-right: 10px;
}
.left-empty{
  flex:1
}
.bottom-btn .one-cancenl-btn{
  height: 28px;
    background: #fff;
    border: 1px solid #376DEE;
    opacity: 1;
    border-radius: 2px;
    padding: 0px 10px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    cursor: pointer;
}

.bottom-btn .one-sure-btn{
  height: 28px;
  background: #ff4d4e;
  border: 1px solid  #ff4d4e;
  opacity: 1;
  border-radius: 2px;
  padding: 0px 10px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  margin-left: 10px;
  cursor: pointer;
}
.bottom-btn .el-icon-circle-close{
  font-size: 18px;
  margin-right: 5px;
  color:  #ff4d4e;
}
.bottom-btn .one-cancenl-btn-name{
  font-size: 12px;
    font-family: PingFang SC;
    font-weight: 400;
    color: #ff4d4e;
}
.bottom-btn .el-icon-s-order{
  font-size: 18px;
  margin-right: 5px;
  color: #fff;
}
.bottom-btn .el-icon-success{
  font-size: 18px;
  margin-right: 5px;
  color: #fff;
}
.bottom-btn .one-sure-btn-name{
  color: #fff;
  font-size: 12px;
  font-family: PingFang SC;
  font-weight: 400;
}
.commons_popup .el-dialog__body{
  padding: 0px 20px 15px 20px!important;
}

.common-in-box .el-table::before{
  height: 0px;
}
.common-in-box .el-table--border::after{
  height: 0px;
}

.report-list .el-scrollbar__wrap{
  overflow-x: hidden!important;
}
.el-table th.el-table__cell>.cell{
  text-align: center !important;
}
// ********** 公共弹窗样式 **********//

.radio-tree{
  .el-checkbox__inner{
    border-radius: 50% !important;
  }
}

.el-table th.el-table__cell.is-leaf {
  height: 48px;
  background: #F4F8FC;
}
::v-deep.el-radio,.el-radio{
  margin-right:20px !important;
}
.row:before, .row:after {
  content: '';
  display: block;
  clear: both;
}
.el-table .cell, .el-table--border .el-table__cell:first-child .cell {
  padding:0 10px;
}
.el-tooltip__popper{
  max-width: 95% !important;
}

.el-table  .el-table__fixed,
.el-table  .el-table__fixed-left, .el-table .el-table__fixed-right {
    pointer-events: none;
    td {
      pointer-events: auto;
    }
  }
.el-table th {
  pointer-events: auto;
}
