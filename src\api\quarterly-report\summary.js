import request from '@/utils/request'

export function queryQuarterReportProvInfo(data,param){
  return request({
    url: '/quarterReport/queryQuarterReportProvInfo',
    method: 'post'
    ,data
    ,params:param
  })
}

export function saveQuarterReportProvSummary(data){
  return request({
    url: '/quarterReport/saveQuarterReportProvSummary',
    method: 'post'
    ,data
  })
}
export function queryQuarterReportProvSummary(data,param){
  return request({
    url: '/quarterReport/queryQuarterReportProvSummary',
    method: 'post'
    ,data
    ,params:param
  })
}

//查询流程参数
export function flowParams() {
  return request({
    url: '/quarter/summary/flow/flowParams',
    method: 'post'
  })
}
