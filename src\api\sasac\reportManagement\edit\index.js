import request from '@/utils/request';

export function reportDetailBasicData(reportSasacId) {
  return request({
    url: '/colligate/violSasacReportManagement/reportDetailBasicData/' + reportSasacId,
    method: 'post'
  });
}

export function reportContentConfirm(data) {
  return request({
    url: '/colligate/violSasacReportManagement/reportContentConfirm',
    method: 'post',
    data: data
  });
}

export function validEachContentConfirmedStatus(reportSasacId) {
  return request({
    url: '/colligate/violSasacReportManagement/validEachContentConfirmedStatus/' + reportSasacId,
    method: 'post'
  });
}

export function deleteReportContent(data) {
  return request({
    url: '/colligate/violSasacReportManagement/deleteReportContent',
    method: 'post',
    data: data
  });
}

export function saveReportSasacRecord(data) {
  return request({
    url: '/colligate/violSasacReportManagement/saveReportSasacRecord',
    method: 'post',
    data: data
  });
}

export function reportStructuredDataPackage(reportSasacId) {
  return request({
    url: '/colligate/violCouncilIndex/reportIndexDbZip/' + reportSasacId,
    method: 'post'
  });
}

//查询流程参数
export function flowParams() {
  return request({
    url: '/sasac/flow/flowParams',
    method: 'post'
  })
}


export function cancelConfirmedStatus(data) {
  return request({
    url: '/colligate/violSasacReportManagement/cancelConfirmedStatus',
    method: 'post',
    data:data
  });
}







