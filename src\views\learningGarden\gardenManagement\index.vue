<!--园地管理-->
<template>
  <div id="basequery" class="padding10_20">
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane label="政策理论" name="firstYear"><tables1 v-if="activeName=='firstYear'"></tables1></el-tab-pane>
      <el-tab-pane label="信息发布" name="secondYear"><tables2 v-if="activeName=='secondYear'"></tables2></el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import tables1 from './components/table1'
import tables2 from './components/table2'
export default {
  name: "HistoryQuestion/index",
  components: { tables1,tables2 },
  data() {
    return {
      activeName: 'firstYear',
    }
  },
  methods: {
    handleClick(tab, event) {
      this.activeName = tab.name
    },
  },
};
</script>
<style rel="stylesheet/scss" lang="scss">
</style>

