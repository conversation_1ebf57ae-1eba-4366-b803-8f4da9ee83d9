#请根据注释修改，其余内容不修改
apiVersion: apps/v1
kind: Deployment
metadata:
  name: prosupervisionui        #[1]必选，Deployment模板名称，可填写服务名称
  namespace: jtauditpro
  labels:
    app: prosupervisionui       #[2] 必选，标签名，可填写服务名称
spec:
  replicas: 3
  revisionHistoryLimit: 5
  minReadySeconds: 5
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: prosupervisionui  #[3] 必选，标签名，可填写服务名称
  template:
    metadata:
      labels:
        app: prosupervisionui     #[4] 必填，pod标签名称，可填写服务名称
    spec:
      containers:
      - image: harbor.dcos.xixian.unicom.local/jtauditccr/nginx:latest   #[6] 镜像名称，请将“supervisionui”替换为服务名，其余不变
        name: prosupervisionui        #[5] 必选，容器名称，可填写服务名称
        volumeMounts:
        - name: nglog
          mountPath: /var/log/nginx
      volumes:
      - name: nglog
        persistentVolumeClaim:
          claimName: supervisionnglogpvc
