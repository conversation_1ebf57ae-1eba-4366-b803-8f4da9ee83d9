import request from '@/utils/request'


// 下一环节名称
export function flowParams(url) {
  return request({
    url: url+'/flowParams',
    method: 'post'
  })
}

// 下一环节名称 (公用)
export function processLinkData(processDefinitionKey,processData) {
  return request({
    url: '/workflowRestController/tasklinkforstart/'+processDefinitionKey,
    method: 'get',
    params: processData
  })
}

// 下一环节处理人
export function refreshNextAssignee(url,data) {
  return request({
    url: url+'/refreshNextAssignee',
    method: 'post',
    data: data
  })
}
// 通过或者退回下一环节名称
export function tasklink(data,processData) {
  return request({
    url: '/workflowRestController/tasklink/'+data.processInstanceId+'/'+data.linkKey+'/'+data.processDefinitionKey+'/'+data.flowKeyReV+'/'+data.handleType,
    method: 'get',
    params: processData
  })
}
// 退回下一环节处理人
export function backAssignee(data) {
  return request({
    url:'/workflowRestController/refreshBackAssignee',
    method: 'post',
    data: data
  })
}

// 转派下一环节处理人
export function refreshTurnAssignee(data) {
  return request({
    url: '/workflowRestController/refreshTurnAssignee',
    method: 'post',
    data: data
  })
}


// 流程启动并送审
export function startAndSubmitProcess(url,data) {
  return request({
    url: url+'/startAndSubmitProcess',
    method: 'post',
    data: data
  })
}

// 流程推进
export function pushProcess(url,data) {
  return request({
    url: url+'/pushProcess',
    method: 'post',
    data: data
  })
}

// 退回
export function backProcess(url,data) {
  return request({
    url: url+'/backProcess',
    method: 'post',
    data: data
  })
}

// 中止
export function breakProcess(url,data) {
  return request({
    url: url+'/breakProcess',
    method: 'post',
    data: data
  })
}

// 转派
export function transferProcess(url,data) {
  return request({
    url: url+'/transferProcess',
    method: 'post',
    data: data
  })
}

// 已办撤回
export function withdrawProcess(data) {
  return request({
    url: '/workflowRestController/withdrawProcess',
    method: 'post',
    data: data
  })
}

// 根据所在环节查询需展现的自定义标签
export function taburls(processDefinitionId,taskDefinitionKey,tabFlag) {
  return request({
    url: '/workflowRestController/taburls/'+processDefinitionId+'/'+taskDefinitionKey+'/'+tabFlag,
    method: 'get'
  })
}

// 获取已办页面业务主页面及参数
export function taskhasdonepath(data) {
  return request({
    url: '/workflowRestController/taskhasdonepath/'+data.processInstanceId+'/'+data.linkKey+'/'+data.taskId+'/'+data.typeId,
    method: 'get'
  })
}

// 获取流程图地址
export function flowChatData(data) {
  return request({
    url: '/workflowRestController/getProcessChartByProcInstId/'+data.processInstanceId,
    method: 'get'
  })
}
