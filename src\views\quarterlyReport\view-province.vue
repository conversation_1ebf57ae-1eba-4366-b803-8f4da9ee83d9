<!-- 季度报告--查看--省分 -->

<template>
  <div class="wai-container" style="background-color: #fff">
    <div class="layui-row width height">
      <div class="width height">
        <div class="common-wai-box" style="height: 100%">
          <div class="common-in-box" style="height: auto; min-height: 100%">
            <div class="common-in-box-header">
              <div class="common-in-box-header-line"></div>
              <div class="common-in-box-header-text">基本信息</div>
              <div class="flex-1"></div>
              <el-button  type="primary" size="mini" @click="detailedExport()">明细导出</el-button>


            </div>

            <div class="common-in-box-content">
              <div class="top-search">
                <el-col :span="8" class="height">
                  <div class="layui-form">
                    <div class="layui-form-left">上报年度</div>

                    <div class="layui-form-value">{{ infoData.reportYear }}</div>
                  </div>
                </el-col>

                <el-col :span="8" class="height">
                  <div class="layui-form">
                    <div class="layui-form-left">上报季度</div>
                    <div class="layui-form-value">
                      {{
                        infoData.reportQuarter
                          | fromatComon(dict.type.REPORT_QUARTER)
                      }}
                    </div>
                  </div>
                </el-col>

                <el-col :span="8" class="height">
                  <div class="layui-form">
                    <div class="layui-form-left">上报截止日期</div>

                    <div class="layui-form-value">
                      <el-date-picker
                        format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd"
                        v-model="infoData.reportCloseTime"
                        type="datetime"
                        placeholder=""
                        readonly
                      >
                      </el-date-picker>
                    </div>
                  </div>
                </el-col>
              </div>

              <div class="top-search">
                <div class="layui-col-md12 layui-col-sm12 layui-col-lg12">
                  <div class="layui-form">
                    <div class="layui-form-left">上报标题</div>

                    <div class="layui-form-value">
                      {{ infoData.reportTitle }}
                    </div>
                  </div>
                </div>
              </div>

              <div class="top-search">
                <div class="layui-col-md12 layui-col-sm12 layui-col-lg12">
                  <div class="layui-form">
                    <div class="layui-form-left">上报要求</div>
                    <div class="layui-form-value" id="uploadAsk">
                      {{ infoData.reportRequire }}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div class="common-in-box-header" style="margin-top: 10px">
              <div class="common-in-box-header-line"></div>
              <div class="common-in-box-header-text">附件列表</div>
            </div>

            <div class="tables tables_1">
              <el-table
                :data="filesData"
                border
                v-loading="tableLoading"
                style="width: 100%"
              >
                <el-table-column
                  label="序号"
                  type="index"
                  min-width="5%"
                  align="center"
                />
                <el-table-column label="文件名" prop="fileName" min-width="50%">
                  <template slot-scope="scope">
                    <div
                      style="text-align: left"
                      class="overflowHidden-1"
                      :title="scope.row.fileName"
                    >
                      {{ scope.row.fileName || "" }}
                    </div>
                  </template>
                </el-table-column>
                <el-table-column
                  label="上传人"
                  prop="createUserName"
                  min-width="10%"
                />
                <el-table-column
                  label="上传时间"
                  prop="createTime"
                  min-width="20%"
                />

                <el-table-column
                  label="操作"
                  fixed="right"
                  align="center"
                  class-name="small-padding fixed-width"
                >
                  <template slot-scope="scope">
                    <el-button
                      size="mini"
                      type="text"
                      title="下载"
                      icon="el-icon-bottom"
                      @click="fileDownload(scope.row)"
                    ></el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>

            <div class="common-in-box-header" style="margin-top: 20px">
              <div class="common-in-box-header-line"></div>
              <div class="common-in-box-header-text">本单位汇总信息</div>
              <div class="common-in-box-header-right">
                <el-button plain type="primary" icon="el-icon-tickets" v-show="showType!='area'" size="mini" @click="subordinateReport">下级单位数据上报</el-button>
              </div>
            </div>

            <div
              class="common-in-box-header"
              style="margin-top: 10px; border: 0px; padding-left: 10px"
            >
              <div class="common-in-box-header-text">工作部署情况</div>
            </div>

            <div class="common-in-box-content">
              <div class="top-search">
                <el-col :span="8" class="height">
                  <div class="layui-form">
                    <div class="layui-form-left width-label-2">
                      本季度召开领导小组会议（次）
                    </div>
                    <div class="layui-form-value">{{ infoData.quarterTeamMeetingTime||'0' }}</div>
                  </div>
                </el-col>

                <el-col :span="8" class="height">
                  <div class="layui-form">
                    <div class="layui-form-left width-label-2">
                      本季度召开领导小组办公室会议（次）
                    </div>
                    <div class="layui-form-value">{{ infoData.quarterTeamOfficeMeetingTime||'0' }}</div>
                  </div>
                </el-col>

                <el-col :span="8" class="height">
                  <div class="layui-form">
                    <div class="layui-form-left width-label-2">
                      本季度召开专题会议（次）
                    </div>
                    <div class="layui-form-value">{{ infoData.quarterSpecialMeetingTime||'0' }}</div>
                  </div>
                </el-col>
              </div>

              <div class="top-search">
                <el-col :span="8" class="height">
                  <div class="layui-form">
                    <div class="layui-form-left width-label-2">
                      当年累计召开领导小组会议（次）
                    </div>
                    <div class="layui-form-value">{{ infoData.totalLeaderTeamMeetingTime||'0' }}</div>
                  </div>
                </el-col>

                <el-col :span="8" class="height">
                  <div class="layui-form">
                    <div class="layui-form-left width-label-2">
                      当年累计召开领导小组办公室会议（次）
                    </div>
                    <div class="layui-form-value">{{ infoData.totalTeamOfficeMeetingTime||'0' }}</div>
                  </div>
                </el-col>

                <el-col :span="8" class="height">
                  <div class="layui-form">
                    <div class="layui-form-left width-label-2">
                      当年累计召开专题会议（次）
                    </div>
                    <div class="layui-form-value">{{ infoData.totalSpecialMeetingTime||'0' }}</div>
                  </div>
                </el-col>
              </div>
            </div>

            <div
              class="common-in-box-header"
              style="margin-top: 10px; border: 0px; padding-left: 10px"
            >
              <div class="common-in-box-header-text">体系建设情况</div>
            </div>

            <div class="common-in-box-content">
              <div class="top-search">
                <el-col :span="8" class="height">
                  <div class="layui-form">
                    <div class="layui-form-left width-label-2">
                      2019年至今累计印发责任追究相关制度数量（项）
                    </div>
                    <div class="layui-form-value">{{ infoData.totalAccountabilitySystemNumber||'0' }}</div>
                  </div>
                </el-col>

                <el-col :span="8" class="height">
                  <div class="layui-form">
                    <div class="layui-form-left width-label-2">
                      2019年至今累计专职人员数量 （人）
                    </div>
                    <div class="layui-form-value">{{ infoData.totalProfessionalNumber||'0' }}</div>
                  </div>
                </el-col>
              </div>

              <div class="top-search">
                <el-col :span="8" class="height">
                  <div class="layui-form">
                    <div class="layui-form-left width-label-2">
                      当年累计新增配套制度（项）
                    </div>
                    <div class="layui-form-value">{{ infoData.totalNewSupportingSystem||'0' }}</div>
                  </div>
                </el-col>

                <el-col :span="8" class="height">
                  <div class="layui-form">
                    <div class="layui-form-left width-label-2">
                      当年累计新增工作机制（项）
                    </div>
                    <div class="layui-form-value">{{ infoData.totalNewWorkSystem||'0' }}</div>
                  </div>
                </el-col>

                <el-col :span="8" class="height">
                  <div class="layui-form">
                    <div class="layui-form-left width-label-2">
                      当年累计新增专职人员数量（人）
                    </div>
                    <div class="layui-form-value">{{ infoData.totalNewSpecialPersonNumber||'0' }}</div>
                  </div>
                </el-col>
              </div>

              <div class="top-search">
                <el-col :span="24" class="height">
                  <div class="layui-form">
                    <div class="layui-form-left width-label-2">
                      新增配套制度名称
                    </div>
                    <div class="layui-form-value">{{ infoData.newSupportingName }}</div>
                  </div>
                </el-col>
              </div>

              <div class="top-search">
                <el-col :span="24" class="height">
                  <div class="layui-form">
                    <div class="layui-form-left width-label-2">
                      新增工作机制名称
                    </div>
                    <div class="layui-form-value">{{ infoData.newWorkName }}</div>
                  </div>
                </el-col>
              </div>

              <div class="top-search">
                <el-col :span="12" class="height">
                  <div class="layui-form">
                    <div class="layui-form-left width-label-2">主要部门</div>
                    <div class="layui-form-value">{{ infoData.groupMainDept||'' }}</div>
                  </div>
                </el-col>
              </div>

              <div
                class="common-in-box-header"
                style="margin-top: 10px; border: 0px; padding-left: 10px"
              >
                <div class="common-in-box-header-text">
                  违规问题线索查办情况
                </div>
              </div>

              <div class="common-in-box-content">
                <div class="top-search">
                  <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left width-label-2">
                        本季度新受理问题线索数量（件）
                      </div>
                      <div class="layui-form-value">{{ infoData.quarterNewProblemNumber||'0' }}</div>
                    </div>
                  </el-col>

                  <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left width-label-2">
                        本季度涉及资产损失（万元）
                      </div>
                      <div class="layui-form-value">{{ infoData.lossAmount||'0.00' }}</div>
                    </div>
                  </el-col>

                  <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left width-label-2">
                        本季度涉及资产损失风险（万元）
                      </div>
                      <div class="layui-form-value">{{ infoData.lossRisk||'0.00' }}</div>
                    </div>
                  </el-col>
                </div>

                <div class="top-search">
                  <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left width-label-2">
                        当年累计受理问题线索数量（件）
                      </div>
                      <div class="layui-form-value">{{ infoData.totalProblemSourceNumber||'0' }}</div>
                    </div>
                  </el-col>

                  <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left width-label-2">
                        上年结转问题线索数量（件）
                      </div>
                      <div class="layui-form-value">{{ infoData.lastYearProblemSourceNumber||'0' }}</div>
                    </div>
                  </el-col>
                </div>

                <div class="top-search">
                  <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left width-label-2">
                        其中:未启动核查（件）
                      </div>
                      <div class="layui-form-value">{{ infoData.checkNoStartedNumber||'0' }}</div>
                    </div>
                  </el-col>

                  <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left width-label-2">
                        其中: 正在核查（件）
                      </div>
                      <div class="layui-form-value">{{ infoData.checkInProcessNumber||'0' }}</div>
                    </div>
                  </el-col>

                  <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left width-label-2">
                        其中: 完成核查（件）
                      </div>
                      <div class="layui-form-value">{{ infoData.checkCompletedNumber||'0' }}</div>
                    </div>
                  </el-col>
                </div>
              </div>

              <div
                class="common-in-box-header"
                style="margin-top: 10px; border: 0px; padding-left: 10px"
              >
                <div class="common-in-box-header-text">追责整改工作成效</div>
              </div>

              <div class="common-in-box-content">
                <div class="top-search new-change-height-bottom-view">
                  <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left width-label-2">
                        当年累计完成追责问题数量（件）
                      </div>
                      <div class="layui-form-value">{{ infoData.totalCompletedProblemNumber||'0' }}</div>
                    </div>
                  </el-col>
                  <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left width-label-2">
                        当年累计追责总人数（人）
                      </div>
                      <div class="layui-form-value">{{ infoData.totalAccountabilityPersonNumber||'0' }}</div>
                    </div>
                  </el-col>
                  <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left width-label-2">
                        其中: 子企业管理干部（人）
                      </div>
                      <div class="layui-form-value">{{ infoData.subManagementNumber||'0' }}</div>
                    </div>
                  </el-col>

                </div>

                <div class="top-search">


                  <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left width-label-2">
                        当年累计追责总人次（人次）
                      </div>
                      <div class="layui-form-value">{{ infoData.totalAccountabilityPersonTime||'0' }}</div>
                    </div>
                  </el-col>

                  <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left width-label-2">
                        其中: 组织处理 （人次）
                      </div>
                      <div class="layui-form-value">{{ infoData.orgHandleTime||'0' }}</div>
                    </div>
                  </el-col>

                  <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left width-label-2">
                        其中: 扣减薪酬 （人次）
                      </div>
                      <div class="layui-form-value">{{ infoData.deductionSalaryTime||'0' }}</div>
                    </div>
                  </el-col>

                </div>

                <div class="top-search new-change-height-bottom-view">
                  <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left width-label-2">
                        其中: 党纪处分（人次）
                      </div>
                      <div class="layui-form-value">{{ infoData.partyPunishmentTime||'0' }}</div>
                    </div>
                  </el-col>

                  <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left width-label-2">
                        其中:移送监察机关或司法机关 （人次）
                      </div>
                      <div class="layui-form-value">{{ infoData.transferAuthorityTime||'0' }}</div>
                    </div>
                  </el-col>

                  <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left width-label-2">
                        其中: 其他 （人次）
                      </div>
                      <div class="layui-form-value">{{ infoData.processingOtherItem||'0' }}</div>
                    </div>
                  </el-col>


                </div>

                <div class="top-search new-change-height-bottom-view">

                  <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left width-label-2">
                        当年累计扣减薪酬金额（万元）
                      </div>
                      <div class="layui-form-value">{{ infoData.totalDeductionSalary||'0.00' }}</div>
                    </div>
                  </el-col>
                </div>

                <div class="top-search new-change-height-bottom-view">
                  <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left width-label-2">
                        责任约谈-当年累计责任约谈次数（次）
                      </div>
                      <div class="layui-form-value">{{ infoData.dutyInterviewNumber||'0' }}</div>
                    </div>
                  </el-col>

                  <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left width-label-2">
                        责任约谈-当年累计责任约谈总人次（人次）
                      </div>
                      <div class="layui-form-value">{{ infoData.dutyInterviewPersonTime||'0' }}</div>
                    </div>
                  </el-col>

                </div>

                <div class="top-search">
                  <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left width-label-2">
                        当年累计挽回资产损失（万元）
                      </div>
                      <div class="layui-form-value">{{ infoData.totalRetrieveLossAmount||'0.00' }}</div>
                    </div>
                  </el-col>

                  <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left width-label-2">
                        当年累计降低损失风险（万元）
                      </div>
                      <div class="layui-form-value">{{ infoData.totalReduceLossRisk||'0.00' }}</div>
                    </div>
                  </el-col>

                  <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left width-label-2">
                        当年累计制修订管理制度（项）
                      </div>
                      <div class="layui-form-value">{{ infoData.totalPerfectSystemNumber||'0' }}</div>
                    </div>
                  </el-col>
                </div>

                <div class="top-search">
                  <el-col :span="24" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left width-label-2">
                        其他工作成效
                      </div>
                      <div class="layui-form-value">{{ infoData.otherAchievement||'' }}</div>
                    </div>
                  </el-col>
                </div>

                <div
                  class="common-in-box-header"
                  style="margin-top: 10px; border: 0px; padding-left: 10px"
                >
                  <div class="common-in-box-header-text">其他</div>
                </div>

                <div class="top-search">
                  <el-col :span="24" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left">备注</div>
                      <div class="layui-form-value">{{ infoData.remark||'' }}</div>
                    </div>
                  </el-col>
                </div>

                <div class="top-search">
                  <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left">追责部门填报人</div>
                      <div class="layui-form-value">{{ infoData.informantName||'' }}</div>
                    </div>
                  </el-col>

                  <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left">联系电话</div>
                      <div class="layui-form-value">{{ infoData.informantPhone||'' }}</div>
                    </div>
                  </el-col>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>


    <subordinateReportDetail  ref="subordinateReport" />
  </div>
</template>
<script>
import { Loading } from 'element-ui'
import pubSub from 'pubsub-js'
import {queryQuarterReportProv} from '@/api/quarterly-report/view'
import {queryQuarterReportFileList} from "@/api/quarterly-report";
import subordinateReportDetail from "@/views/quarterlyReport/subordinateReport/detail";
export default {
  components: { subordinateReportDetail },
  props: {
    //编辑内容
    rowData: {
      type: Object,
      default: () => {},
    },
    //是否为地市查看进入
    showType: {
      type: String,
      default: () => '',
    },
  },
  dicts: ['REPORT_QUARTER'],
  data() {
    return {
      infoData: {}, //基本信息
      tableLoading: false, //表格loading
      filesData: [{}, {}], //附件列表
      //附件上传
      files: {
        busiTableId: "",
        busiTableName: "",
      },
      //本单位汇总信息
      formData: {},
    };
  },
  created() {
    this.formData = this.rowData;
    this.queryQuarterReportProv(this.formData)
    //查询附件列表
    this.queryFileList();
  },
  methods: {
    //查询附件列表
    queryFileList(){
      queryQuarterReportFileList(this.formData.quarterReportProvId).then((res)=>{
        this.filesData = res.data;
      })
    },
    queryQuarterReportProv(obj){
      queryQuarterReportProv(obj).then(response => {
        this.infoData = response.data;
      });
    } ,

    /**下载文件*/
    fileDownload(obj) {
      this.download(
        "/sys/attachment/downloadSysAttachment/" + obj.attachmentId,
        {},
        obj.fileName
      );
    },
    // 5查看-明细导出
    detailedExport() {
      this.download(
        "/quarterReport/getDataDetailExport",
        { quarterReportId: this.formData.quarterReportId,
          reportUnitCode:this.formData.reportUnitCode
          },
        this.getFileName()
      );
    },
    fromatComon (value, list) {
      let lastLabel = '-'

      if (value && list.length > 0) {
        list.forEach(element => {
          if (element.value == value) {
            lastLabel = element.label
          }
        })
      }
      return lastLabel
    },
    getFileName(){
      const date =  this.infoData.reportYear+'年'+this.fromatComon(this.formData.reportQuarter,this.dict.type.REPORT_QUARTER)
      const  filename = date+'中央企业违规经营投资责任追究工作情况表.xlsx'
      return filename;
    },
    //下级单位数据上报
    subordinateReport() {
      this.$refs.subordinateReport.open(this.infoData.id);
    },
  },
};
</script>
<style lang="scss" scoped>
@import "~@/assets/styles/quarterly-report/index.css";
</style>
