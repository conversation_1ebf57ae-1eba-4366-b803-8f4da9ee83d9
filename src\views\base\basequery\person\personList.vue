<!--企业联系人-->
<template>
  <div class="padding_b10 app-personList">
    <SearchList :searchList="searchList" @on-search="handleQuery" @on-reset="resetQuery"></SearchList>
    <el-table :data="tableList" :header-cell-style="{background:'#F4F8FC',color:'#606266','text-align':'center'}" :cell-style="{'text-align':'center'}">
      <el-table-column label="序号" type="index" min-width="4%" align="center">
        <template slot-scope="scope">
          <table-index
          :index="scope.$index"
          :pageNum="queryParams.pageNum"
          :pageSize="queryParams.pageSize"
          />
        </template>
      </el-table-column>
      <el-table-column label="省分" prop="involProvName" min-width="8%"/>
      <el-table-column label="责任追究工作领导小组" prop="groupNum"  min-width="20%"/>
      <el-table-column label="责任追究工作领导小组办公室" prop="officeDeptNum" min-width="10%"/>
      <el-table-column label="职能部门" prop="funDeptNum" min-width="10%"/>
      <el-table-column label="职能处室" prop="funOfficeNum" min-width="10%"/>
      <el-table-column label="联系人" prop="contactsNum" min-width="8%"/>
      <el-table-column label="信息系统管理员" prop="infoAdminNum" min-width="10%"/>
      <el-table-column label="操作" fixed="right" width="100" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            v-if="scope.row.dispatchStatus != 0"
            size="mini"
            type="text"
            icon="el-icon-search"
            title="查看"
            @click="handleDetails(scope.row)"
          ></el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="personBaseInfo"
    />
    <personDetail v-if="dialogVisible" v-on:closeModal="closeModal" :id="id"></personDetail>
  </div>
</template>

<script>
  import {getAreaPersonBaseInfoPage} from "@/api/base/person";
  import personDetail from "./personDetail";
  import SearchList from "../../common/SearchList";
  export default {
    name: "personList",
    components: { personDetail, SearchList   },
    data() {
      return {
        // 显示搜索条件
        showSearch: true,
        // 总条数
        total: 0,
        // 表格数据
        tableList: [],
        // 是否显示弹出层
        dialogVisible: false,
        id:'',
        //新增主键
        //日常问题查询 参数
        queryParams: {
          pageNum: 1,
          pageSize: 10,
          involProvName:'',
          involAreaName:'',
        },
        searchList: [
          {
            label: "省分",
            name: "involProvName",
            value: null,
            type: "Input"
          },
          {
            label: "地市",
            name: "involAreaName",
            value: null,
            type: "Input"
          }
        ]
      };
    },
    created() {
      this.personBaseInfo();
    },
    methods: {
      /**查询企业联系人列表*/
      personBaseInfo() {
       //this.loading = true;
        getAreaPersonBaseInfoPage(this.queryParams).then(
          response => {
            this.tableList = response.rows;
            this.total = response.total;
            //this.loading = false;
          }
        );
      },
      /** 搜索按钮操作*/
      handleQuery(params) {
        this.queryParams={
          ...this.queryParams,
          ...params,
          pageNum: 1,
        }
        this.personBaseInfo();
      },
      /**重置按钮操作*/
      resetQuery() {
        this.queryParams = {
          pageNum: 1,
          pageSize: 10,
        };
        this.personBaseInfo();
      },
      /**查看按钮操作*/
      handleDetails(row) {
        this.dialogVisible=true;
        this.id = row.id;
      },
      /**关闭模态框*/
      closeModal(){
        this.dialogVisible = !this.dialogVisible;
      },
    }
  };
</script>
<style rel="stylesheet/scss" lang="scss">
</style>







