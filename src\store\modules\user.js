import { login, logout, getInfo, postSwitch, authentic, taskToDoDetail, taskReadDetail, toDoType, jtAuthentic } from '@/api/login'
import { getToken, setToken, removeToken } from '@/utils/auth'

const user = {
  state: {
    token: getToken(),
    name: '',
    avatar: '',
    roles: [],
    permissions: [],
    nickName: '',
    orgName: '',
    depts: [],
    userId: '',
    dept: {},
    provinceCode: '',
    areaCode: '',
    waterMark: {},
    loginIP: '',
    orgGrade: ''
  },

  mutations: {
    SET_TOKEN: (state, token) => {
      state.token = token
    },
    SET_NAME: (state, name) => {
      state.name = name
    },
    SET_AVATAR: (state, avatar) => {
      state.avatar = avatar
    },
    SET_ROLES: (state, roles) => {
      state.roles = roles
    },
    SET_PERMISSIONS: (state, permissions) => {
      state.permissions = permissions
    },
    SET_NICKNAME: (state, nickName) => {
      state.nickName = nickName
    },
    SET_ORGNAME: (state, orgName) => {
      state.orgName = orgName
    },
    SET_DEPTS: (state, depts) => {
      state.depts = depts
    },
    SET_DEPT: (state, dept) => {
      state.dept = dept
    },
    SET_USERID: (state, userId) => {
      state.userId = userId
    },

    SET_PROVIN_CODE: (state, provinceCode) => {
      state.provinceCode = provinceCode
    },

    SET_AREACODE: (state, areaCode) => {
      state.areaCode = areaCode
    },

    SET_WATERMARK: (state, waterMark) => {
      state.waterMark = waterMark
    },
    SET_ORG_GRADE: (state, orgGrade) => {
      state.orgGrade = orgGrade
    },
    SET_LOGINIP: (state, loginIP) => {
      state.loginIP = loginIP
    }
  },

  actions: {
    // 登录
    Login({ commit }, userInfo) {
      const username = userInfo.info.trim()
      const password = userInfo.word
      const code = userInfo.code
      const uuid = userInfo.uuid
      return new Promise((resolve, reject) => {
        login(username, password, code, uuid).then(res => {
          setToken(res.token)
          commit('SET_TOKEN', res.token)
          resolve()
        }).catch(error => {
          reject(error)
        })
      })
    },

    // 获取用户信息
    GetInfo({ commit, state }) {
      return new Promise((resolve, reject) => {
        getInfo().then(res => {
          const user = res.user
          const avatar = user.avatar == '' ? require('@/assets/images/profile.jpg') : process.env.VUE_APP_BASE_API + user.avatar
          if (res.roles && res.roles.length > 0) { // 验证返回的roles是否是一个非空数组
            commit('SET_ROLES', res.roles)
            commit('SET_PERMISSIONS', res.permissions)
          } else {
            commit('SET_ROLES', ['ROLE_DEFAULT'])
          }
          commit('SET_NAME', user.userName)
          commit('SET_AVATAR', avatar)
          commit('SET_NICKNAME', user.nickName)
          commit('SET_ORGNAME', user.orgName)
          commit('SET_DEPT', user.dept)
          commit('SET_DEPTS', res.depts)
          commit('SET_USERID', user.userId)
          commit('SET_PROVIN_CODE', user.provinceCode)
          commit('SET_AREACODE', user.areaCode)

          commit('SET_ORG_GRADE', user.orgGrade)

          commit('SET_WATERMARK', res.watermark)
          commit('SET_LOGINIP', res.loginIP)
          resolve(res)
        }).catch(error => {
          reject(error)
        })
      })
    },

    // 审计跳转系统认证
    JtAuthentic({ commit, state }, obj) {
      return new Promise((resolve, reject) => {
        jtAuthentic(obj).then(res => {
          resolve(res)
        }).catch(error => {
          reject(error)
        })
      })
    },

    // 门户待办待阅
    AuthenticTask({ commit, state }, obj) {
      return new Promise((resolve, reject) => {
        authentic(obj).then(res => {
          resolve(res)
        }).catch(error => {
          reject(error)
        })
      })
    },

    // 门户待办待阅详情
    TaskToDoDetail({ commit, state }, obj) {
      return new Promise((resolve, reject) => {
        taskToDoDetail(obj).then(res => {
          resolve(res)
        }).catch(error => {
          reject(error)
        })
      })
    },

    // 门户已办已阅详情
    TaskReadDetail({ commit, state }, obj) {
      return new Promise((resolve, reject) => {
        taskReadDetail(obj).then(res => {
          resolve(res)
        }).catch(error => {
          reject(error)
        })
      })
    },

    // 门户登录
    Authentic({ commit, state }) {
      const serial = new Date().getTime()
      return new Promise((resolve, reject) => {
        authentic({
          direct: serial,
          returnUrl: '/fromAudit?'
        }).then(res => {
          resolve(res)
        }).catch(error => {
          reject(error)
        })
      })
    },

    // 岗位切换
    PostSwitch({ commit }, userId) {
      return new Promise((resolve, reject) => {
        postSwitch(userId).then(res => {
          setToken(res.token)
          commit('SET_TOKEN', res.token)
          resolve()
        }).catch(error => {
          reject(error)
        })
      })
    },

    // 退出系统
    LogOut({ commit, state }) {
      return new Promise((resolve, reject) => {
        logout(state.token).then(() => {
          commit('SET_TOKEN', '')
          commit('SET_ROLES', [])
          commit('SET_PERMISSIONS', [])
          commit('SET_WATERMARK', {})
          removeToken()
          resolve()
        }).catch(error => {
          reject(error)
        })
      })
    },

    // 前端 登出
    FedLogOut({ commit }) {
      return new Promise(resolve => {
        commit('SET_TOKEN', '')
        removeToken()
        resolve()
      })
    }
  }
}

export default user
