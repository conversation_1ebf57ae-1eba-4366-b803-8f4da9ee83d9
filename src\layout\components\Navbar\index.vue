<!--支撑渠道-->
<template>
  <div class="scope">
    <el-dialog class="navbar" :visible.sync="visible" @close="close" :modal-append-to-body="false" append-to-body title="支撑渠道" width="90%">

      <el-row>
        <el-col :span="8">
          <BlockCard title="支撑方式" style="height: 155px">
            <div class="navbar-support">
              <div class="navbar-support-li">
                <div class="navbar-support-icon"><i class="el-icon-phone-outline"></i></div>
                <div class="navbar-support-text">010-67882255转3转0</div>
              </div>
              <div class="navbar-support-li">
                <div class="navbar-support-icon"><i class="el-icon-document"></i></div>
                <div class="navbar-support-text">智慧门户-应用中心-沃运营，发起沃运营工单</div>
              </div>
            </div>
          </BlockCard>
        </el-col>
        <el-col :span="16">
          <BlockCard title="帮助文档" style="height: 155px">
            <div class="navbar-support">
              <div class="navbar-support-li">
                <div class="navbar-support-file" @click="fileDownload" title="点击下载">{{fileInfo.fileName}}</div>
              </div>
            </div>
          </BlockCard>
        </el-col>
        <el-col :span="24">
          <BlockCard title="版本信息列表">
           <div class="padding-20">
             <el-table border v-loading="loading" height="calc(75vh - 335px)" :data="noticeList">
               <el-table-column label="序号" type="index" min-width="10%" align="center">
                 <template slot-scope="scope">
                   <table-index
                     :index="scope.$index"
                     :pageNum="queryParams.pageNum"
                     :pageSize="queryParams.pageSize"
                   />
                 </template>
               </el-table-column>
               <el-table-column
                 label="公告主题"
                 min-width="80%"
                 align="left"
                 prop="noticeTitle"
                 :show-overflow-tooltip="true"
               />
               <el-table-column label="操作" align="center" min-width="10%" class-name="small-padding fixed-width">
                 <template slot-scope="scope">
                   <el-button
                     size="mini"
                     type="text"
                     icon="el-icon-search"
                     title="查看"
                     @click="handleDetail(scope.row)"
                   ></el-button>
                 </template>
               </el-table-column>
             </el-table>
             <pagination
               v-show="total>0"
               :total="total"
               :page.sync="queryParams.pageNum"
               :limit.sync="queryParams.pageSize"
               @pagination="getList"
             />
           </div>
          </BlockCard>
        </el-col>
      </el-row>
<!--      <div slot="footer"><el-button size="mini" @click="close">关闭</el-button></div>-->
    </el-dialog>
    <!--公告-->
    <el-dialog class="notice" :visible.sync="openDetail" @close="closeNotice" :modal-append-to-body="false" append-to-body title="系统公告" width="55%">

      <div class="notice-container">
        <el-card class="box-card">
          <el-descriptions :title="form.noticeTitle" direction="vertical">
            <el-descriptions-item label="公告内容">
              <editor v-model="form.noticeContent" height="350" readOnly="true"/>
            </el-descriptions-item>
          </el-descriptions>
        </el-card>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import BlockCard from '@/components/BlockCard';
import {getOperateManualFileInfo} from "@/api/system/manual";
import { listNotice } from "@/api/system/notice";
export default {
  name: "Navbar",
  components: { BlockCard },
  dicts: ['sys_notice_status', 'sys_notice_type'],
  props: {},
  data() {
    return {
      openDetail:false,
      form:{},
      visible:false,
      fileInfo:{},//操作手册
      loading: true,// 遮罩层
      total: 0,
      // 公告表格数据
      noticeList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        noticeTitle: undefined,
        createBy: undefined,
        status: undefined,
        homeFlag:'1'
      },
    }
  }
  ,created(){
    this.getOperateManualFileInfo();
    this.getList()
  }
  ,methods: {
    // 显示弹框
    open(id) {
      this.visible = true;
      this.queryParams.pageNum=1
      this.getOperateManualFileInfo();
      this.getList()
    },
    //关闭弹窗
    close() {
      this.visible = false;
    },
    /**获取操作文件信息*/
    getOperateManualFileInfo(){
      getOperateManualFileInfo().then((res)=>{
        this.fileInfo = res.data;
      })
    },
    /**下载文件*/
    fileDownload() {
      this.download(
        "/sys/attachment/downloadSysAttachment/" + this.fileInfo.attachmentId,
        {},
        this.fileInfo.fileName
      );
    },
    /** 查询公告列表 */
    getList() {
      this.loading = true;
      listNotice(this.queryParams).then(response => {
        this.noticeList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    //查看
    handleDetail(row){
      this.form = row;
      this.openDetail = true;
    },
    closeNotice(){
      this.openDetail = false;
      this.form = {};
    }
  }
}
</script>

<style lang="scss" scoped>
.notice{
  ::v-deep .el-dialog__body{
    height: auto;
    padding:10px;
    overflow: auto;
  }
}
.navbar{
  .margin-top-8{
    margin-top: 8px;
  }
  ::v-deep .el-dialog__body{
    height: 76vh;
    padding:10px;
    overflow: auto;
  }
  ::v-deep .public-box-element {
    background-color: transparent;
    border-radius: 4px;
    -webkit-box-shadow: 0px 0px 10px 0px rgba(155, 11, 9, 0.1);
    box-shadow: 0px 0px 10px 0px rgba(155, 11, 9, 0.1);
  }
  ::v-deep .public-box-content{
    padding:0;
  }
  .navbar-support {
    width: 100%;
    .navbar-support-li {
      width: 100%;
      border-bottom: 1px solid #eee;
      display: flex;
      align-items: center;
      height: 50px;
      &:last-child{
        border-bottom: 0;
      }
      .navbar-support-icon {
        width: 40px;
        text-align: center;
        font-size: 16px;
        color:#f5222d;
      }
      .navbar-support-text {
        font-size: 14px;
      }
      .navbar-support-file{
        font-size: 14px;
        color:#f5222d;
        cursor: pointer;
      }
    }
  }
  .padding-20 {
    padding:10px 0 20px;
  }


    blockquote {
      padding: 10px 20px;
      margin: 0 0 20px;
      font-size: 17.5px;
      border-left: 5px solid #eee;
    }
    hr {
      margin-top: 20px;
      margin-bottom: 20px;
      border: 0;
      border-top: 1px solid #eee;
    }
    .col-item {
      margin-bottom: 20px;
    }

    ul {
      padding: 0;
      margin: 0;
    }

    font-family: "open sans", "Helvetica Neue", Helvetica, Arial, sans-serif;
    font-size: 13px;
    color: #676a6c;
    overflow-x: hidden;

    ul {
      list-style-type: none;
    }

    h4 {
      margin-top: 0px;
    }

    h2 {
      margin-top: 10px;
      font-size: 26px;
      font-weight: 100;
    }

    p {
      margin-top: 10px;

      b {
        font-weight: 700;
      }
    }

    .update-log {
      ol {
        display: block;
        list-style-type: decimal;
        margin-block-start: 1em;
        margin-block-end: 1em;
        margin-inline-start: 0;
        margin-inline-end: 0;
        padding-inline-start: 40px;
      }
    }
}
</style>
