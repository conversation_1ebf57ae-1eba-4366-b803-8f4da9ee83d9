<!--  超时待阅页面 -->
<template>
  <div>
    <div style="padding:20px 10px"> 您好，{{infoData.projectName}}{{readInfo}}</div>
   <BlockCard title="项目信息">
     <el-form r size="medium" label-width="150px">
       <el-row>
         <el-col :span="16">
           <el-form-item label="项目名称">
             <span>{{ infoData.projectName }}</span>
           </el-form-item>
         </el-col>
         <el-col :span="8">
           <el-form-item label="项目编码">
             <span> {{infoData.projectNum}}</span>
           </el-form-item>
         </el-col>
         <el-col :span="8">
           <el-form-item label="项目类型">
             <span> {{infoData.projectTypeEnumId | fromatComon(dict.type.SPR_PROJECT_TYPE_ALL)}}</span>
           </el-form-item>
         </el-col>
         <el-col :span="8">
           <el-form-item label="审计对象">
             <span> {{infoData.projectOrgName}}</span>
           </el-form-item>
         </el-col>
         <el-col :span="8">
           <el-form-item label="项目年度">
             <span> {{infoData.projectYear}}</span>
           </el-form-item>
         </el-col>
       </el-row>
     </el-form>
    </BlockCard>
    <BlockCard title="审计报告信息">
      <el-form>
        <el-table v-loading="tableLoading" :data="auditAttachment"
                  border
                  ref="table">
          <el-table-column  type="index" min-width="8%" align="center" label="序号" >
            <template slot-scope="scope">
              <table-index
                :index="scope.$index"
              />
            </template>
          </el-table-column>
          <el-table-column label="附件名称" prop="fileName" min-width="30%" />
          <el-table-column label="附件类型" prop="attachmentTypeName" min-width="20%" />
          <el-table-column label="上传人" prop="uploadNickName" min-width="10%"/>
          <el-table-column label="上传时间" prop="createTime" min-width="12%" />
          <el-table-column
            label="操作"
            fixed="right"
            min-width="10%"
            align="center"
            class-name="small-padding fixed-width"
          >
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                title="下载"
                icon="el-icon-bottom"
                @click="downLoadAuditFile(scope.row.enAttachmentUrl)"
              >
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
    </BlockCard>
    <BlockCard title="项目台账信息">
      <el-form>
        <el-table border v-loading="tableLoading" :data="ledgerTable">
          <el-table-column  type="index" min-width="8%" align="center" label="序号" show-overflow-tooltip>
            <template slot-scope="scope">
              <table-index
                :index="scope.$index"
              />
            </template>
          </el-table-column>
          <el-table-column label="问题编号" prop="problemNum" min-width="15%" align="center" show-overflow-tooltip/>
          <el-table-column label="发现问题业务类型" prop="problemTypeEnumName" min-width="20%" align="center" show-overflow-tooltip/>
          <el-table-column label="审计发现问题" prop="problemAudit"  min-width="30%" align="center" show-overflow-tooltip>
            <template slot-scope="scope">
              <div class="table-text-left ovflowHidden">{{ scope.row.problemAudit }}</div>
            </template>
          </el-table-column>
          <el-table-column label="具体问题描述" prop="problemDescription"  min-width="30%" align="center" show-overflow-tooltip>
            <template slot-scope="scope">
              <div  class="table-text-left ovflowHidden">{{ scope.row.problemDescription }}</div>
            </template>
          </el-table-column>
          <el-table-column label="是否上报告" prop="reportFlag"  min-width="10%" align="center" show-overflow-tooltip>
            <template slot-scope="scope" class="text-center">
              {{ scope.row.reportFlag==1?'是':scope.row.reportFlag==0?'否':'' }}
            </template>
          </el-table-column>
          <el-table-column label="是否追责" prop="ifDuty"  min-width="10%" align="center" show-overflow-tooltip>
            <template slot-scope="scope" class="text-center">
              {{ scope.row.ifDuty==1?'是':scope.row.ifDuty==0?'否':'' }}
            </template>
          </el-table-column>
          <el-table-column label="是否移交纪检" prop="transferFlag"  min-width="10%" align="center" show-overflow-tooltip>
            <template slot-scope="scope" class="text-center">
              {{ scope.row.transferFlag==1?'是':scope.row.transferFlag==0?'否':'' }}
            </template>
          </el-table-column>
        </el-table>
      </el-form>
    </BlockCard>
    <BlockCard
      title="初核专项报告"
    >
      <FileUpload
        :key="id"
        :projectId = "id"
        ref="file"
      ></FileUpload>

    </BlockCard>
    <SynchronizeData
      ref="synchronizeData"
      :key="index"
      :id="id"
      @close="queryFlowInfo"
    ></SynchronizeData>

    <AddLedger
      ref="addLedger"
      :key="index1"
      :projectId="id"
      :ledgerId="ledgerId"
      :ledgerType="ledgerType"
      @close="queryLedgersList"
    ></AddLedger>
  </div>
</template>
<script>
import BlockCard from '@/components/BlockCard';
import AddLedger from '../flow/operation/addLedger';
import SynchronizeData from '../flow/operation/synchronizeData';//同步数据存在重复信息，请重新核查手工录入的问题信息。
import FileUpload from '../flow/operation/fileUpload';//附件
  import {
    queryProjectInfo
    ,queryAuditFile
    ,queryLedgersList
    ,queryReportFileList
    ,queryReadInfo
  } from '@/api/special-report'
  export default {
    components: { BlockCard,SynchronizeData,AddLedger,FileUpload },
    props: {
      // 编辑内容
      rowData: {
        type: Object,
        default: () => {}
      },
      //流程参数
      centerVariable: {
        type: Object
      },
    },
    dicts: ['SPR_PROJECT_TYPE_ALL'],
    data() {
      return {
        edit:true,
        index:0,
        index1:0,
        infoData: {}, // 项目信息
        tableLoading: false, // 表格loading
        id : '',//主键
        auditAttachment:[],//审计报告
        ledgerTable:[],//台账信息
        attachmentTable:[],//附件信息
        readInfo:'',//待阅信息
      }
    },
    created() {
      this.$emit('collocation',{
        refreshAssigneeUrl:'/spr/flow',//业务url
      })
      this.id = this.centerVariable.busiKey;
      //查询项目信息
      this.queryFlowInfo();
    },
    methods: {
      queryFlowInfo(){
        //查询项目信息
        this.queryProjectInfo();
        //审计报告信息
        this.queryAuditFile();
        //台账信息
        this.queryLedgersList();
      },
      //根据主键查询项目信息
      queryProjectInfo(){
        queryProjectInfo(this.id).then((res)=>{
          //项目信息
          this.infoData = res.data;
          this.$forceUpdate();
          this.queryOverTimeReadInfoById();
       })
      },
      //查询超时待阅信息
      queryOverTimeReadInfoById(){
        queryReadInfo('special_report_over_time_read').then((res)=>{
            this.readInfo = res.readInfo;
            this.$forceUpdate();
        })
      },
      //根据主键查询项目信息
      queryAuditFile(){
        queryAuditFile(this.id).then((res)=>{
          //审计报告
          this.auditAttachment = res.data;
          this.$forceUpdate();
        })
      },
      //根据主键查询项目信息
      queryLedgersList(){
        queryLedgersList(this.id).then((res)=>{
          //台账信息
          this.ledgerTable = res.data.ledgerReturnList;
          this.handleAddFlag = res.data.handleAddFlag;
          this.$forceUpdate();
        })
      },
      //审计报告附件下载
      downLoadAuditFile(url){
        window.location.href = url
      },
      loadProcessData(){
        return ;
      },
      //校验
      passValidate(){
       return true;
      },

      openLoading(){//打开加载...
        this.$emit('openLoading');
      },
      closeLoading(){//关闭加载...
        this.$emit('closeLoading');
      },
    }
  }
</script>
<style scoped>
  .position-select {
    position: absolute;
    width: 100%;
    text-align: right;
    top: -54px;
  }
</style>
