<template>
    <el-dialog
        title="企业基本信息详情"
        :visible.sync="dialogVisible"
        width="30%"
        :before-close="handleClose">
        <div>
          <el-form class="common-card padding10_0" size="medium"  label-width="108px">
            <el-row>
              <el-col :span="24">
                <el-form-item label="企业名称"><span>{{dataDetails.involOrgName}}</span></el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="企业简称"><span>{{dataDetails.involOrgNameBak}}</span></el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="社会信用代码"><span>{{dataDetails.socialCreditCode}}</span></el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="所属行业名称"><span>{{dataDetails.industryName}}</span></el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="行业代码"><span>{{dataDetails.industryCode}}</span></el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
    </el-dialog>
</template>

<script lang="ts">
  import {getAreaBaseInfoById} from "@/api/base/area";

  export default {
    name: "areaDetail",
    props: {
      dialogVisible: {
        type: Boolean,
        default: true
      },
      id: {
        type: String,
        default: ''
      },
    },
    data() {
      return {
        dataDetails: {},
      };
    },
    created() {
      this.areaBaseInfo();
    },
    methods: {
      /**查询企业基本信息详情*/
      areaBaseInfo() {
       //this.loading = true;
        getAreaBaseInfoById({id: this.id}).then(
          response => {
            this.dataDetails = response.data;
            //this.loading = false;
          }
        );
      },
      /**关闭模态框*/
      handleClose() {
        this.$emit("closeModal");
      }
    }
  };
</script>
