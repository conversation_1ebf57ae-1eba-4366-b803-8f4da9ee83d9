<template>
  <div
    :id="id"
    ref="chart"
    :class="className"
    :style="{ height: height, width: width }"
  />
</template>
<script>
import echarts from 'echarts'
import { fontSizeEchars } from './mixins/fontSizeEchars'
import resize from './mixins/resize'
require('echarts/theme/macarons')
import pubSub from 'pubsub-js'
export default {
  mixins: [resize],
  props: {
    charsData: {
      type: null,
      default: () => {}
    },
    id: {
      type: String,
      default: 'myChart'
    },
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '100%'
    }
  },
  data() {
    return {
      chart: null
    }
  },
  watch: {
    charsData: {
      handler(val, oldVal) {
        this.chart.clear()
        setTimeout(() => {
          this.initChart()
        }, 1000)
      },
      deep: true
    }
  },
  mounted() {
    this.initChart()
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.clear()
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    initChart() {
      var color = ['#FCED85', '#FDAF82', '#FC8586', '#8DA6FC', '#8ECFFD', '#3ABE7B', '#90FAFE', '#A2D72D']
      this.chart = echarts.init(this.$refs.chart, 'macarons')
      var option = {
        title: {
          text: '{a|' + this.charsData[0].name.slice(0, 6) + '}{a1|' + this.charsData[0].name.slice(6, this.charsData[0].name.length) + '\n}{c|' + this.charsData[0].value + '}',
          triggerEvent: true, // 是否触发事件
          textStyle: {
            color: '#fff',
            rich: {
              a: {
                fontSize: fontSizeEchars(0.13),
                color: '#B4BBBE',
                fontWeight: '500',
                padding: [-30, 0, 0, 0]
              },
              b: {
                fontSize: fontSizeEchars(0.13),
                color: '#A9B0B4',
                padding: [-70, 0, 0, 0]
              },
              c: {
                fontSize: fontSizeEchars(0.24),
                color: '#0B0B0B',
                fontWeight: '500',
                padding: [0, 0, 80, 0]
              }
            }
          },
          x: 'center',
          y: 'center'
        },

        color: color,

        series: [
          {
            type: 'pie',
            clockwise: true, // 饼图的扇区是否是顺时针排布
            hoverAnimation: true,
            minAngle: 25, // 最小的扇区角度（0 ~ 360）
            radius: ['40%', '60%'],
            center: ['50%', '50%'],
            avoidLabelOverlap: false,
            itemStyle: {
              // 图形样式
              normal: {
                borderColor: '#ffffff',
                borderWidth: 2
              }
            },

            label: {
              show: true,
              color: '#000',
              fontSize: fontSizeEchars(0.12),
              formatter: function(v) {
                let text = v.name
                if (text.length <= 8) {
                  return text
                } else if (text.length > 8 && text.length <= 16) {
                  return text = `${text.slice(0, 8)}\n${text.slice(8)}`
                } else if (text.length > 16 && text.length <= 24) {
                  return text = `${text.slice(0, 8)}\n${text.slice(8, 16)}\n${text.slice(16)}`
                } else if (text.length > 24 && text.length <= 30) {
                  return text = `${text.slice(0, 8)}\n${text.slice(8, 16)}\n${text.slice(16, 24)}\n${text.slice(24)}`
                } else if (text.length > 30) {
                  return text = `${text.slice(0, 8)}\n${text.slice(8, 16)}\n${text.slice(16, 24)}\n${text.slice(24, 30)}\n${text.slice(30)}`
                }
              }
            },
            labelLine: {
              show: true,
              length: fontSizeEchars(0.15), // 第一段长度
              length2: fontSizeEchars(0.1), // 第二段长度 设置0不显示第二段
              lineStyle: {
                width: fontSizeEchars(0.01)

              }
            },
            emphasis: {
              label: {
                color: '#f5222d',
                fontWeight: 800,
                fontSize: fontSizeEchars(0.14)
              },
              labelLine: {
                lineStyle: {
                  color: '#f5222d'
                }
              }
            },
            data: this.charsData
          }
        ]
      }
      this.chart.setOption(
        option,
        true
      )

      this.$nextTick(() => {
        setTimeout(() => {
          this.chart.on('mouseover', (e) => {
            if (e.name) {
              option.title.text =
              '{a|' + e.name.slice(0, 6) + '\n}{b|' + e.name.slice(6, e.name.length) + '\n}{c|' + e.value + '}'
            }

            this.chart.setOption(option, true)
            for (var i = 0; i < this.charsData.length; i++) {
              this.chart.dispatchAction({
                type: e.dataIndex == i ? 'highlight' : 'downplay',
                seriesIndex: 0,
                dataIndex: i
              })
            }
          })
          this.chart.on('mouseout', (e) => {
            this.chart.dispatchAction({
              type: 'highlight',
              seriesIndex: 0,
              dataIndex: e.dataIndex
            })
          })
          this.chart.dispatchAction({
            type: 'highlight',
            seriesIndex: 0,
            dataIndex: 0
          })

          this.chart.getZr().on('click', params => {
            if (params.target.eventData.componentType === 'title') {
              const values = params.target.style.text.split('}')
              let value = ''
              let code = ''
              values.forEach(ele => {
                const eleE = ele.substr(1, 1)
                if (eleE == 'a' || eleE == 'b') {
                  value += ele.substr(3)
                }
              })
              value = value.replace(/\n/g, '')
              this.charsData.forEach(element => {
                if (value == element.name) {
                  code = element
                }
              })
              pubSub.publish('echars_huan-4', { type: 'wtcljd', element: code })
            }
          })
        }, 1000)
      })
    }
  }
}
</script>
<style lang="scss" scoped>
#myChart {
  width: 100%;
  height: 100%;
  div {
    width: 100%;
    height: 100%;
  }
}
</style>
