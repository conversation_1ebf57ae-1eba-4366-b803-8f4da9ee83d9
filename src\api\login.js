import request from '@/utils/request'

// 登录方法
export function login(info, word, code, uuid) {
  const data = {
    info,
    word,
    code,
    uuid
  }
  return request({
    url: '/login',
    headers: {
      isToken: false
    },
    method: 'post',
    data: data
  })
}

// 注册方法
export function register(data) {
  return request({
    url: '/register',
    headers: {
      isToken: false
    },
    method: 'post',
    data: data
  })
}

// 校验接口
export function check() {
  return request({
    url: '/check/check',
    method: 'post'
  })
}

// 获取用户详细信息
export function getInfo() {
  return request({
    url: '/getInfo',
    method: 'get'
  })
}

// 退出方法
export function logout() {
  return request({
    url: '/logout',
    method: 'post'
  })
}

// 获取验证码
export function getCodeImg() {
  return request({
    url: '/captchaImage',
    headers: {
      isToken: false
    },
    method: 'get',
    timeout: 20000
  })
}

// 切换岗位
export function postSwitch(data) {
  return request({
    url: '/changeStation/' + data,
    method: 'get',
    timeout: 20000
  })
}

// 门户登录
export function authentic(params) {
  return request({
    url: '/ssoCloud/authentic',
    method: 'post',
    data: params,
    params: params,
    timeout: 20000
  })
}

// 审计登录
export function jtAuthentic(params) {
  return request({
    url: '/jtauditCloud/authentic',
    method: 'post',
    data: params,
    params: params,
    timeout: 20000
  })
}

// 待办已办信息
export function taskToDoDetail(params) {
  return request({
    url: '/ssoCloudTask/taskDetailCloud',
    method: 'post',
    data: params,
    params: params,
    timeout: 20000
  })
}

// 待办待阅信息
export function toDoType(params) {
  return request({
    url: '/system/org/postId/' + params,
    method: 'post',
    timeout: 20000
  })
}

// 待阅已阅信息
export function taskReadDetail(params) {
  return request({
    url: '/ssoCloudTask/taskReaderDetailCloud',
    method: 'post',
    data: params,
    params: params,
    timeout: 20000
  })
}
