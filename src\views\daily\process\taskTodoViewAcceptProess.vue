<!--1：问题受理-复核-->
<template>
  <div>
    <div>
      <ModifyrecordBtn
        :key="problemId||relevantTableId||relevantTableName"
        :problem-id="problemId"
        :relevant-table-id="relevantTableId"
        :relevant-table-name="relevantTableName"
        :problem-status="1"
        :edit="edit"
      />
      <opinion
        :processInstanceId="procInsId"
        :isShow="isShow"
      />
      <BlockCard
        title="基本信息"
      >
        <el-row>
          <el-form ref="elForm" :model="formData" :rules="rules" size="medium" label-width="138px">
            <el-col :span="6">
              <el-form-item label="发现日期" prop="findTime">
                <span>{{ formData.findTime | timeYear }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="6" v-show="formData.acceptTime != null">
              <el-form-item label="受理日期" prop="acceptTime" >
                <span>{{ formData.acceptTime | timeYear }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="问题线索来源" prop="problemSource">
                <el-select
                  v-model="formData.problemSource"
                  :disabled="!edit"
                  placeholder="请选择问题线索来源"
                  clearable
                  :style="{width: '100%'}"
                >
                  <el-option
                    v-for="(item, index) in problemSourceList"
                    :key="index"
                    :label="item.dictLabel"
                    :value="item.dictValue"
                  >{{ item.dictLabel }}</el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="spLedgerShow?12:24">
              <el-form-item label="违规事项" prop="problemTitle">
                <span>{{ formData.problemTitle }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="spLedgerShow">
              <SpLedger
                :key="problemId"
                ref="scope"
                :edit="edit"
                :problem-id="problemId"
              />
            </el-col>
            <el-col :span="24">
              <el-form-item label="问题线索描述" prop="problemDescribe">
                <span>{{ formData.problemDescribe }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="涉及专业线" prop="specLists">
                <el-checkbox-group :key="formData.specLists" v-model="formData.specLists" size="medium">
                  <el-checkbox
                    v-for="(item, index) in specList"
                    :key="item.dictValue"
                    :disabled="!edit"
                    border
                    :label="item.dictValue"
                  >{{ item.dictLabel }}</el-checkbox>
                </el-checkbox-group>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="涉及单位/部门/人员" prop="field107">
                <PersList
                  :key="problemId||relevantTableId||relevantTableName"
                  ref="pers"
                  :edit="edit"
                  :problem-id="problemId"
                  :relevant-table-id="relevantTableId"
                  :relevant-table-name="relevantTableName"
                />
              </el-form-item>
            </el-col>
          </el-form>
        </el-row>
      </BlockCard>
      <!--<Remind-->
      <!--:key="actualFlag"-->
      <!--:actualFlag="actualFlag"-->
      <!--&gt;</Remind>-->
      <BlockCard
        title="造成的损失风险"
      >
        <el-row>
          <el-form ref="elForm" :model="formData" :rules="rules" size="medium" label-width="138px">
            <el-col :span="24">
              <el-form-item label="是否产生资产损失" prop="lossStateAssetsFlag">
                <el-radio-group v-model="formData.lossStateAssetsFlag" size="medium">
                  <el-radio v-for="(item, index) in whetherLossOptions"
                            :key="index"
                            :label="item.value"
                            :disabled="!edit"
                            @change="lossStateAssetsChanged">
                    {{item.label}}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="预估损失金额（万元）" prop="lossAmount">
                <span>{{ (formData.lossAmount).toFixed(2) }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="预估损失风险（万元）" prop="lossRisk">
                <span>{{ (formData.lossRisk).toFixed(2) }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="损失形成主要原因" prop="lossReason">
                <span>{{ formData.lossReason }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="是否产生不良影响" prop="isAdverseEffects">
                <el-radio-group v-model="formData.isAdverseEffect" size="medium">
                  <el-radio v-for="(item, index) in whetherEffectOptions"
                            :key="index"
                            :label="item.value"
                            :disabled="!edit"
                            @change="radioEffectChanged">
                    {{item.label}}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="对应不良影响" prop="correspondingAdverseEffects" v-show="formData.isAdverseEffect">
                <el-select v-model="formData.correspondingAdverseEffects"
                           :style="{width: '100%'}"
                           clearable="clearable"
                           multiple="multiple"
                           value=""
                           :disabled="!edit">
                  <el-option v-for="(item, index) in dict.type.corresponding_adverse_effect"
                             :key="index"
                             :label="item.label"
                             :value="item.value">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="造成的不良影响" prop="adverseEffects">
                <span>{{ formData.adverseEffects }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="是否产生严重不良影响" prop="seriousAdverseEffectsFlag" v-show="formData.isAdverseEffect">
                <el-radio-group v-model="formData.seriousAdverseEffectsFlag" size="medium">
                  <el-radio
                    v-for="(item, index) in seriousAdverseEffectsFlagOptions"
                    :key="index"
                    :label="item.value"
                    :disabled="!edit"
                  >{{ item.label }}</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col v-show="formData.seriousAdverseEffectsFlag" :span="18">
              <el-form-item label="严重不良影响描述" prop="seriousAdverseEffectsDesc" v-show="formData.isAdverseEffect">
                <el-select
                  v-model="formData.seriousAdverseEffectsDesc"
                  placeholder="请选择严重不良影响描述"
                  clearable
                  :disabled="!edit"
                  :style="{width: '100%'}"
                  value="formData.seriousAdverseEffectsDesc"
                >
                  <el-option
                    v-for="(item, index) in dict.type.VIOLD_ADVER_EFFECT_DES"
                    :key="index"
                    :label="item.label"
                    :value="item.value"
                    :disabled="item.disabled"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="存在风险或涉嫌的违法违纪行为" prop="illegalActivities">
                <span>{{ formData.illegalActivities }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <ScopeSituation
                v-if="scopeSituation"
                ref="scope"
                :edit="edit"
                :problem-id="problemId"
                :relevant-table-id="relevantTableId"
                :relevant-table-name="relevantTableName"
              />
            </el-col>
            <el-col :span="24">
              <el-form-item label="其他说明事项" prop="otherNotes">
                <span>{{ formData.otherNotes }}</span>
              </el-form-item>
            </el-col>
          </el-form>
        </el-row>
      </BlockCard>
      <BlockCard
        title="附件列表"
      >
        <FileUpload
          ref="file"
          :edit="edit"
          :problem-id="problemId"
          :relevant-table-id="relevantTableId"
          :relevant-table-name="relevantTableName"
          flow-type="VIOL_DAILY"
          problem-status="1"
          link-key="a001"
        />
        <el-dialog class="tree-body-dialog" :visible.sync="visibleTree" width="90%" append-to-body title="人员选择">
          <Tree
            v-if="visibleTree"
            :key="problemId||relevantTableId||relevantTableName"
            :problem-id="problemId"
            :relevant-table-id="relevantTableId"
            :relevant-table-name="relevantTableName"
          />
          <div slot="footer" class="dialog-footer">
            <el-button size="mini" type="primary" @click="closeTree">保存</el-button>
          </div>
        </el-dialog>
      </BlockCard>
    </div>
  </div>
</template>
<script>
import { queryViolateInfo, saveViolateInfo, checkAndSaveViolateInfo,checkActualMainDept } from '@/api/daily/process/taskTodoViewAccept'
import BlockCard from '@/components/BlockCard'
import ScopeSituation from './../scopeSituation/scopeSituationData'// 范围情形展示
import ModifyrecordBtn from '../modifyRecord/btn'
import opinion from '../modifyRecord/opinion'
import FileUpload from './../../components/fileUpload'// 附件
import PersList from './../tree/persList'// tree
import Tree from './../tree'// tree
import Remind from './../../components/remind'
import TaskTodoViewAccept from './../process/taskTodoViewAccept'// tree
import Process from '@/components/Process/daily'
import moment from 'moment'


export default {
  components: { BlockCard, ScopeSituation, FileUpload, Tree, PersList, Process, TaskTodoViewAccept, Remind, ModifyrecordBtn,opinion
  , SpLedger: (d) => import("@/views/daily/spledger/spLedgerData.vue"), //解决递归加载组件报错

  },
  dicts: ['VIOLD_DAILY_SPEC', 'VIOLD_ADVER_EFFECT_DES', 'corresponding_adverse_effect'],
  filters: {
    timeYear: function(value) {
      if (!value) return ''
      return moment(value).format('YYYY-MM-DD')
    }
  },
  props: {
    isShow:{
      type: String,
      default: '0'
    },
    procInsId:{
      type: String
    },
    edit: {
      type: Boolean,
      default: false
    },
    problemId: {
      type: String
    }
  },
  data() {
    return {
      lossAmountDisabled: false,
      showAdverseEffectFlag: false,
      actualFlag: 1,
      scopeSituation: false,
      relevantTableId: '',
      relevantTableName: '',
      flag: false,
      visible: false,
      visibleTree: false,
      formData: {
        findTime: null,
        acceptTime: null,
        problemSource: null,
        problemTitle: null,
        problemDescribe: undefined,
        field107: undefined,
        lossAmount: undefined,
        lossRisk: undefined,
        lossReason: undefined,
        adverseEffects: undefined,
        seriousAdverseEffectsFlag: 1,
        seriousAdverseEffectsDesc: undefined,
        illegalActivities: undefined,
        otherNotes: undefined,
        specLists: [],
        correspondingAdverseEffects: []
      },
      specList: [],
      rules: {},
      seriousAdverseEffectsFlagOptions: [{
        'label': '是',
        'value': 1
      }, {
        'label': '否',
        'value': 0
      }],
      whetherLossOptions: [{
        "label": "是",
        "value": 1
      }, {
        "label": "否",
        "value": 0
      }],
      whetherEffectOptions: [{
        "label": "是",
        "value": 1
      }, {
        "label": "否",
        "value": 0
      }],
      problemSourceList: [],
      spLedgerShow:false,//关联追责台账是否显示
    }
  },
  computed: {},
  watch: {},
  created() {
  },
  mounted() {
    this.QueryViolateInfo()
  },
  methods: {
    // 关闭
    close() {
      this.visible = false
      this.$emit('close')
    },
    //问题线索来源切换事件
    problemSourceChanged(problemSource){
      //循环数组，取对应值的remark
      let remark = this.problemSourceList[this.problemSourceList.findIndex(item => item.dictValue === problemSource)].remark;
      if(remark.indexOf("LEDGER")>0){
        this.spLedgerShow = true;
      }else{
        this.spLedgerShow = false;
      }
    },
    /** 初始化数据*/
    QueryViolateInfo() {
      this.loading = true
      const array = []
      queryViolateInfo({ problemId: this.problemId, relevantTableId: this.relevantTableId }).then(
        response => {
          const specSelectedList = response.data.specSelectedList
          this.formData = { ...this.formData, ...response.data.acceptEntity }
          for (let i = 0, len = specSelectedList.length; i < len; i++) {
            array.push(specSelectedList[i].specCode)
          }
          this.actualFlag = response.data.actualFlag
          this.formData.specLists = array
          this.specList = response.data.specList
          this.formData.relevantTableId = this.relevantTableId
          this.problemSourceList = response.data.problemSourceList
          this.relevantTableId = response.data.acceptEntity.id
          this.relevantTableName = response.data.acceptEntity.relevantTableName
          this.formData.correspondingAdverseEffects = response.data.correspondingAdverseEffects;
          //判断是否显示关联台账
          if(this.formData.problemSource){
            this.problemSourceChanged(this.formData.problemSource)
          }
          this.loading = false
          this.$nextTick(() => {
            this.scopeSituation = true
            this.$refs.pers.DueryDepartmentSelectInfo()
            this.$refs.file.ViolationFileItems()
          })
          this.$emit('closeLoading')
        }
      )
    },
    /** 提交数据*/
    nextStep() {
      const that = this;
      checkActualMainDept({problemId: this.problemId}).then(res => {
        if(!res.data.flag){
          //不发起实时，直接下一步
          that.$emit('handle', 1)
        }else{
          //发起实时，弹出提示
          this.$modal.confirm('即将以"' + res.data.compenyName + '"为主责单位发起实时，是否确认？').then(function() {
            that.$emit('handle', 1);
          }).then(() => {
          }).catch(() => {});
        }
      })

    },
    /** 保存数据*/
    publicSave() {
      // this.formData.specList=[];
      const array = []
      const specSelectedList = this.formData.specLists
      const specList = this.specList
      for (let i = 0, len = specSelectedList.length; i < len; i++) {
        for (let j = 0, leng = specList.length; j < leng; j++) {
          if (specList[j].dictValue == specSelectedList[i]) {
            array.push(specList[j])
          }
        }
      }
      this.formData.specList = array
      saveViolateInfo(this.formData).then(
        response => {
          this.$modal.msgSuccess('保存成功')
        }
      )
    },
    resetForm() {
      this.$refs['elForm'].resetFields()
    },
    // 打开弹窗
    show() {
      this.visible = true
    },
    // 关闭弹窗
    closeTree() {
      this.visibleTree = false
      this.$refs.pers.DueryDepartmentSelectInfo()
    },
    // 选择人员
    treeOpen() {
      this.flag = !this.flag
      this.visibleTree = true
    }
  }
}

</script>
<style scoped>
.tree-body-dialog ::v-deep.el-dialog__body{
  padding:0 20px;
}
</style>
