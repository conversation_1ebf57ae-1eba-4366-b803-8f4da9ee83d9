<template>
  <div class="height">
    <el-row class="height">
      <div class="process-left">
        <div class="processSeeBox float-left">
          <div class="process-box">
            <div v-for="(item,index) in processBox" :class="
        processIndex==item.problemStatus?problemStatus==item.problemStatus?'active selects process-li':'green selects process-li':
        problemStatus==item.problemStatus?'active green process-li':problemStatus<item.problemStatus?'process-li':'green process-li'"
                 @click="iframeUrl(item.problemStatus)">
              <span class="process-number float-left">{{item.problemStatus}}</span>
              <div class="process-name float-right">
                <span class="span1">{{item.statusName}}</span>
                <span class="span2 name" v-if="item.userName">操作人：{{item.userName}}</span>
                <span class="span2 time-title" v-if="item.endTime">通过时间：</span>
                <span class="span2 time" v-if="item.endTime">{{item.endTime}}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="process-content">
        <URL1
          v-if="processIndex==='1'"
          :key="actualProblemId"
          ref="accepts"
          :detail='detail'
          isShow="0"
          :procInsId="procInsId"
          :field="actualProblemId"
          @handle="handle"
        ></URL1>
        <URL2
          v-if="processIndex==='2'"
          :key="actualProblemId"
          ref="accepts"
          :detail='detail'
          isShow="0"
          :procInsId="procInsId"
          :field="actualProblemId"
          @handle="handle"
        ></URL2>
        <URL3
          v-if="processIndex==='3'"
          :key="actualProblemId||processBox[2].procInsId"
          ref="accepts"
          :detail='detail'
          isShow="1"
          :procInsId="processBox[2].procInsId"
          :field="actualProblemId"
          @handle="handle"
        ></URL3>
        <URL4
          v-if="processIndex==='4'"
          :key="actualProblemId||processBox[3].procInsId"
          ref="accepts"
          :detail='detail'
          isShow="1"
          :procInsId="processBox[3].procInsId"
          :field="actualProblemId"
          @handle="handle"
        ></URL4>
        <URL5
          v-if="processIndex==='5'"
          :key="actualProblemId||processBox[4].procInsId"
          ref="accepts"
          :detail='detail'
          isShow="1"
          :procInsId="processBox[4].procInsId"
          :field="actualProblemId"
          @handle="handle"
        ></URL5>
      </div>
    </el-row>
  </div>
</template>

<script>
  import URL1 from './actualFiveDaysReportRead';
  import URL2 from './actualFifteenDaysReportRead';
  import URL3 from './actualThirtyDaysReportRead';
  import URL4 from './actualProgressReportRead';
  import URL5 from './actualCheckDisReportRead';
  import {selectActualFlowInfo} from '@/api/actual/index';

  export default {
    name: "detail",
    props: {
      selectValue: {},
      actualProblemId: {
        type: String
      },
      actualProblemStatus: {
        type: String
      },
    procInsId:{
      type: String
    }
    },
    components: {
      URL1, URL2, URL3, URL4, URL5
    },
    data() {
      return {
        detail:true,
        activeName: 1,
        processIndex: 1,
        edit: false,
        problemStatus: '',
        processBox: [],
      }
    },
    mounted() {
      this.SelectActualFlowInfo();
    },
    methods: {
      iframeUrl(index) {
        if (index > this.problemStatus) {
          return false;
        } else if (index == this.problemStatus) {
          this.processIndex = index;
        } else {
          this.processIndex = index;
        }
        this.$nextTick(()=>{
          this.$refs.accepts.show();
        })
      },
      //环节名称
      SelectActualFlowInfo() {
        this.problemStatus = this.actualProblemStatus ? this.actualProblemStatus : '1';
        this.processIndex = this.actualProblemStatus ? this.actualProblemStatus : '1';
        selectActualFlowInfo({actualProblemId: this.actualProblemId}).then(response => {
          this.processBox = response.data;
          this.$nextTick(()=>{
            this.$refs.accepts.show();
          })
        });
      }
    }
  }
</script>

<style scoped lang="scss">
  .processSeeBox {
    width: 240px;
    margin-top: 10px;

    .process-box {
      height: auto;
      display: block;
      border-bottom: 0px;
      margin-top: 6px;
    }

    .process-box:before {
      position: absolute;
      content: '';
      width: 0;
      height: 0;
    }

    .process-li:before {
      position: absolute;
      content: "";
      left: 33px;
      top: 42px;
      width: 1px;
      z-index: 10;
      height: 47px;
      border-left: 1px solid rgb(217, 217, 217);
    }

    .process-li {
      width: 100%;
      min-height: 100px;
      box-sizing: border-box;
      position: relative;
      overflow: hidden;
      padding-left: 18px;

      .process-number {
        width: 32px;
        height: 32px;
        background-color: #fff;
        font-size: 16px;
        color: #f5222d;
        border: solid 1px #f5222d;
        position: relative;
        border-radius: 50%;
        text-align: center;
        line-height: 32px;
      }

      .process-name {
        width: 170px;

        span {
          display: block;
        }

        .span1 {
          line-height: 32px;
          color: #000000;
          opacity: 0.85;
          font-size: 16px;
          font-weight: bold;
        }

      }
    }
    .process-li.suspension:after {
      position: absolute;
      content: "";
      width: 70px;
      height: 100px;
      background-size: 70px;
      opacity: 0.7;
      right: 24px;
    }

    .process-li.complete:after {
      position: absolute;
      content: "";
      width: 100px;
      height: 100px;
      background-size: 70px;
      opacity: 0.7;
      right: 24px;
    }

    .process-li:last-child:before {
      position: absolute;
      content: "";
      left: 33px;
      top: 42px;
      width: 1px;
      z-index: 10;
      height: 0;
      border-width: 0;
    }

    .process-li.green {
      cursor: pointer;

      .process-number {
        background-color: #ffe2e4;
        color: #f5222d;
        border-color: #f5222d;
      }

      .process-name {
        color: #a9b0b4;
      }

    }
    .process-li.active {
      cursor: pointer;

      .process-number {
        background: #f5222d;
        color: #fff;
        border-color: #f5222d;
      }

      .process-name {
        color: #000;

        .span1 {
          font-weight: bold;
        }

      }
    }
    .process-li.selects {

      .process-number:before {
        position: absolute;
        content: '';
        bottom: -8px;
        width: 100%;
        left: 0;
        height: 4px;
        background-color: #f5222d;
      }

    }
  }

  .process-right {
    padding: 8px;
    float: right;
    width: calc(100% - 270px);
    box-shadow: 0px 0px 10px 0px rgb(0 0 0 / 10%);
    border-radius: 2px;
    border: solid 1px #d9d9d9;
    box-sizing: border-box;
  }

  @media screen and ( max-width: 1680px ) {
    .processSeeBox {

      .process-li {
        min-height: 90px;
      }

      .process-li:before {
        height: 40px;
      }
    }

  }

  @media screen and ( max-width: 1480px ) {
    .processSeeBox {

      .process-li {
        min-height: 78px;

        .process-number {
          width: 28px;
          height: 28px;
          line-height: 29px;
          font-size: 14px;
        }

        .process-name {
          font-size: 12px;

          .span1 {
            font-size: 14px;
          }
        }

      }
      .process-li:before {
        top: 38px;
        left: 31px;
        height: 30px;
      }

    }
  }

  @media screen and ( max-width: 1300px ) {
    .processSeeBox {

      .process-li {
        min-height: 58px;

        .process-name {

          .span1 {
            line-height: 20px;
            font-size: 12px;
          }
        }

        span.time-title {
          display: none;
        }

      }
    }
  }

  .position-top {
    position: fixed;
    top: 0;
  }

  .process-box:before {
    position: absolute;
    content: '';
    left: 0;
    top: 31px;
    width: 100%;
    z-index: 10;
    height: 1px;
    border-bottom: 1px solid #d9d9d9;
  }

  .verify-top-title {
    color: #a9b0b4;
    padding: 10px 0;
    text-align: center;
    border-bottom: 1px solid #d9d9d9;
  }

  .verify-bottom-title {
    color: #a9b0b4;
    padding: 10px 0;
    text-align: center;
    border-top: 1px solid #d9d9d9;
  }


  .process-box {
    height: 64px;
  }

  .process-left {
    float: left;
    width: 270px;
    height: 100%;
    overflow-x: hidden;
    overflow-y: auto;
  }

  .process-content {
    border: 1px solid #ddd;
    width: calc(100% - 270px);
    height: 100%;
    overflow: auto;
  }

</style>
