<template>
  <div class="trig-tag text-center" :style="{background:bgColor?bgColor:''}">
    <slot></slot>
  </div>
</template>

<script>
    export default {
       name: "trig-tag",
        props: {
          bgColor: {
           type: String
        },
      }
    }
</script>

<style scoped>
    .trig-tag{
      display: inline-block;
      height: 18px;
      font-size: 12px;
      font-weight: 400;
      line-height: 18px;
      color: #FFFFFF;
      width: 32px;
      background: #F3545C;
      border-radius: 2px;
    }
</style>
