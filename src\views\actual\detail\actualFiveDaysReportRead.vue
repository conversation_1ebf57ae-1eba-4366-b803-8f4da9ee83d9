<!-- 监督追责实时报送-五个工作日实时报告快报 -->
<template>
  <div class="height100 app-report">
    <ModifyrecordBtn
      :key="detailInfo"
      :businessData="detailInfo"
    ></ModifyrecordBtn>
      <Jscrollbar :height="detail?'100%':'68vh'">
        <el-row class="el-dialog-div">
          <el-col :span="24">
            <BlockCard
              title="基本信息"
            >
              <el-form ref="elForm" :model="detailInfo" :rules="rules" size="medium" label-width="150px">
                <el-row>
                  <el-col :span="8">
                    <el-form-item label="系统编号">
                      <span> {{detailInfo.auditCode}}</span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="问题编号">
                      <span> {{detailInfo.problemCode}}</span>
                    </el-form-item>
                  </el-col>

                  <el-col :span="24">
                    <el-form-item label="违规事项 ">
                      <span class="cursor text-red" @click="dailyDetail"> {{detailInfo.problemTitle}}</span>
                    </el-form-item>
                  </el-col>

                  <el-col :span="8">
                    <el-form-item label="发生时间" prop="findTime">
                      <span> {{detailInfo.findTime}}</span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="损失金额（万元）" prop="lossAmount">
                      <span> {{(detailInfo.lossAmount).toFixed(2)}}</span>
                    </el-form-item>
                  </el-col>

                  <el-col :span="8">
                    <el-form-item label="损失风险（万元）" prop="lossRisk">
                      <span> {{(detailInfo.lossRisk).toFixed(2)}}</span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="涉及企业级次" prop="involveUnitGrade">
                      <span>{{detailInfo.involveUnitGrade}}</span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="24">
                    <el-form-item  label="涉及企业名称">
                      <div class="select-list">
                        <div v-for="(item,index) of unitData" :key="index" class="list-li">
                          <span>{{ item.involveUnitName }}</span>
                        </div>
                      </div>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
            </BlockCard>
          </el-col>
          <el-col :span="24">
            <BlockCard
              title="5 个工作日实时报告快报"
            >
              <el-form size="medium" label-width="150px">
                <el-row>
                  <el-col :span="12">
                    <el-form-item label="问题线索来源" prop="problemSource">
                      <el-select v-model="detailInfo.problemSource" disabled  placeholder="请选择问题线索来源" clearable
                                 :style="{width: '100%'}">
                        <el-option v-for="(item, index) in problemSourceList" :key="index" :label="item.dictLabel"
                                   :value="item.dictValue">{{item.dictLabel}}</el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="24">
                    <el-form-item label="问题线索描述" prop="problemDescribe">
                      <span>{{detailInfo.problemDescribe}}</span>
                    </el-form-item>
                  </el-col>

                  <el-col :span="24">
                    <el-form-item label="其他严重不良后果" prop="otherSeriousAdverseEffects">
                      <span>{{detailInfo.otherSeriousAdverseEffects}}</span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="24">
                    <actualSituationRange
                      :key="actualProblemId"
                      :edit='edit'
                      :actualProblemId="actualProblemId"
                      :relevantTableId="relevantTableId"
                      :relevantTableName="relevantTableName"
                      ref="scope"
                    ></actualSituationRange>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="联系人" prop="companyContacts">
                      <span>{{detailInfo.companyContacts}}</span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="联系电话" prop="contactsTel">
                      <span>{{detailInfo.contactsTel}}</span>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
            </BlockCard>
          </el-col>
          <el-col :span="24">
            <BlockCard
              title="报告附件"
            >
              <FileUpload
                :edit='edit'
                :problemId="field"
                :relevantTableId="relevantTableId"
                :relevantTableName="relevantTableName"
                flowType="VIOL_ACTUAL"
                problemStatus="1"
                linkKey="a001"
                ref="file"
                flowKey = "SupervisionDailyReport"
              ></FileUpload>
            </BlockCard>
          </el-col>
          <el-col :span="24">
            <BlockCard
              title="待阅接收人"
            >
              <el-form size="medium" label-width="50px">
                <el-row>
                  <el-col :span="24" v-if="detailInfo.orgGrade=='A'||detailInfo.orgGrade=='G'||detailInfo.orgGrade=='P'">
                    <el-form-item label="集团">
                      <div  style="padding:4px 0">
                        <ul class="float-left">
                          <li v-for="item in groupData.G" class="depart_li">
                            <span class="float-left">{{item.receiverShowName}}</span>
                          </li>
                        </ul>
                      </div>
                    </el-form-item>
                  </el-col>
                  <el-col :span="24" v-if="detailInfo.orgGrade=='A'||detailInfo.orgGrade=='P'">
                    <el-form-item label="省分">
                      <div   style="padding:4px 0">
                        <ul class="float-left">
                          <li v-for="item in groupData.P" class="depart_li">
                            <span class="float-left">{{item.receiverShowName}}</span>
                          </li>
                        </ul>
                      </div>
                    </el-form-item>
                  </el-col>
                  <el-col :span="24" v-if="detailInfo.orgGrade=='A'">
                    <el-form-item label="地市">
                      <div   style="padding:4px 0">
                        <ul class="float-left">
                          <li v-for="item in groupData.A" class="depart_li">
                            <span class="float-left">{{item.receiverShowName}}</span>
                          </li>
                        </ul>
                      </div>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                </el-row>
              </el-form>
            </BlockCard>
          </el-col>
        </el-row>
      </Jscrollbar>
    <el-dialog :visible.sync="VisibleCheckTree" width="60%" append-to-body title="涉及企业名称">
      <CheckTree
        :key="selectTree"
        ref="checkTree"
        :url="url"
        :selectTree="selectTree"
        :params="{
        actualProblemId:actualProblemId,
        involveUnitName:'',
        relevantTableId:relevantTableId
        }"
        @list="persList"
      />
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="savePers">保存</el-button>
      </div>
    </el-dialog>
    <Recipient
      ref="recipient"
      :key="receiverGrade||actualProblemId"
      :actualProblemId="actualProblemId"
      :relevantTableId="relevantTableId"
      :relevantTableName="relevantTableName"
      :receiverGrade="receiverGrade"
      @save="ActualReadReceiverGroupData"
    >
    </Recipient>
    <ModifyRecord
      ref="modify"
      :key="receiverGrade||actualProblemId"
      :actualProblemId="actualProblemId"
      :relevantTableId="relevantTableId"
      :relevantTableName="relevantTableName"
      :type="edit"
      @saveModify="saveModify"
    >
    </ModifyRecord>
    <el-dialog :visible.sync="dailyVisible" width="90%" :title="'日常问题-'+detailInfo.problemTitle" append-to-body>
      <Details
        :key="detailInfo"
        :selectValue="detailInfo"
        activeName="0"
      >
      </Details>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary"  @click="dailyClose" >确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
  import {queryFiveReportInfo, saveFiveReport, fiveReportCompareWithDailyProblem, submitFiveReport} from "@/api/actual/task/actualFiveDaysReport";
  import {actualReadReceiverGroupData, deleteActualReadReceiver, actualReadReceiverCandidate} from '@/api/actual/common/actualReadReceiver';
  import {queryActualInvolveUnit, deleteActualInvolveUnit, saveActualInvolveUnitData} from '@/api/actual/common/actualInvolveUnit';
  import BlockCard from "@/components/BlockCard";
  import actualSituationRange from '@/views/actual/common/actualSituationRange';//范围情形选择页面
  import FileUpload from '../../components/fileUpload/index';//附件
  import CheckTree from '../common/checkTree';// checkTree
  import Recipient from '../common/recipient';// recipient
  import ModifyRecord from '../common/modifyRecord';// modifyRecord
  import ModifyrecordBtn from '../common/modifyRecordBtn';
  import Details from '@/views/daily/actualDetail';

  export default {
    components: {BlockCard,actualSituationRange,FileUpload,CheckTree,Recipient,ModifyRecord,ModifyrecordBtn,Details},
    props: {
      field:{
        type: String
      },
      detail:{
        type: Boolean,
        default:false
      }
    },
    data() {
      return {
        dailyVisible:false,
        selectTree:[],
        VisibleCheckTree:false,
        url:'colligate/violActualInvolveUnit/actualInvolveUnitTreeData',
        actualProblemId: "1",
        relevantTableId: undefined,
        relevantTableName: undefined,
        edit: false,
        flag:false,
        visible:false,
        visibleTree:false,
        detailInfo:'',
        findTime: null,
        acceptTime: null,
        problemSource:null,
        problemTitle: null,
        problemDescribe: undefined,
        contactsTel: undefined,
        lossAmount: 0,
        lossRisk: 0,
        groupReceivers: undefined,
        provinceReceivers: undefined,
        seriousAdverseEffectsFlag: 1,
        otherSeriousAdverseEffects: undefined,
        illegalActivities: undefined,
        companyContacts: undefined,
        involveUnitGrade:'',
        specList: [],
        problemSourceList:[],
        unitData:[],
        groupData:{},//待阅接收人
        receiverGrade:'G'
      }
    },
    computed: {},
    watch: {},
    created() {
    },
    mounted() {
    },
    methods: {
      cancel(){
        this.visible=false;
      },
      /**初始化数据*/
      show(){
        this.visible=true;
        queryFiveReportInfo(this.field).then(
          response => {
            const { code, data } = response
            if (code === 200) {
              this.detailInfo = Object.assign({}, data);
              this.actualProblemId = this.detailInfo.actualProblemId;
              this.relevantTableId = this.detailInfo.id;
              this.relevantTableName = 'T_COL_VIOL_ACTUAL_FIVE_REPORT';
              this.detailInfo.businessTable = this.relevantTableName;
              this.problemSourceList = this.detailInfo.problemSourceOptions;
              this.$nextTick(()=>{
                this.$refs.file.ViolationFileItems();
              });
              this.QueryFiveReportInvolveUnit();
              this.ActualReadReceiverGroupData();
            }
          }
        );
      },
      //企业数据
      QueryFiveReportInvolveUnit(){
        queryActualInvolveUnit({actualProblemId: this.detailInfo.actualProblemId, relevantTableId: this.detailInfo.id}).then(
          response => {
            this.selectTree = [];
            this.detailInfo.involveUnitGrade = response.involveUnitGrade;
            this.unitData = response.data;
            for(let i=0;i<this.unitData.length;i++){
              this.selectTree.push({id:this.unitData[i].compareId,name:this.unitData[i].involveUnitName})
            }
          }
        );
      },
      //待阅接收人
      ActualReadReceiverGroupData(){
        actualReadReceiverGroupData({actualProblemId: this.detailInfo.actualProblemId, relevantTableId: this.relevantTableId}).then(response => {
          this.groupData = response.data;
        });
      },

      saveModify() {},
      dailyDetail(){
        this.dailyVisible=true;
      },
      dailyClose(){
        this.dailyVisible=false;
      }
    }
  }

</script>
<style lang="scss" scoped>
  .dialog-body {
    height: 70vh;
  }
  .depart_li {
    min-width: 84px;
    height: auto;
    position: relative;
    background-color: #e6f7ff;
    color: #40a9ff;
    line-height: 30px;
    margin: 0 6px 0;
    display: inline-block;
    padding: 0 30px 0 12px;
    border-radius: 2px;
    box-sizing: border-box;
    .icon {
      float: right;
      cursor: pointer;
      position: absolute;
      right: 8px;
      top: 6px;
      font-size: 16px;
    }
  }
</style>
