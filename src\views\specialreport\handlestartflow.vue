
<!-- 初核报告--手动发起待办（调用定时任务） -->

<template>
  <div class="wai-container" style="background-color: #fff">
    <div class="layui-row width height">
      <div class="width height">
        <div class="common-wai-box" style="height: 100%">
          <div class="common-in-box" style="height: auto; min-height: 100%">
            <div class="common-in-box-header">
              <div class="common-in-box-header-line" />
                  初核专项手动执行
              <div class="flex-1" />
            </div>
          </div>
        </div>
      </div>
    </div>

    <el-form :model="formData" ref="queryForm" :inline="true" label-width="68px">
      1、<el-form-item label="项目id" prop="projectId">
        <el-input
          v-model="formData.projectId"
          placeholder="项目id"
          clearable
          size="small"
          style="width: 240px"
        />
        <el-button
          size="mini"
          @click="handleReadFlow()"

        >发起专项待阅
        </el-button>
      （原本由审计系统调用监督追责发起的待阅，提供手动发起）
      </el-form-item>
    </el-form>

    <el-form :inline="true" label-width="68px">
      2、<el-form-item label="" prop="projectId">
      <el-button
        size="mini"
        @click="handleStartFlow()"
      >发起专项定时待办
      </el-button>
    </el-form-item>
      （待阅发起45工作日后，发起的待办）
    </el-form>

  </div>
</template>
<script>
  import {
    handleStartFlow
    ,handleReadFlow
  } from '@/api/special-report'
  export default {
    components: { },
    props: {
      // 编辑内容
      rowData: {
        type: Object,
        default: () => {}
      },
      //流程参数
      centerVariable: {
        type: Object
      },
    },
    data() {
      return {
        tableLoading: false, // 表格loading
        id : '',//主键
        // 参数
        formData: {
          projectId: undefined
        },
      }
    },
    created() {
      this.id = this.centerVariable.busiKey;

    },
    methods: {

      //发起45工作日后的待办
      handleStartFlow(){
        handleStartFlow(this.id).then((res)=>{
        })
      },

      //发起审计系统调用监督追责系统的待阅
      handleReadFlow(){
        handleReadFlow({businessKey:this.formData.projectId}).then((res)=>{

        })
      },



      openLoading(){//打开加载...
        this.$emit('openLoading');
      },
      closeLoading(){//关闭加载...
        this.$emit('closeLoading');
      },
    }
  }
</script>
<style lang="scss" scoped>
  @import "~@/assets/styles/quarterly-report/index.css";
</style>
