<template>
  <div>
    <el-form ref="dataDetails" :model="dataDetails" label-width="150px">
      <el-row>
        <el-col :span="8">
          <el-form-item label="禁止人姓名">
            <span>{{ dataDetails.userName }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="性别">
            <span>{{ dataDetails.sex=='1'?'男':'女' }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="身份证件号">
            <span>{{ dataDetails.idCard }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="24" />
        <el-col :span="8">
          <el-form-item label="所在企业">
            <span>{{ dataDetails.involAreaName }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="企业层级">
            <span>{{ dataDetails.orgGradeName }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="处理前职位">
            <span>{{ dataDetails.postName }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="24" />
        <el-col :span="8">
          <el-form-item label="干部类别">
            <el-select v-model="dataDetails.cadreCategory" disabled placeholder="请选择" style="width: 100%">
              <el-option
                v-for="item in dataDetails.cadreCategoryList"
                :key="item.dictValue"
                :label="item.dictLabel"
                :value="item.dictValue"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="禁入限制期间开始">
            <el-date-picker v-model="dataDetails.limitStartTime" readonly type="date" placeholder="选择禁入限制期间开始" style="width: 100%;" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="禁入限制期间结束">
            <el-date-picker v-model="dataDetails.limitEndTime" readonly type="date" placeholder="选择禁入限制期间结束" style="width: 100%;" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item label="责任追究处理部门">
            <div class="list1">
              <div v-for="(item,index) in relevorgByType" class="list1-one">
                <span>{{ item.orgName }}</span>
              </div>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="责任处理联系人">
            <div v-for="(item,index) in relevpersonByType" class="list1">
              <div class="list1-one">
                <span>{{ item.userName }}</span>
              </div>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="责任处理联系方式">
            <span>{{ dataDetails.contactInformation }}</span>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="工作简历">
            <span>{{ dataDetails.workResume }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="违规问题">
            <span>{{ dataDetails.violationsProblem }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="不良后果">
            <span>{{ dataDetails.adverseConsequences }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="责任认定情况">
            <span>{{ dataDetails.responIdenty }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="责任追究处理情况">
            <span>{{ dataDetails.accountabHandle }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="备注">
            <span>{{ dataDetails.remark }}</span>
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item label="附件列表">
            <el-table :data="fileList" max-height="250" style="width: 100%" border :show-header="false" :cell-class-name="rowClass">
              <el-table-column label="序号" type="index" min-width="10%" align="center" />
              <el-table-column label="文档名称" prop="fileName" min-width="56%" />
              <el-table-column label="上传人" prop="uploaderName" min-width="10%" />
              <el-table-column label="上传时间" prop="createTime" :formatter="dateFormat" min-width="15%" />
              <el-table-column label="操作" fixed="right" min-width="9%" align="center" class-name="small-padding fixed-width">
                <template slot-scope="scope">
                  <i class="el-icon-download" style="color: red;margin-right: 15px;cursor: pointer;" title="下载" @click="fileDownload(scope.row)" />
                  <!--<i class="el-icon-delete" style="color: red;cursor: pointer;" title="删除" @click="deleteFile(scope.row)" />-->
                </template>
              </el-table-column>
            </el-table>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <el-dialog :visible.sync="VisibleRadioTree" width="60%" append-to-body title="责任追究处理部门">
      <RadioTree
        v-if="VisibleRadioTree"
        :key="id"
        ref="radioTree"
        url="/colligate/relevorg/queryRelevorgTree"
        :select-tree="[]"
        :params="{
          problemId:problemId,
          relevantTableId:relevantTableId,
          relevorgType:'HANDLE_DEPARTMENT'
        }"
        @accept="orgList"
      />
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="getTree">保存</el-button>
      </div>
    </el-dialog>
    <el-dialog :visible.sync="VisibleConTree" width="60%" append-to-body title="责任处理联系人">
      <RadioConTree
        v-if="VisibleConTree"
        :key="id"
        ref="conTree"
        url="/colligate/violDailyRelevperson/checkStaffOrgTree"
        :select-tree="[]"
        :params="{
          problemId:problemId,
          relevantTableId:relevantTableId,
          personType: 'HANDLE_PERSON'
        }"
        @accept="conList"
      />
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="conTree">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { queryLimitedPersonInfoById, queryRelevorgByType, saveRelevorg, delrelevorg, queryRelevpersonByType, delCheckGroupMember, saveCheckGroupMember, saveLimitedPersonInfo, selectViolFilesPage } from '@/api/daily/process/handlingAppealRecords'
import RadioTree from './../tree/radioTree'// tree
import RadioConTree from './../tree/radioConTree'// tree
export default {
  name: 'Edit',
  components: {
    RadioConTree,
    RadioTree
  },
  props: {
    id: {
      type: String,
      default: ''
    },
    problemId: '',
    relevantTableId: '',
    relevantTableName: '',
    deleteEnable: {type: Boolean}
  },
  data() {
    return {
      VisibleConTree: false,
      VisibleRadioTree: false,
      dataDetails: {},
      relevorgByType: [],
      relevpersonByType: [],
      sexRadio: [
        { label: '男', value: 1 },
        { label: '女', value: 2 }
      ],
      fileList: []
    }
  },
  created() {
    this.limitPersonInfo()
    this.selectViolFilesPage()
  },
  methods: {
    rowClass({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 1 || columnIndex === 2 || columnIndex === 3) {
        return 'no-right-border'
      } else if (columnIndex === 0) {
        return 'cell-color'
      }
    },
    // 查询file列表
    selectViolFilesPage() {
      selectViolFilesPage({
        busiTableId: this.relevantTableId,
        problemId: this.problemId,
        limit: 9999999,
        page: 1
      }).then(
        response => {
          if (response.code === 200) {
            this.fileList = response.data
          }
        }
      )
    },
    // 下载附件
    fileDownload(obj) {
      this.download('/sys/attachment/downloadSysAttachment/' + obj.attachmentId, {
      }, obj.fileName)
    },
    addA() {
      this.VisibleRadioTree = true
    },
    addB() {
      this.VisibleConTree = true
    },
    // 保存
    getTree() {
      this.$refs.radioTree.save()
    },
    // 保存人
    conTree() {
      this.$refs.conTree.save()
    },
    orgList(data) {
      if (data.length) {
        saveRelevorg({
          checkStyle: 'radio',
          orgIds: [data[0].id],
          orgName: data[0].name,
          problemId: this.problemId,
          relevantTableId: this.relevantTableId,
          relevantTableName: this.relevantTableName,
          relevorgType: 'HANDLE_DEPARTMENT'
        }).then(
          response => {
            this.QueryRelevorgByType()
            this.VisibleRadioTree = false
            this.dataDetails.accountabDepartment = data[0].name
          }
        )
      }
    },
    DelOrgList(orgId) {
      delrelevorg({
        orgId: orgId,
        problemId: this.problemId,
        relevantTableId: this.relevantTableId,
        relevantTableName: this.relevantTableName,
        relevorgType: 'HANDLE_DEPARTMENT'
      }).then(
        response => {
          this.QueryRelevorgByType()
        }
      )
    },
    DelConList(postId) {
      delCheckGroupMember({
        personType: 'HANDLE_PERSON',
        postId: postId,
        problemId: this.problemId,
        relevantTableId: this.relevantTableId,
        relevantTableName: this.relevantTableName
      }).then(
        response => {
          this.QueryRelevpersonByType()
        }
      )
    },

    conList(data) {
      if (data.length) {
        saveCheckGroupMember({
          problemId: this.problemId,
          relevantTableId: this.relevantTableId,
          personType: 'HANDLE_PERSON',
          relevantTableName: this.relevantTableName,
          postIds: [data[0].id],
          userName: data[0].name,
          orgId: data[0].pId,
          phone: data[0].phone,
          checkStyle: 'radio'
        }).then(
          response => {
            this.QueryRelevpersonByType()
            this.VisibleConTree = false
            this.dataDetails.contactsName = data[0].name
          }
        )
      }
    },
    /** 查询禁入限制人员详情*/
    limitPersonInfo() {
      queryLimitedPersonInfoById({ id: this.id }).then(
        response => {
          this.dataDetails = {
            ...response.data
          }
          this.QueryRelevorgByType()
          this.QueryRelevpersonByType()
        }
      )
    },
    // 查询组织
    QueryRelevorgByType() {
      queryRelevorgByType({
        problemId: this.problemId,
        relevantTableId: this.relevantTableId,
        relevorgType: 'HANDLE_DEPARTMENT'
      }).then(
        response => {
          this.relevorgByType = response.data
        }
      )
    },
    // 查询联系人
    QueryRelevpersonByType() {
      queryRelevpersonByType({
        problemId: this.problemId,
        relevantTableId: this.relevantTableId,
        personType: 'HANDLE_PERSON'
      }).then(
        response => {
          this.relevpersonByType = response.data
        }
      )
    },
    // 最终保存
    saveLimitedPersonInfo() {
      this.$emit('close', this)
    }
  }
}
</script>
<style scoped lang="scss">
  .list1 {
    overflow: hidden;
    .list1-one {
      background-color: #e6f7ff;
      color: #40a9ff;
      margin: 0 10px 2px 10px;
      float: left;
      height: 30px;
      line-height: 30px;
      padding: 0 12px 0 12px;
      border-radius: 2px;
      .close {
        padding: 8px;
        cursor: pointer;
      }
    }
  }
  ::v-deep.el-textarea .el-input__count {
    color: #909399;
    background: rgba(255,255,255,0);
    position: absolute;
    font-size: 12px;
    bottom: 5px;
    right: 10px;
  }
</style>
