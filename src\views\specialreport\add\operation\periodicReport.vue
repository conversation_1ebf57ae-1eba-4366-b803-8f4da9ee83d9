<!--阶段性报告-->
<template>
  <div class="scope">
    <el-dialog :visible.sync="visible" @close="close" :modal-append-to-body="false" append-to-body :title="title" width="90%">
      <BlockCard title="项目基本信息">
        <el-form ref="elForm" :model="formData" size="medium" label-width="128px">
          <el-row>
            <el-col :span="16">
              <el-form-item label="项目名称" prop="projectName">
                {{formData.projectName}}
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="项目编号" prop="projectNum">
                {{formData.projectNum}}
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="项目类型" prop="projectTypeEnumId">
                {{formData.projectTypeEnumId | fromatComon(dict.type.SPR_PROJECT_TYPE)}}
              </el-form-item>
            </el-col>

            <el-col :span="8">
              <el-form-item label="审计对象" prop="projectOrgName">
                {{formData.projectOrgName}}
              </el-form-item>
            </el-col>

            <el-col :span="8">
              <el-form-item label="项目年度" prop="projectYear">
                {{formData.projectYear}}
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </BlockCard>

      <BlockCard
        title="审计报告信息"
      >
        <AuditReportInformationUpload
          :key="id"
          :id="id"
          ref="file"
        ></AuditReportInformationUpload>

      </BlockCard>
      <BlockCard title="项目台账信息">
        <el-form>
          <el-table border v-loading="tableLoading" :data="ledgerTable">
            <el-table-column type="index" width="50" align="center" label="序号" >
              <template slot-scope="scope">
                <table-index
                  :index="scope.$index"
                />
              </template>
            </el-table-column>
            <el-table-column label="问题编号" prop="problemNum" min-width="15%" show-overflow-tooltip align="center" />
            <el-table-column label="发现问题业务类型" prop="problemTypeEnumName" min-width="15%" show-overflow-tooltip align="center"/>
            <el-table-column label="审计发现问题" prop="problemAudit"  min-width="25%" show-overflow-tooltip align="center">
              <template slot-scope="scope">
                <div class="table-text-left ovflowHidden">{{ scope.row.problemAudit }}</div>
              </template>
            </el-table-column>
            <el-table-column label="具体问题描述" prop="problemDescription"  min-width="25%" align="center" show-overflow-tooltip>
              <template slot-scope="scope">
                <div  class="table-text-left ovflowHidden">{{ scope.row.problemDescription }}</div>
              </template>
            </el-table-column>
            <el-table-column label="是否上报告" prop="reportFlag"  min-width="8%" align="center" show-overflow-tooltip>
              <template slot-scope="scope" class="text-center">
                {{ scope.row.reportFlag==1?'是':scope.row.reportFlag==0?'否':'' }}
              </template>
            </el-table-column>
            <el-table-column label="是否追责" prop="ifDuty"  min-width="8%" align="center" show-overflow-tooltip>
              <template slot-scope="scope" class="text-center">
                {{ scope.row.ifDuty==1?'是':scope.row.ifDuty==0?'否':'' }}
              </template>
            </el-table-column>
            <el-table-column label="是否移交纪检" prop="transferFlag"  min-width="8%" align="center" show-overflow-tooltip>
              <template slot-scope="scope" class="text-center">
                {{ scope.row.transferFlag==1?'是':scope.row.transferFlag==0?'否':'' }}
              </template>
            </el-table-column>
          </el-table>
        </el-form>
      </BlockCard>
      <BlockCard
        title="初核专项报告"
      >
        <PreliminaryFileUpload
          :key="id"
          :projectId = "id"
          ref="file"
        ></PreliminaryFileUpload>

      </BlockCard>

      <BlockCard
        title="阶段性报告"
      >
        <StageWiseUpload
          :edit='edit'
          :key="id"
          :id="id"
          problemStatus="2"
          ref="stageWiseUpload"
        ></StageWiseUpload>

      </BlockCard>
      <div slot="footer">
        <el-button size="mini" type="primary" @click="toSaveStageFile" plain>保存</el-button>
        <el-button size="mini" @click="close">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import {
    queryLedgersList
    ,queryProjectInfo
    ,toSaveStageFile} from '@/api/special-report'
  import BlockCard from '@/components/BlockCard'
  import AuditReportInformationUpload from './auditReportInformationUpload';//审计报告信息
  import PreliminaryFileUpload from '@/views/specialreport/flow/operation/fileUpload';//初核审计报告
  import StageWiseUpload from './stageWiseUpload';//阶段性报告
  export default {
    name: "specialReportEdit",
    components: {BlockCard,AuditReportInformationUpload,PreliminaryFileUpload,StageWiseUpload},
    dicts: ['SPR_PROJECT_TYPE']
    , props: {},
    data() {
      return {
        edit:true,
        edit2:false,
        title: '',
        id: '',//主键
        visible: false,//弹框
        formData: {
          projectName: ''
          , projectNum: ''
          , projectTypeEnumId: ''
          , projectYear: ''
          , projectOrgId: ''
          , projectOrgName: ''
        },
        tableLoading: false, // 表格loading
        ledgerTable:[],//台账信息
        showTip:false,
      }
    }
    , created() {
    }
    , methods: {
      // 显示弹框
      show(id) {
        this.id = id;
        this.visible = true;
        this.queryProjectInfo()
        this.queryLedgersList()
      },
      //关闭弹窗
      close() {
        this.visible = false;
        this.$emit("close");
      },
      //根据主键查询项目信息
      queryProjectInfo(){
        queryProjectInfo(this.id).then((res)=>{
          this.formData = res.data;
          this.$forceUpdate();
        })
      },
      //根据主键查询台账信息
      queryLedgersList(){
        queryLedgersList(this.id).then((res)=>{
          //台账信息
          this.ledgerTable = res.data.ledgerReturnList;
          this.$forceUpdate();
        })
      },

      //保存提交阶段性报告后的项目状态
      toSaveStageFile(){
        toSaveStageFile(this.id).then((res)=>{
          this.$modal.msgSuccess("操作成功");
          this.close();
        })
      }


    }
  }
</script>

<style lang="scss" scoped>
  ::v-deep .el-select {
    width: 100%;
  }

  ::v-deep .el-dialog__body {
    height: 70vh;
    overflow: auto;
    padding-top: 10px !important;
    background: #fff !important;
  }
</style>
