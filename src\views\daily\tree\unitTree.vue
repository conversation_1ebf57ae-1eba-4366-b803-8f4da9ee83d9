<template>
  <el-row class="height">
    <el-col :span="24" class="tree-box">
      <el-tree
        ref="tree"
        :data="data"
        lazy
        show-checkbox
        :default-checked-keys="defaultTree"
        node-key="id"
        check-strictly
        :load="loadnode"
        :props="defaultProps"
        @check-change="checkChange"
        @node-click="nodeclick"
      />
    </el-col>
    <el-col :span="24" class="tree-bottom">
      <div class="flex border-top">
        <p>已选单位</p>
      </div>
      <TreeSelect
        type="company"
        :is-delete="isDelete"
        :select-tree="selectTree"
        @noCheck="noCheck"
      />
    </el-col>
  </el-row>
</template>

<script>
import { queryCompanyInvolved, saveCompanyInvolved, delCompanyInvolved } from '@/api/daily/tree'
import TreeSelect from '@/components/TreeSelect'

export default {
  components: {
    TreeSelect
  },
  props: {
    problemId: {
      type: String
    },
    relevantTableId: {
      type: String
    },
    relevantTableName: {
      type: String
    }

  },
  data() {
    return {
      isDelete: false,
      height: 0,
      data: [],
      selectTree: [],
      defaultTree: [],
      defaultProps: { // 树对象属性对应关系
        children: 'children',
        label: 'name',
        isLeaf: function(data, node) {
          return !data.isParent
        },
        disabled: function(data, node) {
          return data.nocheck
        }
      }
    }
  },
  methods: {
    loadnode(node, resolve) {
      // 如果展开第一级节点，从后台加载一级节点列表
      if (node.level == 0) {
        this.loadfirstnode(resolve)
      }
      // 如果展开其他级节点，动态从后台加载下一级节点列表
      if (node.level >= 1) {
        this.loadchildnode(node, resolve)
      }
    },
    // 加载第一级节点
    loadfirstnode(resolve) {
      // resolve(this.data);
      this.defaultTree = []
      const query = {
        problemId: this.problemId,
        relevantTableId: this.relevantTableId,
        relevantTableName: this.relevantTableName,
        orgType: 'C',
        pIds: [],
        pId: ''
      }
      queryCompanyInvolved(query).then(
        response => {
          this.selectTree = response.data.selectedList
          for (let i = 0, len = this.selectTree.length; i < len; i++) {
            this.defaultTree.push(this.selectTree[i].involAreaCode)
          }
          resolve(response.data.resultList)
          this.$emit('newPid', this.defaultTree)
        }
      )
    },
    // 加载节点的子节点集合
    loadchildnode(node, resolve) {
      const query = {
        problemId: this.problemId,
        relevantTableId: this.relevantTableId,
        relevantTableName: this.relevantTableName,
        orgType: 'C',
        pIds: [],
        pId: node.data.id
      }
      queryCompanyInvolved(query).then(
        response => {
          resolve(response.data.resultList)
        }
      )
    },
    // 点击节点上触发的事件，传递三个参数，数据对象使用第一个参数
    nodeclick(data, dataObj, self) {},
    // 修改状态
    checkChange(node, type) {
      if (type) { // 选中
        // if (this.selectTree.length) {
        //   this.$modal.confirm('涉及单位只能选择一个，是否将原涉及单位删除？').then(() => {
        //     for (let i = 0, len = this.selectTree.length; i < len; i++) {
        //       this.noCheck(this.selectTree[i])
        //     }
        //     this.selectTree = []
        //     this.defaultTree = []
        //     this.SaveViolateInfo(node)
        //   }).catch(() => {
        //     this.$refs.tree.setChecked(node.id, false)
        //   })
        // } else {
          this.SaveViolateInfo(node)
        // }
      } else {
        let treeArry = [{ id: node.id }]
        if (node.orgDepartmentId) {
          treeArry = [{ id: node.orgDepartmentId }]
        }

        if (node.id === this.defaultTree[0]) {
          // this.selectTree.splice(this.selectTree.findIndex(item => item.involCompany === node.id), 1);
          // this.defaultTree.splice(this.defaultTree.findIndex(item => item === node.id), 1);
          // this.selectTree = []
          // this.defaultTree = []
          console.log(node)
          console.log(this.selectTree,this.defaultTree)
        }
        this.DelCompanyInvolved(treeArry)
      }
    },
    // 删除某节点
    noCheck(obj) {
      if (this.$refs.tree.getNode(obj.involAreaCode)) {
        this.$refs.tree.setChecked(obj.involAreaCode, false)
      } else {
        let treeArry = [{ id: obj.involCompany }]
        if (obj.orgDepartmentId) {
          treeArry = [{ id: obj.orgDepartmentId }]
        }
        this.DelCompanyInvolved(treeArry)
      }
    },
    /**
       * 保存
       * @return {boolean}
       */
    SaveViolateInfo(treeNode) {
      // this.selectTree = [treeNode]
      // this.defaultTree = [treeNode.id]
      for (let i = 0, len = this.selectTree.length; i < len; i++) {
        if (this.selectTree[i].involCompany === treeNode.id) {
          return false
        }
      }
      if (treeNode.orgDepartmentId) {
        treeNode.involCompanyName = treeNode.orgDepartmentName + '(' + treeNode.name + ')'
        treeNode.involCompany = treeNode.orgDepartmentId
      } else {
        treeNode.involCompanyName = treeNode.name + '(' + treeNode.pname + ')'
        treeNode.involCompany = treeNode.id
      }
      treeNode.involAreaCode = treeNode.id
      this.selectTree.push(treeNode);
      this.defaultTree.push(treeNode.id);
      const treeArry = [{
        id: treeNode.involAreaCode,
        name: treeNode.name,
        pId: treeNode.pid,
        pid: treeNode.pid,
        pName: treeNode.pname,
        orgGrade: treeNode.orgGrade,
        orgDepartmentId: treeNode.orgDepartmentId,
        orgDepartmentName: treeNode.orgDepartmentName
      }]
      const query = {
        problemId: this.problemId,
        relevantTableId: this.relevantTableId,
        relevantTableName: this.relevantTableName,
        companyList: treeArry
      }
      saveCompanyInvolved(query).then(
        response => {}
      )
      this.$emit('newPid', this.defaultTree)
    },
    /**
       * 删除
       */
    DelCompanyInvolved(treeArry) {
      const obj = treeArry[0]
      const query = {
        problemId: this.problemId,
        relevantTableId: this.relevantTableId,
        relevantTableName: this.relevantTableName,
        companyList: treeArry
      }
      delCompanyInvolved(query).then(
        response => {
          this.selectTree.splice(this.selectTree.findIndex(item => item.involCompany === obj.id), 1);
          this.defaultTree.splice(this.defaultTree.findIndex(item => item === obj.id), 1);
          this.$emit('newPid', this.defaultTree)
        }
      )
    }
  }
}
</script>
<style  rel="stylesheet/scss" scoped lang="scss">
  .tree-box{
    .is-disabled{
      display: none !important;
    }
  }
  .height{
    height: 100%;
  }
  .tree-body{
    height: 100%;
    .public-box-content{
      height: 60vh !important;
    }
    .tree-height{
      height: calc(100% - 40px);
      .tree-box{
        height: calc(100% - 100px);
        overflow: auto;
      }
      .tree-bottom{
        height: 140px;
        overflow: auto;
      }
    }
  }
</style>
