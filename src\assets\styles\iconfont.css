@font-face {
  font-family: "iconfont"; /* Project id 4825825 */
  src: url('//at.alicdn.com/t/c/font_4825825_example.woff2?t=1234567890') format('woff2'),
       url('//at.alicdn.com/t/c/font_4825825_example.woff?t=1234567890') format('woff'),
       url('//at.alicdn.com/t/c/font_4825825_example.ttf?t=1234567890') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 常用图标类名示例 - 请根据您的实际iconfont项目替换 */
.icon-home:before {
  content: "\e600";
}

.icon-user:before {
  content: "\e601";
}

.icon-setting:before {
  content: "\e602";
}

.icon-search:before {
  content: "\e603";
}

.icon-edit:before {
  content: "\e604";
}

.icon-delete:before {
  content: "\e605";
}

.icon-add:before {
  content: "\e606";
}

.icon-close:before {
  content: "\e607";
}

.icon-check:before {
  content: "\e608";
}

.icon-arrow-left:before {
  content: "\e609";
}

.icon-arrow-right:before {
  content: "\e60a";
}

.icon-arrow-up:before {
  content: "\e60b";
}

.icon-arrow-down:before {
  content: "\e60c";
}

.icon-menu:before {
  content: "\e60d";
}

.icon-more:before {
  content: "\e60e";
}

.icon-download:before {
  content: "\e60f";
}

.icon-upload:before {
  content: "\e610";
}

.icon-file:before {
  content: "\e611";
}

.icon-folder:before {
  content: "\e612";
}

.icon-image:before {
  content: "\e613";
}

.icon-video:before {
  content: "\e614";
}

.icon-audio:before {
  content: "\e615";
}

.icon-phone:before {
  content: "\e616";
}

.icon-email:before {
  content: "\e617";
}

.icon-location:before {
  content: "\e618";
}

.icon-time:before {
  content: "\e619";
}

.icon-calendar:before {
  content: "\e61a";
}

.icon-star:before {
  content: "\e61b";
}

.icon-heart:before {
  content: "\e61c";
}

.icon-like:before {
  content: "\e61d";
}

.icon-share:before {
  content: "\e61e";
}

.icon-print:before {
  content: "\e61f";
}

.icon-refresh:before {
  content: "\e620";
}

.icon-loading:before {
  content: "\e621";
}

.icon-warning:before {
  content: "\e622";
}

.icon-error:before {
  content: "\e623";
}

.icon-success:before {
  content: "\e624";
}

.icon-info:before {
  content: "\e625";
}

.icon-question:before {
  content: "\e626";
}

.icon-lock:before {
  content: "\e627";
}

.icon-unlock:before {
  content: "\e628";
}

.icon-eye:before {
  content: "\e629";
}

.icon-eye-close:before {
  content: "\e62a";
}

.icon-copy:before {
  content: "\e62b";
}

.icon-paste:before {
  content: "\e62c";
}

.icon-cut:before {
  content: "\e62d";
}

.icon-save:before {
  content: "\e62e";
}

.icon-export:before {
  content: "\e62f";
}

.icon-import:before {
  content: "\e630";
}

/* 自定义图标大小类 */
.iconfont-xs {
  font-size: 12px;
}

.iconfont-sm {
  font-size: 14px;
}

.iconfont-md {
  font-size: 16px;
}

.iconfont-lg {
  font-size: 18px;
}

.iconfont-xl {
  font-size: 20px;
}

.iconfont-xxl {
  font-size: 24px;
}

/* 图标颜色类 */
.iconfont-primary {
  color: #409eff;
}

.iconfont-success {
  color: #67c23a;
}

.iconfont-warning {
  color: #e6a23c;
}

.iconfont-danger {
  color: #f56c6c;
}

.iconfont-info {
  color: #909399;
}

/* 图标动画效果 */
.iconfont-spin {
  animation: iconfont-spin 1s linear infinite;
}

@keyframes iconfont-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.iconfont-pulse {
  animation: iconfont-pulse 1s ease-in-out infinite;
}

@keyframes iconfont-pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}
