<template>
<div v-loading="loading">
  <el-tree :data="treeList" :props="defaultProps"
           id="elTree"
    style="margin-top: 20px"
     :expand-on-click-node="false"
     node-key="relation"
     show-checkbox
     ref="tree"
     @check="handleCheckChange"
     :default-checked-keys="checkedIds"
  >
  </el-tree>
</div>
</template>

<script lang="ts">
  export default {
    name: "checkeboxTree",
    props: {
      treeList: {
        type: Array,
        default: ()=>{ return []}
      },
      checkedList: {  //默认选中节点的数据
        type: Array,
        default: ()=>{ return []}
      },
      personInfo:{//人员类型信息
        type: Object,
        default: ()=>{ return {}}
      },
      selectType: {//标识多选还是单选
        type: String,
        default: 'more'
      },
    },
    created() {
      this.getCheckedIds();
    },
    data() {
      return {
        loading:true,
        defaultProps: {
          children: 'children',
          label: 'label'
        },
        checkedIds: [],
        checkedMap: {},
      };
    },
    watch: {
      checkedList: 'changeCheckedList'
    },
    methods: {
      //删除
      deleteTree(id,list){
        this.$refs.tree.setChecked(id,false);
        this.checkedIds=[]
        this.checkedMap=[]
        list.map(item => {
          if(item.personName){
            this.checkedIds.push(item.personId);
            this.checkedMap[item.personId]=item;
          }
        });
      },
      /**获取默认选择的节点*/
      getCheckedIds(){
        let index = 0;
        this.checkedList.map(item => {
          if(item.personName && item.personType == this.personInfo.personType){
            this.checkedIds.push(item.personId);
            this.checkedMap[item.personId]=item;
          }
        });
        setTimeout(() => {
          this.loading = false;
        }, 1000);
      },
      /* 选中节点改变时 */
      changeCheckedList(){
        this.checkedIds = [];
        this.checkedList.map(item => {
          if(item.personName && item.personType == this.personInfo.personType){
            this.checkedIds.push(item.personId);
            this.checkedMap[item.personId]=item;
          }
        });
        this.$refs.tree.setCheckedKeys(this.checkedIds, true);
      },
      /**
      *data 选中或取消选中的对象
      *checked 节点本身是否被选中
      *indeterminate 节点的子树中是否有被选中的节点
      */
      handleCheckChange(data, keys) {
        let checked = keys.checkedKeys.indexOf(data.personId)>-1;
        let sel = this;
        if(this.selectType == 'more'){//人员多选时
          var promise = new Promise(function(resolve, reject){
            let list = [];
            let num = 0;
            if(checked){
              sel.checkedIds.map(item => {
                list.push(sel.checkedMap[item]);
                if(item == data.personId){
                  num = 1;
                }
              });
            }else{
              num = 1;
              let checkedIdsCopy = JSON.parse(JSON.stringify(sel.checkedIds));
              let checkedIdsTemp = [];
              for(let i=0; i<checkedIdsCopy.length; i++){
                let item = checkedIdsCopy[i];
                if(item != data.personId){
                  list.push(sel.checkedMap[item]);
                  checkedIdsTemp.push(item);
                }
              }
              sel.checkedIds = JSON.parse(JSON.stringify(checkedIdsTemp));
            }
            return resolve({list, num,});
          });

          promise.then(function(obj){
            if(obj.num == 0){
              let checkedIdsCopy = JSON.parse(JSON.stringify(sel.checkedIds));
              obj.list.push({...data, ...sel.personInfo});
              sel.checkedMap[data.personId]={...data, ...sel.personInfo};
              checkedIdsCopy.push(data.personId);
              sel.checkedIds = JSON.parse(JSON.stringify(checkedIdsCopy));
            }
            if(sel.checkedList[0].personType != sel.personInfo.personType){
              obj.list.unshift(sel.checkedList[0]);
            }
            sel.$emit("selectNode", obj.list);
          });
        }else{//点编辑按钮进入，单选人员
          sel.$refs.tree.setChecked(sel.checkedIds[0], false,false);
          sel.checkedIds = [data.personId];
          sel.checkedMap[data.personId] = {...data, ...sel.personInfo};
          sel.$emit("selectNode", [{...data, ...sel.personInfo}]);
        }
      },
    }
  };
</script>

<style>
  .text {
    font-size: 14px;
  }

  .item {
    margin-bottom: 18px;
  }

  .clearfix:before,
  .clearfix:after {
    display: table;
    content: "";
  }
  .clearfix:after {
    clear: both
  }
</style>
