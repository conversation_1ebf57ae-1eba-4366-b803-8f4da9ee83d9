<!--实时问题录入-->
<template>
  <div class="app-container realTime">
    <div class="row">
      <el-form
        v-show="showSearch"
        id="queryParams"
        ref="queryForm"
        :model="queryParams"
        :inline="true"
        label-width="120px"
      >
        <el-form-item label="系统编号">
          <el-input v-model="queryParams.auditCode" placeholder="系统编号" :style="{width: '100%'}" />
        </el-form-item>
        <el-form-item label="问题编号">
          <el-input v-model="queryParams.problemCode" placeholder="问题编号" :style="{width: '100%'}" />
        </el-form-item>
        <el-form-item label="已完成最新阶段" prop="status">
          <el-select v-model="queryParams.status" placeholder="请选择已完成最新阶段" clearable size="small">
            <el-option
              v-for="(item, index) in actualStatusList"
              :key="index"
              :label="item.dictLabel"
              :value="item.dictValue"
            >{{ item.dictLabel }}
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="违规事项">
          <el-input v-model="queryParams.problemTitle" placeholder="违规事项" :style="{width: '100%'}" />
        </el-form-item>
        <div class="float-right">
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
        </div>

      </el-form>
    </div>
    <el-form style="height: calc(100vh - 335px)">
      <el-table v-loading="loading" :data="tableList" height="100%">
        <el-table-column label="序号" type="index" min-width="10%"  align="center" />
        <el-table-column label="系统编号" prop="auditCode" min-width="15%" align="center"/>
        <el-table-column label="问题编号" prop="problemCode" min-width="15%" align="center"/>
        <el-table-column label="违规事项" prop="problemTitle" min-width="32%" />
        <el-table-column label="已完成最新状态" prop="statusName" min-width="20%" align="center"/>
        <!--<el-table-column label="经办人" prop="createUserName" width="200" align="center"/>-->
        <el-table-column label="操作"  min-width="15%" align="center" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              title="报告"
              @click="openReportList(scope.row.id)"
            >
            </el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-setting"
              title="变更填报人"
              @click="changeFilling(scope.row)"
            >
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-form>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="violActualDraftList"
    />
    <!-- 报告列表 -->
    <el-dialog :title="title" :visible.sync="open" width="70%" append-to-body @close="closeDialog">
      <el-row style="height:400px">
        <el-table v-loading="loading" :data="reportList" height="100%">
          <el-table-column label="序号" type="index"  min-width="12%" align="center" />
          <el-table-column label="填报阶段" prop="codeText"  min-width="28%" align="center" />
          <el-table-column label="提交时间" prop="submitTime"  min-width="20%" align="center">
            <template slot-scope="scope">
             {{scope.row.status=='3'?scope.row.submitTime:''}}
            </template>
          </el-table-column>
          <el-table-column label="状态" prop="statusText"  min-width="17%" align="center" />

          <el-table-column label="操作" min-width="23%" align="center" class-name="small-padding fixed-width">
            <template slot-scope="scope">
              <el-button
                v-if="scope.row.operationType === 1"
                size="mini"
                type="text"
                icon="el-icon-edit"
                title="编辑"
                @click="handleDetails(scope.row)"
              >
              </el-button>
              <el-button
                v-if="scope.row.operationType === 2||scope.row.operationType === 3"
                size="mini"
                type="text"
                icon="el-icon-search"
                title="详细"
                @click="handleRead(scope.row)"
              ></el-button>
              <el-button
                v-if="!scope.row.operationType"
                disabled
                size="mini"
                type="text"
                icon="el-icon-edit"
                title="编辑"
              ></el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-row>
    </el-dialog>

    <!--五个工作日实时报告快报-->
    <ActualFiveDaysReport
      v-if="code==='1'"
      :key="field"
      ref="edit"
      :field="field"
      :close-btn="cancel"
      :is-edit="isEdit"
      @reportList="ReportListData"
    />

    <!--15个工作日实时报告快报-->
    <ActualFifteenDaysReport
      v-if="code==='2'"
      :key="field"
      ref="edit"
      :field="field"
      :close-btn="cancel"
      :is-edit="isEdit"
      @reportList="ReportListData"
    />

    <!--30个工作日实时报告快报-->
    <ActualThirtyDaysReport
      v-if="code==='3'"
      :key="field"
      ref="edit"
      :field="field"
      :close-btn="cancel"
      :is-edit="isEdit"
      @reportList="ReportListData"
    />

    <!--后续工作进展报告-->
    <ActualProgressReport
      v-if="code==='4'"
      :key="field"
      ref="edit"
      :field="field"
      :close-btn="cancel"
      :is-edit="isEdit"
      @reportList="ReportListData"
    />

    <!--核查处置结果报告-->
    <ActualCheckDisReport
      v-if="code==='5'"
      :key="field"
      ref="edit"
      :field="field"
      :close-btn="cancel"
      :is-edit="isEdit"
      @reportList="ReportListData"
    />

    <el-dialog title="实时报告变更填报人" class="app-report" :visible.sync="visible" width="300">
      <div class="drafts">
        <el-select v-model="fillingValue" filterable placeholder="请选择">
          <el-option
            v-for="(item,index) in fillingList"
            :key="index"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="ActualTransfer">变更填报人</el-button>
        <el-button size="mini" @click="fillingClose">取消</el-button>
      </div>
    </el-dialog>
    <el-dialog :title="title" class="app-report" :visible.sync="detailVisible" width="80%" append-to-body>
      <!--五个工作日实时报告快报查看-->
      <ActualFiveDaysReportRead
        v-if="code==='1'"
        :key="field"
        ref="read"
        :field="field"
        :close-btn="cancel"
      />
      <!--15个工作日实时报告快报查看-->
      <ActualFifteenDaysReportRead
        v-if="code==='2'"
        :key="field"
        ref="read"
        :field="field"
        :close-btn="cancel"
        :is-edit="isEdit"
      />
      <!--30个工作日实时报告快报查看-->
      <ActualThirtyDaysReportRead
        v-if="code==='3'"
        :key="field"
        ref="read"
        :field="field"
        :close-btn="cancel"
        :is-edit="isEdit"
      />

      <!--后续工作进展报告查看-->
      <ActualProgressReportRead
        v-if="code==='4'"
        :key="field"
        ref="read"
        :field="field"
        :close-btn="cancel"
        :is-edit="isEdit"
      />
      <!--核查处置结果报告查看-->
      <ActualCheckDisReportRead
        v-if="code==='5'"
        :key="field"
        ref="read"
        :field="field"
        :close-btn="cancel"
        :is-edit="isEdit"
      />
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="cancelRead">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { violActualDraftList, actualStatus, openReportList, refreshTurnAssignee, actualTransfer } from '@/api/actual/index'
import ActualFiveDaysReport from './task/actualFiveDaysReport'
import ActualFiveDaysReportRead from './detail/actualFiveDaysReportRead'

import ActualFifteenDaysReport from './task/actualFifteenDaysReport'
import ActualFifteenDaysReportRead from './detail/actualFifteenDaysReportRead'

import ActualThirtyDaysReport from './task/actualThirtyDaysReport'
import ActualThirtyDaysReportRead from './detail/actualThirtyDaysReportRead'

import ActualProgressReport from './task/actualProgressReport'
import ActualProgressReportRead from './detail/actualProgressReportRead'

import ActualCheckDisReport from './task/actualCheckDisReport'
import ActualCheckDisReportRead from './detail/actualCheckDisReportRead'

export default {
  name: 'Actual/actualQuestionDrafts',
  components: {
    ActualFiveDaysReport,
    ActualFiveDaysReportRead,
    ActualFifteenDaysReport,
    ActualFifteenDaysReportRead,
    ActualThirtyDaysReport,
    ActualThirtyDaysReportRead,
    ActualProgressReport,
    ActualProgressReportRead,
    ActualCheckDisReport,
    ActualCheckDisReportRead
  },
  dicts: [],
  filters: {
    timeType: function(value) {
      if (!value) return ''
      value = value.substring(0, 10)
      return value
    }
  },
  data() {
    return {
      detailVisible: false,
      visible: false,
      // 遮罩层
      loading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 表格数据
      tableList: [],
      // 是否显示弹出层
      open: false,
      // 实时问题查询 参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        auditCode: '',
        problemCode: '',
        problemTitle: '',
        status: '',
        actualProblemId: ''
      },
      actualStatusList: [{
        required: true,
        message: '已完成最新阶段',
        trigger: 'change'
      }],
      // 是否显示弹出层（数据权限）
      openSelect: false,
      // 对应的报告列表
      reportList: [],
      field: null,
      isEdit: true,
      detailopen: false,
      title: '',
      filling: {}, // 需要变更的主键以及数据
      fillingList: [], // 变更人员选择
      fillingValue: '', // 选中的变更人
      code: 0
    }
  },
  created() {
    this.violActualDraftList()
  },
  mounted() {
    this.QueryViolateInfo()
  },
  methods: {
    cancelRead() {
      this.detailVisible = false
    },
    /** 初始化数据*/
    QueryViolateInfo() {
      this.loading = true
      actualStatus().then(
        response => {
          this.actualStatusList = response.data
          this.loading = false
        }
      )
    },
    /** 查询实时问题列表*/
    violActualDraftList() {
      this.loading = true
      violActualDraftList(this.queryParams).then(
        response => {
          this.tableList = response.rows
          this.total = response.total
          this.loading = false
        }
      )
    },
    /** 搜索按钮操作*/
    handleQuery() {
      this.queryParams.pageNum = 1
      this.violActualDraftList()
    },
    /** 重置按钮操作*/
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        auditCode: '',
        problemCode: '',
        problemTitle: '',
        status: '',
        actualProblemId: ''
      }
      this.violActualDraftList()
    },
    /** 变更填报人*/
    changeFilling(row) {
      this.filling = row
      this.refreshAssignee()
    },
    /** 关闭变更填报人*/
    fillingClose() {
      this.visible = false
    },
    /** 获取人员选择列表*/
    refreshAssignee(row) {
      this.fillingValue = ''
      refreshTurnAssignee().then(response => {
        this.fillingList = response.data
        this.visible = true
      })
    },
    /** 确认变更填报人*/
    ActualTransfer() {
      if (this.fillingValue) { // 选择了值
        this.$confirm('确认变更此问题的填报人吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          console.log('确定')
          this.ActualTransferSave()
        }).catch(() => {
          console.log('取消')
          this.visible = false
        })
      } else { // 未选
        this.$message.error('请选择处理人！')
      }
    },
    /** 保存填报联系人*/
    ActualTransferSave() {
      actualTransfer({ problemId: this.filling.id, assignee: this.fillingValue, status: this.filling.status }).then(response => {
        if (response.code === 200) {
          this.$modal.msgSuccess('保存成功')
        } else {
          this.$modal.alertError(response.msg)
        }
        this.visible = false
        this.violActualDraftList()
      })
    },
    actualStatus() {
      violActualDraftList().then(
        response => {
          this.actualStatus = response.actualStatus
          this.loading = false
        }
      )
    },
    /** 打开报告列表 */
    openReportList(actualProblemId) {
      this.open = true
      this.title = '填报'
      this.loading = true
      this.queryParams.actualProblemId = actualProblemId
      this.ReportListData()
    },
    // 请求弹框表格数据
    ReportListData() {
      openReportList(this.queryParams).then(
        response => {
          this.reportList = response.data
          this.loading = false
        }
      )
    },
    /** 监听关闭--关闭*/
    closeDialog() {
      this.violActualDraftList()
    },
    cancel() {
      this.detailopen = false
    },
    handleDetails(row) {
      this.field = row.actualProblemId
      this.code = row.code
      this.$nextTick(() => {
        this.$refs.edit.show()
      })
    },
    handleRead(row) {
      this.code = row.code
      this.field = row.actualProblemId
      if (row.code === '1') {
        this.title = '5个工作日实时报告快报'
      } else if (row.code === '2') {
        this.title = '15个工作日实时报告'
      } else if (row.code === '3') {
        this.title = '30个工作日初核报告'
      } else if (row.code === '4') {
        this.title = '后续工作进展情况报告'
      } else if (row.code === '5') {
        this.title = '核查处置结果报告'
      }
      this.detailVisible = true
      this.$nextTick(() => {
        this.$refs.read.show()
      })
    }
  }
}
</script>
<style rel="stylesheet/scss" scoped  lang="scss">

  .el-select {
    width: 100%;
  }
  .drafts {
    height: 300px;
  }

 @media screen and (min-width: 1400px) and (max-width: 1600px) {
  ::v-deep .el-form-item__label{
     font-size: 12px!important;
     width: auto!important;
     padding: 0px 10px 0px 0px!important;
   }

 }
</style>

