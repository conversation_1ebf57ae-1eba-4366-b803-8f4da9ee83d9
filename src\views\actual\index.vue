<!--实时问题查询-->
<template>
  <div class="grayBackground padding4 daily">
    <el-row>
      <el-col :span="8">
        <BlockCard
          title="问题统计"
        >
          <div class="ry-common-card-content" style="height: 170px;">
            <div class="early-warning-data">
              <div class="early-warning-data-li ">
                <el-row>
                  <div class="ewd-li ewd-li-5">
                    <div class="ewd-li-box">
                      <el-col :span="10">
                        <div class="ewd-li-5-left">
                          <p class="ewd-li-5-top ewd-li-5-num problemNums">
                            {{ problemNumsData.problemNums||0 }}</p>
                          <p>全部问题</p>
                        </div>
                      </el-col>
                      <el-col :span="14">
                        <div class="ewd-li-5-right">
                          <p class="ewd-li-5-top ">其中：产生严重不良影响问题</p>
                          <div class="ewd-li-5-box ">
                            <p class="ewd-li-5-percentage">
                              {{ problemNumsData.problemNumsActualRate?(problemNumsData.problemNumsActualRate).toFixed(2):'0.00' }}%</p>
                            <div class="ewd-li-5-per">
                              <span
                                class="ewd-li-5-span ewd-li-5-span-1"
                                :style="{ width:problemNumsData.problemNumsActualRate + '%' }"
                              />
                            </div>
                            <p class="ewd-li-5-percentage">{{ problemNumsData.problemNumsActual||0 }}</p>
                          </div>
                        </div>
                      </el-col>
                    </div>
                  </div>
                </el-row>
              </div>
              <div class="early-warning-data-li " style="margin-top: 10px">
                <el-row>
                  <div class="ewd-li ewd-li-5">
                    <div class="ewd-li-box">
                      <el-col :span="10">
                        <div class="ewd-li-5-left">
                          <p id="lossAmount" class="ewd-li-5-top ewd-li-5-num ">
                            {{ problemNumsData.lossAmount?(problemNumsData.lossAmount).toFixed(2):'0.00' }}</p>
                          <p class="">损失金额(万元)</p>
                        </div>
                      </el-col>
                      <el-col :span="14">
                        <div class="ewd-li-5-right">
                          <p class="ewd-li-5-top ovflowHidden" title="其中：产生严重不良影响问题(万元)">其中：产生严重不良影响问题(万元)</p>
                          <div class="ewd-li-5-box ">
                            <p id="lossAmountActualRate" class="ewd-li-5-percentage">
                              {{ problemNumsData.lossAmountActualRate?(problemNumsData.lossAmountActualRate).toFixed(2):'0.00' }}%</p>
                            <div class="ewd-li-5-per">
                              <span
                                id="lossAmountActualRates"
                                class="ewd-li-5-span ewd-li-5-span-2"
                                :style="{ width:problemNumsData.lossAmountActualRate + '%' }"
                              />
                            </div>
                            <p id="lossAmountActual" class="ewd-li-5-percentage lossAmountActual ovflowHidden" :title="problemNumsData.lossAmountActual">
                              {{ problemNumsData.lossAmountActual?(problemNumsData.lossAmountActual).toFixed(2):'0.00' }}</p>
                          </div>
                        </div>
                      </el-col>
                    </div>
                  </div>
                </el-row>
              </div>
            </div>
          </div>
        </BlockCard>
        <BlockCard
          title="问题排行榜"
        >
          <el-form ref="elForm" size="medium" label-width="0">
            <el-row>
              <el-col :span="12">
                <el-form-item label="" prop="lossStateAssetsFlag">
                  <el-radio-group v-model="problemType" size="medium">
                    <el-radio
                      v-for="(item, index) in problemTypeList"
                      :key="index"
                      :label="item.value"
                      @change="ProblemRankList"
                    >{{ item.label }}
                    </el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item>
                  <div class="float-right">
                    <el-select v-model="orderFlag" placeholder="请选择活动区域" @change="ProblemRankList">
                      <el-option label="按数量排序" value="1" />
                      <el-option label="按损失金额排序" value="2" />
                    </el-select>
                  </div>
                </el-form-item>

              </el-col>
              <el-col :span="24">
                <ul id="rankList" class="question-ul">
                  <li v-for="(item,index) in problemRankData" class="question-li">
                    <div class="question-li-1 ovflowHidden">
                      <span class="question-li-ranking question-li-ranking1">{{ index+1 }}</span>
                      <span class="question-li-text">{{ item.involOrgName||item.involCompanyName }}</span>
                    </div>
                    <div class="question-li-2 ovflowHidden">
                      <i class="el-icon-question icon iconfont" />
                      <span class="question-li-text">{{ item.problemNums }}</span>
                    </div>
                    <div class="question-li-4 ovflowHidden">
                      <i class="iconfont icon el-icon-info" />
                      <span class="question-li-text">{{ item.lossAmounts?(item.lossAmounts).toFixed(2):'0.00' }}</span>
                    </div>
                  </li>
                </ul>
              </el-col>
            </el-row>
          </el-form>

        </BlockCard>
        <BlockCard
          title="问题分布统计"
        >
          <el-form>
            <div class="position">
              <div class="position-select">
                <el-row>
                  <el-col :span="24">
                    <el-form-item>
                      <div class="float-right">
                        <el-select v-model="echartType" @change="ProblemEchartList">
                          <el-option label="按问题情形统计" value="problemRangeNumsList" />
                          <el-option label="按问题状态统计" value="problemStatusNumsList" />
                          <el-option label="按专业线统计" value="problemSpecNumsList" />
                        </el-select>
                      </div>
                    </el-form-item>
                  </el-col>
                </el-row>
              </div>
              <div id="canvas" class="canvas" />
              <div class="echart-text-box">
                <p class="echart-text-p">{{ echartName }}</p>
                <p class="echart-text-num">{{ ehcartNum }}</p>
                <p class="echart-text-p">{{ echartPercent }}</p>
              </div>
            </div>
          </el-form>
        </BlockCard>
      </el-col>
      <el-col :span="16">
        <BlockCard
          title="实时问题列表"
          :height="772"
        >
          <el-form
            ref="elForm"
            :model="queryParams"
            size="medium"
            label-width="120px"
          >
            <el-row>
              <el-col :span="8">
                <el-form-item label="系统编号">
                  <el-input
                    v-model="queryParams.auditCode"
                    :style="{width: '100%'}"
                    placeholder="系统编号"
                    clearable
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="问题编号">
                  <el-input
                    v-model="queryParams.problemCode"
                    placeholder="问题编号"
                    :style="{width: '100%'}"
                    clearable
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="已完成最新阶段">
                  <el-select
                    v-model="queryParams.status"
                    :style="{width: '100%'}"
                    clearable
                    filterable
                  >
                    <el-option
                      v-for="(item,index) in dailyStatusList"
                      :label="item.dictLabel"
                      :value="item.dictValue"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row v-show="showSearch">
              <el-col :span="8">
                <el-form-item label="受理单位">
                  <el-select
                    v-model="queryParams.problemProvCode"
                    :style="{width: '100%'}"
                    clearable
                    filterable
                    @change="QueryAreaList"
                  >
                    <el-option
                      v-for="(item,index) in provList"
                      :label="item.provName"
                      :value="item.provCode"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="受理地市">
                  <el-select v-model="queryParams.problemAreaCode" :style="{width: '100%'}"
                             clearable
                             filterable>
                    <el-option
                      v-for="(item,index) in areaList"
                      :label="item.areaName"
                      :value="item.areaCode"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="违规事项">
                  <el-input
                    v-model="queryParams.problemTitle"
                    placeholder="违规事项"
                    :style="{width: '100%'}"
                    clearable
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
          <el-row>
            <div class="float-right">
              <el-button type="warning" icon="el-icon-download" size="mini" @click="handleExport">导出</el-button>
              <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
              <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
              <el-button type="info" plain icon="el-icon-sort" size="mini" @click="showSearch=!showSearch">展开/折叠</el-button>
            </div>
          </el-row>
          <el-form :style="{height: showSearch?'calc(100% - 200px)':'calc(100% - 140px)'}">
            <el-table v-loading="loading" :data="tableList" height="100%">
              <el-table-column label="" type="index" width="50" align="center">
                <template slot-scope="scope">
                  <table-index
                    :index="scope.$index"
                    :page-num="queryParams.pageNum"
                    :page-size="queryParams.pageSize"
                  />
                </template>
              </el-table-column>
              <el-table-column label="受理单位" prop="problemProvName" width="150" />
              <el-table-column label="受理地市" prop="problemAreaName" width="150" />
              <el-table-column label="系统编号" prop="auditCode" width="150" />
              <el-table-column label="问题编号" prop="problemCode" width="150" />
              <el-table-column
                prop="tag"
                show-overflow-tooltip
                label="违规事项"
                width="280"
              >
                <template slot-scope="scope">
                  <el-tooltip v-if="scope.row.actualFlag === '1'" class="item" effect="dark" content="实时报送" placement="top-start">
                    <img class="scope-chao-shi" :src="require('@/assets/images/colligate/shi.png')" alt="">
                  </el-tooltip>
                  <el-tooltip v-if="scope.row.warnFlag === '1'" class="item" effect="dark" :content="'【' + scope.row.warnProblemStatus + '】超时'" placement="top-start">
                    <img class="scope-chao-shi" :src="require('@/assets/images/colligate/chao.png')" alt="">
                  </el-tooltip>

                  {{ scope.row.problemTitle }}
                </template>
              </el-table-column>
              <el-table-column label="已完成最新状态" prop="statusName" width="150" />
              <el-table-column label="经办人" prop="createUserName" width="150" />
              <el-table-column label="操作" fixed="right" width="150" align="center" class-name="small-padding fixed-width">
                <template slot-scope="scope">
                  <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-search"
                    title="查看"
                    @click="handleDetail(scope.row)"
                  >
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-form>
          <pagination
            v-show="total>0"
            :total="total"
            :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize"
            @pagination="ViolDailyDraftList"
          />
        </BlockCard>
      </el-col>
    </el-row>
    <el-dialog v-bind="$attrs" :visible.sync="visible" width="90%" :title="'实时问题-'+rows.problemTitle">
      <Details
        v-if="rows"
        :key="rows"
        :select-value="rows"
        :active-name="activeName"
        :procInsId="rows.procInsId"
      />
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="close">确定</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import echarts from 'echarts'
import Details from '@/views/daily/details'// tree
import {
  violDailyList,
  violDailyDraftDelete,
  problemNumsList,
  problemRankList,
  problemEchartList,
  actualStatus,
  queryProvList,
  queryAreaList,
  sourceSasacStatus,
  stepDeal
} from '@/api/daily/index'
import BlockCard from '@/components/BlockCard'

export default {
  name: 'Actual/index',
  components: {
    Details,
    BlockCard
  },
  dicts: [],
  filters: {},
  data() {
    return {
      activeName: '1',
      rows: {},
      echartName: '',
      ehcartNum: '',
      echartPercent: '',
      chart: null,
      echartType: 'problemRangeNumsList',
      problemNumsData: {}, // 问题统计
      problemType: 'problemRankCompanyList',
      problemTypeList: [
        { label: '涉及单位', value: 'problemRankCompanyList' },
        { label: '涉及部门', value: 'problemRankDepartList' }
      ],
      problemRankData: [], // 问题涉及单位统计
      dailyStatusList: [],
      orderFlag: '1', // 排序
      loading: false,
      // 填报遮罩层
      visible: false,
      // 选中的数据
      problemId: '',
      relevantTableId: '',
      relevantTableName: '',
      // 查看与编辑
      edit: true,
      // 显示搜索条件
      showSearch: false,
      // 总条数
      total: 0,
      // 表格数据
      tableList: [],
      provList: [], // 省分
      areaList: [], // 地市
      sourceSasacList: [], // 问题线索来源下拉
      // 新增主键
      // 日常问题查询 参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        problemProvCode: '',
        problemAreaCode: '',
        auditCode: '',
        problemCode: '',
        status: '',
        involCompany: '',
        acceptTimes: [],
        acceptTimeStart: '',
        acceptTimeEnd: '',
        problemTitle: '',
        problemSource: ''
      }
    }
  },
  created() {
    this.ViolDailyDraftList()
    this.ProblemNumsList()
    this.ProblemRankList()
    this.ProblemEchartList()
    this.ActualStatus()
    this.QueryProvList()
    this.SourceSasacStatus()
  },
  mounted() {

  },
  methods: {
    close() {
      this.visible = false
    },
    /** 查询日常问题列表*/
    ViolDailyDraftList() {
      this.loading = true
      this.list = []
      if (this.queryParams.acceptTimes && this.queryParams.acceptTimes.length) {
        this.queryParams.acceptTimeStart = this.queryParams.acceptTime[0]
        this.queryParams.acceptTimeStart = this.queryParams.acceptTime[1]
      }
      this.queryParams.actualFlag = '1'
      violDailyList(this.queryParams).then(
        response => {
          this.tableList = response.rows
          this.total = response.total
          this.loading = false
        }
      )
    },
    // 问题状态
    ActualStatus() {
      actualStatus().then(
        response => {
          this.dailyStatusList = response.data
        }
      )
    },
    /** 搜索按钮操作*/
    handleQuery() {
      this.queryParams.pageNum = 1
      this.ViolDailyDraftList()
    },
    /** 重置按钮操作*/
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        involCompanyName: '',
        acceptTime: '',
        problemTitle: ''
      }
      this.ViolDailyDraftList()
    },

    /** 删除按钮操作 */
    handleDel(row) {
      this.$modal.confirm('是否确定删除该数据？').then(function() {
        return violDailyDraftDelete({ id: row.id })
      }).then(() => {
        this.$modal.msgSuccess('删除成功')
        this.ViolDailyDraftList()
      }).catch(() => {
      })
    },
    /** 编辑与查看操作 */
    handleDetail(row) {
      this.rows = row
      // this.problemId = row.id;
      // this.relevantTableId = row.relevantTableId;
      // this.relevantTableName = row.relevantTableName;
      this.visible = true
    },
    // 问题统计
    ProblemNumsList() {
      problemNumsList({ actualFlag: '1' }).then(
        response => {
          this.problemNumsData = response.data
        }
      )
    },
    // 问题涉及单位统计
    ProblemRankList() {
      problemRankList(this.problemType, { orderFlag: this.orderFlag, actualFlag: '1' }).then(
        response => {
          this.problemRankData = response.data
        }
      )
    },
    // 自适应字体大小
    fontSizeFun(res) {
      const docEl = document.documentElement
      const clientWidth =
            window.innerWidth ||
            document.documentElement.clientWidth ||
            document.body.clientWidth
      if (!clientWidth) return
      const fontSize = 100 * (clientWidth / 1920)
      return res * fontSize
    },
    // echarts问题分布统计
    ProblemEchartList() {
      problemRankList(this.echartType, { actualFlag: '1' }).then(
        response => {
          this.echartName = ''
          this.ehcartNum = ''
          this.echartPercent = ''
          const personData = response.data
          const chartDom = document.getElementById('canvas')
          const commonMyChart = echarts.init(chartDom)
          const colorlist = [
            '#ff4d4e',
            '#a2d72d',
            '#ffe7ba',
            '#1890ff',
            '#8da6fc',
            '#18bb07',
            '#ffc069',
            '#69c0ff',
            '#ff7774',
            '#fa8b16',
            '#ffa39d',
            '#ffd800'
          ]
          const option = {
            color: colorlist,
            tooltip: {
              trigger: 'item'
            },
            grid: {
              top: this.fontSizeFun(0.20)
            },
            legend: {
              itemWidth: 14,
              orient: 'vertical',
              x: 'left',
              top: 10,
              left: '45%',
              textStyle: {
                padding: [this.fontSizeFun(0.03), this.fontSizeFun(0.04), this.fontSizeFun(0.05), this.fontSizeFun(0.06)],
                fontSize: this.fontSizeFun(0.12)
              },
              formatter: function(params) {
                if (!params) return ''
                if (params.length > 7) {
                  params = params.slice(0, 7) + '...'
                }
                return params
              },
              tooltip: {
                show: true
              }
            },
            calculable: true,
            series: [
              {
                name: '问题分布统计',
                type: 'pie',
                radius: ['50%', '75%'], // 环形饼状图
                center: ['23%', '50%'],
                label: {
                  show: false
                },
                data: personData
              }
            ]
          }
          option && commonMyChart.setOption(option)
          commonMyChart.on('click', (e) => {
            this.echartName = e.data.name
            this.ehcartNum = e.data.value
            this.echartPercent = e.percent + '%'
          })
        }
      )
    },
    // 省分
    QueryProvList() {
      queryProvList().then(
        response => {
          this.provList = response.data
        }
      )
    },
    // 地市
    QueryAreaList() {
      queryAreaList({ provCode: this.queryParams.problemProvCode }).then(
        response => {
          this.queryParams.problemAreaCode = ''
          this.areaList = response.data
        }
      )
    },
    // 问题线索来源下拉
    SourceSasacStatus() {
      sourceSasacStatus().then(
        response => {
          this.sourceSasacList = response.data
        }
      )
    },
    // 提级查办
    StepDeal(problemId, status, procInsId) {
      this.$confirm('是否确定提级查办？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => { // 确定
        stepDeal({
          id: problemId,
          status: status,
          procInsId: procInsId
        }).then(
          response => {
            this.ViolDailyDraftList()
          }
        )
      }).catch(() => {
      })
    },
    /** 导出按钮操作 */
    handleExport() {
      var nowDate = new Date()
      var month = nowDate.getMonth() + 1 + ''
      if (month.length === 1) {
        month = '0' + month
      }
      var dateTime = nowDate.getFullYear() + '' + month + nowDate.getDate() + '' + nowDate.getHours() + '' + nowDate.getMinutes() + '' + nowDate.getSeconds()
      this.download('/colligate/violQuery/exportActualData', {
        ...this.queryParams
      }, `违规追责实时报送问题列表${dateTime}.xlsx`)
    }
  }
}
</script>
<style rel="stylesheet/scss" lang="scss">
  .daily{
    .el-dialog__body{
      height: 73vh;
    }
  }
  .question-ul {
    height: 156px;
    overflow: hidden;
  }
  .scope-chao-shi{
    margin-right:5px;
    margin-bottom: 0;
  }
  .canvas {
    width: 100%;
    height: 194px;
    z-index: 999;
  }

  .echart-text-box {
    position: absolute;
    width: calc((100% - 40px) / 2);
    height: 100px;
    top: 56px;
    z-index: 9;
    text-align: center;
    p {
      text-align: center;
      line-height: 32px;
    }
    .echart-text-p {
      font-size: 12px;
    }
  }

  .early-warning-data {
    margin-bottom: 10px;
  }

  .early-warning-data-li {
    width: 100%;
    margin: 4px 0;
  }

  .ewd-li {
    float: left;
    padding: 4px;
    box-sizing: border-box;
    height: 80px;
  }

  .ewd-li-box {
    box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    border: solid 1px #eeeeee;
    height: 100%;
    padding: 8px;
    box-sizing: border-box;
  }

  .ewd-li-1 {
    width: 83px;
  }

  .ewd-li-1 .ewd-li-1-circular {
    width: 68px;
    height: 68px;
    display: inline-block;
    line-height: 68px;
    border-radius: 50%;
    text-align: center;
    font-size: 18px;
    color: #ffffff;
    margin-top: 8px;
  }

  .circular-1 {
    background-image: linear-gradient(-30deg,
      #ff4d4e 0%,
      #ffa39d 100%);
  }

  .circular-2 {
    background-image: linear-gradient(-30deg,
      #40a9ff 0%,
      #91d5ff 100%);
  }

  .ewd-li-right {
    float: right;
    width: calc(100% - 83px);
  }

  .ewd-li-2 {
    width: 30%;
  }

  .ewd-li-3 {
    width: 50%;
  }

  .ewd-li-4 {
    width: 20%;
  }

  .ewd-li-5 {
    width: 100%;
    padding: 0;
  }

  .ewd-li-6 {
    padding: 0;
  }

  .ewd-li-5 .ewd-li-box, .ewd-li-6 .ewd-li-box {
    padding: 10px 0 10px 20px;
  }

  .ewd-li-5-left {
    height: 100%;
    border-right: 1px solid #d9d9d9;
  }

  .ewd-li-5-right {
    height: 100%;
    padding-left: 20px;
    box-sizing: border-box;
  }

  .ewd-li-5-top {
    width: 100%;
    font-size: 14px;
    line-height: 24px;
    color: #73777a;
  }

  .ewd-li-5-num {
    font-size: 20px;
    line-height: 24px;
    color: #181818;
    margin-bottom: 10px;
  }

  .ewd-li-6-num {
    font-size: 18px;
    line-height: 24px;
    color: #333333;
  }

  .ewd-li-6-title {
    width: 100%;
    font-size: 14px;
    line-height: 24px;
    color: #898D8F;
    margin-bottom: 10px;
  }

  .ewd-li-5-box-img {
    margin-right: 10px;
  }

  .ewd-li-5-box-b {
    padding-left: 10px;
    font-size: 18px;
  }

  .ewd-li-top {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    height: 40px;
  }

  .ewd-li-5-box {
    line-height: 24px;
    .lossAmountActual{
      width:calc(74% - 76px);
    }
  }

  .ewd-li-5-percentage {
    float: left;
    font-size: 14px;
  }

  .ewd-li-5-per {
    float: left;
    width: 26%;
    margin: 3px 10px 0 10px;
    height: 18px;
    background-color: #f5f5f5;
    border-radius: 2px;
  }

  .ewd-li-5-span {
    height: 18px;
    display: inline-block;
    border-radius: 2px;
  }

  .ewd-li-5-span-1 {
    background-color: #ff4d4e;
  }

  .ewd-li-5-span-2 {
    background-color: #ffa940;
  }

  .ewd-li-5-span-3 {
    background-color: #ff8787;
  }

  .ewd-li-5-span-4 {
    background-color: #8da2fe;
  }

  .ewd-li-top .ewd-li-top-left .iconfont {
    font-size: 20px;
  }

  .ewd-li-top-left {
    display: flex;
    align-items: center;
  }

  .nodara {
    width: 100%;
    height: 100%;
    min-height: 130px;
    color: #b5b5b5;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .ewd-li-top .ewd-li-top-left .icon-processed {
    font-size: 16px;
  }

  .ewd-li-top .ewd-li-top-left span {
    font-size: 14px;
    color: #000;
    margin-left: 4px;
    line-height: 14px;
  }

  .ewm-conter-li-box {
    display: flex;
    border-radius: 2px;
    overflow: hidden;
  }

  .ewd-li-box .ry-form-radio, .early-warning-model .ry-form-radio {
    margin: 0;
  }

  .ry-form-radioed > i, .ry-form-radio > i:hover {
    color: #f5222d
  }

  .right-btn-box .ry-form-radio {
    margin-top: 0;
    padding: 0;
  }

  .ewd-li-top-right span {
    font-size: 22px;
    font-weight: bold;
    color: #333333;
  }

  .ewd-li-bottom {
    width: 100%;
    display: flex;
    height: 28px;
    justify-content: space-between;
    align-items: center;
  }

  .ewd-li-bottom .ewd-li-bottom-li-label {
    font-size: 12px;
    color: #888888;
  }

  .ewd-li-bottom .ewd-li-bottom-li-num {
    font-size: 14px;
    color: #f5212d;
  }

  .ewd-li-bottom .ewd-li-bottom-li-model {
    font-size: 12px;
    background-color: #eeeeee;
    border-radius: 4px;
    border: solid 1px #cccccc;
    color: #888888;
    padding: 2px 12px;
  }

  .ewd-li-bottom .ewd-li-bottom-li-value {
    font-size: 14px;
    color: #03ac2b;
  }

  .ry-quarantine {
    width: 100%;
    height: 13px;
    background-color: #eeeeee;
  }

  .early-warning-model-box {
    width: 100%;
    height: 466px;
    position: relative;
  }

  .early-warning-model-list {
    width: 100%;
    height: 466px;
    position: relative;
  }

  .early-warning-model-box:before {
    position: absolute;
    content: '\7cbe\51c6\5ea6';
    height: calc(100% - 20px);
    left: 50%;
    top: 10px;
    width: 1px;
    background: #cccccc;
    display: flex;
    justify-content: center;
    padding-top: 30px;
    box-sizing: border-box;
    color: #888888;
  }

  .early-warning-model-box:after {
    position: absolute;
    content: "\91cd\8981\6027";
    width: calc(100% - 20px);
    top: 50%;
    left: 10px;
    height: 1px;
    background: #cccccc;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding-right: 30px;
    box-sizing: border-box;
    color: #888888;
  }

  .ewm-box-ul {
  }

  .ewm-box-ul-li {
    height: 233px;
    box-sizing: border-box;
    position: relative;
  }

  .ewm-box-ul-li1 {
    padding: 20px 0 20px 20px;
  }

  .ewm-box-ul-li2 {
    padding: 20px 10px 20px 0;
    position: relative;
  }

  .ewm-box-ul-li3 {
    padding: 35px 0 20px 20px;
  }

  .ewm-box-ul-li4 {
    padding: 35px 10px 20px 0;
  }

  .ewm-box-ul-li1:after {
    content: "";
    position: absolute;
    left: -7px;
    top: 5px;
    width: 0;
    height: 0;
    border-width: 0 8px 8px;
    border-style: solid;
    border-color: transparent transparent #888888;
  }

  .ewm-box-ul-li2:after {
    content: "";
    width: 8px;
    height: 8px;
    background: #fff;
    display: inline-block;
    position: absolute;
    left: 0;
    bottom: -5px;
    border-radius: 50%;
    border: 1px solid #888888;
  }

  .ewm-box-ul-li3:after {
    content: "";
    position: absolute;
    right: -6px;
    top: -7px;
    width: 0;
    height: 0;
    border-width: 8px 8px 8px;
    border-style: solid;
    border-color: transparent transparent transparent #888888;
  }

  .ewm-box-ul-li4:after {
    content: "";
    width: 8px;
    height: 8px;
    background: #fff;
    display: inline-block;
    position: absolute;
    right: -5px;
    bottom: 0;
    border-radius: 50%;
    border: 1px solid #888888;
  }

  .ewm-conter-list {
    height: 180px;
    overflow: auto;
    padding-right: 10px;
  }

  .ewm-conter-li {
    width: 100%;
    height: 20px;
    margin: 10px 0;
    cursor: pointer;
  }

  .ewm-conter-li.active .ewm-conter-li-title {
    color: #f5222d;
  }

  .ewm-conter-li-title {
    padding: 0;
    float: left;
    line-height: 20px;
    width: 32%;
    font-size: 13px;
  }

  .ewm-conter-li-right {
    float: right;
    width: 68%;
  }

  .ewm-conter-li-data {
    width: 46px;
    float: left;
    line-height: 20px;
    padding-left: 2px;
    box-sizing: border-box;
  }

  .ewm-conter-li-data .iconfont {
    font-size: 14px;
    vertical-align: middle;
  }

  .ewm-conter-li-num {
    font-size: 14px;
    color: #333333;
    vertical-align: top;
  }

  .ewm-conter-li-speed {
    float: right;
    width: calc(100% - 46px);
  }

  .ewm-conter-li-value {
    box-sizing: border-box;
    float: left;
    height: 20px;
    color: #ffffff;
    font-size: 14px;
    line-height: 20px;
  }

  /*******************  */
  .ewm-conter-li-value1 {
    text-align: center;
    background-image: linear-gradient(90deg,
      rgba(3, 172, 43, 0.8) 0%,
      rgba(3, 172, 43, 0.3) 100%);
    border-radius: 2px 0px 0px 2px;
  }

  .ewm-conter-li-value2 {
    text-align: center;
    background-image: linear-gradient(90deg,
      rgba(250, 139, 22, 0.8) 0%,
      rgba(250, 139, 22, 0.3) 100%);
    border-radius: 0px 2px 2px 0px;
  }

  .drill-model-list {
  }

  .drill-model-li {
    display: flex;
    align-items: center;
    height: 38px;
  }

  .drill-model-text {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 40px;
  }

  .drill-model-span1 {
    color: #73777a;
  }

  .drill-model-span2 {
    color: #181818;
    margin-right: 8px;
  }

  .ewm-conter-li-values1 {
    text-align: left;
    background: #ffb180;
    padding-left: 6px;
  }

  .ewm-conter-li-values2 {
    padding-left: 8px;
    text-align: left;
    background: #5ad8a6;
  }

  /*******************  */
  .ewm-conter-fixed {
    position: absolute;
  }

  .position-select {
    position: absolute;
    width: 100%;
    text-align: right;
    top: -54px;
  }

  .ewm-conter-fixed-title {
    width: 24px;
    height: 24px;
    line-height: 24px;
    text-align: center;
    background-color: #555555;
    border: solid 2px #cccccc;
    display: inline-block;
    border-radius: 50%;
    font-size: 14px;
    color: #ffffff;
    position: relative;
    z-index: 999;
  }

  .ewm-conter-fixed:hover .ewm-conter-fixed-data {
    display: inline-block;
  }

  .ewm-conter-fixed-data {
    display: none;
    height: 20px;
    background-color: #888888;
    border-radius: 10px 9px 9px 10px;
    border: solid 1px #cccccc;
    width: 110px;
    text-align: center;
    position: absolute;
    right: 15px;
    top: 4px;
    z-index: 0;
    line-height: 20px;
  }

  .ewm-conter-fixed1 {
    left: 10px;
    bottom: 10px;
  }

  .ewm-conter-fixed1 .ewm-conter-fixed-data {
    left: 15px;
    top: 4px;
  }

  .ewm-conter-fixed2 {
    right: 10px;
    bottom: 10px;
  }

  .ewm-conter-fixed2 .ewm-conter-fixed-data {
    right: 15px;
    top: 4px;
  }

  .ewm-conter-fixed3 {
    left: 10px;
    top: 10px;
  }

  .ewm-conter-fixed3 .ewm-conter-fixed-data {
    left: 15px;
    top: 4px;
  }

  .ewm-conter-fixed4 {
    right: 10px;
    top: 10px;
  }

  .ewm-conter-fixed4 .ewm-conter-fixed-data {
    right: 15px;
    top: 4px;
  }

  .ewm-conter-fixed-data .iconfont {
    color: #fff;
    font-size: 10px;
    margin-left: 5px;
  }

  .right-btn-box .ry-form-switch {
    margin-top: -2px;
  }

  .ewm-conter-fixed-data span {
    color: #fff;
  }

  .question-li {
    height: 32px;
    line-height: 32px;
    border-bottom: 1px dashed #cccccc;
  }

  .question-li-1 {
    float: left;
    width: 62%;
  }

  .question-li-2 {
    float: left;
    width: 14%;
    display: flex;
    align-items: center;
  }

  .question-li-3 {
    float: left;
    width: 24%;
  }

  .question-li-4 {
    float: right;
    width: 24%;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .question-li-ranking {
    width: 16px;
    font-size: 12px;
    height: 16px;
    line-height: 16px;
    color: #fff;
    display: inline-block;
    border-radius: 50%;
    text-align: center;
  }

  .question-li-ranking1 {
    background-color: #73777a;
  }

  .question-li-ranking2 {
    background-color: #a9b0b4;
  }

  .question-li-text {
    font-size: 14px;
    margin-left: 10px;
    color: #555555;
  }

  .question-li-2 .iconfont {
    color: #ff4e4f;
    font-size: 18px;
  }

  .question-li-3 .iconfont, .question-li-4 .iconfont {
    color: #ffa940;
    font-size: 18px;
  }

</style>

