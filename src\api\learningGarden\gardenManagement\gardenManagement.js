import request from '@/utils/request'


//获取站点
export function getSiteData() {
  return request({
    url: '/gardenManage/getSiteData',
    method: 'post'
  })
}

//获取所属模块-查询
export function getModuleTreeNodeVo(data) {
  return request({
    url: '/gardenManage/getModuleTreeNodeVo',
    method: 'post',
    data: data
  })
}
//查询文章列表(分页)
export function getArticleList(params,data) {
  return request({
    url: '/gardenManage/getArticleList',
    method: 'post',
    data: data,
    params: params
  })
}
//发布、取消发布文章
export function publishArticle(data) {
  return request({
    url: '/gardenManage/publishArticle',
    method: 'post',
    data: data,
  })
}
//批量发布文章
export function batchPublishArticle(data){
  return request({
    url: '/gardenManage/batchPublishArticle',
    method: 'post',
    data: data,
  })
}

//初始化信息发布ID
export function initInformationId(){
  return request({
    url: '/gardenManage/initInformationId',
    method: 'post',
  })
}
//查询信息列表(分页)
export function getInformationList(params,data) {
  return request({
    url: '/gardenManage/getInformationList',
    method: 'post',
    data: data,
    params: params
  })
}
//根据ID查询信息发布数据
export function getInformationById(data){
  return request({
    url: '/gardenManage/getInformationById',
    method: 'post',
    data
  })
}

//信息发布 新增保存
export function saveInformation(data){
  return request({
    url: '/gardenManage/saveInformation',
    method: 'post',
    data: data,
  })
}


//发布、取消发布信息
export function publishInformation(data) {
  return request({
    url: '/gardenManage/publishInformation',
    method: 'post',
    data: data,
  })
}

//批量发布信息
export function batchPublishInformation(data) {
  return request({
    url: '/gardenManage/batchPublishInformation',
    method: 'post',
    data: data,
  })
}
//删除信息
export function delInformation(data) {
  return request({
    url: '/gardenManage/delInformation',
    method: 'post',
    data: data,
  })
}

//删除附件
export function delInformationFile(id) {
  return request({
    url: '/gardenManage/delInformationFile/'+id,
    method: 'post',
  })
}


//根据业务ID查询文件列表
export function queryInformationFileList(busiId){
  return request({
    url: '/gardenManage/queryInformationFileList/'+busiId,
    method: 'post',
  })
}


