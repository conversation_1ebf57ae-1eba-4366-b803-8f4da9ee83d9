<!--待阅-->
<template>
  <div class="grayBackground pd4">
    <WhiteCard>
      <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="110px">

        <el-form-item label="待阅标题" prop="readTitle">
          <el-input
            v-model="queryParams.readTitle"
            placeholder="待阅标题"
            clearable
            size="small"
            style="width: 240px"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="发送人" prop="sendPersonName">
          <el-input
            v-model="queryParams.sendPersonName"
            placeholder="发送人"
            clearable
            size="small"
            style="width: 240px"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <div class="float-right">
          <el-form-item >
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
        </div>

      </el-form>
    </WhiteCard>
    <WhiteCard>
      <div class="task-box">
        <el-row class="task-header">
          <el-col class="task-header-th task-header-th-1" :span="14">标题</el-col>
          <el-col class="task-header-th task-header-th-3 text-center" :span="3">处理人</el-col>
          <el-col class="task-header-th task-header-th-4 text-right" :span="4">处理时间</el-col>
        </el-row>
        <div class="task-list"  id="tableDataDom">
          <el-scrollbar style="height:100%">
          <div v-for="(item,index) in dataList" @click="checkTaskorg(item)">
            <el-row class="task-li cursor">
              <el-col  class="task-li-td task-li-td-1 ovflowHidden" :span="14" :title="item.readTitle">
                {{item.readTitle}}
              </el-col>
              <el-col class="task-li-td task-li-td-3 text-center ovflowHidden" :title="item.sendPersonName"  :span="3">{{item.sendPersonName}}</el-col>
              <el-col class="task-li-td task-li-td-4 text-right" :span="4">{{item.sendTime}}</el-col>
            </el-row>
          </div>
          </el-scrollbar>
        </div>
        <pagination
          v-show="total>0"
          :total="total"
          :page.sync="queryParam.pageNum"
          :limit.sync="queryParam.pageSize"
          @pagination="getList"/>
      </div>
    </WhiteCard>
    <taskToRead
      ref="todo"
      tabFlag="1"
      :selectValue="selectValue"
      @refresh="getList">
    </taskToRead>
  </div>
</template>

<script>
  import WhiteCard from '@/components/WhiteCard';
  import TrigTag from '@/components/trig-tag';
  import TaskToRead from '../common/taskToRead';
  import { taskToRead } from "@/api/workflow/task";
  import { Loading } from 'element-ui';

  export default {
    name: "Task-toread",
    components: {
      WhiteCard,
      TrigTag,
      TaskToRead
    },
    data(){
      return {
        // 显示搜索条件
        showSearch: true,
        // 查询参数
        queryParams: {
          readTitle: undefined,
          createByName:undefined
        },
        queryParam: {
          pageNum: 1,
          pageSize: 10
        },
        selectValue:{},
        total:6,
        handleColor:{
          0:'#6AC9B4',
          1:'#6AC9B4',
          2:'#F3545C',
          3:'#909399',
          4:'#F8A334',
          5:'#80A2C9',
          6:'#54c4f3'
        },
        dataList:[],
        loadingInstance: ''
      }
    },
    created() {
      this.getList();
    },
    methods: {
      openLoading() {
        this.loadingInstance = Loading.service({
          target: document.querySelector('#tableDataDom'),
          spinner: 'el-icon-loading', // 自定义加载图标类名
          text: '正在加载...', // 显示在加载图标下方的加载文案
          lock: false // lock的修改符--默认是false
        })
        return this.loadingInstance
      },
      /** 待办列表 */
      getList(){
        this.openLoading();
        taskToRead(this.queryParam,this.queryParams).then(
          response => {
            this.dataList = response.rows;
            this.total = response.total;
            this.loading = false;
            this.$nextTick(() => { // 以服务的方式调用的 Loading 需要异步关闭
              this.loadingInstance.close()
            })
          }
        );
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParams.pageNum = 1;
        this.getList();
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.resetForm("queryForm");
        this.handleQuery();
      },
      /** 详情*/
      checkTaskorg(item) {
        this.selectValue = item;
        this.$nextTick(() => {
          this.$refs.todo.show();
        });
      },
    }
  }
</script>

<style scoped lang="scss">
  .task-box{
    height: calc(100vh - 210px);
    padding:0 30px;
    .task-header{
      line-height: 60px;
      .task-header-th{
        font-size: 16px;
        font-weight: bold;
        color: #333333;
      }
      .task-header-th-1{
        padding-left:70px;
      }
      .task-header-th-2{
        padding-left:30px;
      }
      .task-header-th-4{
        padding-right:30px;
      }
    }
    .task-list{
      height: calc(100% - 140px);
      .task-li{
        height: 48px;
        line-height: 48px;
        background: #F6F6F6;
        margin-bottom:12px;
        .task-li-td{
          font-size: 14px;
          font-weight: 400;
          line-height: 48px;
          height: 48px;
          color: #333333;
        }
        .task-li-td-1{
          padding-left:20px;
        }
        .task-li-td-4{
          padding-right:20px;
        }
      }
    }
  }
</style>
