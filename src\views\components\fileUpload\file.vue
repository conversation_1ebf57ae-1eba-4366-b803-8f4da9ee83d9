<template>
  <div class="upload-file">
    <el-upload
      :action="uploadFileUrl"
      :file-list="fileList"
      :limit="limit"
      :on-error="handleUploadError"
      :on-success="handleUploadSuccess"
      :show-file-list="false"
      :headers="headers"
      :data="param"
      class="upload-file-uploader"
      ref="upload"
    >
      <!-- 上传按钮 -->
      <!--<el-button size="mini" style="display: none" type="primary">{{btnTitle}}</el-button>-->
    </el-upload>

    <!-- 文件列表 -->
    <transition-group class="upload-file-list el-upload-list el-upload-list--text" name="el-fade-in-linear" tag="ul">
      <li :key="file.uid" class="el-upload-list__item ele-upload-list__item-content" v-for="(file, index) in fileList">
        <el-link :href="`${baseUrl}${file.url}`" :underline="false" target="_blank">
          <span class="el-icon-document"> {{ getFileName(file.name) }} </span>
        </el-link>
        <div class="ele-upload-list__item-content-action">
          <el-link :underline="false" @click="handleDelete(index)" type="danger">删除</el-link>
        </div>
      </li>
    </transition-group>
  </div>
</template>

<script>
  import { getToken } from "@/utils/auth";

  export default {
    name: "FileUpload",
    props: {
      // 值
      value: [String, Object, Array],
      // 数量限制
      limit: {
        type: Number
      },
      // 大小限制(MB)
      fileSize: {
      },

      //上传地址
      fileUrl:{
        type:String
      },
      // 是否显示提示
      isShowTip: {
        type: Boolean,
        default: true
      },
      //按钮名称
      btnTitle:{
        type:String,
        default:'上传附件'
      },
      //额外参数
      param:{
        type:String
      }
    },
    data() {
      return {
        baseUrl: process.env.VUE_APP_BASE_API,
        uploadFileUrl: process.env.VUE_APP_BASE_API + this.fileUrl,
        headers: {
          Authorization: "Bearer " + getToken(),
        },
        fileList: [],
      };
    },
    watch: {
      value: {
        handler(val) {
          if (val) {
            let temp = 1;
            // 首先将值转为数组
            const list = Array.isArray(val) ? val : this.value.split(',');
            // 然后将数组转为对象数组
            this.fileList = list.map(item => {
              if (typeof item === "string") {
                item = { name: item, url: item };
              }
              item.uid = item.uid || new Date().getTime() + temp++;
              return item;
            });
          } else {
            this.fileList = [];
            return [];
          }
        },
        deep: true,
        immediate: true
      }
    },
    computed: {
      // 是否显示提示
      showTip() {
        return this.isShowTip ;
      },
    },
    methods: {
      //上传
      uploadClick(){
        this.$refs['upload'].$children[0].$refs.input.click()
      },
      // 上传失败
      handleUploadError(err) {
        this.$message.error("上传失败, 请重试");
      },
      // 上传成功回调
      handleUploadSuccess(res, file) {
        if(res.code===200){
          this.$emit("handleUploadSuccess", this.listToString(this.fileList));
          this.$message.success("上传成功");
          this.fileList.push({ name: res.fileName, url: res.fileName });
        }else{
          this.$confirm(res.msg, '提示', {
            confirmButtonText: '确定',
            type: 'error'
          }).then(() => {
          })
          // this.$message.error(res.msg);
        }
      },
      // 删除文件
      handleDelete(index) {
        this.fileList.splice(index, 1);
        this.$emit("input", this.listToString(this.fileList));
      },
      // 获取文件名称
      getFileName(name) {
        if (name.lastIndexOf("/") > -1) {
          return name.slice(name.lastIndexOf("/") + 1).toLowerCase();
        } else {
          return "";
        }
      },
      // 对象转成指定字符串分隔
      listToString(list, separator) {
        let strs = "";
        separator = separator || ",";
        for (let i in list) {
          strs += list[i].url + separator;
        }
        return strs != '' ? strs.substr(0, strs.length - 1) : '';
      }
    }
  };
</script>

<style scoped lang="scss">
  .upload-file-uploader {
    margin-bottom: 5px;
  }
  .upload-file-list .el-upload-list__item {
    border: 1px solid #e4e7ed;
    line-height: 2;
    margin-bottom: 10px;
    position: relative;
  }
  .upload-file-list .ele-upload-list__item-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: inherit;
  }
  .ele-upload-list__item-content-action .el-link {
    margin-right: 10px;
  }
</style>
