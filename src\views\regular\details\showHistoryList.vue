<template>
  <div class="regular">
    <el-dialog  :visible.sync="visible" @open="showBackHistoryList"  append-to-body  :title="title" width="90%">
      <el-form ref="detailForm" size="medium" label-width="118px">
        <el-row>
          <el-col :span="24">
            <div style="height:calc(71vh - 280px);min-height:440px">
              <el-table v-loading="loading" :data="tableList" height="100%">
                <el-table-column label="序号" type="index" min-width="4%" align="center"/>
                <el-table-column label="上报单位" prop="reportUnitName" align="center" min-width="10%"/>
                <el-table-column label="接口人" prop="interfaceUserName" align="center" min-width="10%"/>
                <el-table-column label="邮箱" prop="interfaceUserMail"  align="center" min-width="15%"/>
                <el-table-column label="联系电话" prop="interfaceUserPhone" align="center" min-width="10%"/>
                <el-table-column label="完成时间" prop="procEndTime" align="center" min-width="10%"/>
                <el-table-column label="是否退回" prop="backFlag" align="center" min-width="10%">
                 <template slot-scope="scope">
                    {{
                    scope.row.backFlag
                    | fromatComon(backFlagDict)
                    }}
                  </template>
                </el-table-column>
                <el-table-column label="退回时间" prop="backTime" align="center" min-width="10%"/>
                <el-table-column label="操作"  width="100px" align="center">
                  <template slot-scope="scope">
                    <el-button
                      size="mini"
                      type="text"
                      icon="el-icon-show"
                      @click="detail(scope.row)"
                      v-if="scope.row.procStatus == '2'"
                    >查看
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </el-dialog>
    <showDetail
      key="reportUnitId"
      ref="showDetail"
      :check="check"
      :reportUnitId="reportUnitId"
      :regularReportId="regularReportId"
      :procInsId="procInsId"
    >
    </showDetail>
  </div>
</template>
<script>
  import {
    showBackHistoryList//显示历史数据
  } from '@/api/regular/add/addAndEditRegular.js';
  import showDetail from './provHandlerDetail';//已完成详情
  export default {
    name: "regularDetail"
    ,components:{
      showDetail//已完成详情
    }
    ,props:{
      regularReportId:{
        type:String,
        default:''
      }
     ,reportUnitCode:{
        type:String,
        default:''
      }
      ,orgGrade:{
        type:String,
        default:''
      }
    }
    ,data() {
      return {
        check:false,
        visible:false//弹窗
        ,loading:false
        ,procInsId:''//流程主键
        ,reportUnitId:''//业务信息主键
        ,tableList: []//表格数据
        ,backFlagDict:[
          {value:'0',label:'否'}
          ,{value:'1',label:'是'}
        ]
      }
    }
    ,methods: {
      // 显示弹框
      show() {
        this.visible = true;
      },
      //查询表格数据
      showBackHistoryList(){
        this.loading = true;
        showBackHistoryList(this.regularReportId,this.reportUnitCode,this.orgGrade).then(
          response =>{
            this.loading = false;
            this.tableList = response.data;
          }
        )
      },
      //已完成详情
      detail(row){
        this.procInsId = row.procInsId;
        this.reportUnitId = row.reportUnitId;
        this.$refs.showDetail.show();
      },
    }
  }

</script>
