<template>
    <div class="select-next">
      <el-dialog :visible.sync="radioTreeVisible" width="60%" append-to-body title="流程选择">
        <radioTree
          :key="selectId||radioTreeVisible"
          ref="radioTree"
          url="/riskEvaluationForm/provinceScopeUserTree"
          :select-tree-data="selectTreeData"
          :provCode="fillUserProvince"
          @accept="replaceTaskFillPerson"
        />
        <div slot="footer" class="dialog-footer">
          <el-button @click="close" size="small" >取消</el-button>
          <el-button type="primary" size="small" @click="savePers">确认</el-button>
        </div>
      </el-dialog>
    </div>
</template>

<script>
  import radioTree from '@/components/process-common/common/radioTree.vue'// tree
    export default {
        name: "renderNoNextLinkUser",
        components: { radioTree },
      props:['selectTreeData'],
      data() {
        return {
          radioTreeVisible:false,//人员选择
          id:'',//主键
          selectId:'',//人员选择id
          selectTree:[{name:'',id:''}],//选中的人
          formData:{},
          tableData: [],
          fillUserProvince: '0037',
          loadingInstance: ''
        }
      },
      methods:{
        //弹出层
        openSelect(){
          this.radioTreeVisible = true;
        },
        //关闭弹出层
        close(){
          this.radioTreeVisible = false;
        },
        //保存人员
        replaceTaskFillPerson(selectedUsers){
          this.$emit('nextLinkUser',selectedUsers)
        },
        // 确定选择的人员
        savePers() {
          this.$refs.radioTree.save();
        },
      }
    }
</script>

<style scoped lang="scss">
     ::v-deep .el-dialog__body{
      padding: 10px;
     }


</style>
