<!--查看专项报告-->
<template>
  <div class="scope">

    <el-dialog :visible.sync="visible" :modal-append-to-body="false" append-to-body :title="title" width="90%">

      <BlockCard title="项目基本信息">
      <div  style="position: relative">
        <opinion
          v-if="procInsId"
          :key="procInsId"
          :processInstanceId="procInsId"
          :isShow="isShow"
        />
      </div>
        <el-form ref="elForm" :model="formData" size="medium" label-width="128px">
          <el-row>
            <el-col :span="16">
              <el-form-item label="项目名称" prop="projectName">
                {{formData.projectName}}
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="项目编号" prop="projectNum">
                {{formData.projectNum}}
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="项目类型" prop="projectTypeEnumId">
                {{formData.projectTypeEnumId | fromatComon(dict.type.SPR_PROJECT_TYPE_ALL)}}
              </el-form-item>
            </el-col>

            <el-col :span="8">
              <el-form-item label="审计对象" prop="projectOrgName">
                {{formData.projectOrgName}}
              </el-form-item>
            </el-col>

            <el-col :span="8">
              <el-form-item label="项目年度" prop="projectYear">
                {{formData.projectYear}}
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </BlockCard>

      <BlockCard
        title="审计报告信息"
      >
        <AuditReportInformationUpload
          :key="id"
          :id="id"
          ref="file"
        ></AuditReportInformationUpload>

      </BlockCard>
      <BlockCard title="项目台账信息">
        <el-form>
          <el-table border v-loading="tableLoading" :data="ledgerTable">
            <el-table-column  type="index" width="50" align="center" label="序号" >
              <template slot-scope="scope">
                <table-index
                  :index="scope.$index"
                />
              </template>
            </el-table-column>
            <el-table-column label="问题编号" prop="problemNum" min-width="13%" align="center" />
            <el-table-column label="发现问题业务类型" prop="problemTypeEnumName" min-width="13%" show-overflow-tooltip align="center"/>
            <el-table-column label="审计发现问题" prop="problemAudit"  min-width="23%" show-overflow-tooltip align="left">
            </el-table-column>
            <el-table-column label="具体问题描述" prop="problemDescription"  min-width="23%" show-overflow-tooltip align="left">
            </el-table-column>
            <el-table-column label="是否上报告" prop="reportFlag"  min-width="8%" show-overflow-tooltip align="center">
              <template slot-scope="scope" class="text-center">
                {{ scope.row.reportFlag==1?'是':scope.row.reportFlag==0?'否':'' }}
              </template>
            </el-table-column>
            <el-table-column label="是否追责" prop="ifDuty"  min-width="8%" show-overflow-tooltip align="center">
              <template slot-scope="scope" class="text-center">
                {{ scope.row.ifDuty==1?'是':scope.row.ifDuty==0?'否':'' }}
              </template>
            </el-table-column>
            <el-table-column label="是否移交纪检" prop="transferFlag"  min-width="8%" show-overflow-tooltip align="center">
              <template slot-scope="scope" class="text-center">
                {{ scope.row.transferFlag==1?'是':scope.row.transferFlag==0?'否':'' }}
              </template>
            </el-table-column>
            <el-table-column label="日常问题" prop="problemNum"  min-width="8%" align="center" >
              <template slot-scope="scope" class="text-center">
                <a @click="showProblemInfo(scope.row.id)" class="table-btn" style='color: #c20000;'>
                  {{ scope.row.problemCount }}
                </a>
              </template>
            </el-table-column>
          </el-table>
        </el-form>
      </BlockCard>
      <BlockCard
        title="初核专项报告"
      >
        <PreliminaryFileUpload
          :key="id"
          :projectId = "id"
          ref="file"
        ></PreliminaryFileUpload>

      </BlockCard>

      <BlockCard
        title="阶段性报告"
      >
        <StageWiseUpload
          :key="id"
          :id="id"
          problemStatus="2"
          ref="stageWiseUpload"
        ></StageWiseUpload>

      </BlockCard>
      <div slot="footer">
        <el-button size="mini" @click="close">关闭</el-button>
      </div>
    </el-dialog>

    <!--已关联日常问题列表页面-->
    <AssociatedDailyProblem
      ref="associated"
      :key="associatedIndex"
      :pbInId="ledgerId"
      :projectOrgId="formData.projectOrgId"
      :edit="edit"
      @onClose="queryLedgersList"
    ></AssociatedDailyProblem>
  </div>
</template>

<script>
import {
  queryLedgersList
  ,queryProjectInfo} from '@/api/special-report'
import BlockCard from '@/components/BlockCard'
import AuditReportInformationUpload from './auditReportInformationUpload';//审计报告信息
import PreliminaryFileUpload from '@/views/specialreport/flow/operation/fileUpload';//初核审计报告
import StageWiseUpload from '@/views/specialreport/add/operation/stageWiseUpload';//阶段性报告
import AssociatedDailyProblem from '@/views/specialreport/spledger/associatedDailyProblem';//已关联问题

import opinion from '@/views/daily/modifyRecord/opinionFloat';//流转历史

export default {
  name: "specialReportEdit",
  components: {
    BlockCard
    ,AuditReportInformationUpload
    ,PreliminaryFileUpload
    ,StageWiseUpload
    ,AssociatedDailyProblem
    ,opinion},
  dicts: ['SPR_PROJECT_TYPE_ALL']
  , props: {},
  data() {
    return {
      // edit:false,
      title: '查看',
      id: '',//主键
      visible: false,//弹框
      formData: {
        projectName: ''
        , projectNum: ''
        , projectTypeEnumId: ''
        , projectYear: ''
        , projectOrgId: ''
        , projectOrgName: ''
      },
      tableLoading: false, // 表格loading
      ledgerTable:[],//台账信息
      showTip:false,
      associatedIndex:0,//已关联问题
      edit:'show',//已关联问题不可操作
      ledgerId:'',//台账表主键
      procInsId:'',//流程id
      isShow:'0',//流转历史按钮是否显示
    }
  }
  , created() {
  }
  , methods: {
    // 显示弹框
    show(id) {
      this.id = id;
      this.visible = true;
      this.queryProjectInfo()
      this.queryLedgersList()
    },
    //关闭弹窗
    close() {
      this.visible = false;
      this.$emit("close");
    },
    //根据主键查询项目信息
    queryProjectInfo(){
      queryProjectInfo(this.id).then((res)=>{
        this.formData = res.data;
        this.procInsId = res.data.procInsId;
        this.isShow = '1';
        this.$forceUpdate();
      })
    },
    //根据主键查询台账信息
    queryLedgersList(){
      queryLedgersList(this.id).then((res)=>{
        //台账信息
        this.ledgerTable = res.data.ledgerReturnList;
        this.$forceUpdate();
      })
    },
    //下载模板
    handleDownload(){
      this.download('/colligate/violFile/downloadTemplate/id', {
      },'模板.txt')
    },
    // 导入
    handleUploadSuccess(res, file) {
      this.$modal.msgSuccess('上传成功');
      this.queryLedgersList();
    },
    delLedgers(ledgersId){
      this.$modal.confirm("确认删除此条数据").then(function() {
        return delLedgers(ledgersId)
      }).then((response) => {
        this.$modal.msgSuccess("删除成功");
        //刷新台账列表
        queryLedgersList(this.id).then((res)=>{
          this.ledgerTable = res.data;
        })
      }).catch(() => {});

    },
    //校验  保存
    toSaveData() {
      if (!this.formData.problemTypeEnumName) {
        this.$modal.msgError("发现问题业务类型】不能为空！");
        return false;
      } else if (!this.formData.problemAudit) {
        this.$modal.msgError("【审计发现问题】不能为空！");
        return false;
      } else if (!this.formData.problemDescription) {
        this.$modal.msgError("【具体问题描述】不能为空！");
        return false;
      } else if (!this.formData.reportFlag) {
        this.$modal.msgError("请选择【是否上报告】！");
        return false;
      } else if (!this.formData.ifDuty) {
        this.$modal.msgError("请选择【是否追责】！");
        return false;
      } else if (!this.formData.transferFlag) {
        this.$modal.msgError("请选择【是否移交】！");
        return false;
      }
      //后端校验
      saveLedger({...this.formData, ...{id: this.id}}).then(
        response => {
          if (response.code == 200) {
            this.$modal.msgSuccess("保存成功");
            this.$emit('close');
            this.close();
          }
        }
      )
    },
    //攥取已关联问题列表
    showProblemInfo(id){
      this.ledgerId = id;
      this.associatedIndex++;
      this.$nextTick(()=> {
        this.$refs.associated.show();
      });
    },
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-select {
  width: 100%;
}

::v-deep .el-dialog__body {
  height: 70vh;
  overflow: auto;
  padding-top: 10px !important;
  background: #fff !important;
}
</style>
