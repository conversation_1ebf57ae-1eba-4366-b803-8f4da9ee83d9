<!-- 初核专项报告录入-->
<template>
  <div class="app-container app-report">
    <el-form :model="queryParams" ref="queryForm" id="queryParams"  label-width="120px">
      <el-row>
      <el-col :span="12">
      <el-form-item label="项目名称">
        <el-input v-model="queryParams.projectName" placeholder="项目名称"
                  size="medium">
        </el-input>
      </el-form-item>
      </el-col>
      <el-col :span="6">
      <el-form-item label="项目编码">
        <el-input v-model="queryParams.projectNum" placeholder="项目编码"
                  size="medium">
        </el-input>
      </el-form-item>
      </el-col>
      <el-col :span="6">
      <el-form-item label="状态">
        <el-select
          v-model="queryParams.projectStatusList"
          placeholder="状态"
          clearable
          multiple
          size="medium"
        >
          <el-option
            v-for="dict in dict.type.SP_PROJECT_STATUS"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      </el-col>
      </el-row>
      <el-row>
      <el-col :span="6">
        <el-form-item label="项目类型">
          <el-select
            v-model="queryParams.projectTypeEnumId"
            placeholder="项目类型"
            clearable
            size="medium"
          >
            <el-option
              v-for="dict in dict.type.SPR_PROJECT_TYPE_ALL"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="审计对象">
          <el-input v-model="queryParams.projectOrgId" placeholder="审计对象"
                    size="medium"v-show="false"></el-input>
            <el-input v-model="queryParams.projectOrgName" placeholder="审计对象" @focus="showAuditOrg()"
                      size="medium"></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="存在追责事项">
          <el-select
            v-model="queryParams.existDutyLedger"
            placeholder="存在追责事项"
            clearable
            size="medium"
          >
            <el-option  label="--请选择--" value=""></el-option>
            <el-option label="存在" value="1"/>
            <el-option label="不存在" value="0"/>
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="6">
      <div style="text-align: right;">
        <el-button
          type="primary"

          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
        >新增</el-button>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </div>
      </el-col>
      </el-row>
    </el-form>
    <el-form   style="height: calc(100vh - 340px)">
      <el-table v-loading="loading" :data="tableList"  height="100%" border>
        <el-table-column label="序号" type="index" width="80" align="center" show-overflow-tooltip>
          <template slot-scope="scope">
            <table-index
              :index="scope.$index"
              :page-num="queryParams.pageNum"
              :page-size="queryParams.pageSize"
            />
          </template>
        </el-table-column>
        <el-table-column label="项目名称" prop="projectName" width="300" show-overflow-tooltip align="center">
          <template slot-scope="scope">
            <div class="table-text-left ovflowHidden">{{scope.row.projectName }}</div>
          </template>
        </el-table-column>
        <el-table-column label="项目编码" prop="projectNum"  width="200"  show-overflow-tooltip align="center"/>
        <el-table-column label="项目类型" prop="projectTypeEnumId"  width="150"  show-overflow-tooltip align="center">
          <template slot-scope="scope">
            {{scope.row.projectTypeEnumId | fromatComon(dict.type.SPR_PROJECT_TYPE_ALL)}}
          </template>
        </el-table-column>
        <el-table-column label="问题更新方式" prop="updateMethod" width="150" show-overflow-tooltip align="center">
          <template slot-scope="scope">
            {{ scope.row.updateMethod==1?'手动新增':scope.row.updateMethod==0?'同步发起':'' }}
          </template>
        </el-table-column>
        <el-table-column label="审计对象" prop="projectOrgName" width="150" show-overflow-tooltip align="center"/>
        <el-table-column label="存在追责事项" prop="existDutyLedger" width="250" show-overflow-tooltip align="center">
          <template slot-scope="scope">
            {{ scope.row.existDutyLedger==1?'存在':scope.row.existDutyLedger==0?'不存在':'' }}
          </template>
        </el-table-column>
        <el-table-column label="追责事项台账数" prop="dutyLedgerNum" width="150" show-overflow-tooltip align="center"/>
        <el-table-column label="超时天数" prop="overTimeDay" width="100" show-overflow-tooltip align="center"/>
        <el-table-column label="状态" prop="projectStatus"  width="150" show-overflow-tooltip align="center">
          <template slot-scope="scope">
            {{scope.row.projectStatus | fromatComon(dict.type.SP_PROJECT_STATUS)}}
          </template>
        </el-table-column>
        <el-table-column label="更新时间" prop="updateTimeShow" width="200" show-overflow-tooltip align="center"/>
        <el-table-column label="当前处理人" prop="handleName" width="200" show-overflow-tooltip align="center"/>
        <el-table-column label="操作" fixed="right" width="250"  align="center" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              title="查看"
              icon="el-icon-search"
              @click="handleDetail(scope.row.id)"
            ></el-button>


            <el-button
              v-if="scope.row.projectStatus === 'TBZ' && !scope.row.procInsId"
              size="medium"
              type="text"
              icon="el-icon-edit"
              title="编辑"
              @click="handleEdit(scope.row.id)"
            >
            </el-button>

            <el-button
              v-if="scope.row.projectStatus === 'TBZ' && scope.row.procInsId"
              size="medium"
              type="text"
              icon="el-icon-edit"
              title="编辑"
              @click="checkTaskorg(scope.row)"
            >
            </el-button>

            <el-button
              v-if="scope.row.projectStatus === 'TBZ' && !scope.row.procInsId"
              size="medium"
              type="text"
              icon="el-icon-delete"
              title="删除"
              @click="handleDel(scope.row)"
            ></el-button>
            <el-button
              v-if="scope.row.projectStatus === 'YWC' && scope.row.existDutyLedger === '1'"
              size="medium"
              type="text"
              icon="el-icon-upload2"
              title="上传阶段性报告"
              @click="uploadReport(scope.row.id)"
            ></el-button>
          </template>
        </el-table-column>
      </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="specialReportList"
    />
    </el-form>
    <!--编辑与新增-->
    <SpecialReportEdit
      ref="specialReport"
      :key="keyAdd"
      @close="closeAdd"
    >
    </SpecialReportEdit>


    <!--查看-->
    <SpecialReportDetail
      ref="specialReportDetail"
      :key="keyAdd"
    >
    </SpecialReportDetail>

    <!--上传阶段性报告-->
    <PeriodicReport
      ref="periodicReport"
      :key="keyAdd"
      @close="closeAdd"
    >
    </PeriodicReport>

    <taskToDo
      v-if="selectValue"
      :key="index"
      ref="todo"
      tab-flag="1"
      :select-value="selectValue"
      @refresh="closeAdd"
    />
    <el-dialog :visible.sync="auditOrgFlag" width="60%" append-to-body title="审计对象">
      <AuditOrgTree
        v-if="auditOrgFlag"
        :key="auditOrgIndex"
        ref="auditOrgTree"
        :url="auditOrgTreeUrl"
        :projectOrgId="queryParams.projectOrgId"
        @list="departLists"
      />
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="sureAuditOrg" plain>确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  querySpecialReportList,
  specialReportDel
} from '@/api/special-report'
import TaskToDo from '@/views/workflow/tasklist/common/taskToDo'
import SpecialReportEdit from './operation/specialReportEdit';//编辑  新增
import SpecialReportDetail from './operation/specialReportDetail';//查看
import PeriodicReport from './operation/periodicReport';//上传阶段性报告
import AuditOrgTree from './operation/auditOrgTree';// 审计对象import
import {
taskToDo,
  taskHasDone,
  taskToRead,
  taskHasRead
} from '@/api/workflow/task'
export default {
  name: "specialReportList",
  components: {
    SpecialReportEdit
    ,SpecialReportDetail
    ,PeriodicReport
    ,AuditOrgTree
    ,TaskToDo
  },
  dicts: ['SP_PROJECT_STATUS','SPR_PROJECT_TYPE_ALL'],
  data() {
    return {
      keyAdd:0,
      auditOrgIndex:0,
      loading:false,
      //填报遮罩层
      visible:false,
      //查询 参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        projectName:'',
        projectNum:'',
        projectStatus:'',
        projectStatusList:[],
        projectTypeEnumId:'',
        projectOrgId:'',
        projectOrgName:''
      },
      index:0,
      selectValue:{},
      queryType:'0',//查询类型  0：录入查询；1：统计查询
      auditOrgFlag:false,//审计对象是否加载
      auditOrgTreeUrl:'/spr/getAuditOrgList',
    };
  },
  created() {
    this.specialReportList();
  },
  filters: {
  },
  methods: {
    //跳转待办
    checkTaskorg(item) {
      const depts = this.$store.getters.depts
        this.selectValue = item
        this.index++
        this.$nextTick(() => {
          this.$refs.todo.show()
        })

    },
    /**查询列表*/
    specialReportList() {
      this.loading = true;
      querySpecialReportList({...this.queryParams,...{queryType:this.queryType}}).then(
        response => {
          this.tableList = response.rows;
          this.total = response.total;
          this.loading = false;
        }
      );
    },
    //审计对象
    showAuditOrg(){
      this.auditOrgIndex ++;
      this.auditOrgFlag = true;
    },
    //确认审计对象
    sureAuditOrg(){
      //获取选中信息
      this.$refs.auditOrgTree.list();
    },
    //保存上报单位(回调函数)
    departLists(data){
      this.queryParams.projectOrgName =data[0].name
      this.queryParams.projectOrgId =data[0].id
      this.auditOrgFlag = false;
    },
    /** 新增按钮操作*/
    handleAdd() {
      this.keyAdd++;
      this.$nextTick(()=>{
        this.$refs.specialReport.show('');
      })
    },
    /** 编辑按钮操作*/
    handleEdit(id) {
      this.keyAdd++;
      this.$nextTick(()=>{
        this.$refs.specialReport.show(id);
      })
    },
    /** 上传阶段性报告*/
    uploadReport(id) {
      this.keyAdd++;
      this.$nextTick(()=>{
        this.$refs.periodicReport.show(id);
      })
    },
    /** 查看操作 */
    handleDetail(id){
      this.keyAdd++;
      this.$nextTick(()=>{
        this.$refs.specialReportDetail.show(id);
      })
    },
    /** 关闭触发操作*/
    closeAdd() {
      this.queryParams.pageNum = 1;
      this.specialReportList();
    },
    /** 搜索按钮操作*/
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.specialReportList();
    },
    /**重置按钮操作*/
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        projectName:'',
        projectNum:'',
        projectStatus:'',
        projectStatusList:[],
        projectTypeEnumId:'',
        projectOrgId:'',
        projectOrgName:''
      };
      this.specialReportList();
    },

    /** 删除按钮操作 */
    handleDel(row) {
      this.$modal.confirm('是否确定删除该数据？').then(function() {
        return specialReportDel(row.id);
      }).then(() => {
        this.$modal.msgSuccess("删除成功");
        this.specialReportList();
      }).catch(() => {});
    },


  }
};
</script>
<style  lang="scss" scoped>
  ::v-deep .el-select{
    width: 100%;
  }
</style>







