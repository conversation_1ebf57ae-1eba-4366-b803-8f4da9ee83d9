<template>
    <el-container  class="padding_b60" style="border: none;">
      <el-main>
        <div style=" margin-bottom: 20px;">
          <span class="text-blue float-right margin-l10" v-show="dataDetails.commitFlag=='1'">【已提交】</span>
          <span class="text-red float-right margin-l10" v-show="dataDetails.commitFlag=='0'">【待提交】</span>
          <span class="base-warning"><i class="el-icon-info submit-i"></i>温馨提示：状态为已提交时，其他人员才能查询到</span>
        </div>
        <el-form ref="dataDetails" :model="dataDetails" label-width="120px" style="padding: 20px 300px; ">

          <el-form-item label="收件人"
            prop="receivePerson"
            :rules="[
              { required: true, message: '收件人不能为空'},
              { min: 1, max: 64, message: '长度在 1 到 64 个字符', trigger: 'blur' }
            ]"
          >
            <el-input v-model="dataDetails.receivePerson"></el-input>
          </el-form-item>
          <el-form-item label="发件人"
          prop="sendPerson"
          :rules="[
            { required: true, message: '发件人不能为空'},
            { min: 1, max: 64, message: '长度在 1 到 64 个字符', trigger: 'blur' }
          ]"
          >
            <el-input v-model="dataDetails.sendPerson"></el-input>
          </el-form-item>
          <el-form-item label="标题"
          prop="title"
          :rules="[
            { required: true, message: '标题不能为空'},
            { min: 1, max: 128, message: '长度在 1 到 128 个字符', trigger: 'blur' }
          ]"
          >
            <el-input v-model="dataDetails.title"></el-input>
          </el-form-item>
          <el-form-item label="发件内容"
            prop="mailContent"
            :rules="[
              { required: true, message: '发件内容不能为空'}
            ]"
          >
            <el-input v-model="dataDetails.mailContent"></el-input>
          </el-form-item>
        </el-form>

        <div style="height: 36px; line-height: 36px; margin-bottom: 20px">
          <span class="file-title">附件列表</span>
          <div style="float: right; padding: 3px 0; white-space:nowrap" type="text" >
            <el-upload
              class="upload-demo"
              ref="upload"
              accept=".doc, .docx"
              :headers="headers"
              :data="fileParams"
              :action="url"
              :show-file-list="false"
              :before-upload="handlePreview"
              :on-success="handleFileSuccess"
              >
              <el-button size="small" type="primary">上传</el-button>
            </el-upload>
          </div>
        </div>
        <div>
          <el-table :data="dataDetails.files" max-height="250" style="width: 100%" border :show-header="false":cell-class-name="rowClass">
            <el-table-column label="序号" type="index"  min-width="10%" align="center" />
            <el-table-column label="文档名称" prop="fileName" min-width="55%"/>
            <el-table-column label="上传人" prop="createLoginName"  min-width="13%"/>
            <el-table-column label="上传时间" prop="createTime" :formatter="dateFormat" min-width="13%"/>
            <el-table-column label="操作" fixed="right" min-width="9%" align="center" class-name="small-padding fixed-width">
              <template slot-scope="scope">
                <i class="el-icon-delete" style="color: red" @click="deleteFile(scope.row)" ></i>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-main>
      <FooterBox>
        <div class="float-right">
          <el-button size="mini" @click="saveSendBoxBaseInfo(0)" :loading="buttonLoading=='save'">保存</el-button>
          <el-button size="mini" type="primary" @click="saveSendBoxBaseInfo(1)" :loading="buttonLoading=='sub'">提交</el-button>
        </div>
      </FooterBox>
    </el-container>
</template>

<script lang="ts">
  import {getSendBoxOne, saveSendBox, deleteViolFile, getSendBoxById, getBaseSendBoxNull, getBaseSendBoxNewVerson } from "@/api/base/sendBox";
  import moment from "moment";
  import { getToken } from "@/utils/auth";

  export default {
    name: "sendBox",
    data() {
      return {
        dataDetails: {},
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        url: process.env.VUE_APP_BASE_API + '/colligate/baseInfo/uploadViolFile',
        fileParams: {
          busiId: '',
          busiTableName: 'T_COL_VIOL_BASE_SEND_BOX',
        },
        promise: null,
        buttonLoading: null,
      };
    },
    created() {
      this.sendBoxBaseInfo();
    },
    methods: {
      /**查询企业基本信息详情*/
      sendBoxBaseInfo() {
        getSendBoxOne().then(
          response => {
            if(response.code == 200 && response.data){
              this.dataDetails = response.data;
              this.fileParams = {
                ...this.fileParams,
                busiId: response.data.id,
              };
            }
            this.buttonLoading = null;
          }
        );
      },
      /**保存或提交*/
      saveSendBoxBaseInfo(commitFlag){
        this.$refs["dataDetails"].validate((valid) => {
          const params = {
            ...this.dataDetails,
            commitFlag
          };
          if (valid) {
            if(commitFlag==1){
              this.buttonLoading = 'sub';
            }else{
              this.buttonLoading = 'save';
            }
            saveSendBox(params).then(
              response => {
                if(response.code == 200){
                  this.$message({
                    message: response.msg,
                    type: 'success'
                  });
                  this.sendBoxBaseInfo();
                }
              }
            ).catch(err=>{
              this.buttonLoading = null;
            });
          } else {
            return false;
          }
        });
      },
      /*日期处理*/
      dateFormat:function(row){
        if(row.createTime === undefined){
          return ''
        }
        return moment(row.createTime).format("YYYY-MM-DD")
      },
      /*删除附件*/
      deleteFile: function(row){
        this.$confirm('确认删除附件【' + row.fileName + '】吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          deleteViolFile({
            busiTableName:'T_COL_VIOL_BASE_SEND_BOX',
            busiId:this.dataDetails.id,
            id:row.id,
          }).then(response =>{
              if(response.code === 200){
                this.handleFileSuccess();
              }else{
                this.$message.error(response.msg);
              }
          })
        });
      },
      /*附件上传之前*/
      handlePreview: function(file){
        let sel = this;
        return new Promise((resolve, reject) => {
          // 控制附件大小
          if(file.size / 1024 / 1024 > 100){
            this.$message.error('附件大小不能超过 100MB!');
            reject();
          }
          if(!this.dataDetails || !this.dataDetails.id){
            getBaseSendBoxNull().then(
                response => {
                  sel.dataDetails = {
                    ...sel.dataDetails,
                    id: response.data.id,
                    uniqueCode: response.data.uniqueCode,
                    version: response.data.version,
                    commitFlag: "0",
                  };
                  sel.fileParams = {
                    ...sel.fileParams,
                    busiId: response.data.id,
                  };
                  resolve(true);
                }
              );
          }else{
            resolve(true);
          }
	      })
      },
      /**附件上传成功*/
      handleFileSuccess(){
        getBaseSendBoxNewVerson({uniqueCode: this.dataDetails.uniqueCode}).then(
          response =>{
            this.dataDetails = {
              ...this.dataDetails,
              id: response.data.id,
              files: response.data.files,
              version: response.data.version,
              commitFlag: response.data.version==-2 ? "0" : response.data.commitFlag,
            };
            this.fileParams = {
              ...this.fileParams,
              busiId: response.data.id,
            };
          }
        )
      },
      /** 修改附件表样式 */
      rowClass ({ row, column, rowIndex, columnIndex }) {
        if (columnIndex === 1 || columnIndex === 2 || columnIndex === 3) {
          return 'no-right-border'
        }else if(columnIndex === 0){
          return 'cell-color'
        }
      },
    }
  };
</script>

<style>
  @import "../common/common.css";

</style>
