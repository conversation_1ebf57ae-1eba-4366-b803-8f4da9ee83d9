<template>
  <i 
    :class="iconClass" 
    :style="iconStyle"
    @click="handleClick"
  ></i>
</template>

<script>
export default {
  name: 'IconFont',
  props: {
    // 图标名称（不需要包含icon-前缀）
    icon: {
      type: String,
      required: true
    },
    // 图标大小
    size: {
      type: [String, Number],
      default: ''
    },
    // 图标颜色
    color: {
      type: String,
      default: ''
    },
    // 是否可点击
    clickable: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    iconClass() {
      const classes = ['iconfont']
      
      // 添加图标类名，如果没有icon-前缀则自动添加
      const iconName = this.icon.startsWith('icon-') ? this.icon : `icon-${this.icon}`
      classes.push(iconName)
      
      // 如果可点击，添加指针样式
      if (this.clickable) {
        classes.push('icon-clickable')
      }
      
      return classes
    },
    iconStyle() {
      const style = {}
      
      // 设置大小
      if (this.size) {
        if (typeof this.size === 'number') {
          style.fontSize = `${this.size}px`
        } else {
          style.fontSize = this.size
        }
      }
      
      // 设置颜色
      if (this.color) {
        style.color = this.color
      }
      
      // 如果可点击，添加指针样式
      if (this.clickable) {
        style.cursor = 'pointer'
      }
      
      return style
    }
  },
  methods: {
    handleClick(event) {
      if (this.clickable) {
        this.$emit('click', event)
      }
    }
  }
}
</script>

<style scoped>
.icon-clickable {
  cursor: pointer;
  transition: opacity 0.2s;
}

.icon-clickable:hover {
  opacity: 0.7;
}

.icon-clickable:active {
  opacity: 0.5;
}
</style>
