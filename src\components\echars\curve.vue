<template>
  <div
    :id="id"
    ref="chart"
    :class="className"
    :style="{ height: height, width: width }"
  />
</template>
<script>
import echarts from "echarts";
import { fontSizeEchars } from "./mixins/fontSizeEchars";
import resize from "./mixins/resize";
require("echarts/theme/macarons");

export default {
  mixins: [resize],
  props: {
    charsData: {
      type: null,
      default: null,
    },
    id: {
      type: String,
      default: "myChart",
    },
    className: {
      type: String,
      default: "chart",
    },
    width: {
      type: String,
      default: "100%",
    },
    height: {
      type: String,
      default: "100%",
    },
  },
  data() {
    return {
      chart: null,
    };
  },
  watch: {
    charsData: {
      handler(val, oldVal) {
        this.chart.clear();
        setTimeout(() => {
          this.initChart();
        }, 1000);
      },
      deep: true,
    },
  },
  mounted() {
    this.initChart();
  },
  beforeDestroy() {
    if (!this.chart) {
      return;
    }
    this.chart.clear();
    this.chart.dispose();
    this.chart = null;
  },
  methods: {
    initChart() {
      var color = ["#8DA6FC", "#FC8586"];
      var hexToRgba = function (hex, opacity) {
        let rgbaColor = "";
        let reg = /^#[\da-f]{6}$/i;
        if (reg.test(hex)) {
          rgbaColor =
            "rgba(" +
            parseInt("0x" + hex.slice(1, 3)) +
            "," +
            parseInt("0x" + hex.slice(3, 5)) +
            "," +
            parseInt("0x" + hex.slice(5, 7)) +
            "," +
            opacity +
            ")";
        }
        return rgbaColor;
      };

  
      this.chart = echarts.init(this.$refs.chart, "macarons");
      this.chart.setOption(
        {
          backgroundColor: "#fff",
          color: color,
          legend: {
            top: "0",
            icon: "roundRect",
            right: "5%", //left:"10%"  // // 组件离容器的距离
            width: fontSizeEchars(0.24),
            height: fontSizeEchars(0.15),
            orient: "vertical",
            textStyle: {
              color: "#73777A", // 文字的颜色。
              fontSize: fontSizeEchars(0.12), // 文字的字体大小。
            },
          },
          tooltip: {
            trigger: "axis",
          },
          grid: {
            top: 50,
            bottom: 20,
            left: 40,
            right: 40,
            containLabel: true,
          },
          xAxis: [
            {
              type: "category",
              boundaryGap: true,
              axisLabel: {
                formatter: "{value}",
                textStyle: {
                  color: "#73777A",
                  fontSize: fontSizeEchars(0.14),
                },
              },
              axisLine: {
                lineStyle: {
                  color: "#D9D9D9",
                },
              },

              
              data: this.charsData.problemYear,
            },
          ],
          yAxis: [
            {
              type: "value",
              name: "•",
              axisLabel: {
                textStyle: {
                  color: "#73777A",
                  fontSize: fontSizeEchars(0.14),
                },
              },
              nameTextStyle: {
                color: "#8DA6FC",
                fontSize: fontSizeEchars(0.4),
                lineHeight: 40,
                padding: [0, 40, -15, 0],
              },
              // 分割线
              splitLine: {
                lineStyle: {
                  type: "solid",
                  color: "#FFF9F9",
                },
              },
              axisLine: {
                show: false,
              },
              axisTick: {
                show: false,
              },
            },
            {
              type: "value",
              name: "•",
              min: 0,
              boundaryGap: [0.2, 0.2],
              axisLabel: {
                textStyle: {
                  color: "#73777A",
                  fontSize: fontSizeEchars(0.14),
                },
              },
              nameTextStyle: {
                color: "#FC8586",
                fontSize: fontSizeEchars(0.4),
                lineHeight: 40,
                padding: [0, 0, -15, 40],
              },
              // 分割线
              splitLine: {
                lineStyle: {
                  type: "solid",
                  color: "#FFF9F9",
                },
              },
              axisLine: {
                show: false,
              },
              axisTick: {
                show: false,
              },
            },
          ],
          series: [
            {
              name: "问题数量",
              type: "line",
              smooth: true,
              symbolSize: 8,
              zlevel: 3,
              yAxisIndex: 0,
              lineStyle: {
                normal: {
                  color: color[0],
                },
              },
              areaStyle: {
                normal: {
                  color: new echarts.graphic.LinearGradient(
                    0,
                    0,
                    0,
                    1,
                    [
                      {
                        offset: 0,
                        color: hexToRgba(color[0], 0.4),
                      },
                      {
                        offset: 1,
                        color: hexToRgba(color[0], 0),
                      },
                    ],
                    false
                  ),
                  shadowColor: hexToRgba(color[0], 0.1),
                  shadowBlur: 10,
                },
              },
              data: this.charsData.problemNumber,
            },
            {
              name: "损失金额",
              type: "line",
              smooth: true,
              symbolSize: 8,
              zlevel: 3,
              yAxisIndex: 1,
              lineStyle: {
                normal: {
                  color: color[1],
                },
              },
              areaStyle: {
                normal: {
                  color: new echarts.graphic.LinearGradient(
                    0,
                    0,
                    0,
                    1,
                    [
                      {
                        offset: 0,
                        color: hexToRgba(color[1], 0.4),
                      },
                      {
                        offset: 1,
                        color: hexToRgba(color[1], 0),
                      },
                    ],
                    false
                  ),
                  shadowColor: hexToRgba(color[1], 0.1),
                  shadowBlur: 10,
                },
              },
              data: this.charsData.lossAmount,
            },
          ],
        },
        true
      );
    },
  },
};
</script>
<style lang="scss" scoped>
#myChart {
  width: 100%;
  height: 100%;
  div {
    width: 100%;
    height: 100%;
  }
}
</style>
