<template>
  <div class="app-container">
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-upload
          ref="upload"
          class="upload-demo"
          :action="files.actionUrl"
          :headers="files.myHeaders"
          :on-success="handleFileSuccess"
          :data="{}"
          :show-file-list="false"
        >
          <el-button
            size="small"
            type="primary"
            icon="el-icon-upload2"
            class="el-button-common"
            v-show="true"
          >操作手册上传
          </el-button>
        </el-upload>
      </el-col>
<!--      <el-col :span="1.5">-->
<!--        <el-button-->
<!--          type="success"-->
<!--          plain-->
<!--          icon="el-icon-edit"-->
<!--          size="mini"-->
<!--          :disabled="single"-->
<!--          @click="handleUpdate"-->
<!--          v-hasPermi="['system:manual:edit']"-->
<!--        >修改</el-button>-->
<!--      </el-col>-->
<!--      <el-col :span="1.5">-->
<!--        <el-button-->
<!--          type="danger"-->
<!--          plain-->
<!--          icon="el-icon-delete"-->
<!--          size="mini"-->
<!--          :disabled="multiple"-->
<!--          @click="handleDelete"-->
<!--          v-hasPermi="['system:manual:remove']"-->
<!--        >删除</el-button>-->
<!--      </el-col>-->
<!--      <el-col :span="1.5">-->
<!--        <el-button-->
<!--          type="warning"-->
<!--          plain-->
<!--          icon="el-icon-download"-->
<!--          size="mini"-->
<!--          @click="handleExport"-->
<!--          v-hasPermi="['system:manual:export']"-->
<!--        >导出</el-button>-->
<!--      </el-col>-->
<!--      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>-->
    </el-row>

    <el-table v-loading="loading" :data="manualList" >
      <el-table-column type="selection" width="55" align="center" />
<!--      <el-table-column label="主键" align="center" prop="id" />-->
      <el-table-column label="版本号" align="center" prop="version" />
      <el-table-column label="版本" align="center" prop="versionNum" />
      <el-table-column label="文件名称" align="center" prop="fileName" />
      <el-table-column label="创建人" align="center" prop="createBy" />
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="更新人" align="center" prop="updateBy" />
      <el-table-column label="更新时间" align="center" prop="updateTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.updateTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
<!--      <el-table-column label="状态" align="center" prop="deletedFlag" />-->
<!--      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">-->
<!--        <template slot-scope="scope">-->
<!--          <el-button-->
<!--            size="mini"-->
<!--            type="text"-->
<!--            icon="el-icon-edit"-->
<!--            @click="handleUpdate(scope.row)"-->
<!--            v-hasPermi="['system:manual:edit']"-->
<!--          >修改</el-button>-->
<!--          <el-button-->
<!--            size="mini"-->
<!--            type="text"-->
<!--            icon="el-icon-delete"-->
<!--            @click="handleDelete(scope.row)"-->
<!--            v-hasPermi="['system:manual:remove']"-->
<!--          >删除</el-button>-->
<!--        </template>-->
<!--      </el-table-column>-->
    </el-table>

<!--    <pagination-->
<!--      v-show="total>0"-->
<!--      :total="total"-->
<!--      :page.sync="queryParams.pageNum"-->
<!--      :limit.sync="queryParams.pageSize"-->
<!--      @pagination="getList"-->
<!--    />-->

  </div>
</template>

<script>
import { listManual } from "@/api/system/manual";
import { getToken } from "@/utils/auth";
export default {
  name: "Manual",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 操作手册表格数据
      manualList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      },
      files: {
        // busiTableId: "",
        // busiTableName: "t_col_viol_operate_manual",
        actionUrl: process.env.VUE_APP_BASE_API + "/system/manual/uploadFiledManual", // 上传地址
        myHeaders: { Authorization: "Bearer " + getToken() }, // 上传header
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    // 附件上传成功
    handleFileSuccess(res, file, fileList) {
      const loading = this.$loading({
        lock: true,//lock的修改符--默认是false
        text: '正在上传中',//显示在加载图标下方的加载文案
        background: 'transparent',
        spinner: 'el-icon-loading',//自定义加载图标类名
        target: document.querySelector('#table')//loadin覆盖的dom元素节点
      });
      if (res.code == 200) {
        this.getList()
        loading.close()
      } else {
        this.$message.error(res.msg);
        loading.close()
      }
    },
    /** 查询操作手册列表 */
    getList() {
      this.loading = true;
      listManual(this.queryParams).then(response => {
        this.manualList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加操作手册";
    },
  }
};
</script>
