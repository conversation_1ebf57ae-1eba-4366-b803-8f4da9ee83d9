<template>
    <div>
      <el-form class="common-card padding10_0"  size="medium" ref="elForm" label-width="80px">
        <el-row>
          <el-col :span="8">
            <el-form-item label="收件人"><span>{{formData && formData.receivePerson}}</span></el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="发件人">{{formData.sendPerson}}</el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="标题">{{formData.title}}</el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="发件内容">{{formData.mailContent}}</el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <BlockCard
        title="附件列表"
      >
        <el-table
          border
          :data="fileList"
          ref="table2"
          :height="height"
        >
          <el-table-column
            fixed
            align="center"
            label="序号"
            type="index"
            min-width="10%">
          </el-table-column>
          <el-table-column label="文档名称" prop="fileName" min-width="60%" show-overflow-tooltip/>
          <el-table-column label="上传人" prop="createLoginName"  min-width="13%"/>
          <el-table-column label="上传时间" prop="createTime" :formatter="dateFormat" min-width="15%"/>
          <el-table-column label="操作" prop="del" min-width="15%" fixed="right"
                           align="center"
          >
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                title="下载"
                icon="el-icon-bottom"
                @click="downloadFile(scope.row)"
              >
              </el-button>
              <!-- <el-button
                size="mini"
                type="text"
                icon="el-icon-search"
              >预览
              </el-button> -->
            </template>
          </el-table-column>
        </el-table>
      </BlockCard>
    </div>
</template>

<script>
  import {getReportSendBox, refreshReportSendBox,getReportedSendBox} from "@/api/sasac/reportManagement/edit/detail/index";
  import BlockCard from '@/components/BlockCard'
  import moment from "moment";

  export default {
    components:{BlockCard},
    name: "outbox",
    props: {
      problemId: {
        type: String
      },
      height:{
        type: String
      }
    },
    data() {
      return {
        formData: {},
        total: 0,
        visible:false,
        fileList:[],//附件列表
        hasSelectList:[],//选中的值
        params: {
          pageNum: 1,
          pageSize: 10,
        }
      }
    },
    created() {
      this.GetReportSendBox();
    },
    mounted() {
    },
    methods: {
      // 获取查询页数据
      GetReportSendBox() {
        //接口：/colligate/baseInfo/report/getReportSendBox
        getReportedSendBox({ reportId:this.problemId}).then(response => {
          if(response && response.code == 200){
            this.formData = response.data && response.data[0] && response.data[0];
            this.fileList = response.data && response.data[0] && response.data[0].files;
          }
        });
      },
      // 获取刷新数据
      onRefresh() {
        //接口：colligate/baseInfo/report/refreshReportSendBox
        refreshReportSendBox({ reportId:this.problemId}).then(response => {
          if(response && response.code == 200){
            this.formData = response.data && response.data[0] && response.data[0];
            this.fileList = response.data && response.data[0] && response.data[0].files;
          }
        });
      },
      /** 下载附件 */
      downloadFile(row) {
        this.download('/sys/documentTemplate/downloadByAttachmentId', { id: row.id }, row.fileName)
      },
      //附件列表关闭
      close(){
        this.visible=false;
      },
      /*日期处理*/
      dateFormat:function(row){
        if(row.createTime === undefined){
          return ''
        }
        return moment(row.createTime).format("YYYY-MM-DD")
      },
    }
  }
</script>

<style scoped>

</style>
