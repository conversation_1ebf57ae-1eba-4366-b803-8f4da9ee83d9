import request from '@/utils/request';

/**
 * 查询违规追责实时报送涉及单位
 * @param parameter
 */
export function queryActualInvolveUnit(parameter) {
  return request({
    url: '/colligate/violActualInvolveUnit/actualInvolveUnitData',
    method: 'post',
    data: parameter
  });
}

/**
 * 删除违规追责实时报送涉及单位
 * @param id
 */
export function deleteActualInvolveUnit(id) {
  return request({
    url: '/colligate/violActualInvolveUnit/deleteActualInvolveUnit/' + id,
    method: 'post'
  });
}

/**
 * 保存实时报送涉及单位数据
 * @param data
 */
export function saveActualInvolveUnitData(data) {
  return request({
    url: '/colligate/violActualInvolveUnit/saveActualInvolveUnitData',
    method: 'post',
    data: data
  });
}
