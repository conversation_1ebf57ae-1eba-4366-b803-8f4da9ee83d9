<template>
  <div>
    <el-dialog  title="修改记录" class="app-report" :visible.sync="visible" width="70%" append-to-body>
      <Jscrollbar height="70vh">
        <ul>
        <li  v-if="modifyDataList.businessFieldDifference" class="modify-record-li" v-for="(item,index) in modifyDataList.businessDifferenceFields">
          <div class="modify-record-label">
            <span class="question-li-ranking2 question-li-ranking">{{index+1}}</span>
            <span class="modify-record-label-name">{{item.fieldNameCn}}</span>
          </div>
          <div class="modify-record-content">
            <div class="modify-record-content-li ry-row">
              <span class="modify-record-content-span float-left">日常报送值</span>
              <p class="modify-record-content-p float-right">{{item.dailyValue}}</p>
            </div>
            <div class="modify-record-content-li ry-row">
              <span class="modify-record-content-span float-left">实时报送值</span>
              <p class="modify-record-content-p float-right">{{item.actualValue}}</p>
            </div>
          </div>
        </li>
        <!--涉及单位-->
        <li class="modify-record-li" v-if="modifyDataList.involveUnitDifference">
          <div class="modify-record-label">
            <span class="question-li-ranking2 question-li-ranking">{{modifyDataList.businessDifferenceFields.length+1}}</span>
            <span class="modify-record-label-name">涉及单位</span>
          </div>
          <div class="modify-record-content">
            <div class="modify-record-content-li ry-row">
              <span class="modify-record-content-span float-left">日常报送值</span>
              <p class="modify-record-content-p float-right">
                <span v-for="(item,index) in modifyDataList.dailyInvolveUnits">
                  {{item.mainFlag == '1'?'(主责单位)'+item.involCompanyName:item.involCompanyName}}{{(index+1)==modifyDataList.dailyInvolveUnits.length?'':'、'}}
                </span>
              </p>
            </div>
            <div class="modify-record-content-li ry-row">
              <span class="modify-record-content-span float-left">实时报送值</span>
              <p class="modify-record-content-p float-right">
                <span v-for="(item,index) in modifyDataList.actualInvolveUnits">
                  {{item.involveUnitName}}{{(index+1)==modifyDataList.actualInvolveUnits.length?'':'、'}}
                </span>
              </p>
            </div>
          </div>
        </li>
          <!--情形范围-->
          <li class="modify-record-li" v-if="modifyDataList.situationRangeDifference">
            <div class="modify-record-label">
              <span class="question-li-ranking2 question-li-ranking">{{modifyDataList.involveUnitDifference?modifyDataList.businessDifferenceFields.length+2:modifyDataList.businessDifferenceFields.length+1}}</span>
              <span class="modify-record-label-name">对应《违规经营投资责任追究办法》</span>
            </div>
            <div class="modify-record-content">
              <div class="modify-record-content-li ry-row">
                <span class="modify-record-content-span float-left">日常报送值</span>
                <p class="modify-record-content-p float-right">
                  <span v-for="(item,index) in modifyDataList.dailyRanges">
                   {{item.aspectName}} - {{item.situationName}}{{(index+1)==modifyDataList.dailyRanges.length?'':'、'}}
                  </span>
                </p>
              </div>
              <div class="modify-record-content-li ry-row">
                <span class="modify-record-content-span float-left">实时报送值</span>
                <p class="modify-record-content-p float-right">
                  <span v-for="(item,index) in modifyDataList.actualRanges">
                   {{item.aspectName}} - {{item.situationName}}{{(index+1)==modifyDataList.actualRanges.length?'':'、'}}
                  </span>
                </p>
              </div>
            </div>
          </li>
          </ul>
      </Jscrollbar>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" v-show="type" plain @click="save" icon="el-icon-tickets">保存</el-button>
        <el-button size="mini" @click="cancel" icon="el-icon-close" >关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import {saveActualCompareWithDailyData} from '@/api/actual/common/actualCompareResult';
  export default {
    name: "modifyRecord",
    data(){
      return{
        visible:false,
        modifyDataList:{},
      }
    },
    props: {
      type: {type: Boolean}
    },
    created(){

    },
    mounted(){

    },
    methods:{
      //弹出
      show(data){
        this.visible=true;
        this.modifyDataList=data;
      },
      //取消
      cancel(){
        this.visible=false;
      },
      //保存
      save(){
        saveActualCompareWithDailyData(this.modifyDataList).then(response => {
          if (200 === response.code) {
            this.visible=false;
            this.$emit('saveModify');
          } else {
            this.$modal.alertError(response.msg);
          }
        });
      }
    }
  }
</script>

<style scoped>
  .modify-label {
    height: 48px;
    font-size: 16px;
    line-height: 48px;
    color: #373d41;
  }

  .modify-record {
    width: 100%;
    overflow: auto;
    padding-right: 10px;
    box-sizing: border-box;
    height: calc(100% - 166px);
  }

  .modify-record-ul {
  }

  .modify-record-li {
  }

  .modify-record-label {
    line-height: 40px;
  }

  .modify-record-label-name {
    color: #A8ACB0;
  }

  .question-li-ranking {
    margin-right: 10px;
  }

  .modify-record-content {
    padding: 0 0 8px 0;
  }

  .modify-record-content-p {
    background: #F4F4F4;
    border-radius: 2px;
    padding: 10px;
    width: calc(100% - 84px);
    box-sizing: border-box;
    min-height: 37px;
  }

  .modify-record-content-li {
    margin-bottom: 20px;
  }

  .modify-record-content-span {
    width: 82px;
    padding: 8px 0;
  }

  .modify-record-content-direction {
    width: 100%;
    text-align: center;
    height: 32px;
    line-height: 32px;
  }

  .modify-record-content-direction img {
    transform: rotate(
      90deg
    );
  }

  .modify-history {
    height: calc(100% - 45px);
    padding: 20px 0;
    box-sizing: border-box;
    overflow: auto;
  }

  .modify-history-li {
    position: relative;
    width: 100%;
  }

  .modify-history-li:before {
    position: absolute;
    content: '';
    width: 1px;
    height: 100%;
    border-left: 1px dashed #d9d9d9;
    left: 158px;
    top: 18px;
  }

  .history-li-left {
    width: 140px;
    position: relative;
  }

  .history-li-left-name {
    font-size: 14px;
    line-height: 24px;
    text-align: right;
  }

  .history-li-left-time {
    font-size: 14px;
    line-height: 24px;
    color: #a9b0b4;
    text-align: right;
  }

  .history-li-left:before {
    position: absolute;
    content: '';
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: #ffffff;
    border: solid 2px #f5222d;
    right: -25px;
    top: 4px;
  }

  .history-li-right {
    width: calc(100% - 180px);
  }

  .history-li-title {
    font-size: 14px;
    line-height: 24px;
  }

  .history-li-user {
    width: 80px;
  }

  .history-li-text {
    padding-left: 10px;
    box-sizing: border-box;
    width: calc(100% - 80px);
  }

  .history-li-text span {
    color: #a9b0b4;
  }

  .history-li-content {
    border-radius: 2px;
    padding: 10px;
    box-sizing: border-box;
    margin: 0 10px 30px 0;
  }

  .question-li-ranking {
    width: 20px;
    background-color: #a9b0b4;
    height: 20px;
    line-height: 22px;
    color: #fff;
    display: inline-block;
    border-radius: 50%;
    text-align: center;
  }
</style>
