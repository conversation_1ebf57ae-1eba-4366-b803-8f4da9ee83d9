{"version": 3, "sources": ["webpack:///src/views/monitor/job/log.vue", "webpack:///./src/views/monitor/job/log.vue?9527", "webpack:///./src/api/monitor/job.js", "webpack:///./src/api/monitor/jobLog.js", "webpack:///./src/views/monitor/job/log.vue", "webpack:///./src/views/monitor/job/log.vue?e191", "webpack:///./src/views/monitor/job/log.vue?db8f"], "names": ["name", "dicts", "data", "loading", "ids", "multiple", "showSearch", "total", "jobLogList", "open", "date<PERSON><PERSON><PERSON>", "form", "queryParams", "pageNum", "pageSize", "job<PERSON>ame", "undefined", "jobGroup", "status", "created", "_this", "jobId", "$route", "query", "get<PERSON>ob", "then", "response", "getList", "methods", "_this2", "listJobLog", "addDateRange", "rows", "handleClose", "obj", "path", "$tab", "closeOpenPage", "handleQuery", "reset<PERSON><PERSON>y", "resetForm", "handleSelectionChange", "selection", "map", "item", "jobLogId", "length", "handleView", "row", "handleDelete", "_this3", "jobLogIds", "$modal", "confirm", "delJobLog", "msgSuccess", "catch", "handleClean", "_this4", "cleanJobLog", "handleExport", "download", "_objectSpread", "concat", "Date", "getTime", "listJob", "request", "url", "method", "params", "addJob", "updateJob", "<PERSON><PERSON><PERSON>", "changeJobStatus", "runJob"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0LA;AACA;AAEe;EACfA,IAAA;EACAC,KAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,GAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,UAAA;MACA;MACAC,IAAA;MACA;MACAC,SAAA;MACA;MACAC,IAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,OAAA,EAAAC,SAAA;QACAC,QAAA,EAAAD,SAAA;QACAE,MAAA,EAAAF;MACA;IACA;EACA;EACAG,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IACA,IAAAC,KAAA,QAAAC,MAAA,CAAAC,KAAA,CAAAF,KAAA;IACA,IAAAA,KAAA,KAAAL,SAAA,IAAAK,KAAA;MACAG,+DAAA,CAAAH,KAAA,EAAAI,IAAA,WAAAC,QAAA;QACAN,KAAA,CAAAR,WAAA,CAAAG,OAAA,GAAAW,QAAA,CAAAxB,IAAA,CAAAa,OAAA;QACAK,KAAA,CAAAR,WAAA,CAAAK,QAAA,GAAAS,QAAA,CAAAxB,IAAA,CAAAe,QAAA;QACAG,KAAA,CAAAO,OAAA;MACA;IACA;MACA,KAAAA,OAAA;IACA;EACA;EACAC,OAAA;IACA,eACAD,OAAA,WAAAA,QAAA;MAAA,IAAAE,MAAA;MACA,KAAA1B,OAAA;MACA2B,sEAAA,MAAAC,YAAA,MAAAnB,WAAA,OAAAF,SAAA,GAAAe,IAAA,WAAAC,QAAA;QACAG,MAAA,CAAArB,UAAA,GAAAkB,QAAA,CAAAM,IAAA;QACAH,MAAA,CAAAtB,KAAA,GAAAmB,QAAA,CAAAnB,KAAA;QACAsB,MAAA,CAAA1B,OAAA;MACA,CACA;IACA;IACA;IACA8B,WAAA,WAAAA,YAAA;MACA,IAAAC,GAAA;QAAAC,IAAA;MAAA;MACA,KAAAC,IAAA,CAAAC,aAAA,CAAAH,GAAA;IACA;IACA,aACAI,WAAA,WAAAA,YAAA;MACA,KAAA1B,WAAA,CAAAC,OAAA;MACA,KAAAc,OAAA;IACA;IACA,aACAY,UAAA,WAAAA,WAAA;MACA,KAAA7B,SAAA;MACA,KAAA8B,SAAA;MACA,KAAAF,WAAA;IACA;IACA;IACAG,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAAtC,GAAA,GAAAsC,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAC,QAAA;MAAA;MACA,KAAAxC,QAAA,IAAAqC,SAAA,CAAAI,MAAA;IACA;IACA,aACAC,UAAA,WAAAA,WAAAC,GAAA;MACA,KAAAvC,IAAA;MACA,KAAAE,IAAA,GAAAqC,GAAA;IACA;IACA,aACAC,YAAA,WAAAA,aAAAD,GAAA;MAAA,IAAAE,MAAA;MACA,IAAAC,SAAA,QAAA/C,GAAA;MACA,KAAAgD,MAAA,CAAAC,OAAA,oBAAAF,SAAA,aAAA1B,IAAA;QACA,OAAA6B,qEAAA,CAAAH,SAAA;MACA,GAAA1B,IAAA;QACAyB,MAAA,CAAAvB,OAAA;QACAuB,MAAA,CAAAE,MAAA,CAAAG,UAAA;MACA,GAAAC,KAAA;IACA;IACA,aACAC,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MACA,KAAAN,MAAA,CAAAC,OAAA,qBAAA5B,IAAA;QACA,OAAAkC,uEAAA;MACA,GAAAlC,IAAA;QACAiC,MAAA,CAAA/B,OAAA;QACA+B,MAAA,CAAAN,MAAA,CAAAG,UAAA;MACA,GAAAC,KAAA;IACA;IACA,aACAI,YAAA,WAAAA,aAAA;MACA,KAAAC,QAAA,2BAAAC,mIAAA,KACA,KAAAlD,WAAA,UAAAmD,MAAA,CACA,IAAAC,IAAA,GAAAC,OAAA;IACA;EACA;AACA,CAAC,E;;;;;;;;;;;;ACzSD;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK,+BAA+B;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX,SAAS;AACT;AACA;AACA;AACA,aAAa,SAAS,iCAAiC,EAAE;AACzD;AACA;AACA,8BAA8B,iBAAiB;AAC/C;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB;AACnB,iBAAiB;AACjB;AACA;AACA;AACA;AACA,mBAAmB;AACnB;AACA,iBAAiB;AACjB,eAAe;AACf;AACA;AACA;AACA;AACA;AACA,aAAa,SAAS,kCAAkC,EAAE;AAC1D;AACA;AACA;AACA;AACA,gCAAgC,iBAAiB;AACjD;AACA;AACA;AACA;AACA,mBAAmB;AACnB;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA,mBAAmB;AACnB,iBAAiB;AACjB;AACA;AACA;AACA,4BAA4B,uCAAuC;AACnE,mBAAmB;AACnB,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa,SAAS,gCAAgC,EAAE;AACxD;AACA;AACA;AACA;AACA,gCAAgC,iBAAiB;AACjD;AACA;AACA;AACA;AACA,mBAAmB;AACnB;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA,mBAAmB;AACnB,iBAAiB;AACjB;AACA;AACA;AACA,4BAA4B,uCAAuC;AACnE,mBAAmB;AACnB,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa,SAAS,gBAAgB,EAAE;AACxC;AACA;AACA,8BAA8B,iBAAiB;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,mBAAmB;AACnB;AACA,iBAAiB;AACjB,eAAe;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB;AACnB,uBAAuB,yBAAyB;AAChD,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,0BAA0B,wCAAwC;AAClE,uBAAuB,wBAAwB;AAC/C,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,6BAA6B,aAAa,EAAE;AACrD;AACA;AACA;AACA,aAAa,SAAS,YAAY,EAAE;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB;AACnB,uBAAuB,0BAA0B;AACjD,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa,SAAS,YAAY,EAAE;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB;AACnB,uBAAuB,yBAAyB;AAChD,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa,SAAS,YAAY,EAAE;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB;AACnB,uBAAuB,0BAA0B;AACjD,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa,SAAS,YAAY,EAAE;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB;AACnB,uBAAuB,yBAAyB;AAChD,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,6BAA6B;AACjD;AACA;AACA;AACA,eAAe;AACf;AACA;AACA,eAAe;AACf;AACA,aAAa;AACb,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA,kBAAkB,uBAAuB;AACzC,eAAe,gDAAgD;AAC/D,SAAS;AACT;AACA;AACA,oBAAoB,kDAAkD;AACtE,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB;AACvB,qBAAqB;AACrB;AACA,iBAAiB;AACjB,eAAe;AACf;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb,WAAW;AACX;AACA,oBAAoB,iDAAiD;AACrE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB;AACvB,qBAAqB;AACrB;AACA,iBAAiB;AACjB,eAAe;AACf;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB,eAAe;AACf;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2BAA2B;AAC3B;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA,2BAA2B;AAC3B,yBAAyB;AACzB,uBAAuB;AACvB;AACA;AACA;AACA,iBAAiB;AACjB,eAAe;AACf;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,WAAW;AACX;AACA;AACA,WAAW;AACX;AACA,SAAS;AACT,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA,aAAa;AACb,WAAW;AACX,SAAS;AACT;AACA;AACA;AACA;AACA;AACA,sBAAsB,wDAAwD;AAC9E,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB,SAAS,WAAW,EAAE;AAC3C;AACA,0CAA0C,SAAS,iBAAiB,EAAE;AACtE;AACA;AACA,0CAA0C,SAAS,iBAAiB,EAAE;AACtE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB,SAAS,WAAW,EAAE;AAC3C;AACA,0CAA0C,SAAS,iBAAiB,EAAE;AACtE;AACA;AACA,0CAA0C,SAAS,iBAAiB,EAAE;AACtE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB,SAAS,WAAW,EAAE;AAC3C;AACA,0CAA0C,SAAS,iBAAiB,EAAE;AACtE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB,SAAS,WAAW,EAAE;AAC3C;AACA,0CAA0C,SAAS,iBAAiB,EAAE;AACtE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB,SAAS,WAAW,EAAE;AAC3C;AACA,0CAA0C,SAAS,iBAAiB,EAAE;AACtE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB,SAAS,WAAW,EAAE;AAC3C;AACA;AACA;AACA;AACA,6BAA6B,SAAS,iBAAiB,EAAE;AACzD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsB,iBAAiB;AACvC;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB,mBAAmB;AACnB,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACxmBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAqC;;AAErC;AACO,SAASC,OAAOA,CAAC3C,KAAK,EAAE;EAC7B,OAAO4C,8DAAO,CAAC;IACbC,GAAG,EAAE,mBAAmB;IACxBC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAE/C;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,MAAMA,CAACH,KAAK,EAAE;EAC5B,OAAO8C,8DAAO,CAAC;IACbC,GAAG,EAAE,eAAe,GAAG/C,KAAK;IAC5BgD,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,MAAMA,CAACrE,IAAI,EAAE;EAC3B,OAAOiE,8DAAO,CAAC;IACbC,GAAG,EAAE,cAAc;IACnBC,MAAM,EAAE,MAAM;IACdnE,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASsE,SAASA,CAACtE,IAAI,EAAE;EAC9B,OAAOiE,8DAAO,CAAC;IACbC,GAAG,EAAE,cAAc;IACnBC,MAAM,EAAE,KAAK;IACbnE,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASuE,MAAMA,CAACpD,KAAK,EAAE;EAC5B,OAAO8C,8DAAO,CAAC;IACbC,GAAG,EAAE,eAAe,GAAG/C,KAAK;IAC5BgD,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,eAAeA,CAACrD,KAAK,EAAEH,MAAM,EAAE;EAC7C,IAAMhB,IAAI,GAAG;IACXmB,KAAK,EAALA,KAAK;IACLH,MAAM,EAANA;EACF,CAAC;EACD,OAAOiD,8DAAO,CAAC;IACbC,GAAG,EAAE,2BAA2B;IAChCC,MAAM,EAAE,KAAK;IACbnE,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAGA;AACO,SAASyE,MAAMA,CAACtD,KAAK,EAAEJ,QAAQ,EAAE;EACtC,IAAMf,IAAI,GAAG;IACXmB,KAAK,EAALA,KAAK;IACLJ,QAAQ,EAARA;EACF,CAAC;EACD,OAAOkD,8DAAO,CAAC;IACbC,GAAG,EAAE,kBAAkB;IACvBC,MAAM,EAAE,KAAK;IACbnE,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ,C;;;;;;;;;;;;ACtEA;AAAA;AAAA;AAAA;AAAA;AAAqC;;AAErC;AACO,SAAS4B,UAAUA,CAACP,KAAK,EAAE;EAChC,OAAO4C,8DAAO,CAAC;IACbC,GAAG,EAAE,sBAAsB;IAC3BC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAE/C;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAAS+B,SAASA,CAACT,QAAQ,EAAE;EAClC,OAAOsB,8DAAO,CAAC;IACbC,GAAG,EAAE,kBAAkB,GAAGvB,QAAQ;IAClCwB,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASV,WAAWA,CAAA,EAAG;EAC5B,OAAOQ,8DAAO,CAAC;IACbC,GAAG,EAAE,uBAAuB;IAC5BC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ,C;;;;;;;;;;;;ACzBA;AAAA;AAAA;AAAA;AAAkF;AAC3B;AACL;;;AAGlD;AACgG;AAChG,gBAAgB,2GAAU;AAC1B,EAAE,yEAAM;AACR,EAAE,8EAAM;AACR,EAAE,uFAAe;AACjB;AACA;AACA;AACA;;AAEA;;AAEA;AACA,IAAI,KAAU,EAAE,YAiBf;AACD;AACe,gF;;;;;;;;;;;;ACtCf;AAAA;AAAA,wCAAwS,CAAgB,oUAAG,EAAC,C;;;;;;;;;;;;ACA5T;AAAA;AAAA;AAAA;AAAA;AAAA", "file": "js/19.1693388085916.js", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\r\n      <el-form-item label=\"任务名称\" prop=\"jobName\">\r\n        <el-input\r\n          v-model=\"queryParams.jobName\"\r\n          placeholder=\"请输入任务名称\"\r\n          clearable\r\n          size=\"small\"\r\n          style=\"width: 240px\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"任务组名\" prop=\"jobGroup\">\r\n        <el-select\r\n          v-model=\"queryParams.jobGroup\"\r\n          placeholder=\"请任务组名\"\r\n          clearable\r\n          size=\"small\"\r\n          style=\"width: 240px\"\r\n        >\r\n          <el-option\r\n            v-for=\"dict in dict.type.sys_job_group\"\r\n            :key=\"dict.value\"\r\n            :label=\"dict.label\"\r\n            :value=\"dict.value\"\r\n          />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"执行状态\" prop=\"status\">\r\n        <el-select\r\n          v-model=\"queryParams.status\"\r\n          placeholder=\"请选择执行状态\"\r\n          clearable\r\n          size=\"small\"\r\n          style=\"width: 240px\"\r\n        >\r\n          <el-option\r\n            v-for=\"dict in dict.type.sys_common_status\"\r\n            :key=\"dict.value\"\r\n            :label=\"dict.label\"\r\n            :value=\"dict.value\"\r\n          />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"执行时间\">\r\n        <el-date-picker\r\n          v-model=\"dateRange\"\r\n          size=\"small\"\r\n          style=\"width: 240px\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          type=\"daterange\"\r\n          range-separator=\"-\"\r\n          start-placeholder=\"开始日期\"\r\n          end-placeholder=\"结束日期\"\r\n        ></el-date-picker>\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"danger\"\r\n          plain\r\n          icon=\"el-icon-delete\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handleDelete\"\r\n          v-hasPermi=\"['monitor:job:remove']\"\r\n        >删除</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"danger\"\r\n          plain\r\n          icon=\"el-icon-delete\"\r\n          size=\"mini\"\r\n          @click=\"handleClean\"\r\n          v-hasPermi=\"['monitor:job:remove']\"\r\n        >清空</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"warning\"\r\n          plain\r\n          icon=\"el-icon-download\"\r\n          size=\"mini\"\r\n          @click=\"handleExport\"\r\n          v-hasPermi=\"['monitor:job:export']\"\r\n        >导出</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"warning\"\r\n          plain\r\n          icon=\"el-icon-close\"\r\n          size=\"mini\"\r\n          @click=\"handleClose\"\r\n        >关闭</el-button>\r\n      </el-col>\r\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n    </el-row>\r\n\r\n    <el-table v-loading=\"loading\" :data=\"jobLogList\" @selection-change=\"handleSelectionChange\">\r\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n      <el-table-column label=\"日志编号\" width=\"80\" align=\"center\" prop=\"jobLogId\" />\r\n      <el-table-column label=\"任务名称\" align=\"center\" prop=\"jobName\" :show-overflow-tooltip=\"true\" />\r\n      <el-table-column label=\"任务组名\" align=\"center\" prop=\"jobGroup\" :show-overflow-tooltip=\"true\">\r\n        <template slot-scope=\"scope\">\r\n          <dict-tag :options=\"dict.type.sys_job_group\" :value=\"scope.row.jobGroup\"/>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"调用目标字符串\" align=\"center\" prop=\"invokeTarget\" :show-overflow-tooltip=\"true\" />\r\n      <el-table-column label=\"日志信息\" align=\"center\" prop=\"jobMessage\" :show-overflow-tooltip=\"true\" />\r\n      <el-table-column label=\"执行状态\" align=\"center\" prop=\"status\">\r\n        <template slot-scope=\"scope\">\r\n          <dict-tag :options=\"dict.type.sys_common_status\" :value=\"scope.row.status\"/>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"执行时间\" align=\"center\" prop=\"createTime\" width=\"180\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.createTime) }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-view\"\r\n            @click=\"handleView(scope.row)\"\r\n            v-hasPermi=\"['monitor:job:query']\"\r\n          >详细</el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <pagination\r\n      v-show=\"total>0\"\r\n      :total=\"total\"\r\n      :page.sync=\"queryParams.pageNum\"\r\n      :limit.sync=\"queryParams.pageSize\"\r\n      @pagination=\"getList\"\r\n    />\r\n\r\n    <!-- 调度日志详细 -->\r\n    <el-dialog title=\"调度日志详细\" :visible.sync=\"open\" width=\"700px\" append-to-body>\r\n      <el-form ref=\"form\" :model=\"form\" label-width=\"100px\" size=\"mini\">\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"日志序号：\">{{ form.jobLogId }}</el-form-item>\r\n            <el-form-item label=\"任务名称：\">{{ form.jobName }}</el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"任务分组：\">{{ form.jobGroup }}</el-form-item>\r\n            <el-form-item label=\"执行时间：\">{{ form.createTime }}</el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"调用方法：\">{{ form.invokeTarget }}</el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"日志信息：\">{{ form.jobMessage }}</el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"执行状态：\">\r\n              <div v-if=\"form.status == 0\">正常</div>\r\n              <div v-else-if=\"form.status == 1\">失败</div>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"异常信息：\" v-if=\"form.status == 1\">{{ form.exceptionInfo }}</el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"open = false\">关 闭</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getJob} from \"@/api/monitor/job\";\r\nimport { listJobLog, delJobLog, cleanJobLog } from \"@/api/monitor/jobLog\";\r\n\r\nexport default {\r\n  name: \"JobLog\",\r\n  dicts: ['sys_common_status', 'sys_job_group'],\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 调度日志表格数据\r\n      jobLogList: [],\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 日期范围\r\n      dateRange: [],\r\n      // 表单参数\r\n      form: {},\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        jobName: undefined,\r\n        jobGroup: undefined,\r\n        status: undefined\r\n      }\r\n    };\r\n  },\r\n  created() {\r\n    const jobId = this.$route.query.jobId;\r\n    if (jobId !== undefined && jobId != 0) {\r\n      getJob(jobId).then(response => {\r\n        this.queryParams.jobName = response.data.jobName;\r\n        this.queryParams.jobGroup = response.data.jobGroup;\r\n        this.getList();\r\n      });\r\n    } else {\r\n      this.getList();\r\n    }\r\n  },\r\n  methods: {\r\n    /** 查询调度日志列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      listJobLog(this.addDateRange(this.queryParams, this.dateRange)).then(response => {\r\n          this.jobLogList = response.rows;\r\n          this.total = response.total;\r\n          this.loading = false;\r\n        }\r\n      );\r\n    },\r\n    // 返回按钮\r\n    handleClose() {\r\n      const obj = { path: \"/monitor/job\" };\r\n      this.$tab.closeOpenPage(obj);\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.dateRange = [];\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.jobLogId);\r\n      this.multiple = !selection.length;\r\n    },\r\n    /** 详细按钮操作 */\r\n    handleView(row) {\r\n      this.open = true;\r\n      this.form = row;\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const jobLogIds = this.ids;\r\n      this.$modal.confirm('是否确认删除调度日志编号为\"' + jobLogIds + '\"的数据项？').then(function() {\r\n        return delJobLog(jobLogIds);\r\n      }).then(() => {\r\n        this.getList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n      }).catch(() => {});\r\n    },\r\n    /** 清空按钮操作 */\r\n    handleClean() {\r\n      this.$modal.confirm('是否确认清空所有调度日志数据项？').then(function() {\r\n        return cleanJobLog();\r\n      }).then(() => {\r\n        this.getList();\r\n        this.$modal.msgSuccess(\"清空成功\");\r\n      }).catch(() => {});\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download('/monitor/jobLog/export', {\r\n        ...this.queryParams\r\n      }, `log_${new Date().getTime()}.xlsx`)\r\n    }\r\n  }\r\n};\r\n</script>\r\n", "var render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return _c(\n    \"div\",\n    { staticClass: \"app-container\" },\n    [\n      _c(\n        \"el-form\",\n        {\n          directives: [\n            {\n              name: \"show\",\n              rawName: \"v-show\",\n              value: _vm.showSearch,\n              expression: \"showSearch\",\n            },\n          ],\n          ref: \"queryForm\",\n          attrs: {\n            model: _vm.queryParams,\n            inline: true,\n            \"label-width\": \"68px\",\n          },\n        },\n        [\n          _c(\n            \"el-form-item\",\n            { attrs: { label: \"任务名称\", prop: \"jobName\" } },\n            [\n              _c(\"el-input\", {\n                staticStyle: { width: \"240px\" },\n                attrs: {\n                  placeholder: \"请输入任务名称\",\n                  clearable: \"\",\n                  size: \"small\",\n                },\n                nativeOn: {\n                  keyup: function ($event) {\n                    if (\n                      !$event.type.indexOf(\"key\") &&\n                      _vm._k($event.keyCode, \"enter\", 13, $event.key, \"Enter\")\n                    ) {\n                      return null\n                    }\n                    return _vm.handleQuery($event)\n                  },\n                },\n                model: {\n                  value: _vm.queryParams.jobName,\n                  callback: function ($$v) {\n                    _vm.$set(_vm.queryParams, \"jobName\", $$v)\n                  },\n                  expression: \"queryParams.jobName\",\n                },\n              }),\n            ],\n            1\n          ),\n          _c(\n            \"el-form-item\",\n            { attrs: { label: \"任务组名\", prop: \"jobGroup\" } },\n            [\n              _c(\n                \"el-select\",\n                {\n                  staticStyle: { width: \"240px\" },\n                  attrs: {\n                    placeholder: \"请任务组名\",\n                    clearable: \"\",\n                    size: \"small\",\n                  },\n                  model: {\n                    value: _vm.queryParams.jobGroup,\n                    callback: function ($$v) {\n                      _vm.$set(_vm.queryParams, \"jobGroup\", $$v)\n                    },\n                    expression: \"queryParams.jobGroup\",\n                  },\n                },\n                _vm._l(_vm.dict.type.sys_job_group, function (dict) {\n                  return _c(\"el-option\", {\n                    key: dict.value,\n                    attrs: { label: dict.label, value: dict.value },\n                  })\n                }),\n                1\n              ),\n            ],\n            1\n          ),\n          _c(\n            \"el-form-item\",\n            { attrs: { label: \"执行状态\", prop: \"status\" } },\n            [\n              _c(\n                \"el-select\",\n                {\n                  staticStyle: { width: \"240px\" },\n                  attrs: {\n                    placeholder: \"请选择执行状态\",\n                    clearable: \"\",\n                    size: \"small\",\n                  },\n                  model: {\n                    value: _vm.queryParams.status,\n                    callback: function ($$v) {\n                      _vm.$set(_vm.queryParams, \"status\", $$v)\n                    },\n                    expression: \"queryParams.status\",\n                  },\n                },\n                _vm._l(_vm.dict.type.sys_common_status, function (dict) {\n                  return _c(\"el-option\", {\n                    key: dict.value,\n                    attrs: { label: dict.label, value: dict.value },\n                  })\n                }),\n                1\n              ),\n            ],\n            1\n          ),\n          _c(\n            \"el-form-item\",\n            { attrs: { label: \"执行时间\" } },\n            [\n              _c(\"el-date-picker\", {\n                staticStyle: { width: \"240px\" },\n                attrs: {\n                  size: \"small\",\n                  \"value-format\": \"yyyy-MM-dd\",\n                  type: \"daterange\",\n                  \"range-separator\": \"-\",\n                  \"start-placeholder\": \"开始日期\",\n                  \"end-placeholder\": \"结束日期\",\n                },\n                model: {\n                  value: _vm.dateRange,\n                  callback: function ($$v) {\n                    _vm.dateRange = $$v\n                  },\n                  expression: \"dateRange\",\n                },\n              }),\n            ],\n            1\n          ),\n          _c(\n            \"el-form-item\",\n            [\n              _c(\n                \"el-button\",\n                {\n                  attrs: {\n                    type: \"primary\",\n                    icon: \"el-icon-search\",\n                    size: \"mini\",\n                  },\n                  on: { click: _vm.handleQuery },\n                },\n                [_vm._v(\"搜索\")]\n              ),\n              _c(\n                \"el-button\",\n                {\n                  attrs: { icon: \"el-icon-refresh\", size: \"mini\" },\n                  on: { click: _vm.resetQuery },\n                },\n                [_vm._v(\"重置\")]\n              ),\n            ],\n            1\n          ),\n        ],\n        1\n      ),\n      _c(\n        \"el-row\",\n        { staticClass: \"mb8\", attrs: { gutter: 10 } },\n        [\n          _c(\n            \"el-col\",\n            { attrs: { span: 1.5 } },\n            [\n              _c(\n                \"el-button\",\n                {\n                  directives: [\n                    {\n                      name: \"hasPermi\",\n                      rawName: \"v-hasPermi\",\n                      value: [\"monitor:job:remove\"],\n                      expression: \"['monitor:job:remove']\",\n                    },\n                  ],\n                  attrs: {\n                    type: \"danger\",\n                    plain: \"\",\n                    icon: \"el-icon-delete\",\n                    size: \"mini\",\n                    disabled: _vm.multiple,\n                  },\n                  on: { click: _vm.handleDelete },\n                },\n                [_vm._v(\"删除\")]\n              ),\n            ],\n            1\n          ),\n          _c(\n            \"el-col\",\n            { attrs: { span: 1.5 } },\n            [\n              _c(\n                \"el-button\",\n                {\n                  directives: [\n                    {\n                      name: \"hasPermi\",\n                      rawName: \"v-hasPermi\",\n                      value: [\"monitor:job:remove\"],\n                      expression: \"['monitor:job:remove']\",\n                    },\n                  ],\n                  attrs: {\n                    type: \"danger\",\n                    plain: \"\",\n                    icon: \"el-icon-delete\",\n                    size: \"mini\",\n                  },\n                  on: { click: _vm.handleClean },\n                },\n                [_vm._v(\"清空\")]\n              ),\n            ],\n            1\n          ),\n          _c(\n            \"el-col\",\n            { attrs: { span: 1.5 } },\n            [\n              _c(\n                \"el-button\",\n                {\n                  directives: [\n                    {\n                      name: \"hasPermi\",\n                      rawName: \"v-hasPermi\",\n                      value: [\"monitor:job:export\"],\n                      expression: \"['monitor:job:export']\",\n                    },\n                  ],\n                  attrs: {\n                    type: \"warning\",\n                    plain: \"\",\n                    icon: \"el-icon-download\",\n                    size: \"mini\",\n                  },\n                  on: { click: _vm.handleExport },\n                },\n                [_vm._v(\"导出\")]\n              ),\n            ],\n            1\n          ),\n          _c(\n            \"el-col\",\n            { attrs: { span: 1.5 } },\n            [\n              _c(\n                \"el-button\",\n                {\n                  attrs: {\n                    type: \"warning\",\n                    plain: \"\",\n                    icon: \"el-icon-close\",\n                    size: \"mini\",\n                  },\n                  on: { click: _vm.handleClose },\n                },\n                [_vm._v(\"关闭\")]\n              ),\n            ],\n            1\n          ),\n          _c(\"right-toolbar\", {\n            attrs: { showSearch: _vm.showSearch },\n            on: {\n              \"update:showSearch\": function ($event) {\n                _vm.showSearch = $event\n              },\n              \"update:show-search\": function ($event) {\n                _vm.showSearch = $event\n              },\n              queryTable: _vm.getList,\n            },\n          }),\n        ],\n        1\n      ),\n      _c(\n        \"el-table\",\n        {\n          directives: [\n            {\n              name: \"loading\",\n              rawName: \"v-loading\",\n              value: _vm.loading,\n              expression: \"loading\",\n            },\n          ],\n          attrs: { data: _vm.jobLogList },\n          on: { \"selection-change\": _vm.handleSelectionChange },\n        },\n        [\n          _c(\"el-table-column\", {\n            attrs: { type: \"selection\", width: \"55\", align: \"center\" },\n          }),\n          _c(\"el-table-column\", {\n            attrs: {\n              label: \"日志编号\",\n              width: \"80\",\n              align: \"center\",\n              prop: \"jobLogId\",\n            },\n          }),\n          _c(\"el-table-column\", {\n            attrs: {\n              label: \"任务名称\",\n              align: \"center\",\n              prop: \"jobName\",\n              \"show-overflow-tooltip\": true,\n            },\n          }),\n          _c(\"el-table-column\", {\n            attrs: {\n              label: \"任务组名\",\n              align: \"center\",\n              prop: \"jobGroup\",\n              \"show-overflow-tooltip\": true,\n            },\n            scopedSlots: _vm._u([\n              {\n                key: \"default\",\n                fn: function (scope) {\n                  return [\n                    _c(\"dict-tag\", {\n                      attrs: {\n                        options: _vm.dict.type.sys_job_group,\n                        value: scope.row.jobGroup,\n                      },\n                    }),\n                  ]\n                },\n              },\n            ]),\n          }),\n          _c(\"el-table-column\", {\n            attrs: {\n              label: \"调用目标字符串\",\n              align: \"center\",\n              prop: \"invokeTarget\",\n              \"show-overflow-tooltip\": true,\n            },\n          }),\n          _c(\"el-table-column\", {\n            attrs: {\n              label: \"日志信息\",\n              align: \"center\",\n              prop: \"jobMessage\",\n              \"show-overflow-tooltip\": true,\n            },\n          }),\n          _c(\"el-table-column\", {\n            attrs: { label: \"执行状态\", align: \"center\", prop: \"status\" },\n            scopedSlots: _vm._u([\n              {\n                key: \"default\",\n                fn: function (scope) {\n                  return [\n                    _c(\"dict-tag\", {\n                      attrs: {\n                        options: _vm.dict.type.sys_common_status,\n                        value: scope.row.status,\n                      },\n                    }),\n                  ]\n                },\n              },\n            ]),\n          }),\n          _c(\"el-table-column\", {\n            attrs: {\n              label: \"执行时间\",\n              align: \"center\",\n              prop: \"createTime\",\n              width: \"180\",\n            },\n            scopedSlots: _vm._u([\n              {\n                key: \"default\",\n                fn: function (scope) {\n                  return [\n                    _c(\"span\", [\n                      _vm._v(_vm._s(_vm.parseTime(scope.row.createTime))),\n                    ]),\n                  ]\n                },\n              },\n            ]),\n          }),\n          _c(\"el-table-column\", {\n            attrs: {\n              label: \"操作\",\n              align: \"center\",\n              \"class-name\": \"small-padding fixed-width\",\n            },\n            scopedSlots: _vm._u([\n              {\n                key: \"default\",\n                fn: function (scope) {\n                  return [\n                    _c(\n                      \"el-button\",\n                      {\n                        directives: [\n                          {\n                            name: \"hasPermi\",\n                            rawName: \"v-hasPermi\",\n                            value: [\"monitor:job:query\"],\n                            expression: \"['monitor:job:query']\",\n                          },\n                        ],\n                        attrs: {\n                          size: \"mini\",\n                          type: \"text\",\n                          icon: \"el-icon-view\",\n                        },\n                        on: {\n                          click: function ($event) {\n                            return _vm.handleView(scope.row)\n                          },\n                        },\n                      },\n                      [_vm._v(\"详细\")]\n                    ),\n                  ]\n                },\n              },\n            ]),\n          }),\n        ],\n        1\n      ),\n      _c(\"pagination\", {\n        directives: [\n          {\n            name: \"show\",\n            rawName: \"v-show\",\n            value: _vm.total > 0,\n            expression: \"total>0\",\n          },\n        ],\n        attrs: {\n          total: _vm.total,\n          page: _vm.queryParams.pageNum,\n          limit: _vm.queryParams.pageSize,\n        },\n        on: {\n          \"update:page\": function ($event) {\n            return _vm.$set(_vm.queryParams, \"pageNum\", $event)\n          },\n          \"update:limit\": function ($event) {\n            return _vm.$set(_vm.queryParams, \"pageSize\", $event)\n          },\n          pagination: _vm.getList,\n        },\n      }),\n      _c(\n        \"el-dialog\",\n        {\n          attrs: {\n            title: \"调度日志详细\",\n            visible: _vm.open,\n            width: \"700px\",\n            \"append-to-body\": \"\",\n          },\n          on: {\n            \"update:visible\": function ($event) {\n              _vm.open = $event\n            },\n          },\n        },\n        [\n          _c(\n            \"el-form\",\n            {\n              ref: \"form\",\n              attrs: { model: _vm.form, \"label-width\": \"100px\", size: \"mini\" },\n            },\n            [\n              _c(\n                \"el-row\",\n                [\n                  _c(\n                    \"el-col\",\n                    { attrs: { span: 12 } },\n                    [\n                      _c(\"el-form-item\", { attrs: { label: \"日志序号：\" } }, [\n                        _vm._v(_vm._s(_vm.form.jobLogId)),\n                      ]),\n                      _c(\"el-form-item\", { attrs: { label: \"任务名称：\" } }, [\n                        _vm._v(_vm._s(_vm.form.jobName)),\n                      ]),\n                    ],\n                    1\n                  ),\n                  _c(\n                    \"el-col\",\n                    { attrs: { span: 12 } },\n                    [\n                      _c(\"el-form-item\", { attrs: { label: \"任务分组：\" } }, [\n                        _vm._v(_vm._s(_vm.form.jobGroup)),\n                      ]),\n                      _c(\"el-form-item\", { attrs: { label: \"执行时间：\" } }, [\n                        _vm._v(_vm._s(_vm.form.createTime)),\n                      ]),\n                    ],\n                    1\n                  ),\n                  _c(\n                    \"el-col\",\n                    { attrs: { span: 24 } },\n                    [\n                      _c(\"el-form-item\", { attrs: { label: \"调用方法：\" } }, [\n                        _vm._v(_vm._s(_vm.form.invokeTarget)),\n                      ]),\n                    ],\n                    1\n                  ),\n                  _c(\n                    \"el-col\",\n                    { attrs: { span: 24 } },\n                    [\n                      _c(\"el-form-item\", { attrs: { label: \"日志信息：\" } }, [\n                        _vm._v(_vm._s(_vm.form.jobMessage)),\n                      ]),\n                    ],\n                    1\n                  ),\n                  _c(\n                    \"el-col\",\n                    { attrs: { span: 24 } },\n                    [\n                      _c(\"el-form-item\", { attrs: { label: \"执行状态：\" } }, [\n                        _vm.form.status == 0\n                          ? _c(\"div\", [_vm._v(\"正常\")])\n                          : _vm.form.status == 1\n                          ? _c(\"div\", [_vm._v(\"失败\")])\n                          : _vm._e(),\n                      ]),\n                    ],\n                    1\n                  ),\n                  _c(\n                    \"el-col\",\n                    { attrs: { span: 24 } },\n                    [\n                      _vm.form.status == 1\n                        ? _c(\n                            \"el-form-item\",\n                            { attrs: { label: \"异常信息：\" } },\n                            [_vm._v(_vm._s(_vm.form.exceptionInfo))]\n                          )\n                        : _vm._e(),\n                    ],\n                    1\n                  ),\n                ],\n                1\n              ),\n            ],\n            1\n          ),\n          _c(\n            \"div\",\n            {\n              staticClass: \"dialog-footer\",\n              attrs: { slot: \"footer\" },\n              slot: \"footer\",\n            },\n            [\n              _c(\n                \"el-button\",\n                {\n                  on: {\n                    click: function ($event) {\n                      _vm.open = false\n                    },\n                  },\n                },\n                [_vm._v(\"关 闭\")]\n              ),\n            ],\n            1\n          ),\n        ],\n        1\n      ),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }", "import request from '@/utils/request'\r\n\r\n// 查询定时任务调度列表\r\nexport function listJob(query) {\r\n  return request({\r\n    url: '/monitor/job/list',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询定时任务调度详细\r\nexport function getJob(jobId) {\r\n  return request({\r\n    url: '/monitor/job/' + jobId,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 新增定时任务调度\r\nexport function addJob(data) {\r\n  return request({\r\n    url: '/monitor/job',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 修改定时任务调度\r\nexport function updateJob(data) {\r\n  return request({\r\n    url: '/monitor/job',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 删除定时任务调度\r\nexport function delJob(jobId) {\r\n  return request({\r\n    url: '/monitor/job/' + jobId,\r\n    method: 'delete'\r\n  })\r\n}\r\n\r\n// 任务状态修改\r\nexport function changeJobStatus(jobId, status) {\r\n  const data = {\r\n    jobId,\r\n    status\r\n  }\r\n  return request({\r\n    url: '/monitor/job/changeStatus',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n\r\n// 定时任务立即执行一次\r\nexport function runJob(jobId, jobGroup) {\r\n  const data = {\r\n    jobId,\r\n    jobGroup\r\n  }\r\n  return request({\r\n    url: '/monitor/job/run',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}", "import request from '@/utils/request'\r\n\r\n// 查询调度日志列表\r\nexport function listJobLog(query) {\r\n  return request({\r\n    url: '/monitor/jobLog/list',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 删除调度日志\r\nexport function delJobLog(jobLogId) {\r\n  return request({\r\n    url: '/monitor/jobLog/' + jobLogId,\r\n    method: 'delete'\r\n  })\r\n}\r\n\r\n// 清空调度日志\r\nexport function cleanJobLog() {\r\n  return request({\r\n    url: '/monitor/jobLog/clean',\r\n    method: 'delete'\r\n  })\r\n}\r\n", "import { render, staticRenderFns } from \"./log.vue?vue&type=template&id=2709a093&\"\nimport script from \"./log.vue?vue&type=script&lang=js&\"\nexport * from \"./log.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\n/* hot reload */\nif (module.hot) {\n  var api = require(\"E:\\\\workspace\\\\info\\\\trigram-ui\\\\node_modules\\\\vue-hot-reload-api\\\\dist\\\\index.js\")\n  api.install(require('vue'))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!api.isRecorded('2709a093')) {\n      api.createRecord('2709a093', component.options)\n    } else {\n      api.reload('2709a093', component.options)\n    }\n    module.hot.accept(\"./log.vue?vue&type=template&id=2709a093&\", function () {\n      api.rerender('2709a093', {\n        render: render,\n        staticRenderFns: staticRenderFns\n      })\n    })\n  }\n}\ncomponent.options.__file = \"src/views/monitor/job/log.vue\"\nexport default component.exports", "import mod from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./log.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./log.vue?vue&type=script&lang=js&\"", "export * from \"-!../../../../node_modules/cache-loader/dist/cjs.js?{\\\"cacheDirectory\\\":\\\"node_modules/.cache/vue-loader\\\",\\\"cacheIdentifier\\\":\\\"f1aaaf00-vue-loader-template\\\"}!../../../../node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./log.vue?vue&type=template&id=2709a093&\""], "sourceRoot": ""}