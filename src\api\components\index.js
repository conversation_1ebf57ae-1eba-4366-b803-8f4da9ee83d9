import request from '@/utils/request'

// 人员树
export function userTree(query) {
  return request({
    url: '/system/user/userTree',
    method: 'post',
    data:query
  })
}

// 人员树
export function treeUrl(url,query) {
  return request({
    url:url,
    method: 'post',
    data:query
  })
}

// 附件类型
export function violationFileItems(query) {
  return request({
    url: '/colligate/violFile/violationFileItems',
    method: 'post',
    data:query
  })
}

// 附件删除
export function deleteViolFile(id) {
  return request({
    url: '/colligate/violFile/deleteViolFile/'+id,
    method: 'post'
  })
}

// 单位校验方法
export function checkInvolve(id) {
  return request({
    url: '/colligate/violDaily/checkInvolve/'+id,
    method: 'post'
  })
}

// 单位修改记录
export function generateInvolveItemModifyRecord(data) {
  return request({
    url: '/colligate/violDailyModifyRecord/generateInvolveItemModifyRecord',
    method: 'post',
    data:data
  })
}

//附件填充数据
export function generateHandlerFillTemplate(ajaxData) {
  return request({
    url: '/colligate/violRegularHandlerFill/generateHandlerFillTemplate',
    method: 'post'
    ,data:JSON.stringify(ajaxData)
  })
}

//实时报告生成带有内容的模板
export function generateTemplateWithContent(data) {
  return request({
    url: '/colligate/violFile/generateTemplateWithContent',
    method: 'post',
    data: data
  });
}

//精准查询
export function queryAccuratePersonList(data) {
  return request({
    url: '/colligate/supervisionInvolve/queryAccuratePersonList',
    method: 'post',
    data: data
  });
}

//保存精准查询的涉及人员
export function saveAccuratePersonInfo(data) {
  return request({
    url: '/colligate/supervisionInvolve/saveAccuratePersonInfo',
    method: 'post',
    data: data
  });
}

