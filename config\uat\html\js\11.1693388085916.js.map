{"version": 3, "sources": ["webpack:///src/views/tool/gen/basicInfoForm.vue", "webpack:///src/views/tool/gen/editTable.vue", "webpack:///src/views/tool/gen/genInfoForm.vue", "webpack:///./src/views/tool/gen/basicInfoForm.vue?2a7f", "webpack:///./src/views/tool/gen/editTable.vue?2b45", "webpack:///./src/views/tool/gen/genInfoForm.vue?9a98", "webpack:///./src/api/system/dict/type.js", "webpack:///./src/api/system/menu.js", "webpack:///./src/api/tool/gen.js", "webpack:///./src/views/tool/gen/basicInfoForm.vue", "webpack:///./src/views/tool/gen/basicInfoForm.vue?31d6", "webpack:///./src/views/tool/gen/basicInfoForm.vue?f0d9", "webpack:///./src/views/tool/gen/editTable.vue", "webpack:///./src/views/tool/gen/editTable.vue?cc38", "webpack:///./src/views/tool/gen/editTable.vue?e7af", "webpack:///./src/views/tool/gen/genInfoForm.vue", "webpack:///./src/views/tool/gen/genInfoForm.vue?8828", "webpack:///./src/views/tool/gen/genInfoForm.vue?1126"], "names": ["name", "props", "info", "type", "Object", "default", "data", "rules", "tableName", "required", "message", "trigger", "tableComment", "className", "function<PERSON><PERSON>or", "components", "basicInfoForm", "genInfoForm", "activeName", "tableHeight", "document", "documentElement", "scrollHeight", "tables", "columns", "dictOptions", "menus", "created", "_this", "tableId", "$route", "query", "getGenTable", "then", "res", "rows", "getDictOptionselect", "response", "getMenuTreeselect", "handleTree", "methods", "submitForm", "_this2", "basicForm", "$refs", "basicInfo", "genForm", "genInfo", "Promise", "all", "map", "getFormPromise", "validateResult", "every", "item", "genTable", "assign", "model", "params", "treeCode", "treeName", "treeParentCode", "parentMenuId", "updateGenTable", "$modal", "msgSuccess", "msg", "code", "close", "msgError", "form", "resolve", "validate", "obj", "path", "t", "Date", "now", "pageNum", "$tab", "closeOpenPage", "mounted", "_this3", "el", "dragTable", "$el", "querySelectorAll", "sortable", "Sortable", "create", "handle", "onEnd", "evt", "targetRow", "splice", "oldIndex", "newIndex", "index", "sort", "parseInt", "Treeselect", "Array", "subColumns", "tplCategory", "packageName", "moduleName", "businessName", "functionName", "watch", "infoSubTableName", "val", "setSubTableColumns", "normalizer", "node", "children", "length", "id", "menuId", "label", "menuName", "subSelectChange", "value", "subTableFkName", "tplSelectChange", "subTableName", "listType", "request", "url", "method", "getType", "dictId", "addType", "updateType", "delType", "refreshCache", "optionselect", "listMenu", "getMenu", "treeselect", "roleMenuTreeselect", "roleId", "addMenu", "updateMenu", "delMenu", "listTable", "listDbTable", "importTable", "previewTable", "delTable", "genCode", "synchDb"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiCe;EACfA,IAAA;EACAC,KAAA;IACAC,IAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,KAAA;QACAC,SAAA,GACA;UAAAC,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAC,YAAA,GACA;UAAAH,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAE,SAAA,GACA;UAAAJ,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAG,cAAA,GACA;UAAAL,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;IACA;EACA;AACA,CAAC,E;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACoED;AACA;AACA;AACA;AACA;AACA;AAEe;EACfX,IAAA;EACAe,UAAA;IACAC,aAAA,EAAAA,sDAAA;IACAC,WAAA,EAAAA;EACA;EACAX,IAAA,WAAAA,KAAA;IACA;MACA;MACAY,UAAA;MACA;MACAC,WAAA,EAAAC,QAAA,CAAAC,eAAA,CAAAC,YAAA;MACA;MACAC,MAAA;MACA;MACAC,OAAA;MACA;MACAC,WAAA;MACA;MACAC,KAAA;MACA;MACAxB,IAAA;IACA;EACA;EACAyB,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IACA,IAAAC,OAAA,QAAAC,MAAA,CAAAC,KAAA,SAAAD,MAAA,CAAAC,KAAA,CAAAF,OAAA;IACA,IAAAA,OAAA;MACA;MACAG,iEAAA,CAAAH,OAAA,EAAAI,IAAA,WAAAC,GAAA;QACAN,KAAA,CAAAJ,OAAA,GAAAU,GAAA,CAAA5B,IAAA,CAAA6B,IAAA;QACAP,KAAA,CAAA1B,IAAA,GAAAgC,GAAA,CAAA5B,IAAA,CAAAJ,IAAA;QACA0B,KAAA,CAAAL,MAAA,GAAAW,GAAA,CAAA5B,IAAA,CAAAiB,MAAA;MACA;MACA;MACAa,0EAAA,GAAAH,IAAA,WAAAI,QAAA;QACAT,KAAA,CAAAH,WAAA,GAAAY,QAAA,CAAA/B,IAAA;MACA;MACA;MACAgC,iEAAA,GAAAL,IAAA,WAAAI,QAAA;QACAT,KAAA,CAAAF,KAAA,GAAAE,KAAA,CAAAW,UAAA,CAAAF,QAAA,CAAA/B,IAAA;MACA;IACA;EACA;EACAkC,OAAA;IACA,WACAC,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,SAAA,QAAAC,KAAA,CAAAC,SAAA,CAAAD,KAAA,CAAA5B,aAAA;MACA,IAAA8B,OAAA,QAAAF,KAAA,CAAAG,OAAA,CAAAH,KAAA,CAAA3B,WAAA;MACA+B,OAAA,CAAAC,GAAA,EAAAN,SAAA,EAAAG,OAAA,EAAAI,GAAA,MAAAC,cAAA,GAAAlB,IAAA,WAAAC,GAAA;QACA,IAAAkB,cAAA,GAAAlB,GAAA,CAAAmB,KAAA,WAAAC,IAAA;UAAA,SAAAA,IAAA;QAAA;QACA,IAAAF,cAAA;UACA,IAAAG,QAAA,GAAAnD,MAAA,CAAAoD,MAAA,KAAAb,SAAA,CAAAc,KAAA,EAAAX,OAAA,CAAAW,KAAA;UACAF,QAAA,CAAA/B,OAAA,GAAAkB,MAAA,CAAAlB,OAAA;UACA+B,QAAA,CAAAG,MAAA;YACAC,QAAA,EAAAJ,QAAA,CAAAI,QAAA;YACAC,QAAA,EAAAL,QAAA,CAAAK,QAAA;YACAC,cAAA,EAAAN,QAAA,CAAAM,cAAA;YACAC,YAAA,EAAAP,QAAA,CAAAO;UACA;UACAC,oEAAA,CAAAR,QAAA,EAAAtB,IAAA,WAAAC,GAAA;YACAQ,MAAA,CAAAsB,MAAA,CAAAC,UAAA,CAAA/B,GAAA,CAAAgC,GAAA;YACA,IAAAhC,GAAA,CAAAiC,IAAA;cACAzB,MAAA,CAAA0B,KAAA;YACA;UACA;QACA;UACA1B,MAAA,CAAAsB,MAAA,CAAAK,QAAA;QACA;MACA;IACA;IACAlB,cAAA,WAAAA,eAAAmB,IAAA;MACA,WAAAtB,OAAA,WAAAuB,OAAA;QACAD,IAAA,CAAAE,QAAA,WAAAtC,GAAA;UACAqC,OAAA,CAAArC,GAAA;QACA;MACA;IACA;IACA,WACAkC,KAAA,WAAAA,MAAA;MACA,IAAAK,GAAA;QAAAC,IAAA;QAAA3C,KAAA;UAAA4C,CAAA,EAAAC,IAAA,CAAAC,GAAA;UAAAC,OAAA,OAAAhD,MAAA,CAAAC,KAAA,CAAA+C;QAAA;MAAA;MACA,KAAAC,IAAA,CAAAC,aAAA,CAAAP,GAAA;IACA;EACA;EACAQ,OAAA,WAAAA,QAAA;IAAA,IAAAC,MAAA;IACA,IAAAC,EAAA,QAAAvC,KAAA,CAAAwC,SAAA,CAAAC,GAAA,CAAAC,gBAAA;IACA,IAAAC,QAAA,GAAAC,mDAAA,CAAAC,MAAA,CAAAN,EAAA;MACAO,MAAA;MACAC,KAAA,WAAAA,MAAAC,GAAA;QACA,IAAAC,SAAA,GAAAX,MAAA,CAAA1D,OAAA,CAAAsE,MAAA,CAAAF,GAAA,CAAAG,QAAA;QACAb,MAAA,CAAA1D,OAAA,CAAAsE,MAAA,CAAAF,GAAA,CAAAI,QAAA,KAAAH,SAAA;QACA,SAAAI,KAAA,IAAAf,MAAA,CAAA1D,OAAA;UACA0D,MAAA,CAAA1D,OAAA,CAAAyE,KAAA,EAAAC,IAAA,GAAAC,QAAA,CAAAF,KAAA;QACA;MACA;IACA;EACA;AACA,CAAC,E;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACdD;AACyD;AAE1C;EACfjG,IAAA;EACAe,UAAA;IAAAqF,UAAA,EAAAA;EAAA;EACAnG,KAAA;IACAC,IAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IACAkB,MAAA;MACApB,IAAA,EAAAkG,KAAA;MACAhG,OAAA;IACA;IACAqB,KAAA;MACAvB,IAAA,EAAAkG,KAAA;MACAhG,OAAA;IACA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAgG,UAAA;MACA/F,KAAA;QACAgG,WAAA,GACA;UAAA9F,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACA6F,WAAA,GACA;UAAA/F,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACA8F,UAAA,GACA;UAAAhG,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACA+F,YAAA,GACA;UAAAjG,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAgG,YAAA,GACA;UAAAlG,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;IACA;EACA;EACAgB,OAAA,WAAAA,QAAA;EACAiF,KAAA;IACA,8BAAAC,iBAAAC,GAAA;MACA,KAAAC,kBAAA,CAAAD,GAAA;IACA;EACA;EACAtE,OAAA;IACA,eACAwE,UAAA,WAAAA,WAAAC,IAAA;MACA,IAAAA,IAAA,CAAAC,QAAA,KAAAD,IAAA,CAAAC,QAAA,CAAAC,MAAA;QACA,OAAAF,IAAA,CAAAC,QAAA;MACA;MACA;QACAE,EAAA,EAAAH,IAAA,CAAAI,MAAA;QACAC,KAAA,EAAAL,IAAA,CAAAM,QAAA;QACAL,QAAA,EAAAD,IAAA,CAAAC;MACA;IACA;IACA,cACAM,eAAA,WAAAA,gBAAAC,KAAA;MACA,KAAAvH,IAAA,CAAAwH,cAAA;IACA;IACA,eACAC,eAAA,WAAAA,gBAAAF,KAAA;MACA,IAAAA,KAAA;QACA,KAAAvH,IAAA,CAAA0H,YAAA;QACA,KAAA1H,IAAA,CAAAwH,cAAA;MACA;IACA;IACA,aACAX,kBAAA,WAAAA,mBAAAU,KAAA;MACA,SAAAnE,IAAA,SAAA/B,MAAA;QACA,IAAAvB,IAAA,QAAAuB,MAAA,CAAA+B,IAAA,EAAA9C,SAAA;QACA,IAAAiH,KAAA,KAAAzH,IAAA;UACA,KAAAsG,UAAA,QAAA/E,MAAA,CAAA+B,IAAA,EAAA9B,OAAA;UACA;QACA;MACA;IACA;EACA;AACA,CAAC,E;;;;;;;;;;;;AC1SD;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc,4DAA4D;AAC1E,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,aAAa,SAAS,WAAW,EAAE;AACnC;AACA;AACA;AACA,iBAAiB,SAAS,kCAAkC,EAAE;AAC9D;AACA;AACA,4BAA4B,yBAAyB;AACrD;AACA;AACA;AACA;AACA,uBAAuB;AACvB;AACA,qBAAqB;AACrB,mBAAmB;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa,SAAS,WAAW,EAAE;AACnC;AACA;AACA;AACA,iBAAiB,SAAS,qCAAqC,EAAE;AACjE;AACA;AACA,4BAA4B,qBAAqB;AACjD;AACA;AACA;AACA;AACA,uBAAuB;AACvB;AACA,qBAAqB;AACrB,mBAAmB;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa,SAAS,WAAW,EAAE;AACnC;AACA;AACA;AACA,iBAAiB,SAAS,oCAAoC,EAAE;AAChE;AACA;AACA,4BAA4B,qBAAqB;AACjD;AACA;AACA;AACA;AACA,uBAAuB;AACvB;AACA,qBAAqB;AACrB,mBAAmB;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa,SAAS,WAAW,EAAE;AACnC;AACA;AACA;AACA,iBAAiB,SAAS,sCAAsC,EAAE;AAClE;AACA;AACA,4BAA4B,qBAAqB;AACjD;AACA;AACA;AACA;AACA,uBAAuB;AACvB;AACA,qBAAqB;AACrB,mBAAmB;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa,SAAS,WAAW,EAAE;AACnC;AACA;AACA;AACA,iBAAiB,SAAS,8BAA8B,EAAE;AAC1D;AACA;AACA,4BAA4B,4BAA4B;AACxD;AACA;AACA;AACA;AACA,uBAAuB;AACvB;AACA,qBAAqB;AACrB,mBAAmB;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC9IA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA,WAAW;AACX,SAAS;AACT;AACA;AACA;AACA,aAAa,SAAS,+BAA+B,EAAE;AACvD;AACA;AACA;AACA,wBAAwB,iBAAiB;AACzC,eAAe;AACf;AACA;AACA;AACA;AACA;AACA,aAAa,SAAS,oCAAoC,EAAE;AAC5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB;AACnB,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB,mBAAmB;AACnB;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB,mBAAmB;AACnB;AACA,4BAA4B,oCAAoC;AAChE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iCAAiC;AACjC;AACA,+BAA+B;AAC/B,6BAA6B;AAC7B;AACA,yBAAyB;AACzB,uBAAuB;AACvB;AACA,mBAAmB;AACnB;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB,mBAAmB;AACnB;AACA,4BAA4B,sCAAsC;AAClE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mCAAmC;AACnC;AACA,iCAAiC;AACjC,+BAA+B;AAC/B;AACA;AACA,0CAA0C,+BAA+B;AACzE,iCAAiC;AACjC;AACA,0CAA0C,mCAAmC;AAC7E,iCAAiC;AACjC;AACA,0CAA0C,qCAAqC;AAC/E,iCAAiC;AACjC;AACA,0CAA0C,mCAAmC;AAC7E,iCAAiC;AACjC;AACA;AACA;AACA;AACA,mCAAmC;AACnC,iCAAiC;AACjC;AACA,0CAA0C,+BAA+B;AACzE,iCAAiC;AACjC;AACA;AACA;AACA;AACA,yBAAyB;AACzB,uBAAuB;AACvB;AACA,mBAAmB;AACnB;AACA,4BAA4B,sCAAsC;AAClE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iCAAiC;AACjC;AACA,+BAA+B;AAC/B,6BAA6B;AAC7B;AACA,yBAAyB;AACzB,uBAAuB;AACvB;AACA,mBAAmB;AACnB;AACA,4BAA4B,iCAAiC;AAC7D;AACA;AACA;AACA;AACA;AACA;AACA,sCAAsC,oBAAoB;AAC1D;AACA;AACA;AACA;AACA,iCAAiC;AACjC;AACA,+BAA+B;AAC/B,6BAA6B;AAC7B;AACA,yBAAyB;AACzB,uBAAuB;AACvB;AACA,mBAAmB;AACnB;AACA,4BAA4B,iCAAiC;AAC7D;AACA;AACA;AACA;AACA;AACA;AACA,sCAAsC,oBAAoB;AAC1D;AACA;AACA;AACA;AACA,iCAAiC;AACjC;AACA,+BAA+B;AAC/B,6BAA6B;AAC7B;AACA,yBAAyB;AACzB,uBAAuB;AACvB;AACA,mBAAmB;AACnB;AACA,4BAA4B,iCAAiC;AAC7D;AACA;AACA;AACA;AACA;AACA;AACA,sCAAsC,oBAAoB;AAC1D;AACA;AACA;AACA;AACA,iCAAiC;AACjC;AACA,+BAA+B;AAC/B,6BAA6B;AAC7B;AACA,yBAAyB;AACzB,uBAAuB;AACvB;AACA,mBAAmB;AACnB;AACA,4BAA4B,iCAAiC;AAC7D;AACA;AACA;AACA;AACA;AACA;AACA,sCAAsC,oBAAoB;AAC1D;AACA;AACA;AACA;AACA,iCAAiC;AACjC;AACA,+BAA+B;AAC/B,6BAA6B;AAC7B;AACA,yBAAyB;AACzB,uBAAuB;AACvB;AACA,mBAAmB;AACnB;AACA,4BAA4B,oCAAoC;AAChE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mCAAmC;AACnC;AACA,iCAAiC;AACjC,+BAA+B;AAC/B;AACA;AACA,0CAA0C,0BAA0B;AACpE,iCAAiC;AACjC;AACA,0CAA0C,2BAA2B;AACrE,iCAAiC;AACjC;AACA,0CAA0C,0BAA0B;AACpE,iCAAiC;AACjC;AACA,0CAA0C,4BAA4B;AACtE,iCAAiC;AACjC;AACA,0CAA0C,0BAA0B;AACpE,iCAAiC;AACjC;AACA,0CAA0C,4BAA4B;AACtE,iCAAiC;AACjC;AACA,0CAA0C,+BAA+B;AACzE,iCAAiC;AACjC;AACA,0CAA0C,qCAAqC;AAC/E,iCAAiC;AACjC;AACA;AACA;AACA;AACA,yBAAyB;AACzB,uBAAuB;AACvB;AACA,mBAAmB;AACnB;AACA,4BAA4B,iCAAiC;AAC7D;AACA;AACA;AACA;AACA;AACA;AACA,sCAAsC,oBAAoB;AAC1D;AACA;AACA;AACA;AACA,iCAAiC;AACjC;AACA,+BAA+B;AAC/B,6BAA6B;AAC7B;AACA,yBAAyB;AACzB,uBAAuB;AACvB;AACA,mBAAmB;AACnB;AACA,4BAA4B,oCAAoC;AAChE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mCAAmC;AACnC;AACA,iCAAiC;AACjC,+BAA+B;AAC/B;AACA;AACA,0CAA0C,+BAA+B;AACzE,iCAAiC;AACjC;AACA,0CAA0C,kCAAkC;AAC5E,iCAAiC;AACjC;AACA,0CAA0C,gCAAgC;AAC1E,iCAAiC;AACjC;AACA,0CAA0C,+BAA+B;AACzE,iCAAiC;AACjC;AACA,0CAA0C,kCAAkC;AAC5E,iCAAiC;AACjC;AACA;AACA;AACA;AACA,mCAAmC;AACnC,iCAAiC;AACjC;AACA;AACA;AACA;AACA,mCAAmC;AACnC,iCAAiC;AACjC;AACA;AACA;AACA;AACA,mCAAmC;AACnC,iCAAiC;AACjC;AACA;AACA;AACA;AACA,mCAAmC;AACnC,iCAAiC;AACjC;AACA;AACA;AACA;AACA,yBAAyB;AACzB,uBAAuB;AACvB;AACA,mBAAmB;AACnB;AACA,4BAA4B,oCAAoC;AAChE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iCAAiC;AACjC;AACA;AACA;AACA;AACA,mCAAmC;AACnC;AACA,iCAAiC;AACjC,+BAA+B;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qCAAqC;AACrC,mCAAmC;AACnC;AACA;AACA;AACA,uCAAuC,eAAe,gBAAgB,EAAE;AACxE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yCAAyC;AACzC,uCAAuC;AACvC;AACA;AACA;AACA;AACA,+BAA+B;AAC/B;AACA;AACA;AACA,yBAAyB;AACzB,uBAAuB;AACvB;AACA,mBAAmB;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa,SAAS,iCAAiC,EAAE;AACzD;AACA;AACA;AACA,wBAAwB,uDAAuD;AAC/E,eAAe;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,SAAS,yBAAyB,EAAE;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf,aAAa;AACb;AACA;AACA;AACA;AACA,0BAA0B,kBAAkB;AAC5C;AACA;AACA;AACA,qBAAqB;AACrB,mBAAmB;AACnB,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB,mBAAmB;AACnB,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AClgBA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc,4DAA4D;AAC1E,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,aAAa,SAAS,WAAW,EAAE;AACnC;AACA;AACA;AACA,iBAAiB,SAAS,sBAAsB,EAAE;AAClD;AACA,8BAA8B,SAAS,gBAAgB,iBAAiB;AACxE;AACA;AACA;AACA;AACA;AACA,2BAA2B,8BAA8B;AACzD;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA,uBAAuB;AACvB,qBAAqB;AACrB;AACA;AACA,gCAAgC,mCAAmC;AACnE,uBAAuB;AACvB;AACA,gCAAgC,mCAAmC;AACnE,uBAAuB;AACvB;AACA,gCAAgC,mCAAmC;AACnE,uBAAuB;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa,SAAS,WAAW,EAAE;AACnC;AACA;AACA;AACA,iBAAiB,SAAS,sBAAsB,EAAE;AAClD;AACA;AACA;AACA,qBAAqB,SAAS,gBAAgB,iBAAiB;AAC/D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2BAA2B;AAC3B,yBAAyB;AACzB,kCAAkC,kCAAkC;AACpE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB;AACvB;AACA,qBAAqB;AACrB,mBAAmB;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa,SAAS,WAAW,EAAE;AACnC;AACA;AACA;AACA,iBAAiB,SAAS,qBAAqB,EAAE;AACjD;AACA;AACA;AACA,qBAAqB,SAAS,gBAAgB,iBAAiB;AAC/D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2BAA2B;AAC3B,yBAAyB;AACzB,kCAAkC,kCAAkC;AACpE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB;AACvB;AACA,qBAAqB;AACrB,mBAAmB;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa,SAAS,WAAW,EAAE;AACnC;AACA;AACA;AACA,iBAAiB,SAAS,uBAAuB,EAAE;AACnD;AACA;AACA;AACA,qBAAqB,SAAS,gBAAgB,iBAAiB;AAC/D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2BAA2B;AAC3B,yBAAyB;AACzB,kCAAkC,kCAAkC;AACpE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB;AACvB;AACA,qBAAqB;AACrB,mBAAmB;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa,SAAS,WAAW,EAAE;AACnC;AACA;AACA;AACA,iBAAiB,SAAS,uBAAuB,EAAE;AACnD;AACA;AACA;AACA,qBAAqB,SAAS,gBAAgB,iBAAiB;AAC/D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2BAA2B;AAC3B,yBAAyB;AACzB,kCAAkC,kCAAkC;AACpE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB;AACvB;AACA,qBAAqB;AACrB,mBAAmB;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa,SAAS,WAAW,EAAE;AACnC;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB,SAAS,gBAAgB,iBAAiB;AAC/D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2BAA2B;AAC3B,yBAAyB;AACzB,kCAAkC,kCAAkC;AACpE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA,uBAAuB;AACvB;AACA,qBAAqB;AACrB,mBAAmB;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa,SAAS,WAAW,EAAE;AACnC;AACA;AACA;AACA,iBAAiB,SAAS,kBAAkB,EAAE;AAC9C;AACA;AACA;AACA,qBAAqB,SAAS,gBAAgB,iBAAiB;AAC/D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2BAA2B;AAC3B,yBAAyB;AACzB,kCAAkC,kCAAkC;AACpE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8BAA8B,aAAa;AAC3C;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA,uBAAuB;AACvB,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA,8BAA8B,aAAa;AAC3C;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA,uBAAuB;AACvB,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB,SAAS,WAAW,EAAE;AACvC;AACA;AACA;AACA,qBAAqB,SAAS,kBAAkB,EAAE;AAClD;AACA;AACA;AACA,yBAAyB,SAAS,gBAAgB,iBAAiB;AACnE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+BAA+B;AAC/B,6BAA6B;AAC7B,sCAAsC,kCAAkC;AACxE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6BAA6B;AAC7B;AACA,2BAA2B;AAC3B,yBAAyB;AACzB;AACA;AACA;AACA,6BAA6B,SAAS,iBAAiB,kBAAkB;AACzE;AACA,+CAA+C,SAAS,kBAAkB,EAAE;AAC5E;AACA;AACA;AACA;AACA,iCAAiC;AACjC;AACA;AACA;AACA;AACA,0CAA0C,mBAAmB;AAC7D;AACA,iCAAiC;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yCAAyC;AACzC,uCAAuC;AACvC,qCAAqC;AACrC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA,SAAS;AACT;AACA,oBAAoB,6BAA6B;AACjD;AACA;AACA,aAAa,SAAS,WAAW,EAAE;AACnC;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB,SAAS,gBAAgB,iBAAiB;AAC/D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2BAA2B;AAC3B,yBAAyB;AACzB,kCAAkC,kCAAkC;AACpE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8BAA8B,qBAAqB;AACnD;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA,uBAAuB;AACvB,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB,uBAAuB;AACvB,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa,SAAS,WAAW,EAAE;AACnC;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB,SAAS,gBAAgB,iBAAiB;AAC/D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2BAA2B;AAC3B,yBAAyB;AACzB,kCAAkC,kCAAkC;AACpE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8BAA8B,qBAAqB;AACnD;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA,uBAAuB;AACvB,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB,uBAAuB;AACvB,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa,SAAS,WAAW,EAAE;AACnC;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB,SAAS,gBAAgB,iBAAiB;AAC/D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2BAA2B;AAC3B,yBAAyB;AACzB,kCAAkC,kCAAkC;AACpE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8BAA8B,qBAAqB;AACnD;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA,uBAAuB;AACvB,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB,uBAAuB;AACvB,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA,SAAS;AACT;AACA,oBAAoB,6BAA6B;AACjD;AACA;AACA,aAAa,SAAS,WAAW,EAAE;AACnC;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB,SAAS,gBAAgB,iBAAiB;AAC/D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2BAA2B;AAC3B,yBAAyB;AACzB,kCAAkC,kCAAkC;AACpE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8BAA8B,qBAAqB;AACnD,2BAA2B,8BAA8B;AACzD;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA,uBAAuB;AACvB,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB,uBAAuB;AACvB,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa,SAAS,WAAW,EAAE;AACnC;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB,SAAS,gBAAgB,iBAAiB;AAC/D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2BAA2B;AAC3B,yBAAyB;AACzB,kCAAkC,kCAAkC;AACpE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8BAA8B,qBAAqB;AACnD;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA,uBAAuB;AACvB,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB,uBAAuB;AACvB,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC3tBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAqC;;AAErC;AACO,SAASqG,QAAQA,CAAC9F,KAAK,EAAE;EAC9B,OAAO+F,8DAAO,CAAC;IACbC,GAAG,EAAE,wBAAwB;IAC7BC,MAAM,EAAE,KAAK;IACbtE,MAAM,EAAE3B;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASkG,OAAOA,CAACC,MAAM,EAAE;EAC9B,OAAOJ,8DAAO,CAAC;IACbC,GAAG,EAAE,oBAAoB,GAAGG,MAAM;IAClCF,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASG,OAAOA,CAAC7H,IAAI,EAAE;EAC5B,OAAOwH,8DAAO,CAAC;IACbC,GAAG,EAAE,mBAAmB;IACxBC,MAAM,EAAE,MAAM;IACd1H,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAAS8H,UAAUA,CAAC9H,IAAI,EAAE;EAC/B,OAAOwH,8DAAO,CAAC;IACbC,GAAG,EAAE,mBAAmB;IACxBC,MAAM,EAAE,KAAK;IACb1H,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAAS+H,OAAOA,CAACH,MAAM,EAAE;EAC9B,OAAOJ,8DAAO,CAAC;IACbC,GAAG,EAAE,oBAAoB,GAAGG,MAAM;IAClCF,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASM,YAAYA,CAAA,EAAG;EAC7B,OAAOR,8DAAO,CAAC;IACbC,GAAG,EAAE,gCAAgC;IACrCC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASO,YAAYA,CAAA,EAAG;EAC7B,OAAOT,8DAAO,CAAC;IACbC,GAAG,EAAE,gCAAgC;IACrCC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ,C;;;;;;;;;;;;AC3DA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAqC;;AAErC;AACO,SAASQ,QAAQA,CAACzG,KAAK,EAAE;EAC9B,OAAO+F,8DAAO,CAAC;IACbC,GAAG,EAAE,mBAAmB;IACxBC,MAAM,EAAE,KAAK;IACbtE,MAAM,EAAE3B;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAAS0G,OAAOA,CAACpB,MAAM,EAAE;EAC9B,OAAOS,8DAAO,CAAC;IACbC,GAAG,EAAE,eAAe,GAAGV,MAAM;IAC7BW,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASU,UAAUA,CAAA,EAAG;EAC3B,OAAOZ,8DAAO,CAAC;IACbC,GAAG,EAAE,yBAAyB;IAC9BC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASW,kBAAkBA,CAACC,MAAM,EAAE;EACzC,OAAOd,8DAAO,CAAC;IACbC,GAAG,EAAE,kCAAkC,GAAGa,MAAM;IAChDZ,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASa,OAAOA,CAACvI,IAAI,EAAE;EAC5B,OAAOwH,8DAAO,CAAC;IACbC,GAAG,EAAE,cAAc;IACnBC,MAAM,EAAE,MAAM;IACd1H,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASwI,UAAUA,CAACxI,IAAI,EAAE;EAC/B,OAAOwH,8DAAO,CAAC;IACbC,GAAG,EAAE,cAAc;IACnBC,MAAM,EAAE,KAAK;IACb1H,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASyI,OAAOA,CAAC1B,MAAM,EAAE;EAC9B,OAAOS,8DAAO,CAAC;IACbC,GAAG,EAAE,eAAe,GAAGV,MAAM;IAC7BW,MAAM,EAAE;EACV,CAAC,CAAC;AACJ,C;;;;;;;;;;;;AC3DA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAqC;;AAErC;AACO,SAASgB,SAASA,CAACjH,KAAK,EAAE;EAC/B,OAAO+F,8DAAO,CAAC;IACbC,GAAG,EAAE,gBAAgB;IACrBC,MAAM,EAAE,KAAK;IACbtE,MAAM,EAAE3B;EACV,CAAC,CAAC;AACJ;AACA;AACO,SAASkH,WAAWA,CAAClH,KAAK,EAAE;EACjC,OAAO+F,8DAAO,CAAC;IACbC,GAAG,EAAE,mBAAmB;IACxBC,MAAM,EAAE,KAAK;IACbtE,MAAM,EAAE3B;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,WAAWA,CAACH,OAAO,EAAE;EACnC,OAAOiG,8DAAO,CAAC;IACbC,GAAG,EAAE,YAAY,GAAGlG,OAAO;IAC3BmG,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASjE,cAAcA,CAACzD,IAAI,EAAE;EACnC,OAAOwH,8DAAO,CAAC;IACbC,GAAG,EAAE,WAAW;IAChBC,MAAM,EAAE,KAAK;IACb1H,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAAS4I,WAAWA,CAAC5I,IAAI,EAAE;EAChC,OAAOwH,8DAAO,CAAC;IACbC,GAAG,EAAE,uBAAuB;IAC5BC,MAAM,EAAE,MAAM;IACdtE,MAAM,EAAEpD;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAAS6I,YAAYA,CAACtH,OAAO,EAAE;EACpC,OAAOiG,8DAAO,CAAC;IACbC,GAAG,EAAE,oBAAoB,GAAGlG,OAAO;IACnCmG,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASoB,QAAQA,CAACvH,OAAO,EAAE;EAChC,OAAOiG,8DAAO,CAAC;IACbC,GAAG,EAAE,YAAY,GAAGlG,OAAO;IAC3BmG,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASqB,OAAOA,CAAC7I,SAAS,EAAE;EACjC,OAAOsH,8DAAO,CAAC;IACbC,GAAG,EAAE,oBAAoB,GAAGvH,SAAS;IACrCwH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASsB,OAAOA,CAAC9I,SAAS,EAAE;EACjC,OAAOsH,8DAAO,CAAC;IACbC,GAAG,EAAE,oBAAoB,GAAGvH,SAAS;IACrCwH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ,C;;;;;;;;;;;;AC3EA;AAAA;AAAA;AAAA;AAA4F;AAC3B;AACL;;;AAG5D;AACgG;AAChG,gBAAgB,2GAAU;AAC1B,EAAE,mFAAM;AACR,EAAE,wFAAM;AACR,EAAE,iGAAe;AACjB;AACA;AACA;AACA;;AAEA;;AAEA;AACA,IAAI,KAAU,EAAE,YAiBf;AACD;AACe,gF;;;;;;;;;;;;ACtCf;AAAA;AAAA,wCAAkT,CAAgB,8UAAG,EAAC,C;;;;;;;;;;;;ACAtU;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAwF;AAC3B;AACL;;;AAGxD;AACgG;AAChG,gBAAgB,2GAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,oFAAM;AACR,EAAE,6FAAe;AACjB;AACA;AACA;AACA;;AAEA;;AAEA;AACA,IAAI,KAAU,EAAE,YAiBf;AACD;AACe,gF;;;;;;;;;;;;ACtCf;AAAA;AAAA,wCAA8S,CAAgB,0UAAG,EAAC,C;;;;;;;;;;;;ACAlU;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAA0F;AAC3B;AACL;;;AAG1D;AACgG;AAChG,gBAAgB,2GAAU;AAC1B,EAAE,iFAAM;AACR,EAAE,sFAAM;AACR,EAAE,+FAAe;AACjB;AACA;AACA;AACA;;AAEA;;AAEA;AACA,IAAI,KAAU,EAAE,YAiBf;AACD;AACe,gF;;;;;;;;;;;;ACtCf;AAAA;AAAA,wCAAgT,CAAgB,4UAAG,EAAC,C;;;;;;;;;;;;ACApU;AAAA;AAAA;AAAA;AAAA;AAAA", "file": "js/11.1693388085916.js", "sourcesContent": ["<template>\r\n  <el-form ref=\"basicInfoForm\" :model=\"info\" :rules=\"rules\" label-width=\"150px\">\r\n    <el-row>\r\n      <el-col :span=\"12\">\r\n        <el-form-item label=\"表名称\" prop=\"tableName\">\r\n          <el-input placeholder=\"请输入仓库名称\" v-model=\"info.tableName\" />\r\n        </el-form-item>\r\n      </el-col>\r\n      <el-col :span=\"12\">\r\n        <el-form-item label=\"表描述\" prop=\"tableComment\">\r\n          <el-input placeholder=\"请输入\" v-model=\"info.tableComment\" />\r\n        </el-form-item>\r\n      </el-col>\r\n\r\n      <el-col :span=\"12\">\r\n        <el-form-item label=\"实体类名称\" prop=\"className\">\r\n          <el-input placeholder=\"请输入\" v-model=\"info.className\" />\r\n        </el-form-item>\r\n      </el-col>\r\n      <el-col :span=\"12\">\r\n        <el-form-item label=\"作者\" prop=\"functionAuthor\">\r\n          <el-input placeholder=\"请输入\" v-model=\"info.functionAuthor\" />\r\n        </el-form-item>\r\n      </el-col>\r\n      <el-col :span=\"24\">\r\n        <el-form-item label=\"备注\" prop=\"remark\">\r\n          <el-input type=\"textarea\" :rows=\"3\" v-model=\"info.remark\"></el-input>\r\n        </el-form-item>\r\n      </el-col>\r\n    </el-row>\r\n  </el-form>\r\n</template>\r\n<script>\r\nexport default {\r\n  name: \"BasicInfoForm\",\r\n  props: {\r\n    info: {\r\n      type: Object,\r\n      default: null\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      rules: {\r\n        tableName: [\r\n          { required: true, message: \"请输入表名称\", trigger: \"blur\" }\r\n        ],\r\n        tableComment: [\r\n          { required: true, message: \"请输入表描述\", trigger: \"blur\" }\r\n        ],\r\n        className: [\r\n          { required: true, message: \"请输入实体类名称\", trigger: \"blur\" }\r\n        ],\r\n        functionAuthor: [\r\n          { required: true, message: \"请输入作者\", trigger: \"blur\" }\r\n        ]\r\n      }\r\n    };\r\n  }\r\n};\r\n</script>\r\n", "<template>\r\n  <el-card>\r\n    <el-tabs v-model=\"activeName\">\r\n      <el-tab-pane label=\"基本信息\" name=\"basic\">\r\n        <basic-info-form ref=\"basicInfo\" :info=\"info\" />\r\n      </el-tab-pane>\r\n      <el-tab-pane label=\"字段信息\" name=\"columnInfo\">\r\n        <el-table ref=\"dragTable\" :data=\"columns\" row-key=\"columnId\" :max-height=\"tableHeight\">\r\n          <el-table-column label=\"序号\" type=\"index\" min-width=\"5%\" class-name=\"allowDrag\" />\r\n          <el-table-column\r\n            label=\"字段列名\"\r\n            prop=\"columnName\"\r\n            min-width=\"10%\"\r\n            :show-overflow-tooltip=\"true\"\r\n          />\r\n          <el-table-column label=\"字段描述\" min-width=\"10%\">\r\n            <template slot-scope=\"scope\">\r\n              <el-input v-model=\"scope.row.columnComment\"></el-input>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column\r\n            label=\"物理类型\"\r\n            prop=\"columnType\"\r\n            min-width=\"10%\"\r\n            :show-overflow-tooltip=\"true\"\r\n          />\r\n          <el-table-column label=\"Java类型\" min-width=\"11%\">\r\n            <template slot-scope=\"scope\">\r\n              <el-select v-model=\"scope.row.javaType\">\r\n                <el-option label=\"Long\" value=\"Long\" />\r\n                <el-option label=\"String\" value=\"String\" />\r\n                <el-option label=\"Integer\" value=\"Integer\" />\r\n                <el-option label=\"Double\" value=\"Double\" />\r\n                <el-option label=\"BigDecimal\" value=\"BigDecimal\" />\r\n                <el-option label=\"Date\" value=\"Date\" />\r\n              </el-select>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"java属性\" min-width=\"10%\">\r\n            <template slot-scope=\"scope\">\r\n              <el-input v-model=\"scope.row.javaField\"></el-input>\r\n            </template>\r\n          </el-table-column>\r\n\r\n          <el-table-column label=\"插入\" min-width=\"5%\">\r\n            <template slot-scope=\"scope\">\r\n              <el-checkbox true-label=\"1\" v-model=\"scope.row.isInsert\"></el-checkbox>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"编辑\" min-width=\"5%\">\r\n            <template slot-scope=\"scope\">\r\n              <el-checkbox true-label=\"1\" v-model=\"scope.row.isEdit\"></el-checkbox>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"列表\" min-width=\"5%\">\r\n            <template slot-scope=\"scope\">\r\n              <el-checkbox true-label=\"1\" v-model=\"scope.row.isList\"></el-checkbox>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"查询\" min-width=\"5%\">\r\n            <template slot-scope=\"scope\">\r\n              <el-checkbox true-label=\"1\" v-model=\"scope.row.isQuery\"></el-checkbox>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"查询方式\" min-width=\"10%\">\r\n            <template slot-scope=\"scope\">\r\n              <el-select v-model=\"scope.row.queryType\">\r\n                <el-option label=\"=\" value=\"EQ\" />\r\n                <el-option label=\"!=\" value=\"NE\" />\r\n                <el-option label=\">\" value=\"GT\" />\r\n                <el-option label=\">=\" value=\"GTE\" />\r\n                <el-option label=\"<\" value=\"LT\" />\r\n                <el-option label=\"<=\" value=\"LTE\" />\r\n                <el-option label=\"LIKE\" value=\"LIKE\" />\r\n                <el-option label=\"BETWEEN\" value=\"BETWEEN\" />\r\n              </el-select>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"必填\" min-width=\"5%\">\r\n            <template slot-scope=\"scope\">\r\n              <el-checkbox true-label=\"1\" v-model=\"scope.row.isRequired\"></el-checkbox>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"显示类型\" min-width=\"12%\">\r\n            <template slot-scope=\"scope\">\r\n              <el-select v-model=\"scope.row.htmlType\">\r\n                <el-option label=\"文本框\" value=\"input\" />\r\n                <el-option label=\"文本域\" value=\"textarea\" />\r\n                <el-option label=\"下拉框\" value=\"select\" />\r\n                <el-option label=\"单选框\" value=\"radio\" />\r\n                <el-option label=\"复选框\" value=\"checkbox\" />\r\n                <el-option label=\"日期控件\" value=\"datetime\" />\r\n                <el-option label=\"图片上传\" value=\"imageUpload\" />\r\n                <el-option label=\"文件上传\" value=\"fileUpload\" />\r\n                <el-option label=\"富文本控件\" value=\"editor\" />\r\n              </el-select>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"字典类型\" min-width=\"12%\">\r\n            <template slot-scope=\"scope\">\r\n              <el-select v-model=\"scope.row.dictType\" clearable filterable placeholder=\"请选择\">\r\n                <el-option\r\n                  v-for=\"dict in dictOptions\"\r\n                  :key=\"dict.dictType\"\r\n                  :label=\"dict.dictName\"\r\n                  :value=\"dict.dictType\">\r\n                  <span style=\"float: left\">{{ dict.dictName }}</span>\r\n                  <span style=\"float: right; color: #8492a6; font-size: 13px\">{{ dict.dictType }}</span>\r\n              </el-option>\r\n              </el-select>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n      </el-tab-pane>\r\n      <el-tab-pane label=\"生成信息\" name=\"genInfo\">\r\n        <gen-info-form ref=\"genInfo\" :info=\"info\" :tables=\"tables\" :menus=\"menus\"/>\r\n      </el-tab-pane>\r\n    </el-tabs>\r\n    <el-form label-width=\"100px\">\r\n      <el-form-item style=\"text-align: center;margin-left:-100px;margin-top:10px;\">\r\n        <el-button type=\"primary\" @click=\"submitForm()\">提交</el-button>\r\n        <el-button @click=\"close()\">返回</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n  </el-card>\r\n</template>\r\n<script>\r\nimport { getGenTable, updateGenTable } from \"@/api/tool/gen\";\r\nimport { optionselect as getDictOptionselect } from \"@/api/system/dict/type\";\r\nimport { listMenu as getMenuTreeselect } from \"@/api/system/menu\";\r\nimport basicInfoForm from \"./basicInfoForm\";\r\nimport genInfoForm from \"./genInfoForm\";\r\nimport Sortable from 'sortablejs'\r\n\r\nexport default {\r\n  name: \"GenEdit\",\r\n  components: {\r\n    basicInfoForm,\r\n    genInfoForm\r\n  },\r\n  data() {\r\n    return {\r\n      // 选中选项卡的 name\r\n      activeName: \"columnInfo\",\r\n      // 表格的高度\r\n      tableHeight: document.documentElement.scrollHeight - 245 + \"px\",\r\n      // 表信息\r\n      tables: [],\r\n      // 表列信息\r\n      columns: [],\r\n      // 字典信息\r\n      dictOptions: [],\r\n      // 菜单信息\r\n      menus: [],\r\n      // 表详细信息\r\n      info: {}\r\n    };\r\n  },\r\n  created() {\r\n    const tableId = this.$route.query && this.$route.query.tableId;\r\n    if (tableId) {\r\n      // 获取表详细信息\r\n      getGenTable(tableId).then(res => {\r\n        this.columns = res.data.rows;\r\n        this.info = res.data.info;\r\n        this.tables = res.data.tables;\r\n      });\r\n      /** 查询字典下拉列表 */\r\n      getDictOptionselect().then(response => {\r\n        this.dictOptions = response.data;\r\n      });\r\n      /** 查询菜单下拉列表 */\r\n      getMenuTreeselect().then(response => {\r\n        this.menus = this.handleTree(response.data, \"menuId\");\r\n      });\r\n    }\r\n  },\r\n  methods: {\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      const basicForm = this.$refs.basicInfo.$refs.basicInfoForm;\r\n      const genForm = this.$refs.genInfo.$refs.genInfoForm;\r\n      Promise.all([basicForm, genForm].map(this.getFormPromise)).then(res => {\r\n        const validateResult = res.every(item => !!item);\r\n        if (validateResult) {\r\n          const genTable = Object.assign({}, basicForm.model, genForm.model);\r\n          genTable.columns = this.columns;\r\n          genTable.params = {\r\n            treeCode: genTable.treeCode,\r\n            treeName: genTable.treeName,\r\n            treeParentCode: genTable.treeParentCode,\r\n            parentMenuId: genTable.parentMenuId\r\n          };\r\n          updateGenTable(genTable).then(res => {\r\n            this.$modal.msgSuccess(res.msg);\r\n            if (res.code === 200) {\r\n              this.close();\r\n            }\r\n          });\r\n        } else {\r\n          this.$modal.msgError(\"表单校验未通过，请重新检查提交内容\");\r\n        }\r\n      });\r\n    },\r\n    getFormPromise(form) {\r\n      return new Promise(resolve => {\r\n        form.validate(res => {\r\n          resolve(res);\r\n        });\r\n      });\r\n    },\r\n    /** 关闭按钮 */\r\n    close() {\r\n      const obj = { path: \"/tool/gen\", query: { t: Date.now(), pageNum: this.$route.query.pageNum } };\r\n      this.$tab.closeOpenPage(obj);\r\n    }\r\n  },\r\n  mounted() {\r\n    const el = this.$refs.dragTable.$el.querySelectorAll(\".el-table__body-wrapper > table > tbody\")[0];\r\n    const sortable = Sortable.create(el, {\r\n      handle: \".allowDrag\",\r\n      onEnd: evt => {\r\n        const targetRow = this.columns.splice(evt.oldIndex, 1)[0];\r\n        this.columns.splice(evt.newIndex, 0, targetRow);\r\n        for (let index in this.columns) {\r\n          this.columns[index].sort = parseInt(index) + 1;\r\n        }\r\n      }\r\n    });\r\n  }\r\n};\r\n</script>\r\n", "<template>\r\n  <el-form ref=\"genInfoForm\" :model=\"info\" :rules=\"rules\" label-width=\"150px\">\r\n    <el-row>\r\n      <el-col :span=\"12\">\r\n        <el-form-item prop=\"tplCategory\">\r\n          <span slot=\"label\">生成模板</span>\r\n          <el-select v-model=\"info.tplCategory\" @change=\"tplSelectChange\">\r\n            <el-option label=\"单表（增删改查）\" value=\"crud\" />\r\n            <el-option label=\"树表（增删改查）\" value=\"tree\" />\r\n            <el-option label=\"主子表（增删改查）\" value=\"sub\" />\r\n          </el-select>\r\n        </el-form-item>\r\n      </el-col>\r\n\r\n      <el-col :span=\"12\">\r\n        <el-form-item prop=\"packageName\">\r\n          <span slot=\"label\">\r\n            生成包路径\r\n            <el-tooltip content=\"生成在哪个java包下，例如 com.ruoyi.system\" placement=\"top\">\r\n              <i class=\"el-icon-question\"></i>\r\n            </el-tooltip>\r\n          </span>\r\n          <el-input v-model=\"info.packageName\" />\r\n        </el-form-item>\r\n      </el-col>\r\n\r\n      <el-col :span=\"12\">\r\n        <el-form-item prop=\"moduleName\">\r\n          <span slot=\"label\">\r\n            生成模块名\r\n            <el-tooltip content=\"可理解为子系统名，例如 system\" placement=\"top\">\r\n              <i class=\"el-icon-question\"></i>\r\n            </el-tooltip>\r\n          </span>\r\n          <el-input v-model=\"info.moduleName\" />\r\n        </el-form-item>\r\n      </el-col>\r\n\r\n      <el-col :span=\"12\">\r\n        <el-form-item prop=\"businessName\">\r\n          <span slot=\"label\">\r\n            生成业务名\r\n            <el-tooltip content=\"可理解为功能英文名，例如 user\" placement=\"top\">\r\n              <i class=\"el-icon-question\"></i>\r\n            </el-tooltip>\r\n          </span>\r\n          <el-input v-model=\"info.businessName\" />\r\n        </el-form-item>\r\n      </el-col>\r\n\r\n      <el-col :span=\"12\">\r\n        <el-form-item prop=\"functionName\">\r\n          <span slot=\"label\">\r\n            生成功能名\r\n            <el-tooltip content=\"用作类描述，例如 用户\" placement=\"top\">\r\n              <i class=\"el-icon-question\"></i>\r\n            </el-tooltip>\r\n          </span>\r\n          <el-input v-model=\"info.functionName\" />\r\n        </el-form-item>\r\n      </el-col>\r\n\r\n      <el-col :span=\"12\">\r\n        <el-form-item>\r\n          <span slot=\"label\">\r\n            上级菜单\r\n            <el-tooltip content=\"分配到指定菜单下，例如 系统管理\" placement=\"top\">\r\n              <i class=\"el-icon-question\"></i>\r\n            </el-tooltip>\r\n          </span>\r\n          <treeselect\r\n            :append-to-body=\"true\"\r\n            v-model=\"info.parentMenuId\"\r\n            :options=\"menus\"\r\n            :normalizer=\"normalizer\"\r\n            :show-count=\"true\"\r\n            placeholder=\"请选择系统菜单\"\r\n          />\r\n        </el-form-item>\r\n      </el-col>\r\n\r\n      <el-col :span=\"12\">\r\n        <el-form-item prop=\"genType\">\r\n          <span slot=\"label\">\r\n            生成代码方式\r\n            <el-tooltip content=\"默认为zip压缩包下载，也可以自定义生成路径\" placement=\"top\">\r\n              <i class=\"el-icon-question\"></i>\r\n            </el-tooltip>\r\n          </span>\r\n          <el-radio v-model=\"info.genType\" label=\"0\">zip压缩包</el-radio>\r\n          <el-radio v-model=\"info.genType\" label=\"1\">自定义路径</el-radio>\r\n        </el-form-item>\r\n      </el-col>\r\n\r\n      <el-col :span=\"24\" v-if=\"info.genType == '1'\">\r\n        <el-form-item prop=\"genPath\">\r\n          <span slot=\"label\">\r\n            自定义路径\r\n            <el-tooltip content=\"填写磁盘绝对路径，若不填写，则生成到当前Web项目下\" placement=\"top\">\r\n              <i class=\"el-icon-question\"></i>\r\n            </el-tooltip>\r\n          </span>\r\n          <el-input v-model=\"info.genPath\">\r\n            <el-dropdown slot=\"append\">\r\n              <el-button type=\"primary\">\r\n                最近路径快速选择\r\n                <i class=\"el-icon-arrow-down el-icon--right\"></i>\r\n              </el-button>\r\n              <el-dropdown-menu slot=\"dropdown\">\r\n                <el-dropdown-item @click.native=\"info.genPath = '/'\">恢复默认的生成基础路径</el-dropdown-item>\r\n              </el-dropdown-menu>\r\n            </el-dropdown>\r\n          </el-input>\r\n        </el-form-item>\r\n      </el-col>\r\n    </el-row>\r\n\r\n    <el-row v-show=\"info.tplCategory == 'tree'\">\r\n      <h4 class=\"form-header\">其他信息</h4>\r\n      <el-col :span=\"12\">\r\n        <el-form-item>\r\n          <span slot=\"label\">\r\n            树编码字段\r\n            <el-tooltip content=\"树显示的编码字段名， 如：dept_id\" placement=\"top\">\r\n              <i class=\"el-icon-question\"></i>\r\n            </el-tooltip>\r\n          </span>\r\n          <el-select v-model=\"info.treeCode\" placeholder=\"请选择\">\r\n            <el-option\r\n              v-for=\"(column, index) in info.columns\"\r\n              :key=\"index\"\r\n              :label=\"column.columnName + '：' + column.columnComment\"\r\n              :value=\"column.columnName\"\r\n            ></el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n      </el-col>\r\n      <el-col :span=\"12\">\r\n        <el-form-item>\r\n          <span slot=\"label\">\r\n            树父编码字段\r\n            <el-tooltip content=\"树显示的父编码字段名， 如：parent_Id\" placement=\"top\">\r\n              <i class=\"el-icon-question\"></i>\r\n            </el-tooltip>\r\n          </span>\r\n          <el-select v-model=\"info.treeParentCode\" placeholder=\"请选择\">\r\n            <el-option\r\n              v-for=\"(column, index) in info.columns\"\r\n              :key=\"index\"\r\n              :label=\"column.columnName + '：' + column.columnComment\"\r\n              :value=\"column.columnName\"\r\n            ></el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n      </el-col>\r\n      <el-col :span=\"12\">\r\n        <el-form-item>\r\n          <span slot=\"label\">\r\n            树名称字段\r\n            <el-tooltip content=\"树节点的显示名称字段名， 如：dept_name\" placement=\"top\">\r\n              <i class=\"el-icon-question\"></i>\r\n            </el-tooltip>\r\n          </span>\r\n          <el-select v-model=\"info.treeName\" placeholder=\"请选择\">\r\n            <el-option\r\n              v-for=\"(column, index) in info.columns\"\r\n              :key=\"index\"\r\n              :label=\"column.columnName + '：' + column.columnComment\"\r\n              :value=\"column.columnName\"\r\n            ></el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n      </el-col>\r\n    </el-row>\r\n    <el-row v-show=\"info.tplCategory == 'sub'\">\r\n      <h4 class=\"form-header\">关联信息</h4>\r\n      <el-col :span=\"12\">\r\n        <el-form-item>\r\n          <span slot=\"label\">\r\n            关联子表的表名\r\n            <el-tooltip content=\"关联子表的表名， 如：sys_user\" placement=\"top\">\r\n              <i class=\"el-icon-question\"></i>\r\n            </el-tooltip>\r\n          </span>\r\n          <el-select v-model=\"info.subTableName\" placeholder=\"请选择\" @change=\"subSelectChange\">\r\n            <el-option\r\n              v-for=\"(table, index) in tables\"\r\n              :key=\"index\"\r\n              :label=\"table.tableName + '：' + table.tableComment\"\r\n              :value=\"table.tableName\"\r\n            ></el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n      </el-col>\r\n      <el-col :span=\"12\">\r\n        <el-form-item>\r\n          <span slot=\"label\">\r\n            子表关联的外键名\r\n            <el-tooltip content=\"子表关联的外键名， 如：user_id\" placement=\"top\">\r\n              <i class=\"el-icon-question\"></i>\r\n            </el-tooltip>\r\n          </span>\r\n          <el-select v-model=\"info.subTableFkName\" placeholder=\"请选择\">\r\n            <el-option\r\n              v-for=\"(column, index) in subColumns\"\r\n              :key=\"index\"\r\n              :label=\"column.columnName + '：' + column.columnComment\"\r\n              :value=\"column.columnName\"\r\n            ></el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n      </el-col>\r\n    </el-row>\r\n  </el-form>\r\n</template>\r\n<script>\r\nimport Treeselect from \"@riophae/vue-treeselect\";\r\nimport \"@riophae/vue-treeselect/dist/vue-treeselect.css\";\r\n\r\nexport default {\r\n  name: \"BasicInfoForm\",\r\n  components: { Treeselect },\r\n  props: {\r\n    info: {\r\n      type: Object,\r\n      default: null\r\n    },\r\n    tables: {\r\n      type: Array,\r\n      default: null\r\n    },\r\n    menus: {\r\n      type: Array,\r\n      default: []\r\n    },\r\n  },\r\n  data() {\r\n    return {\r\n      subColumns: [],\r\n      rules: {\r\n        tplCategory: [\r\n          { required: true, message: \"请选择生成模板\", trigger: \"blur\" }\r\n        ],\r\n        packageName: [\r\n          { required: true, message: \"请输入生成包路径\", trigger: \"blur\" }\r\n        ],\r\n        moduleName: [\r\n          { required: true, message: \"请输入生成模块名\", trigger: \"blur\" }\r\n        ],\r\n        businessName: [\r\n          { required: true, message: \"请输入生成业务名\", trigger: \"blur\" }\r\n        ],\r\n        functionName: [\r\n          { required: true, message: \"请输入生成功能名\", trigger: \"blur\" }\r\n        ],\r\n      }\r\n    };\r\n  },\r\n  created() {},\r\n  watch: {\r\n    'info.subTableName': function(val) {\r\n      this.setSubTableColumns(val);\r\n    }\r\n  },\r\n  methods: {\r\n    /** 转换菜单数据结构 */\r\n    normalizer(node) {\r\n      if (node.children && !node.children.length) {\r\n        delete node.children;\r\n      }\r\n      return {\r\n        id: node.menuId,\r\n        label: node.menuName,\r\n        children: node.children\r\n      };\r\n    },\r\n    /** 选择子表名触发 */\r\n    subSelectChange(value) {\r\n      this.info.subTableFkName = '';\r\n    },\r\n    /** 选择生成模板触发 */\r\n    tplSelectChange(value) {\r\n      if(value !== 'sub') {\r\n        this.info.subTableName = '';\r\n        this.info.subTableFkName = '';\r\n      }\r\n    },\r\n    /** 设置关联外键 */\r\n    setSubTableColumns(value) {\r\n      for (var item in this.tables) {\r\n        const name = this.tables[item].tableName;\r\n        if (value === name) {\r\n          this.subColumns = this.tables[item].columns;\r\n          break;\r\n        }\r\n      }\r\n    }\r\n  }\r\n};\r\n</script>\r\n", "var render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return _c(\n    \"el-form\",\n    {\n      ref: \"basicInfoForm\",\n      attrs: { model: _vm.info, rules: _vm.rules, \"label-width\": \"150px\" },\n    },\n    [\n      _c(\n        \"el-row\",\n        [\n          _c(\n            \"el-col\",\n            { attrs: { span: 12 } },\n            [\n              _c(\n                \"el-form-item\",\n                { attrs: { label: \"表名称\", prop: \"tableName\" } },\n                [\n                  _c(\"el-input\", {\n                    attrs: { placeholder: \"请输入仓库名称\" },\n                    model: {\n                      value: _vm.info.tableName,\n                      callback: function ($$v) {\n                        _vm.$set(_vm.info, \"tableName\", $$v)\n                      },\n                      expression: \"info.tableName\",\n                    },\n                  }),\n                ],\n                1\n              ),\n            ],\n            1\n          ),\n          _c(\n            \"el-col\",\n            { attrs: { span: 12 } },\n            [\n              _c(\n                \"el-form-item\",\n                { attrs: { label: \"表描述\", prop: \"tableComment\" } },\n                [\n                  _c(\"el-input\", {\n                    attrs: { placeholder: \"请输入\" },\n                    model: {\n                      value: _vm.info.tableComment,\n                      callback: function ($$v) {\n                        _vm.$set(_vm.info, \"tableComment\", $$v)\n                      },\n                      expression: \"info.tableComment\",\n                    },\n                  }),\n                ],\n                1\n              ),\n            ],\n            1\n          ),\n          _c(\n            \"el-col\",\n            { attrs: { span: 12 } },\n            [\n              _c(\n                \"el-form-item\",\n                { attrs: { label: \"实体类名称\", prop: \"className\" } },\n                [\n                  _c(\"el-input\", {\n                    attrs: { placeholder: \"请输入\" },\n                    model: {\n                      value: _vm.info.className,\n                      callback: function ($$v) {\n                        _vm.$set(_vm.info, \"className\", $$v)\n                      },\n                      expression: \"info.className\",\n                    },\n                  }),\n                ],\n                1\n              ),\n            ],\n            1\n          ),\n          _c(\n            \"el-col\",\n            { attrs: { span: 12 } },\n            [\n              _c(\n                \"el-form-item\",\n                { attrs: { label: \"作者\", prop: \"functionAuthor\" } },\n                [\n                  _c(\"el-input\", {\n                    attrs: { placeholder: \"请输入\" },\n                    model: {\n                      value: _vm.info.functionAuthor,\n                      callback: function ($$v) {\n                        _vm.$set(_vm.info, \"functionAuthor\", $$v)\n                      },\n                      expression: \"info.functionAuthor\",\n                    },\n                  }),\n                ],\n                1\n              ),\n            ],\n            1\n          ),\n          _c(\n            \"el-col\",\n            { attrs: { span: 24 } },\n            [\n              _c(\n                \"el-form-item\",\n                { attrs: { label: \"备注\", prop: \"remark\" } },\n                [\n                  _c(\"el-input\", {\n                    attrs: { type: \"textarea\", rows: 3 },\n                    model: {\n                      value: _vm.info.remark,\n                      callback: function ($$v) {\n                        _vm.$set(_vm.info, \"remark\", $$v)\n                      },\n                      expression: \"info.remark\",\n                    },\n                  }),\n                ],\n                1\n              ),\n            ],\n            1\n          ),\n        ],\n        1\n      ),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }", "var render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return _c(\n    \"el-card\",\n    [\n      _c(\n        \"el-tabs\",\n        {\n          model: {\n            value: _vm.activeName,\n            callback: function ($$v) {\n              _vm.activeName = $$v\n            },\n            expression: \"activeName\",\n          },\n        },\n        [\n          _c(\n            \"el-tab-pane\",\n            { attrs: { label: \"基本信息\", name: \"basic\" } },\n            [\n              _c(\"basic-info-form\", {\n                ref: \"basicInfo\",\n                attrs: { info: _vm.info },\n              }),\n            ],\n            1\n          ),\n          _c(\n            \"el-tab-pane\",\n            { attrs: { label: \"字段信息\", name: \"columnInfo\" } },\n            [\n              _c(\n                \"el-table\",\n                {\n                  ref: \"dragTable\",\n                  attrs: {\n                    data: _vm.columns,\n                    \"row-key\": \"columnId\",\n                    \"max-height\": _vm.tableHeight,\n                  },\n                },\n                [\n                  _c(\"el-table-column\", {\n                    attrs: {\n                      label: \"序号\",\n                      type: \"index\",\n                      \"min-width\": \"5%\",\n                      \"class-name\": \"allowDrag\",\n                    },\n                  }),\n                  _c(\"el-table-column\", {\n                    attrs: {\n                      label: \"字段列名\",\n                      prop: \"columnName\",\n                      \"min-width\": \"10%\",\n                      \"show-overflow-tooltip\": true,\n                    },\n                  }),\n                  _c(\"el-table-column\", {\n                    attrs: { label: \"字段描述\", \"min-width\": \"10%\" },\n                    scopedSlots: _vm._u([\n                      {\n                        key: \"default\",\n                        fn: function (scope) {\n                          return [\n                            _c(\"el-input\", {\n                              model: {\n                                value: scope.row.columnComment,\n                                callback: function ($$v) {\n                                  _vm.$set(scope.row, \"columnComment\", $$v)\n                                },\n                                expression: \"scope.row.columnComment\",\n                              },\n                            }),\n                          ]\n                        },\n                      },\n                    ]),\n                  }),\n                  _c(\"el-table-column\", {\n                    attrs: {\n                      label: \"物理类型\",\n                      prop: \"columnType\",\n                      \"min-width\": \"10%\",\n                      \"show-overflow-tooltip\": true,\n                    },\n                  }),\n                  _c(\"el-table-column\", {\n                    attrs: { label: \"Java类型\", \"min-width\": \"11%\" },\n                    scopedSlots: _vm._u([\n                      {\n                        key: \"default\",\n                        fn: function (scope) {\n                          return [\n                            _c(\n                              \"el-select\",\n                              {\n                                model: {\n                                  value: scope.row.javaType,\n                                  callback: function ($$v) {\n                                    _vm.$set(scope.row, \"javaType\", $$v)\n                                  },\n                                  expression: \"scope.row.javaType\",\n                                },\n                              },\n                              [\n                                _c(\"el-option\", {\n                                  attrs: { label: \"Long\", value: \"Long\" },\n                                }),\n                                _c(\"el-option\", {\n                                  attrs: { label: \"String\", value: \"String\" },\n                                }),\n                                _c(\"el-option\", {\n                                  attrs: { label: \"Integer\", value: \"Integer\" },\n                                }),\n                                _c(\"el-option\", {\n                                  attrs: { label: \"Double\", value: \"Double\" },\n                                }),\n                                _c(\"el-option\", {\n                                  attrs: {\n                                    label: \"BigDecimal\",\n                                    value: \"BigDecimal\",\n                                  },\n                                }),\n                                _c(\"el-option\", {\n                                  attrs: { label: \"Date\", value: \"Date\" },\n                                }),\n                              ],\n                              1\n                            ),\n                          ]\n                        },\n                      },\n                    ]),\n                  }),\n                  _c(\"el-table-column\", {\n                    attrs: { label: \"java属性\", \"min-width\": \"10%\" },\n                    scopedSlots: _vm._u([\n                      {\n                        key: \"default\",\n                        fn: function (scope) {\n                          return [\n                            _c(\"el-input\", {\n                              model: {\n                                value: scope.row.javaField,\n                                callback: function ($$v) {\n                                  _vm.$set(scope.row, \"javaField\", $$v)\n                                },\n                                expression: \"scope.row.javaField\",\n                              },\n                            }),\n                          ]\n                        },\n                      },\n                    ]),\n                  }),\n                  _c(\"el-table-column\", {\n                    attrs: { label: \"插入\", \"min-width\": \"5%\" },\n                    scopedSlots: _vm._u([\n                      {\n                        key: \"default\",\n                        fn: function (scope) {\n                          return [\n                            _c(\"el-checkbox\", {\n                              attrs: { \"true-label\": \"1\" },\n                              model: {\n                                value: scope.row.isInsert,\n                                callback: function ($$v) {\n                                  _vm.$set(scope.row, \"isInsert\", $$v)\n                                },\n                                expression: \"scope.row.isInsert\",\n                              },\n                            }),\n                          ]\n                        },\n                      },\n                    ]),\n                  }),\n                  _c(\"el-table-column\", {\n                    attrs: { label: \"编辑\", \"min-width\": \"5%\" },\n                    scopedSlots: _vm._u([\n                      {\n                        key: \"default\",\n                        fn: function (scope) {\n                          return [\n                            _c(\"el-checkbox\", {\n                              attrs: { \"true-label\": \"1\" },\n                              model: {\n                                value: scope.row.isEdit,\n                                callback: function ($$v) {\n                                  _vm.$set(scope.row, \"isEdit\", $$v)\n                                },\n                                expression: \"scope.row.isEdit\",\n                              },\n                            }),\n                          ]\n                        },\n                      },\n                    ]),\n                  }),\n                  _c(\"el-table-column\", {\n                    attrs: { label: \"列表\", \"min-width\": \"5%\" },\n                    scopedSlots: _vm._u([\n                      {\n                        key: \"default\",\n                        fn: function (scope) {\n                          return [\n                            _c(\"el-checkbox\", {\n                              attrs: { \"true-label\": \"1\" },\n                              model: {\n                                value: scope.row.isList,\n                                callback: function ($$v) {\n                                  _vm.$set(scope.row, \"isList\", $$v)\n                                },\n                                expression: \"scope.row.isList\",\n                              },\n                            }),\n                          ]\n                        },\n                      },\n                    ]),\n                  }),\n                  _c(\"el-table-column\", {\n                    attrs: { label: \"查询\", \"min-width\": \"5%\" },\n                    scopedSlots: _vm._u([\n                      {\n                        key: \"default\",\n                        fn: function (scope) {\n                          return [\n                            _c(\"el-checkbox\", {\n                              attrs: { \"true-label\": \"1\" },\n                              model: {\n                                value: scope.row.isQuery,\n                                callback: function ($$v) {\n                                  _vm.$set(scope.row, \"isQuery\", $$v)\n                                },\n                                expression: \"scope.row.isQuery\",\n                              },\n                            }),\n                          ]\n                        },\n                      },\n                    ]),\n                  }),\n                  _c(\"el-table-column\", {\n                    attrs: { label: \"查询方式\", \"min-width\": \"10%\" },\n                    scopedSlots: _vm._u([\n                      {\n                        key: \"default\",\n                        fn: function (scope) {\n                          return [\n                            _c(\n                              \"el-select\",\n                              {\n                                model: {\n                                  value: scope.row.queryType,\n                                  callback: function ($$v) {\n                                    _vm.$set(scope.row, \"queryType\", $$v)\n                                  },\n                                  expression: \"scope.row.queryType\",\n                                },\n                              },\n                              [\n                                _c(\"el-option\", {\n                                  attrs: { label: \"=\", value: \"EQ\" },\n                                }),\n                                _c(\"el-option\", {\n                                  attrs: { label: \"!=\", value: \"NE\" },\n                                }),\n                                _c(\"el-option\", {\n                                  attrs: { label: \">\", value: \"GT\" },\n                                }),\n                                _c(\"el-option\", {\n                                  attrs: { label: \">=\", value: \"GTE\" },\n                                }),\n                                _c(\"el-option\", {\n                                  attrs: { label: \"<\", value: \"LT\" },\n                                }),\n                                _c(\"el-option\", {\n                                  attrs: { label: \"<=\", value: \"LTE\" },\n                                }),\n                                _c(\"el-option\", {\n                                  attrs: { label: \"LIKE\", value: \"LIKE\" },\n                                }),\n                                _c(\"el-option\", {\n                                  attrs: { label: \"BETWEEN\", value: \"BETWEEN\" },\n                                }),\n                              ],\n                              1\n                            ),\n                          ]\n                        },\n                      },\n                    ]),\n                  }),\n                  _c(\"el-table-column\", {\n                    attrs: { label: \"必填\", \"min-width\": \"5%\" },\n                    scopedSlots: _vm._u([\n                      {\n                        key: \"default\",\n                        fn: function (scope) {\n                          return [\n                            _c(\"el-checkbox\", {\n                              attrs: { \"true-label\": \"1\" },\n                              model: {\n                                value: scope.row.isRequired,\n                                callback: function ($$v) {\n                                  _vm.$set(scope.row, \"isRequired\", $$v)\n                                },\n                                expression: \"scope.row.isRequired\",\n                              },\n                            }),\n                          ]\n                        },\n                      },\n                    ]),\n                  }),\n                  _c(\"el-table-column\", {\n                    attrs: { label: \"显示类型\", \"min-width\": \"12%\" },\n                    scopedSlots: _vm._u([\n                      {\n                        key: \"default\",\n                        fn: function (scope) {\n                          return [\n                            _c(\n                              \"el-select\",\n                              {\n                                model: {\n                                  value: scope.row.htmlType,\n                                  callback: function ($$v) {\n                                    _vm.$set(scope.row, \"htmlType\", $$v)\n                                  },\n                                  expression: \"scope.row.htmlType\",\n                                },\n                              },\n                              [\n                                _c(\"el-option\", {\n                                  attrs: { label: \"文本框\", value: \"input\" },\n                                }),\n                                _c(\"el-option\", {\n                                  attrs: { label: \"文本域\", value: \"textarea\" },\n                                }),\n                                _c(\"el-option\", {\n                                  attrs: { label: \"下拉框\", value: \"select\" },\n                                }),\n                                _c(\"el-option\", {\n                                  attrs: { label: \"单选框\", value: \"radio\" },\n                                }),\n                                _c(\"el-option\", {\n                                  attrs: { label: \"复选框\", value: \"checkbox\" },\n                                }),\n                                _c(\"el-option\", {\n                                  attrs: {\n                                    label: \"日期控件\",\n                                    value: \"datetime\",\n                                  },\n                                }),\n                                _c(\"el-option\", {\n                                  attrs: {\n                                    label: \"图片上传\",\n                                    value: \"imageUpload\",\n                                  },\n                                }),\n                                _c(\"el-option\", {\n                                  attrs: {\n                                    label: \"文件上传\",\n                                    value: \"fileUpload\",\n                                  },\n                                }),\n                                _c(\"el-option\", {\n                                  attrs: {\n                                    label: \"富文本控件\",\n                                    value: \"editor\",\n                                  },\n                                }),\n                              ],\n                              1\n                            ),\n                          ]\n                        },\n                      },\n                    ]),\n                  }),\n                  _c(\"el-table-column\", {\n                    attrs: { label: \"字典类型\", \"min-width\": \"12%\" },\n                    scopedSlots: _vm._u([\n                      {\n                        key: \"default\",\n                        fn: function (scope) {\n                          return [\n                            _c(\n                              \"el-select\",\n                              {\n                                attrs: {\n                                  clearable: \"\",\n                                  filterable: \"\",\n                                  placeholder: \"请选择\",\n                                },\n                                model: {\n                                  value: scope.row.dictType,\n                                  callback: function ($$v) {\n                                    _vm.$set(scope.row, \"dictType\", $$v)\n                                  },\n                                  expression: \"scope.row.dictType\",\n                                },\n                              },\n                              _vm._l(_vm.dictOptions, function (dict) {\n                                return _c(\n                                  \"el-option\",\n                                  {\n                                    key: dict.dictType,\n                                    attrs: {\n                                      label: dict.dictName,\n                                      value: dict.dictType,\n                                    },\n                                  },\n                                  [\n                                    _c(\n                                      \"span\",\n                                      { staticStyle: { float: \"left\" } },\n                                      [_vm._v(_vm._s(dict.dictName))]\n                                    ),\n                                    _c(\n                                      \"span\",\n                                      {\n                                        staticStyle: {\n                                          float: \"right\",\n                                          color: \"#8492a6\",\n                                          \"font-size\": \"13px\",\n                                        },\n                                      },\n                                      [_vm._v(_vm._s(dict.dictType))]\n                                    ),\n                                  ]\n                                )\n                              }),\n                              1\n                            ),\n                          ]\n                        },\n                      },\n                    ]),\n                  }),\n                ],\n                1\n              ),\n            ],\n            1\n          ),\n          _c(\n            \"el-tab-pane\",\n            { attrs: { label: \"生成信息\", name: \"genInfo\" } },\n            [\n              _c(\"gen-info-form\", {\n                ref: \"genInfo\",\n                attrs: { info: _vm.info, tables: _vm.tables, menus: _vm.menus },\n              }),\n            ],\n            1\n          ),\n        ],\n        1\n      ),\n      _c(\n        \"el-form\",\n        { attrs: { \"label-width\": \"100px\" } },\n        [\n          _c(\n            \"el-form-item\",\n            {\n              staticStyle: {\n                \"text-align\": \"center\",\n                \"margin-left\": \"-100px\",\n                \"margin-top\": \"10px\",\n              },\n            },\n            [\n              _c(\n                \"el-button\",\n                {\n                  attrs: { type: \"primary\" },\n                  on: {\n                    click: function ($event) {\n                      return _vm.submitForm()\n                    },\n                  },\n                },\n                [_vm._v(\"提交\")]\n              ),\n              _c(\n                \"el-button\",\n                {\n                  on: {\n                    click: function ($event) {\n                      return _vm.close()\n                    },\n                  },\n                },\n                [_vm._v(\"返回\")]\n              ),\n            ],\n            1\n          ),\n        ],\n        1\n      ),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }", "var render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return _c(\n    \"el-form\",\n    {\n      ref: \"genInfoForm\",\n      attrs: { model: _vm.info, rules: _vm.rules, \"label-width\": \"150px\" },\n    },\n    [\n      _c(\n        \"el-row\",\n        [\n          _c(\n            \"el-col\",\n            { attrs: { span: 12 } },\n            [\n              _c(\n                \"el-form-item\",\n                { attrs: { prop: \"tplCategory\" } },\n                [\n                  _c(\"span\", { attrs: { slot: \"label\" }, slot: \"label\" }, [\n                    _vm._v(\"生成模板\"),\n                  ]),\n                  _c(\n                    \"el-select\",\n                    {\n                      on: { change: _vm.tplSelectChange },\n                      model: {\n                        value: _vm.info.tplCategory,\n                        callback: function ($$v) {\n                          _vm.$set(_vm.info, \"tplCategory\", $$v)\n                        },\n                        expression: \"info.tplCategory\",\n                      },\n                    },\n                    [\n                      _c(\"el-option\", {\n                        attrs: { label: \"单表（增删改查）\", value: \"crud\" },\n                      }),\n                      _c(\"el-option\", {\n                        attrs: { label: \"树表（增删改查）\", value: \"tree\" },\n                      }),\n                      _c(\"el-option\", {\n                        attrs: { label: \"主子表（增删改查）\", value: \"sub\" },\n                      }),\n                    ],\n                    1\n                  ),\n                ],\n                1\n              ),\n            ],\n            1\n          ),\n          _c(\n            \"el-col\",\n            { attrs: { span: 12 } },\n            [\n              _c(\n                \"el-form-item\",\n                { attrs: { prop: \"packageName\" } },\n                [\n                  _c(\n                    \"span\",\n                    { attrs: { slot: \"label\" }, slot: \"label\" },\n                    [\n                      _vm._v(\" 生成包路径 \"),\n                      _c(\n                        \"el-tooltip\",\n                        {\n                          attrs: {\n                            content:\n                              \"生成在哪个java包下，例如 com.ruoyi.system\",\n                            placement: \"top\",\n                          },\n                        },\n                        [_c(\"i\", { staticClass: \"el-icon-question\" })]\n                      ),\n                    ],\n                    1\n                  ),\n                  _c(\"el-input\", {\n                    model: {\n                      value: _vm.info.packageName,\n                      callback: function ($$v) {\n                        _vm.$set(_vm.info, \"packageName\", $$v)\n                      },\n                      expression: \"info.packageName\",\n                    },\n                  }),\n                ],\n                1\n              ),\n            ],\n            1\n          ),\n          _c(\n            \"el-col\",\n            { attrs: { span: 12 } },\n            [\n              _c(\n                \"el-form-item\",\n                { attrs: { prop: \"moduleName\" } },\n                [\n                  _c(\n                    \"span\",\n                    { attrs: { slot: \"label\" }, slot: \"label\" },\n                    [\n                      _vm._v(\" 生成模块名 \"),\n                      _c(\n                        \"el-tooltip\",\n                        {\n                          attrs: {\n                            content: \"可理解为子系统名，例如 system\",\n                            placement: \"top\",\n                          },\n                        },\n                        [_c(\"i\", { staticClass: \"el-icon-question\" })]\n                      ),\n                    ],\n                    1\n                  ),\n                  _c(\"el-input\", {\n                    model: {\n                      value: _vm.info.moduleName,\n                      callback: function ($$v) {\n                        _vm.$set(_vm.info, \"moduleName\", $$v)\n                      },\n                      expression: \"info.moduleName\",\n                    },\n                  }),\n                ],\n                1\n              ),\n            ],\n            1\n          ),\n          _c(\n            \"el-col\",\n            { attrs: { span: 12 } },\n            [\n              _c(\n                \"el-form-item\",\n                { attrs: { prop: \"businessName\" } },\n                [\n                  _c(\n                    \"span\",\n                    { attrs: { slot: \"label\" }, slot: \"label\" },\n                    [\n                      _vm._v(\" 生成业务名 \"),\n                      _c(\n                        \"el-tooltip\",\n                        {\n                          attrs: {\n                            content: \"可理解为功能英文名，例如 user\",\n                            placement: \"top\",\n                          },\n                        },\n                        [_c(\"i\", { staticClass: \"el-icon-question\" })]\n                      ),\n                    ],\n                    1\n                  ),\n                  _c(\"el-input\", {\n                    model: {\n                      value: _vm.info.businessName,\n                      callback: function ($$v) {\n                        _vm.$set(_vm.info, \"businessName\", $$v)\n                      },\n                      expression: \"info.businessName\",\n                    },\n                  }),\n                ],\n                1\n              ),\n            ],\n            1\n          ),\n          _c(\n            \"el-col\",\n            { attrs: { span: 12 } },\n            [\n              _c(\n                \"el-form-item\",\n                { attrs: { prop: \"functionName\" } },\n                [\n                  _c(\n                    \"span\",\n                    { attrs: { slot: \"label\" }, slot: \"label\" },\n                    [\n                      _vm._v(\" 生成功能名 \"),\n                      _c(\n                        \"el-tooltip\",\n                        {\n                          attrs: {\n                            content: \"用作类描述，例如 用户\",\n                            placement: \"top\",\n                          },\n                        },\n                        [_c(\"i\", { staticClass: \"el-icon-question\" })]\n                      ),\n                    ],\n                    1\n                  ),\n                  _c(\"el-input\", {\n                    model: {\n                      value: _vm.info.functionName,\n                      callback: function ($$v) {\n                        _vm.$set(_vm.info, \"functionName\", $$v)\n                      },\n                      expression: \"info.functionName\",\n                    },\n                  }),\n                ],\n                1\n              ),\n            ],\n            1\n          ),\n          _c(\n            \"el-col\",\n            { attrs: { span: 12 } },\n            [\n              _c(\n                \"el-form-item\",\n                [\n                  _c(\n                    \"span\",\n                    { attrs: { slot: \"label\" }, slot: \"label\" },\n                    [\n                      _vm._v(\" 上级菜单 \"),\n                      _c(\n                        \"el-tooltip\",\n                        {\n                          attrs: {\n                            content: \"分配到指定菜单下，例如 系统管理\",\n                            placement: \"top\",\n                          },\n                        },\n                        [_c(\"i\", { staticClass: \"el-icon-question\" })]\n                      ),\n                    ],\n                    1\n                  ),\n                  _c(\"treeselect\", {\n                    attrs: {\n                      \"append-to-body\": true,\n                      options: _vm.menus,\n                      normalizer: _vm.normalizer,\n                      \"show-count\": true,\n                      placeholder: \"请选择系统菜单\",\n                    },\n                    model: {\n                      value: _vm.info.parentMenuId,\n                      callback: function ($$v) {\n                        _vm.$set(_vm.info, \"parentMenuId\", $$v)\n                      },\n                      expression: \"info.parentMenuId\",\n                    },\n                  }),\n                ],\n                1\n              ),\n            ],\n            1\n          ),\n          _c(\n            \"el-col\",\n            { attrs: { span: 12 } },\n            [\n              _c(\n                \"el-form-item\",\n                { attrs: { prop: \"genType\" } },\n                [\n                  _c(\n                    \"span\",\n                    { attrs: { slot: \"label\" }, slot: \"label\" },\n                    [\n                      _vm._v(\" 生成代码方式 \"),\n                      _c(\n                        \"el-tooltip\",\n                        {\n                          attrs: {\n                            content:\n                              \"默认为zip压缩包下载，也可以自定义生成路径\",\n                            placement: \"top\",\n                          },\n                        },\n                        [_c(\"i\", { staticClass: \"el-icon-question\" })]\n                      ),\n                    ],\n                    1\n                  ),\n                  _c(\n                    \"el-radio\",\n                    {\n                      attrs: { label: \"0\" },\n                      model: {\n                        value: _vm.info.genType,\n                        callback: function ($$v) {\n                          _vm.$set(_vm.info, \"genType\", $$v)\n                        },\n                        expression: \"info.genType\",\n                      },\n                    },\n                    [_vm._v(\"zip压缩包\")]\n                  ),\n                  _c(\n                    \"el-radio\",\n                    {\n                      attrs: { label: \"1\" },\n                      model: {\n                        value: _vm.info.genType,\n                        callback: function ($$v) {\n                          _vm.$set(_vm.info, \"genType\", $$v)\n                        },\n                        expression: \"info.genType\",\n                      },\n                    },\n                    [_vm._v(\"自定义路径\")]\n                  ),\n                ],\n                1\n              ),\n            ],\n            1\n          ),\n          _vm.info.genType == \"1\"\n            ? _c(\n                \"el-col\",\n                { attrs: { span: 24 } },\n                [\n                  _c(\n                    \"el-form-item\",\n                    { attrs: { prop: \"genPath\" } },\n                    [\n                      _c(\n                        \"span\",\n                        { attrs: { slot: \"label\" }, slot: \"label\" },\n                        [\n                          _vm._v(\" 自定义路径 \"),\n                          _c(\n                            \"el-tooltip\",\n                            {\n                              attrs: {\n                                content:\n                                  \"填写磁盘绝对路径，若不填写，则生成到当前Web项目下\",\n                                placement: \"top\",\n                              },\n                            },\n                            [_c(\"i\", { staticClass: \"el-icon-question\" })]\n                          ),\n                        ],\n                        1\n                      ),\n                      _c(\n                        \"el-input\",\n                        {\n                          model: {\n                            value: _vm.info.genPath,\n                            callback: function ($$v) {\n                              _vm.$set(_vm.info, \"genPath\", $$v)\n                            },\n                            expression: \"info.genPath\",\n                          },\n                        },\n                        [\n                          _c(\n                            \"el-dropdown\",\n                            { attrs: { slot: \"append\" }, slot: \"append\" },\n                            [\n                              _c(\"el-button\", { attrs: { type: \"primary\" } }, [\n                                _vm._v(\" 最近路径快速选择 \"),\n                                _c(\"i\", {\n                                  staticClass:\n                                    \"el-icon-arrow-down el-icon--right\",\n                                }),\n                              ]),\n                              _c(\n                                \"el-dropdown-menu\",\n                                {\n                                  attrs: { slot: \"dropdown\" },\n                                  slot: \"dropdown\",\n                                },\n                                [\n                                  _c(\n                                    \"el-dropdown-item\",\n                                    {\n                                      nativeOn: {\n                                        click: function ($event) {\n                                          _vm.info.genPath = \"/\"\n                                        },\n                                      },\n                                    },\n                                    [_vm._v(\"恢复默认的生成基础路径\")]\n                                  ),\n                                ],\n                                1\n                              ),\n                            ],\n                            1\n                          ),\n                        ],\n                        1\n                      ),\n                    ],\n                    1\n                  ),\n                ],\n                1\n              )\n            : _vm._e(),\n        ],\n        1\n      ),\n      _c(\n        \"el-row\",\n        {\n          directives: [\n            {\n              name: \"show\",\n              rawName: \"v-show\",\n              value: _vm.info.tplCategory == \"tree\",\n              expression: \"info.tplCategory == 'tree'\",\n            },\n          ],\n        },\n        [\n          _c(\"h4\", { staticClass: \"form-header\" }, [_vm._v(\"其他信息\")]),\n          _c(\n            \"el-col\",\n            { attrs: { span: 12 } },\n            [\n              _c(\n                \"el-form-item\",\n                [\n                  _c(\n                    \"span\",\n                    { attrs: { slot: \"label\" }, slot: \"label\" },\n                    [\n                      _vm._v(\" 树编码字段 \"),\n                      _c(\n                        \"el-tooltip\",\n                        {\n                          attrs: {\n                            content: \"树显示的编码字段名， 如：dept_id\",\n                            placement: \"top\",\n                          },\n                        },\n                        [_c(\"i\", { staticClass: \"el-icon-question\" })]\n                      ),\n                    ],\n                    1\n                  ),\n                  _c(\n                    \"el-select\",\n                    {\n                      attrs: { placeholder: \"请选择\" },\n                      model: {\n                        value: _vm.info.treeCode,\n                        callback: function ($$v) {\n                          _vm.$set(_vm.info, \"treeCode\", $$v)\n                        },\n                        expression: \"info.treeCode\",\n                      },\n                    },\n                    _vm._l(_vm.info.columns, function (column, index) {\n                      return _c(\"el-option\", {\n                        key: index,\n                        attrs: {\n                          label:\n                            column.columnName + \"：\" + column.columnComment,\n                          value: column.columnName,\n                        },\n                      })\n                    }),\n                    1\n                  ),\n                ],\n                1\n              ),\n            ],\n            1\n          ),\n          _c(\n            \"el-col\",\n            { attrs: { span: 12 } },\n            [\n              _c(\n                \"el-form-item\",\n                [\n                  _c(\n                    \"span\",\n                    { attrs: { slot: \"label\" }, slot: \"label\" },\n                    [\n                      _vm._v(\" 树父编码字段 \"),\n                      _c(\n                        \"el-tooltip\",\n                        {\n                          attrs: {\n                            content: \"树显示的父编码字段名， 如：parent_Id\",\n                            placement: \"top\",\n                          },\n                        },\n                        [_c(\"i\", { staticClass: \"el-icon-question\" })]\n                      ),\n                    ],\n                    1\n                  ),\n                  _c(\n                    \"el-select\",\n                    {\n                      attrs: { placeholder: \"请选择\" },\n                      model: {\n                        value: _vm.info.treeParentCode,\n                        callback: function ($$v) {\n                          _vm.$set(_vm.info, \"treeParentCode\", $$v)\n                        },\n                        expression: \"info.treeParentCode\",\n                      },\n                    },\n                    _vm._l(_vm.info.columns, function (column, index) {\n                      return _c(\"el-option\", {\n                        key: index,\n                        attrs: {\n                          label:\n                            column.columnName + \"：\" + column.columnComment,\n                          value: column.columnName,\n                        },\n                      })\n                    }),\n                    1\n                  ),\n                ],\n                1\n              ),\n            ],\n            1\n          ),\n          _c(\n            \"el-col\",\n            { attrs: { span: 12 } },\n            [\n              _c(\n                \"el-form-item\",\n                [\n                  _c(\n                    \"span\",\n                    { attrs: { slot: \"label\" }, slot: \"label\" },\n                    [\n                      _vm._v(\" 树名称字段 \"),\n                      _c(\n                        \"el-tooltip\",\n                        {\n                          attrs: {\n                            content: \"树节点的显示名称字段名， 如：dept_name\",\n                            placement: \"top\",\n                          },\n                        },\n                        [_c(\"i\", { staticClass: \"el-icon-question\" })]\n                      ),\n                    ],\n                    1\n                  ),\n                  _c(\n                    \"el-select\",\n                    {\n                      attrs: { placeholder: \"请选择\" },\n                      model: {\n                        value: _vm.info.treeName,\n                        callback: function ($$v) {\n                          _vm.$set(_vm.info, \"treeName\", $$v)\n                        },\n                        expression: \"info.treeName\",\n                      },\n                    },\n                    _vm._l(_vm.info.columns, function (column, index) {\n                      return _c(\"el-option\", {\n                        key: index,\n                        attrs: {\n                          label:\n                            column.columnName + \"：\" + column.columnComment,\n                          value: column.columnName,\n                        },\n                      })\n                    }),\n                    1\n                  ),\n                ],\n                1\n              ),\n            ],\n            1\n          ),\n        ],\n        1\n      ),\n      _c(\n        \"el-row\",\n        {\n          directives: [\n            {\n              name: \"show\",\n              rawName: \"v-show\",\n              value: _vm.info.tplCategory == \"sub\",\n              expression: \"info.tplCategory == 'sub'\",\n            },\n          ],\n        },\n        [\n          _c(\"h4\", { staticClass: \"form-header\" }, [_vm._v(\"关联信息\")]),\n          _c(\n            \"el-col\",\n            { attrs: { span: 12 } },\n            [\n              _c(\n                \"el-form-item\",\n                [\n                  _c(\n                    \"span\",\n                    { attrs: { slot: \"label\" }, slot: \"label\" },\n                    [\n                      _vm._v(\" 关联子表的表名 \"),\n                      _c(\n                        \"el-tooltip\",\n                        {\n                          attrs: {\n                            content: \"关联子表的表名， 如：sys_user\",\n                            placement: \"top\",\n                          },\n                        },\n                        [_c(\"i\", { staticClass: \"el-icon-question\" })]\n                      ),\n                    ],\n                    1\n                  ),\n                  _c(\n                    \"el-select\",\n                    {\n                      attrs: { placeholder: \"请选择\" },\n                      on: { change: _vm.subSelectChange },\n                      model: {\n                        value: _vm.info.subTableName,\n                        callback: function ($$v) {\n                          _vm.$set(_vm.info, \"subTableName\", $$v)\n                        },\n                        expression: \"info.subTableName\",\n                      },\n                    },\n                    _vm._l(_vm.tables, function (table, index) {\n                      return _c(\"el-option\", {\n                        key: index,\n                        attrs: {\n                          label: table.tableName + \"：\" + table.tableComment,\n                          value: table.tableName,\n                        },\n                      })\n                    }),\n                    1\n                  ),\n                ],\n                1\n              ),\n            ],\n            1\n          ),\n          _c(\n            \"el-col\",\n            { attrs: { span: 12 } },\n            [\n              _c(\n                \"el-form-item\",\n                [\n                  _c(\n                    \"span\",\n                    { attrs: { slot: \"label\" }, slot: \"label\" },\n                    [\n                      _vm._v(\" 子表关联的外键名 \"),\n                      _c(\n                        \"el-tooltip\",\n                        {\n                          attrs: {\n                            content: \"子表关联的外键名， 如：user_id\",\n                            placement: \"top\",\n                          },\n                        },\n                        [_c(\"i\", { staticClass: \"el-icon-question\" })]\n                      ),\n                    ],\n                    1\n                  ),\n                  _c(\n                    \"el-select\",\n                    {\n                      attrs: { placeholder: \"请选择\" },\n                      model: {\n                        value: _vm.info.subTableFkName,\n                        callback: function ($$v) {\n                          _vm.$set(_vm.info, \"subTableFkName\", $$v)\n                        },\n                        expression: \"info.subTableFkName\",\n                      },\n                    },\n                    _vm._l(_vm.subColumns, function (column, index) {\n                      return _c(\"el-option\", {\n                        key: index,\n                        attrs: {\n                          label:\n                            column.columnName + \"：\" + column.columnComment,\n                          value: column.columnName,\n                        },\n                      })\n                    }),\n                    1\n                  ),\n                ],\n                1\n              ),\n            ],\n            1\n          ),\n        ],\n        1\n      ),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }", "import request from '@/utils/request'\r\n\r\n// 查询字典类型列表\r\nexport function listType(query) {\r\n  return request({\r\n    url: '/system/dict/type/list',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询字典类型详细\r\nexport function getType(dictId) {\r\n  return request({\r\n    url: '/system/dict/type/' + dictId,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 新增字典类型\r\nexport function addType(data) {\r\n  return request({\r\n    url: '/system/dict/type',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 修改字典类型\r\nexport function updateType(data) {\r\n  return request({\r\n    url: '/system/dict/type',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 删除字典类型\r\nexport function delType(dictId) {\r\n  return request({\r\n    url: '/system/dict/type/' + dictId,\r\n    method: 'delete'\r\n  })\r\n}\r\n\r\n// 刷新字典缓存\r\nexport function refreshCache() {\r\n  return request({\r\n    url: '/system/dict/type/refreshCache',\r\n    method: 'delete'\r\n  })\r\n}\r\n\r\n// 获取字典选择框列表\r\nexport function optionselect() {\r\n  return request({\r\n    url: '/system/dict/type/optionselect',\r\n    method: 'get'\r\n  })\r\n}", "import request from '@/utils/request'\r\n\r\n// 查询菜单列表\r\nexport function listMenu(query) {\r\n  return request({\r\n    url: '/system/menu/list',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询菜单详细\r\nexport function getMenu(menuId) {\r\n  return request({\r\n    url: '/system/menu/' + menuId,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 查询菜单下拉树结构\r\nexport function treeselect() {\r\n  return request({\r\n    url: '/system/menu/treeselect',\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 根据角色ID查询菜单下拉树结构\r\nexport function roleMenuTreeselect(roleId) {\r\n  return request({\r\n    url: '/system/menu/roleMenuTreeselect/' + roleId,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 新增菜单\r\nexport function addMenu(data) {\r\n  return request({\r\n    url: '/system/menu',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 修改菜单\r\nexport function updateMenu(data) {\r\n  return request({\r\n    url: '/system/menu',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 删除菜单\r\nexport function delMenu(menuId) {\r\n  return request({\r\n    url: '/system/menu/' + menuId,\r\n    method: 'delete'\r\n  })\r\n}", "import request from '@/utils/request'\r\n\r\n// 查询生成表数据\r\nexport function listTable(query) {\r\n  return request({\r\n    url: '/tool/gen/list',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n// 查询db数据库列表\r\nexport function listDbTable(query) {\r\n  return request({\r\n    url: '/tool/gen/db/list',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询表详细信息\r\nexport function getGenTable(tableId) {\r\n  return request({\r\n    url: '/tool/gen/' + tableId,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 修改代码生成信息\r\nexport function updateGenTable(data) {\r\n  return request({\r\n    url: '/tool/gen',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 导入表\r\nexport function importTable(data) {\r\n  return request({\r\n    url: '/tool/gen/importTable',\r\n    method: 'post',\r\n    params: data\r\n  })\r\n}\r\n\r\n// 预览生成代码\r\nexport function previewTable(tableId) {\r\n  return request({\r\n    url: '/tool/gen/preview/' + tableId,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 删除表数据\r\nexport function delTable(tableId) {\r\n  return request({\r\n    url: '/tool/gen/' + tableId,\r\n    method: 'delete'\r\n  })\r\n}\r\n\r\n// 生成代码（自定义路径）\r\nexport function genCode(tableName) {\r\n  return request({\r\n    url: '/tool/gen/genCode/' + tableName,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 同步数据库\r\nexport function synchDb(tableName) {\r\n  return request({\r\n    url: '/tool/gen/synchDb/' + tableName,\r\n    method: 'get'\r\n  })\r\n}\r\n", "import { render, staticRenderFns } from \"./basicInfoForm.vue?vue&type=template&id=f6a95578&\"\nimport script from \"./basicInfoForm.vue?vue&type=script&lang=js&\"\nexport * from \"./basicInfoForm.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\n/* hot reload */\nif (module.hot) {\n  var api = require(\"E:\\\\workspace\\\\info\\\\trigram-ui\\\\node_modules\\\\vue-hot-reload-api\\\\dist\\\\index.js\")\n  api.install(require('vue'))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!api.isRecorded('f6a95578')) {\n      api.createRecord('f6a95578', component.options)\n    } else {\n      api.reload('f6a95578', component.options)\n    }\n    module.hot.accept(\"./basicInfoForm.vue?vue&type=template&id=f6a95578&\", function () {\n      api.rerender('f6a95578', {\n        render: render,\n        staticRenderFns: staticRenderFns\n      })\n    })\n  }\n}\ncomponent.options.__file = \"src/views/tool/gen/basicInfoForm.vue\"\nexport default component.exports", "import mod from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./basicInfoForm.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./basicInfoForm.vue?vue&type=script&lang=js&\"", "export * from \"-!../../../../node_modules/cache-loader/dist/cjs.js?{\\\"cacheDirectory\\\":\\\"node_modules/.cache/vue-loader\\\",\\\"cacheIdentifier\\\":\\\"f1aaaf00-vue-loader-template\\\"}!../../../../node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./basicInfoForm.vue?vue&type=template&id=f6a95578&\"", "import { render, staticRenderFns } from \"./editTable.vue?vue&type=template&id=afd7f770&\"\nimport script from \"./editTable.vue?vue&type=script&lang=js&\"\nexport * from \"./editTable.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\n/* hot reload */\nif (module.hot) {\n  var api = require(\"E:\\\\workspace\\\\info\\\\trigram-ui\\\\node_modules\\\\vue-hot-reload-api\\\\dist\\\\index.js\")\n  api.install(require('vue'))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!api.isRecorded('afd7f770')) {\n      api.createRecord('afd7f770', component.options)\n    } else {\n      api.reload('afd7f770', component.options)\n    }\n    module.hot.accept(\"./editTable.vue?vue&type=template&id=afd7f770&\", function () {\n      api.rerender('afd7f770', {\n        render: render,\n        staticRenderFns: staticRenderFns\n      })\n    })\n  }\n}\ncomponent.options.__file = \"src/views/tool/gen/editTable.vue\"\nexport default component.exports", "import mod from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./editTable.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./editTable.vue?vue&type=script&lang=js&\"", "export * from \"-!../../../../node_modules/cache-loader/dist/cjs.js?{\\\"cacheDirectory\\\":\\\"node_modules/.cache/vue-loader\\\",\\\"cacheIdentifier\\\":\\\"f1aaaf00-vue-loader-template\\\"}!../../../../node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./editTable.vue?vue&type=template&id=afd7f770&\"", "import { render, staticRenderFns } from \"./genInfoForm.vue?vue&type=template&id=6b907066&\"\nimport script from \"./genInfoForm.vue?vue&type=script&lang=js&\"\nexport * from \"./genInfoForm.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\n/* hot reload */\nif (module.hot) {\n  var api = require(\"E:\\\\workspace\\\\info\\\\trigram-ui\\\\node_modules\\\\vue-hot-reload-api\\\\dist\\\\index.js\")\n  api.install(require('vue'))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!api.isRecorded('6b907066')) {\n      api.createRecord('6b907066', component.options)\n    } else {\n      api.reload('6b907066', component.options)\n    }\n    module.hot.accept(\"./genInfoForm.vue?vue&type=template&id=6b907066&\", function () {\n      api.rerender('6b907066', {\n        render: render,\n        staticRenderFns: staticRenderFns\n      })\n    })\n  }\n}\ncomponent.options.__file = \"src/views/tool/gen/genInfoForm.vue\"\nexport default component.exports", "import mod from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./genInfoForm.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./genInfoForm.vue?vue&type=script&lang=js&\"", "export * from \"-!../../../../node_modules/cache-loader/dist/cjs.js?{\\\"cacheDirectory\\\":\\\"node_modules/.cache/vue-loader\\\",\\\"cacheIdentifier\\\":\\\"f1aaaf00-vue-loader-template\\\"}!../../../../node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./genInfoForm.vue?vue&type=template&id=6b907066&\""], "sourceRoot": ""}