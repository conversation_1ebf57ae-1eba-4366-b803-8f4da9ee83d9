<!--3：分类处置-复核-->
<template>
  <div>
    <div>
      <ModifyrecordBtn
        :key="problemId || relevantTableId || relevantTableName"
        :problemId="problemId"
        :relevantTableId="relevantTableId"
        :relevantTableName="relevantTableName"
        :problemStatus="3"
      ></ModifyrecordBtn>
      <el-form
        ref="elForm"
        :model="formData"
        :rules="rules"
        size="medium"
        label-width="180px"
      >
        <BlockCard title="基本信息">
          <el-row>
            <el-col :span="6">
              <el-form-item label="系统编号" prop="findTime">
                <span>{{ formData.auditCode }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="问题编号" prop="problemCode">
                <span>{{ formData.problemCode }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="违规事项" prop="problemTitle">
                <span>{{ formData.problemTitle }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="问题线索描述" prop="problemDescribe">
                <span>{{ formData.problemDescribe }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="涉及专业线" prop="specLists">
                <el-checkbox-group
                  v-model="formData.specLists"
                  disabled
                  :key="formData.specLists"
                  size="medium"
                >
                  <el-checkbox
                    v-for="(item, index) in specList"
                    :key="item.dictValue"
                    border
                    :label="item.dictValue"
                    >{{ item.dictLabel }}
                  </el-checkbox>
                </el-checkbox-group>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="涉及单位/部门/人员" prop="field107">
                <PersList
                  :edit="edit"
                  :problemId="problemId"
                  :relevantTableId="relevantTableId"
                  :relevantTableName="relevantTableName"
                  ref="pers"
                ></PersList>
              </el-form-item>
            </el-col>
          </el-row>
        </BlockCard>
        <Remind :key="actualFlag" :actualFlag="actualFlag"></Remind>
        <BlockCard title="分类处置信息">
          <el-row>
            <el-col :span="8">
              <el-form-item label="是否产生资产损失" prop="lossStateAssetsFlag">
                <el-radio-group
                  v-model="formData.lossStateAssetsFlag"
                  size="medium"
                  disabled
                >
                  <el-radio
                    v-for="(item, index) in YesOrNo"
                    :key="index"
                    :label="item.value"
                    >{{ item.label }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>

            <el-col :span="8">
              <el-form-item label="预估损失金额（万元）" prop="lossAmount">
                <el-input-number
                  v-model="formData.lossAmount"
                  disabled
                  :min="0"
                  :precision="2"
                  placeholder="预估损失金额（万元）"
                  controls-position="right"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="预估损失风险（万元）" prop="lossRisk">
                <el-input-number
                  disabled
                  v-model="formData.lossRisk"
                  :min="0"
                  :precision="2"
                  placeholder="预估损失风险（万元）"
                  controls-position="right"
                />
              </el-form-item>
            </el-col>

            <el-col :span="8">
              <el-form-item label="损失风险类别" prop="lossRiskType">
                <el-select
                  disabled
                  v-model="formData.lossRiskType"
                  placeholder="请选择损失风险类别"
                  clearable
                  :style="{ width: '100%' }"
                  value="formData.lossType"
                >
                  <el-option
                    v-for="(item, index) in lossRiskTypeOptions"
                    :key="index"
                    :label="item.dictLabel"
                    :value="item.dictValue"
                    >{{ item.dictLabel }}
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="24" />
            <el-col :span="24">
              <el-form-item label="损失形成主要原因" prop="lossReason">
                <span>{{ formData.lossReason }}</span>
              </el-form-item>
            </el-col>

             <el-col :span="8">
              <el-form-item label="是否产生不良影响" prop="isAdverseEffect">
                 <el-radio-group v-model="formData.isAdverseEffect" size="medium" disabled>
                    <el-radio v-for="(item, index) in whetherEffectOptions" :key="index" :label="item.value" @change="radioEffectChanged"
                              :disabled="item.disabled">{{item.label}}</el-radio>
                  </el-radio-group>
              </el-form-item>
            </el-col>
             <el-col :span="16">
                <el-form-item label="对应不良影响" prop="correspondingAdverseEffects" v-if="formData.isAdverseEffect">
                  <el-select v-model="formData.correspondingAdverseEffects" :style="{width: '100%'}" clearable="clearable" multiple="multiple" value="" disabled>
                    <el-option v-for="(item, index) in dict.type.corresponding_adverse_effect" :key="index" :label="item.label" :value="item.value"></el-option>
                  </el-select>
                </el-form-item>
            </el-col>
            <el-col :span="24" />
            <el-col :span="24">
              <el-form-item label="造成的不良影响" prop="adverseEffects" v-if="formData.isAdverseEffect">
                <span>{{formData.adverseEffects}}</span>
              </el-form-item>
            </el-col>

              <el-col :span="8">
              <el-form-item label="是否产生严重不良影响" prop="seriousAdverseEffectsFlag">
                <el-radio-group v-model="formData.seriousAdverseEffectsFlag" size="medium" >
                  <el-radio
                    v-for="(item, index) in YesOrNo"
                    :key="index"
                    :label="item.value"
                    disabled
                  >{{ item.label }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col v-show="formData.seriousAdverseEffectsFlag" :span="16">
              <el-form-item label="严重不良影响描述" prop="seriousAdverseEffectsDesc">
                <el-select
                disabled
                  v-model="formData.seriousAdverseEffectsDesc"
                  placeholder="请选择严重不良影响描述"
                  clearable
                  :style="{width: '100%'}"
                  value="formData.seriousAdverseEffectsDesc"
                >
                  <el-option
                    v-for="(item, index) in dict.type.VIOLD_ADVER_EFFECT_DES"
                    :key="index"
                    :label="item.label"
                    :value="item.value"
                    :disabled="item.disabled"
                  />
                </el-select>
              </el-form-item>
            </el-col>
             <el-col :span="24" />

            <el-col :span="8">
              <el-form-item
                label="是否上报上级单位"
                prop="isReportSuperiorUnit"
              >
                <el-radio-group
                  disabled
                  v-model="formData.isReportSuperiorUnit"
                  size="medium"
                >
                  <el-radio
                    v-for="(item, index) in YesOrNo"
                    :key="index"
                    :label="item.value"
                    >{{ item.label }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>

             <el-col :span="8">
              <el-form-item label="涉及集团总部" prop="isInvolveGroup">
                <el-radio-group
                  disabled
                  v-model="formData.isInvolveGroup"
                  size="medium"
                >
                  <el-radio
                    v-for="(item, index) in YesOrNo"
                    :key="index"
                    :label="item.value"
                    >{{ item.label }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>

              <el-col :span="8">
              <el-form-item
                label="涉及所属二级单位领导人员"
                prop="isInvolveSecondUnitLeader"
              >
                <el-radio-group
                  disabled
                  v-model="formData.isInvolveSecondUnitLeader"
                  size="medium"
                >
                  <el-radio
                    v-for="(item, index) in YesOrNo"
                    :key="index"
                    :label="item.value"
                    >{{ item.label }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>


            <el-col :span="8">
              <el-form-item label="由审计部核查" prop="isAuditDeptCheck">
                <el-radio-group
                  disabled
                  v-model="formData.isAuditDeptCheck"
                  size="medium"
                >
                  <el-radio
                    v-for="(item, index) in YesOrNo"
                    :key="index"
                    :label="item.value"
                    >{{ item.label }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>




            <el-col :span="8">
              <el-form-item label="需要重点督办" prop="isEmphasizeSupervise">
                <el-radio-group
                  disabled
                  v-model="formData.isEmphasizeSupervise"
                  size="medium"
                >
                  <el-radio
                    v-for="(item, index) in YesOrNo"
                    :key="index"
                    :label="item.value"
                    >{{ item.label }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>

            <el-col :span="8">
              <el-form-item
                label="移送国家监察或司法机关"
                prop="isHandoverNationalJudiciary"
              >
                <el-radio-group
                  disabled
                  v-model="formData.isHandoverNationalJudiciary"
                  size="medium"
                >
                  <el-radio
                    v-for="(item, index) in YesOrNo"
                    :key="index"
                    :label="item.value"
                    >{{ item.label }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="24" v-show="formData.isShowHandoverDiv">
              <el-form-item
                :label="formData.labelName"
                prop="isHandoverSecondUnitHandle"
              >
                <el-radio-group
                  disabled
                  v-model="formData.isHandoverSecondUnitHandle"
                  @change="changeHandler"
                  size="medium"
                >
                  <el-radio
                    v-for="(item, index) in YesOrNo"
                    :key="index"
                    :label="item.value"
                    >{{ item.label }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="24" v-show="formData.isShowHandoverDiv">
              <el-form-item>
                <div class="layui-form-item layui-form-item-sm">
                  <el-col class="layui-input-block">
                    <el-col class="transfer-box">
                      <el-row
                        class="transfer-li ry-row"
                        v-for="(
                          item, index
                        ) in formData.secondaryHandoverRecords"
                        style=""
                      >
                        <el-col :span="8" class="transfer-li-info">
                          <el-row>
                            <el-col :span="16" class="ovflowHidden text-left"
                              ><span>{{ item.fromUnitName }}</span></el-col
                            >
                            <el-col :span="8" class="text-right"
                              ><span>{{ item.fromUserName }}</span></el-col
                            >
                          </el-row>
                        </el-col>
                        <el-col :span="2" class="transfer-li-img">——</el-col>
                        <el-col
                          :span="8"
                          class="transfer-li-info ry-row cursor editSecondary"
                        >
                          <el-row>
                            <el-col :span="16" class="ovflowHidden text-left">
                              <span class="toUnitName">{{
                                item.toUnitName || "请选择被移送单位及人员信息"
                              }}</span>
                            </el-col>
                            <el-col :span="8" class="text-right">
                              <span class="toUserName">{{
                                item.toUserName
                              }}</span>
                            </el-col>
                          </el-row>
                        </el-col>
                        <el-col :span="6" class="transfer-li-edit text-right">
                          <span
                            v-show="item.downloadLink"
                            style="padding-left: 20px"
                            class="table-btn tip-edit float-right text-red cursor ovflowHidden"
                            @click="
                              downloadLink(item.downloadLink, item.fileName)
                            "
                            :title="item.fileName"
                            >{{ item.fileName }}</span
                          >
                        </el-col>
                      </el-row>
                      <el-row
                        class="transfer-li ry-row"
                        v-if="
                          formData.initialSecondaryHandoverObj &&
                          formData.isHandoverSecondUnitHandle == 1
                        "
                      >
                        <el-col :span="8" class="transfer-li-info">
                          <el-row>
                            <el-col :span="16" class="ovflowHidden text-left"
                              ><span>{{
                                formData.initialSecondaryHandoverObj
                                  .fromUnitName
                              }}</span></el-col
                            >
                            <el-col :span="8" class="text-right"
                              ><span>{{
                                formData.initialSecondaryHandoverObj
                                  .fromUserName
                              }}</span></el-col
                            >
                          </el-row>
                        </el-col>
                        <el-col :span="2" class="transfer-li-img">——</el-col>
                        <el-col
                          :span="8"
                          class="transfer-li-info ry-row cursor editSecondary"
                        >
                          <el-row>
                            <el-col :span="16" class="ovflowHidden text-left">
                              <span class="toUnitName">{{
                                formData.initialSecondaryHandoverObj
                                  .toUnitName || "请选择被移送单位及人员信息"
                              }}</span>
                            </el-col>
                            <el-col :span="8" class="text-right">
                              <span class="toUserName">{{
                                formData.initialSecondaryHandoverObj.toUserName
                              }}</span>
                            </el-col>
                          </el-row>
                        </el-col>
                      </el-row>
                    </el-col>
                  </el-col>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item
                label="移送纪检部门"
                prop="isHandoverInspectionDept"
              >
                <el-radio-group
                  disabled
                  v-model="formData.isHandoverInspectionDept"
                  @change="changeDel"
                  size="medium"
                >
                  <el-radio
                    v-for="(item, index) in YesOrNo"
                    :key="index"
                    :label="item.value"
                    >{{ item.label }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item>
                <div class="layui-form-item layui-form-item-sm">
                  <el-col class="layui-input-block">
                    <el-col class="transfer-box">
                      <el-row v-for="(item,index) in formData.inspectionHandoverRecords" class="transfer-li ry-row" style="">
                        <div v-if="formData.isHandoverInspectionDept||!item.editEnable">
                          <el-col :span="8" class="transfer-li-info">
                            <el-row>
                              <el-col :span="16" class="ovflowHidden text-left"><span>{{ item.fromUnitName }}</span></el-col>
                              <el-col :span="8" class="text-right"><span>{{ item.fromUserName }}</span></el-col>
                            </el-row>
                          </el-col>
                          <el-col :span="2" class="transfer-li-img">——</el-col>
                          <el-col :span="8" class="transfer-li-info ry-row cursor editSecondary">
                            <el-row>
                              <el-col :span="16" class="ovflowHidden text-left">
                                <span class="toUnitName" @click="item.editEnable==1&&BstaffOrgTree(2,item.toUserName)">{{ item.toDeptName||'请选择被移送纪检部门及人员信息' }}</span>
                              </el-col>
                              <el-col :span="8" class="text-right">
                                <span class="toUserName" @click="item.editEnable==1&&BstaffOrgTree(2,item.toUserName)">{{ item.toUserName }}</span>
                              </el-col>
                            </el-row>
                          </el-col>
                          <el-col :span="6" class="transfer-li-edit text-right">
                            <span
                              v-show="item.downloadLink"
                              style="padding-left: 20px;"
                              class="table-btn tip-edit float-right text-red ovflowHidden cursor"
                              :title="item.fileName"
                              @click="downloadLink(item.downloadLink,item.fileName)"
                            >{{ item.fileName }}</span>
                          </el-col>
                        </div>

                      </el-row>

                      <el-row v-if="formData.initialInspectionHandoverObj&&formData.isHandoverInspectionDept=='1'" class="transfer-li ry-row">
                        <el-col :span="8" class="transfer-li-info">
                          <el-row>
                            <el-col :span="16" class="ovflowHidden text-left"><span>{{ formData.initialInspectionHandoverObj.fromUnitName }}</span></el-col>
                            <el-col :span="8" class="text-right"><span>{{ formData.initialInspectionHandoverObj.fromUserName }}</span></el-col>
                          </el-row>
                        </el-col>
                        <el-col :span="2" class="transfer-li-img">——</el-col>
                        <el-col :span="8" class="transfer-li-info ry-row cursor editSecondary">
                          <el-row>
                            <el-col :span="16" class="ovflowHidden text-left">
                              <span class="toUnitName">{{ formData.initialInspectionHandoverObj.toUnitName||'请选择被移送单位及人员信息' }}</span>
                            </el-col>
                            <el-col :span="8" class="text-right">
                              <span class="toUserName">{{ formData.initialInspectionHandoverObj.toUserName }}</span>
                            </el-col>
                          </el-row>
                        </el-col>
                      </el-row>
                    </el-col>
                  </el-col>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
        </BlockCard>
      </el-form>
      <BlockCard title="附件列表">
        <FileUpload
          :edit="edit"
          :key="problemId || relevantTableId || relevantTableName"
          :problemId="problemId"
          :relevantTableId="relevantTableId"
          :relevantTableName="relevantTableName"
          flowType="VIOL_DAILY"
          problemStatus="3"
          ref="file"
        ></FileUpload>
      </BlockCard>
    </div>
  </div>
</template>
<script>
import { submitHisData } from '@/api/daily/historyQuestionEnter/index'
import {
  queryVerifyRecord,
  saveViewVerrify,
  saveViolationVerifyRecord,
  saveHandoverSecondaryRecord,
  saveHandoverInspectionRecord,
  deleteDailyHandoverRecord,
} from "@/api/daily/process/taskTodoViewDisposal";
import BlockCard from "@/components/BlockCard";
import Remind from "@/views/components/remind";
import FileUpload from "@/views/components/fileUpload"; //附件
import PersList from "@/views/daily/tree/persList"; //tree
import Tree from "@/views/daily/tree"; //tree
import radioTree from "@/views/components/tree/radioTree"; //tree
import TaskTodoViewAccept from "@/views/daily/process/taskTodoViewAccept"; //tree
import Process from "@/components/Process/daily";
import ModifyrecordBtn from "@/views/daily/modifyRecord/btn";

export default {
  components: {
    BlockCard,
    FileUpload,
    Tree,
    PersList,
    Process,
    TaskTodoViewAccept,
    radioTree,
    Remind,
    ModifyrecordBtn,
  },
  dicts: ["VIOLD_DAILY_SPEC", "VIOLD_ADVER_EFFECT_DES", 'corresponding_adverse_effect'],
  props: {
    problemId: {
      type: String,
    },
    isShow: {
      type: String,
      default: "0",
    },
    procInsId: {
      type: String,
    },
  },
  data() {
    return {
      relevantTableId: "",
      relevantTableName: "",
      actualFlag: 1,
      treeNew: 1,
      type1: 1,
      type2: 1,
      url: "",
      title: "人员选择",
      VisibleRadioTree: false,
      edit: false,
      flag: false,
      visible: false,
      visibleTree: false,
      selectTree: [],
      formData: {
        isHandoverSecondUnitHandle: null,
        secondaryHandoverRecords: [],
        inspectionHandoverRecords: [],
        isReportSuperiorUnit: undefined,
        problemTitle: null,
        problemDescribe: undefined,
        field107: undefined,
        lossReason: undefined,
        adverseEffects: undefined,
        specLists: [],
        seriousAdverseEffectsFlag: undefined,
        lossRiskType: undefined,
        isAuditDeptCheck: undefined,
        isInvolveGroup: undefined,
        isInvolveSecondUnitLeader: undefined,
        isEmphasizeSupervise: undefined,
        isHandoverNationalJudiciary: undefined,
        isHandoverInspectionDept: undefined,
      },
      specList: [],
      rules: {},
      YesOrNo: [
        {
          label: "是",
          value: 1,
        },
        {
          label: "否",
          value: 0,
        },
      ],
             whetherEffectOptions: [{
        "label": "是",
        "value": 1
      }, {
        "label": "否",
        "value": 0
      }],
      DailyHandoverRecordId: "",
      InspectionHandoverId: "",
      problemSourceList: [],
      lossRiskTypeOptions: [],
    };
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {
    if (this.problemId) {
      this.QueryVerifyRecord();
    }
  },
  methods: {
    //下载
    downloadLink(url, fileName) {
      this.download(url, {}, fileName);
    },
    //移送下级单位处理选中否删除
    changeHandler(value) {
      if (value == 0) {
        deleteDailyHandoverRecord(this.DailyHandoverRecordId).then(
          (response) => {
            if (response.code == 200) {
              this.DailyHandoverRecordId = response.data.id;
              if (this.formData.initialSecondaryHandoverObj) {
                this.formData.initialSecondaryHandoverObj.toUnitName = "";
                this.formData.initialSecondaryHandoverObj.toUserName = "";
              }
              if (this.formData.secondaryHandoverRecords) {
                this.formData.secondaryHandoverRecords[
                  this.formData.secondaryHandoverRecords.length - 1
                ].toUnitName = "";
                this.formData.secondaryHandoverRecords[
                  this.formData.secondaryHandoverRecords.length - 1
                ].toUserName = "";
              }
            }
          }
        );
      }
    },
    //删除移交纪检部门
    changeDel(value) {
      if (value == 0) {
        deleteDailyHandoverRecord(this.InspectionHandoverId).then(
          (response) => {
            if (response.code == 200) {
              this.DailyHandoverRecordId = response.data.id;
              if (this.formData.initialInspectionHandoverObj) {
                this.formData.initialInspectionHandoverObj.toUnitName = "";
                this.formData.initialInspectionHandoverObj.toUserName = "";
              } else {
                this.formData.inspectionHandoverRecords[
                  this.formData.inspectionHandoverRecords.length - 1
                ].toUnitName = "";
                this.formData.inspectionHandoverRecords[
                  this.formData.inspectionHandoverRecords.length - 1
                ].toUserName = "";
              }
            }
          }
        );
      }
    },
    //人员选择
    AstaffOrgTree(type, name) {
      this.treeNew = 1;
      this.type1 = type;
      this.title = "人员选择";
      this.url = "/colligate/violationDisposal/staffOrgTree";
      if (name) {
        this.selectTree = [{ name: name }];
      } else {
        this.selectTree = [];
      }
      this.VisibleRadioTree = true;
    },
    //组织树选择
    BstaffOrgTree(type, name) {
      this.treeNew = 2;
      this.type2 = type;
      this.title = "组织选择";
      this.url = "/colligate/violationDisposal/inspectionStaffOrgTree";
      if (name) {
        this.selectTree = [{ name: name }];
      } else {
        this.selectTree = [];
      }
      this.VisibleRadioTree = true;
    },
    //关闭
    close() {
      this.visible = false;
      this.$emit("close");
    },
    //确定选择的人员
    savePers() {
      this.$refs.radioTree.save();
    },
    //调用保存
    acceptList(array) {
      this.VisibleRadioTree = false;
      if (array.length) {
        if (this.treeNew === 1) {
          saveHandoverSecondaryRecord({
            toUserPost: array[0].id,
            businessTableId: this.relevantTableId,
            id: this.DailyHandoverRecordId,
            problemId: this.problemId,
          }).then((response) => {
            if (response.code === 200) {
              if (this.type1 === 1) {
                this.formData.initialSecondaryHandoverObj.toUnitName =
                  response.data.toUnitName;
                this.formData.initialSecondaryHandoverObj.toUserName =
                  response.data.toUserName;
              } else {
                this.formData.secondaryHandoverRecords[
                  this.formData.secondaryHandoverRecords.length - 1
                ].toUnitName = response.data.toUnitName;
                this.formData.secondaryHandoverRecords[
                  this.formData.secondaryHandoverRecords.length - 1
                ].toUserName = response.data.toUserName;
              }
            }
          });
        } else {
          saveHandoverInspectionRecord({
            toUserPost: array[0].id,
            businessTableId: this.relevantTableId,
            id: this.InspectionHandoverId,
            problemId: this.problemId,
          }).then((response) => {
            if (response.code === 200) {
              if (this.type2 === 1) {
                this.formData.initialInspectionHandoverObj.toUnitName =
                  response.data.toUnitName;
                this.formData.initialInspectionHandoverObj.toUserName =
                  response.data.toUserName;
              } else {
                this.formData.inspectionHandoverRecords[
                  this.formData.inspectionHandoverRecords.length - 1
                ].toUnitName = response.data.toUnitName;
                this.formData.inspectionHandoverRecords[
                  this.formData.inspectionHandoverRecords.length - 1
                ].toUserName = response.data.toUserName;
              }
            }
          });
        }
      }
    },
    /**初始化数据*/
    QueryVerifyRecord() {
      this.loading = true;
      let array = [];
      queryVerifyRecord(this.problemId).then((response) => {
        let specSelectedList = response.data.involveProfessionalLines;
        this.formData = { ...this.formData, ...response.data };
        for (let i = 0, len = specSelectedList.length; i < len; i++) {
          array.push(specSelectedList[i].specCode);
        }
        this.actualFlag = response.data.actualFlag;
        this.lossRiskTypeOptions = response.data.lossRiskTypeOptions;
        this.formData.specLists = array;
        this.specList = response.data.professionalLineOptions;
        this.relevantTableId = response.data.id;
        this.problemSourceList = response.data.problemSourceList;
        this.relevantTableName = response.data.businessTable;
        this.loading = false;
        if (!response.data.initialSecondaryHandoverObj) {
          if (
            response.data.secondaryHandoverRecords &&
            response.data.secondaryHandoverRecords.length
          ) {
            this.DailyHandoverRecordId =
              response.data.secondaryHandoverRecords[
                response.data.secondaryHandoverRecords.length - 1
              ].id;
          }
        } else {
          this.DailyHandoverRecordId =
            response.data.initialSecondaryHandoverObj.id;
        }

        if (!response.data.initialInspectionHandoverObj) {
          if (
            response.data.inspectionHandoverRecords &&
            response.data.inspectionHandoverRecords.length
          ) {
            this.InspectionHandoverId =
              response.data.inspectionHandoverRecords[
                response.data.inspectionHandoverRecords.length - 1
              ].id;
          }
        } else {
          this.InspectionHandoverId =
            response.data.initialInspectionHandoverObj.id;
        }
        this.$nextTick(() => {
          this.$refs.pers.DueryDepartmentSelectInfo();
          this.$refs.file.ViolationFileItems();
        });
        this.$emit("closeLoading");
      });
    },
    /**提交数据*/
    nextStep() {
      this.$emit("handle", 1, {
        identityFlag: "identity",
        isHandoverSecondUnitHandle: this.formData.isSecondUnitHandleFlag,
      });
    },
    /**保存数据*/
    publicSave() {},
    resetForm() {
      this.$refs["elForm"].resetFields();
    },
    //打开弹窗
    show() {
      this.visible = true;
    },
    //关闭弹窗
    closeTree() {
      this.visibleTree = false;
      this.$refs.pers.DueryDepartmentSelectInfo();
    },
    //选择人员
    treeOpen() {
      this.flag = !this.flag;
      this.visibleTree = true;
    },
    processData() {
      return processData;
    },
    //最终提交
    submitHisDataFun(){
      this.$confirm('是否提交？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const loading = this.$loading({
          spinner: 'el-icon-loading', // 自定义加载图标类名
          text: '正在加载...', // 显示在加载图标下方的加载文案
          lock: false, // lock的修改符--默认是false
        });
        submitHisData(this.problemId).then(
          response => {
            loading.close();
            this.$modal.msgSuccess("提交成功");
            this.closeEmit();
          }
        ).catch(err => {
          loading.close();
        })
      })
    },
    //流程提交后推进到下一个环节
    closeEmit(){
      this.$emit('hisNext')
    }
  },
};
</script>
<style>
.transfer-box {
}

.transfer-li {
  margin-bottom: 10px;
}

.transfer-li-info {
  height: 45px;
  padding: 0 10px;
  line-height: 45px;
  background-color: #ffffff;
  border-radius: 2px;
  border: solid 1px #d9d9d9;
}

.transfer-li-info span {
  font-size: 14px;
  color: #373d41;
}

.transfer-li-img {
  text-align: center;
  line-height: 45px;
}

.transfer-li-edit {
  line-height: 45px;
}

.transfer-li-edit .icon {
}
</style>
