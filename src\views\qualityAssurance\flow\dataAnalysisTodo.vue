<!--待办 -->
<template>
  <div class="group-index">
    <div class="group-index-box">
      <div class="group-header">
        贵单位{{auditInfo.auditYear}}年{{auditInfo.auditQuarterName}}报送数据存在异常。同日常报送、基础数据模块核对校验时发现，
        <span class="font-weight">{{auditInfo.differentFields}}</span>存在信息不一致情况，请核实原因。
      </div>
      <div class="group-reason">
        <el-form ref="basicInfoForm"  label-width="88px">
          <el-row>
            <el-col :span="24">
              <el-form-item label="不一致原因">
                <el-input
                  v-model="auditInfo.differentReason"
                  type="textarea"
                  maxlength="2000"
                  :autosize="{ minRows: 4, maxRows: 4 }"
                  :style="{ width: '100%' }"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </div>
    <div class="group-table">
      <BlockCard  title="数据不一致的省分">
        <template v-slot:left>
          <el-radio-group v-model="tabPosition" style="margin-left: 30px;" @input="queryNextList">
            <el-radio-button label="1" >实时数据</el-radio-button>
            <el-radio-button label="2">稽核数据</el-radio-button>
          </el-radio-group>
        </template>
        <el-form   style="height:340px">
          <el-table  ref="table" border v-loading="loading" :data="tableList"  height="100%">
            <el-table-column label="序号" type="index" min-width="6%" align="center" >
              <!--<template slot-scope="scope">-->
                <!--<table-index-->
                  <!--:index="scope.$index"-->
                  <!--:page-num="queryParams.pageNum"-->
                  <!--:page-size="queryParams.pageSize"-->
                <!--/>-->
              <!--</template>-->
            </el-table-column>
            <el-table-column label="省分" prop="provName" width="80"  show-overflow-tooltip align="center"/>
            <el-table-column label="当年累计新增配套制度"  align="center">
              <el-table-column label="基础数据" prop="totalNewSupportingSystem" width="120"   show-overflow-tooltip align="center"/>
              <el-table-column label="季度报告" prop="totalNewSupportingSystemQ" width="120"   show-overflow-tooltip align="center"/>
            </el-table-column>
            <el-table-column label="当年累计新增工作机制"  align="center">
              <el-table-column label="基础数据" prop="totalNewWorkSystem" width="120"  show-overflow-tooltip align="center"/>
              <el-table-column label="季度报告" prop="totalNewWorkSystemQ" width="120"  show-overflow-tooltip align="center"/>
            </el-table-column>
            <el-table-column label="本季度新受理问题线索数量"  align="center">
              <el-table-column label="日常报送" prop="quarterNewProblemNumber" width="120"  show-overflow-tooltip align="center"/>
              <el-table-column label="季度报告" prop="quarterNewProblemNumberQ" width="120"  show-overflow-tooltip align="center"/>
            </el-table-column>
            <el-table-column label="本季度涉及资产损失（万元）"  align="center">
              <el-table-column label="日常报送" prop="lossAmount" width="120"  show-overflow-tooltip align="right">
                <template slot-scope="scope">
                  {{ scope.row.lossAmount | filterNum() }}
                </template>
                </el-table-column>
              <el-table-column label="季度报告" prop="lossAmountQ" width="120"  show-overflow-tooltip align="right">
                <template slot-scope="scope">
                  {{ scope.row.lossAmountQ | filterNum }}
                </template>
              </el-table-column>
            </el-table-column>
            <el-table-column label="本季度涉及资产损失风险（万元）" prop="problemTitle" align="center">
              <el-table-column label="日常报送" prop="lossRisk" width="120"  show-overflow-tooltip align="right">
                <template slot-scope="scope">
                  {{ scope.row.lossRisk | filterNum }}
                </template>
              </el-table-column>
              <el-table-column label="季度报告" prop="lossRiskQ" width="120"  show-overflow-tooltip align="right">
                <template slot-scope="scope">
                  {{ scope.row.lossRiskQ | filterNum }}
                </template>
              </el-table-column>
            </el-table-column>
            <!--<el-table-column label="当年累计受理问题线索数量"  align="center">-->
              <!--<el-table-column label="日常报送" prop="totalProblemSourceNumber" min-width="10%"  show-overflow-tooltip align="left"/>-->
              <!--<el-table-column label="季度报告" prop="totalProblemSourceNumberQ" min-width="10%"  show-overflow-tooltip align="left"/>-->
            <!--</el-table-column>-->
            <el-table-column label="当年累计受理问题线索数量"  align="center">
              <el-table-column label="日常报送" prop="totalProblemSourceNumber" width="120"  show-overflow-tooltip align="center"/>
              <el-table-column label="季度报告" prop="totalProblemSourceNumberQ" width="120" show-overflow-tooltip align="center"/>
            </el-table-column>

            <el-table-column label="上年结转问题线索数量"  align="center">
              <el-table-column label="日常报送" prop="lastYearProblemSourceNumber" width="120"  show-overflow-tooltip align="center"/>
              <el-table-column label="季度报告" prop="lastYearProblemSourceNumberQ" width="120"  show-overflow-tooltip align="center"/>
            </el-table-column>

            <el-table-column label="当年累计完成追责问题数量（件）"  align="center">
              <el-table-column label="日常报送" prop="totalCompletedProblemNumber" width="120"  show-overflow-tooltip align="center"/>
              <el-table-column label="季度报告" prop="totalCompletedProblemNumberQ" width="120"  show-overflow-tooltip align="center"/>
            </el-table-column>

            <el-table-column label="当年累计追责总人数（人）"  align="center">
              <el-table-column label="日常报送" prop="totalAccountabilityPersonNumber" width="120"  show-overflow-tooltip align="center"/>
              <el-table-column label="季度报告" prop="totalAccountabilityPersonNumberQ" width="120"  show-overflow-tooltip align="center"/>
            </el-table-column>

            <el-table-column label="当年累计追责总人次"  align="center">
              <el-table-column label="日常报送" prop="totalAccountabilityPersonTime" width="120"  show-overflow-tooltip align="center"/>
              <el-table-column label="季度报告" prop="totalAccountabilityPersonTimeQ" width="120"  show-overflow-tooltip align="center"/>
            </el-table-column>

            <el-table-column label="当年累计追责：组织处理（人次）"  align="center">
              <el-table-column label="日常报送" prop="orgHandleTime" width="120"  show-overflow-tooltip align="center"/>
              <el-table-column label="季度报告" prop="orgHandleTimeQ" width="120"  show-overflow-tooltip align="center"/>
            </el-table-column>

            <el-table-column label="当年累计追责：扣减薪酬（人次）"  align="center">
              <el-table-column label="日常报送" prop="deductionSalaryTime" width="120"  show-overflow-tooltip align="center"/>
              <el-table-column label="季度报告" prop="deductionSalaryTimeQ" width="120"  show-overflow-tooltip align="center"/>
            </el-table-column>

            <el-table-column label="当年累计追责：党纪处分（人次）"  align="center">
              <el-table-column label="日常报送" prop="partyPunishmentTime" width="120"  show-overflow-tooltip align="center"/>
              <el-table-column label="季度报告" prop="partyPunishmentTimeQ" width="120" show-overflow-tooltip align="center"/>
            </el-table-column>

            <el-table-column label="当年累计追责：移送监察机关或司法机关（人次）"  align="center">
              <el-table-column label="日常报送" prop="transferAuthorityTime" width="120"  show-overflow-tooltip align="center"/>
              <el-table-column label="季度报告" prop="transferAuthorityTimeQ" width="120"  show-overflow-tooltip align="center"/>
            </el-table-column>

            <el-table-column label="当年累计追责：其他（人次）"  align="center">
              <el-table-column label="日常报送" prop="" width="120"  show-overflow-tooltip align="center"/>
              <el-table-column label="季度报告" prop="" width="120"  show-overflow-tooltip align="center"/>
            </el-table-column>

            <el-table-column label="当年累计扣减薪酬金额（万元）"  align="center">

              <el-table-column label="日常报送" prop="totalDeductionSalary" width="120"  show-overflow-tooltip align="right">
                <template slot-scope="scope">
                  {{ scope.row.totalDeductionSalary | filterNum }}
                </template>
              </el-table-column>
              <el-table-column label="季度报告" prop="totalDeductionSalaryQ" width="120"  show-overflow-tooltip align="right">
                <template slot-scope="scope">
                  {{ scope.row.totalDeductionSalaryQ | filterNum }}
                </template>
              </el-table-column>
            </el-table-column>

            <el-table-column label="当年累计挽回资产损失（万元）"  align="center">
              <el-table-column label="日常报送" prop="totalRetrieveLossAmount" width="120"  show-overflow-tooltip align="right">
                <template slot-scope="scope">
                  {{ scope.row.totalRetrieveLossAmount | filterNum }}
                </template>
              </el-table-column>
              <el-table-column label="季度报告" prop="totalRetrieveLossAmountQ" width="120"  show-overflow-tooltip align="right">
                <template slot-scope="scope">
                  {{ scope.row.totalRetrieveLossAmountQ | filterNum }}
                </template>
              </el-table-column>
            </el-table-column>

            <el-table-column label="当年累计降低损失风险（万元）"  align="center">
              <el-table-column label="日常报送" prop="totalReduceLossRisk" width="120"  show-overflow-tooltip align="right">
                <template slot-scope="scope">
                  {{ scope.row.totalReduceLossRisk | filterNum }}
                </template>
              </el-table-column>
              <el-table-column label="季度报告" prop="totalReduceLossRiskQ" width="120"  show-overflow-tooltip align="right">
                <template slot-scope="scope">
                  {{ scope.row.totalReduceLossRiskQ | filterNum }}
                </template>
              </el-table-column>
            </el-table-column>

            <el-table-column label="当年累计制修订管理制度（项）"  align="center">
              <el-table-column label="日常报送" prop="totalPerfectSystemNumber" width="120"  show-overflow-tooltip align="center"/>
              <el-table-column label="季度报告" prop="totalPerfectSystemNumberQ" width="120"  show-overflow-tooltip align="center"/>
            </el-table-column>

            <el-table-column label="子企业管理干部"  align="center">
              <el-table-column label="日常报送" prop="subManagementNumber" width="120"  show-overflow-tooltip align="center"/>
              <el-table-column label="季度报告" prop="subManagementNumberQ" width="120"  show-overflow-tooltip align="center"/>
            </el-table-column>

            <el-table-column label="问题状态统计" prop="problemStatusCollect" width="240"  show-overflow-tooltip align="left"/>

            <!--<el-table-column label="不一致原因" prop="problemTitle" min-width="10%"  show-overflow-tooltip align="left"/>-->
          </el-table>
        </el-form>
        <pagination
          v-show="total>0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="queryProvRealList"
        />
      </BlockCard>
    </div>
    <div></div>
  </div>
</template>

<script>

import BlockCard from "@/components/BlockCard";
import {queryProvDataAuditInfo
  ,queryProvRealList
  ,queryProvAuditList
  ,saveDifferentReason
  ,validateAuditInfo
   } from "@/api/qualityAssurance/index";
export default {
  name: "groupDetail",
  components: {
    BlockCard
  },
  props: {
    closeBtn: {
      type: Function,
      default: null,
    },
    //流程参数
    centerVariable: {
      type: Object
    }
  },
  data() {
    return {
      reason:'',//原因
      tabPosition: '1',
      loading:false,
      rows:{},
      // 总条数
      total: 0,
      // 表格数据
      tableList: [],
      //查询 参数
      queryParams: {
        pageNum: 1,
        pageSize: 10
      },
      auditId:'',
      auditInfo:{
        provName:'',
        auditYear:'',
        auditQuarter:'',
        auditQuarterName:'',
        differentReason:'',
        differentFields:'',
      }
    };
  },
  created() {
    this.auditId = this.centerVariable.busiKey;
    this.queryProvDataAuditInfo();
    this.queryProvRealList();
    this.$emit('collocation',{
      refreshAssigneeUrl:'/dataaudit/flow',//业务url
      saveBtn:true,//保存按钮
    })
  },
  methods: {

    //查询稽核信息,传‘db’，始终查询不一致原因
    queryProvDataAuditInfo(){
      queryProvDataAuditInfo({auditId:this.auditId,type:'db'}).then(
        response => {
          this.auditInfo = response.data;
        }
      );
    },
    /**数据质量管理列表-系统实时数据*/
    queryProvRealList() {
      this.loading = true;
      queryProvRealList({auditId:this.auditId}).then(
        response => {
          this.tableList = response.data;
          // this.total = response.total;
          this.loading = false;
        }
      );
    },
    /**数据质量管理列表-稽核数据*/
    queryProvAuditList() {
      this.loading = true;
      queryProvAuditList({auditId:this.auditId}).then(
        response => {
          this.tableList = response.data;
          // this.total = response.total;
          this.loading = false;
        }
      );
    },

    queryNextList(){
      if(this.tabPosition=='1'){//实时数据
        this.queryProvRealList()
      }else{//稽核数据
        this.queryProvAuditList()
      }
    },
    //保存
    publicSave(){
      this.loading = true;
      var params={
        id:this.auditId
        ,differentReason:this.auditInfo.differentReason
      }
      saveDifferentReason(params).then(
        response => {
          this.loading = false;
          if(response.code == 200){
            this.$modal.msgSuccess('保存成功！')
          }else{
            this.$modal.msgError(response.msg);
          }
        }
      )

    },
    //额外参数
    loadProcessData(){
      return {branch:1}
    },
    //校验
    passValidate(){
      if (!this.auditInfo.differentReason) {
        this.$modal.msgError("【不一致原因】不能为空！");
        return false;
      }
      //保存
      saveDifferentReason({
        id:this.auditId
        ,differentReason:this.auditInfo.differentReason
      }).then(
        response => {
          if(response.code == 200){
            validateAuditInfo(this.auditId).then(
              res=>{
                if(res.code == 200){
                  this.passValidateJup()
                }else{
                  this.$modal.msgWarning(res.msg);
                  return false;
                }
              })
          }else{
            this.$modal.msgError(response.msg);
          }
        }
      )
      return false;
    },
    //接口校验异步校验时
    passValidateJup(){
      this.$emit('nextStep',true)
    },
  }
}
</script>

<style scoped lang="scss">
.group-index{
  .group-index-box{
    padding:10px 20px;
    box-sizing: border-box;
    .group-header{
      display: inline-block;
      padding:7px 100px 7px 25px;
      background-image: linear-gradient(90deg, #FFE5E5 0%, #FFFFFF 100%);
      position: relative;
      font-size: 15px;
      color: #333333;
      letter-spacing: 0;
      font-weight: 500;
      &::before{
        position: absolute;
        content: "";
        left: 8px;
        top: 14px;
        width: 7px;
        height: 7px;
        border-radius: 50%;
        background: #F5222D;
      }
    }
    .group-reason{
      border-radius: 2px;
      margin:12px 0 0;
      box-sizing: border-box;
    }
  }
  .group-table{
    ::v-deep .el-radio-button--medium .el-radio-button__inner {
      padding: 6px 20px;
      margin-right:12px;
      border-left:1px solid #ddd;
      border-radius:4px;
    }
  }
}
</style>
