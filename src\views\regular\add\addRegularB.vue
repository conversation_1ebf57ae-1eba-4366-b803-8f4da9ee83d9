<!--新增上报-->
<template>
  <div class="regular">
    <el-dialog  :visible.sync="visible" @close="onClose"  :modal-append-to-body="false" append-to-body  :title="title" width="90%">
      <el-form ref="elForm" :model="formData" :rules="rules" size="medium" label-width="118px">
        <el-col :span="6">
          <el-form-item label="报告类型" prop="reportType">
            <span>{{formData.reportType==='1'?'定期上报':'其他上报'}}</span>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="上报年度" prop="reportYear">
            <span>{{formData.reportYear}}</span>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="上报区间" prop="reportTime">
            <span>{{formData.reportStartTime}}  -  {{formData.reportEndTime}}</span>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="上报截止日期" prop="reportCloseTime">
            <el-date-picker editable = "false" :style="{width: '100%'}"
                            :picker-options="pickerOptions1"
                            v-model="formData.reportCloseTime" value-format="yyyy-MM-dd"
                            type="date" placeholder="上报截止日期">
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="标题" prop="reportTitle">
            <el-input v-model="formData.reportTitle" placeholder="标题" clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="上报要求" prop="reportRequire">
            <el-input v-model="formData.reportRequire" type="textarea" placeholder="上报要求"
                      :autosize="{minRows: 4, maxRows: 4}"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <RegularGlobalTemplate :key="regularReportId" :edit="true" :regularReportId="regularReportId"></RegularGlobalTemplate>
        </el-col>
        <!--新增上报单位-->
        <el-col :span="24">
          <ReportDepartment
            v-if="showDepartment"
            :key="index"
            :edit="true"
            :regularReportId="regularReportId"
            v-on:toSetUnitList = "toSetUnitList"
            ref="reportDepartment"
          ></ReportDepartment>
        </el-col>
      </el-form>
      <div slot="footer">
        <el-button size="mini" type="primary" @click="toSaveReportData">保存</el-button>
        <el-button size="mini" type="primary" @click="submitForm">提交</el-button>
        <el-button size="mini" @click="close">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import {
    getRegularReport//新增加载数据
    ,selectReportInfo//编辑加载数据
    ,checkReportTime
    ,saveReportData
    ,checkReportData
    ,submitRegularReport} from "@/api/regular/add/addAndEditRegular";//新增js
  import ReportDepartment from "./reportDepartment";//上报单位
  import RegularGlobalTemplate from "@/views/regular/common/regularGlobalTemplate";

  export default {
    inheritAttrs: false,
    name: "addAndEditRegular"
    ,components: {
      ReportDepartment,
      RegularGlobalTemplate
    }
    ,props: {
      parentId:{
        type:String,
        default:''
      }
      ,title:{
        type:String,
        default: '新增上报'
      }
    },
    data() {
      return {
        regularReportId:'',
        edit:true,
        visible:false,//弹框
        formData:{
          id:''
          ,reportType:undefined//报告类型 1:定期  2：其他
          ,reportYear:undefined//上报年度
          ,reportTime:[]//上报区间
          ,reportStartTime:undefined//上报区间-开始
          ,reportEndTime:undefined//上报区间-结束
          ,reportCloseTime:undefined//上报截止日期
          ,reportTitle:undefined//标题
          ,reportRequire:undefined//上报要求
          ,reportProvCode:undefined
          ,reportProvName:undefined
          ,orgGrade:undefined
        }
        ,pickerOptions1: {
          disabledDate:(time)=>{
            return time.getTime() < Date.now()-1 * 24 * 3600 * 1000;
          }
        }
        ,showDepartment:false//上报单位是否加载
        ,unitList:[]//上报单位
        ,reportType:''//1：定期 2：其他
        ,reportYear:''//上报年度
      }
    }
    ,created(){
    }
    ,methods:{
      // 显示弹框
      show() {
        this.visible = true;
        this.getRegularReport1();
      },
      onClose(){
        this.$refs['queryForm'].resetFields()
      },
      //关闭弹窗
      close(){
        this.visible = false;
        this.$emit("ViolRegularList");
      },
      //初始化数据
      getRegularReport1(){
          selectReportInfo(this.parentId).then(
            response => {
              this.formData = response.data;
              this.reportType = this.formData.reportType;
              this.GetRegularReport();
            }
          )
      },
      //初始化数据
      GetRegularReport(){
        getRegularReport().then(
          response => {
            this.regularReportId = response.data.id;
            this.formData.regularReportId = response.data.id;
            this.formData.id = response.data.id;
            this.formData.orgGrade = response.data.orgGrade;
            this.formData.reportProvCode = response.data.reportProvCode;
            this.formData.reportProvName = response.data.reportProvName;
            this.$nextTick(()=>{
              //加载上报单位
              this.showDepartment = true;
            })
          }
        )
      },
      //上报区间校验是否存在同一年度区间的上报数据
      toCheckReportTime(data){
        this.formData.reportStartTime = data[0];
        this.formData.reportEndTime = data[1];
        //定期上报校验
        if(this.reportType === '1'){
          this.toRealCheckReportTime();
        }
      },
      //上报年度校验是否存在同一年度区间的上报数据
      reportYearToCheckReportTime(data){
        //重新选择上报年度，则重新校验
        if(this.reportYear != data){
          this.reportYear = data;
          //定期上报校验
          if(this.reportType === '1'){
            this.toRealCheckReportTime();
          }
        }
      },
      //上报类型校验是否存在同一年度区间的上报数据
      reportTypeToCheckReportTime(data){
        this.formData.reportType = data;
        if(this.reportType != data){
          this.reportType = data;
          //定期上报校验
          if(this.reportType === '1'){
            this.toRealCheckReportTime();
          }
        }
      },
      //最终去校验同一年度区间内是否有上报数据的方法
      toRealCheckReportTime(){
        checkReportTime(this.regularReportId,this.formData.reportStartTime,this.formData.reportType,this.formData.reportYear,this.formData.reportEndTime).then(
          response => {
            if (response.code == "200") {
              var data = response.data;
              if (data.codeFlag == true) {//校验通过
              } else {//未通过
                this.formData.reportTime = '';
                this.formData.reportStartTime = '';
                this.formData.reportEndTime = '';
                this.$modal.msgError(data.codeText);
              }
            } else {
              this.$modal.msgError(response.msg);
            }
          }
        )
      },
      //获取上报单位
      toSetUnitList(data){
        this.unitList = data;
      },
      //保存数据
      toSaveReportData(){
        saveReportData(this.formData,this.unitList).then(
          response => {
            if (response.code == "200") {
              this.$modal.msgSuccess(response.msg);
              this.close();
            } else {
              this.$modal.msgError(response.msg);
            }
          }
        )
      },
      //提交
      submitForm() {
        //保存数据
        saveReportData(this.formData,this.unitList).then(
          response => {
            if (response.code == "200") {
              this.toCheckReportData();
            } else {
              this.$modal(res.msg);
            }
          }
        )
      },
      //校验
      toCheckReportData(){
        if (!this.formData.reportType) {
          this.$modal.msgError("请选择【报告类型】！");
          return false;
        } else if (!this.formData.reportYear) {
          this.$modal.msgError("请选择【上报年度】！");
          return false;
        } else if (!this.formData.reportStartTime) {
          this.$modal.msgError("请选择【上报区间】！");
          return false;
        } else if (!this.formData.reportCloseTime) {
          this.$modal.msgError("请选择【上报截止日期】！");
          return false;
        } else if (!this.formData.reportTitle) {
          this.$modal.msgError("【标题】不能为空！");
          return false;
        } else if (!this.formData.reportRequire) {
          this.$modal.msgError("【上报要求】不能为空！");
          return false;
        } else if (this.unitList.length == 0) {
          this.$modal.msgError("请选择【上报单位】！");
          return false;
        }
        //后端校验
        checkReportData(this.regularReportId).then(
          response => {
            if (response.code == "200") {
              if (response.data.codeFlag) {
                this.todoSubmit();
              } else {
                this.$modal.msgError(response.data.codeText);
              }
            } else {
              this.$modal.msgError(response.msg);
            }
          }
        )
      },
      //调用提交
      todoSubmit(){
        this.$modal.confirm('是否确认提交上报？').then(
          ()=> {
            const loading = this.$loading({
              spinner: 'el-icon-loading', // 自定义加载图标类名
              text: '正在加载...', // 显示在加载图标下方的加载文案
              lock: false, // lock的修改符--默认是false
            });
            submitRegularReport(this.regularReportId).then(
              response => {
                loading.close();
                if(response.code === 200){
                  this.$modal.msgSuccess(response.msg);
                  this.close();
                }else {
                  this.$modal.msgError(response.msg);
                }
              }
            ).catch(err => {
              loading.close();
            })
          }
        )
      },
    }
  }
</script>

<style lang="scss">
  .regular{
  .el-dialog__body{
    padding-top:20px  !important;
    background: #fff !important;
  }
  }
</style>
