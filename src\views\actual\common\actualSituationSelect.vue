<template>
    <div class="scope">
      <el-dialog v-bind="$attrs" :visible.sync="visible" width="80%" @open="onOpen" @close="onClose" append-to-body :title="title">
          <Jscrollbar height="68vh">
          <el-form :model="queryParams" ref="queryForm" id="queryParams" :inline="true" label-width="45px">
            <el-form-item label="方面" prop="status">
              <el-select v-model="queryParams.aspectCode" placeholder="方面" :clearable="true" size="small">
                <el-option v-for="item in aspectList" :key="item.code" :value="item.code" :label="item.codeText"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="情形">
              <el-input v-model="queryParams.situationName" :style="{width: '100%'}"></el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="el-icon-search" size="mini" @click="queryAspectSituateList">搜索</el-button>
              <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
          <el-form style="height: calc(100% - 60px)">
            <el-table v-loading="loading" :data="tableList" ref="table" @selection-change="handleSelectionChange" height="100%">
              <el-table-column type="selection" min-width="10%" fixed="left"/>
              <el-table-column label="序号" type="index" min-width="10%" align="center" />
              <el-table-column label="违规经营投资责任追究方面名称" prop="aspectName" min-width="30%"/>
              <el-table-column label="违规经营投资责任追究情形名称" prop="situationName"  min-width="50%"/>
            </el-table>
          </el-form>
          </Jscrollbar>
        <div slot="footer">
          <el-button size="mini" @click="close">取消</el-button>
          <el-button size="mini" type="primary" @click="handelConfirm">确定</el-button>
        </div>
      </el-dialog>
    </div>
</template>

<script>
  import {queryAspectSituateList,queryRangeAspectList} from "@/api/daily/scopeSituation/index";
  import {situationCheckModalData, saveActualSituationData} from "@/api/actual/common/actualSituationRange";
  export default {
    name: "actualSituationSelect",
    props: {
      actualProblemId: '',
      relevantTableId: '',
      relevantTableName: ''
    },
    data() {
      return {
        loading: false,
        title: '违规经营投资责任范围情形列表',
        visible: false,
        status: '',
        showSearch: true,
        total: 0,
        tableList: [],
        hasSelectList: [],
        aspectList: [],
        queryParams: {
          problemId: this.actualProblemId,
          actualProblemId: this.actualProblemId,
          relevantTableId: this.relevantTableId,
          relevantTableName: this.relevantTableName,
          aspectCode: '',
          situationName: ''
        }
      }
    },
    created() {
      this.queryRangeAspectList();
    },
    methods: {
      queryAspectSituateList() {
        this.loading = true;
        situationCheckModalData(this.queryParams).then(response => {
          this.tableList = response.data;
          this.total = response.data.length;
          this.$nextTick(() => {
            this.tableList.forEach(row => {
              if (row.checked) {
                this.$refs.table.toggleRowSelection(row, true);
              }
            });
          });
          this.loading = false;
        });
      },

      queryRangeAspectList() {
        queryRangeAspectList().then(response => {
          this.aspectList = response.data.aspectList;
        });
      },

      resetQuery() {
        this.queryParams.aspectCode = "";
        this.queryParams.situationName = "";
        this.queryAspectSituateList();
      },

      show() {
        this.visible = true;
        this.queryAspectSituateList();
      },

      onOpen() {},

      onClose() {},

      close() { this.visible = false; },

      handleSelectionChange(checkeds) {
        let checkedArray = [];
        checkeds.forEach(function (checkedItem) {
          checkedArray.push(checkedItem);
        });
        this.queryParams.actualRanges = checkedArray;
      },

      handelConfirm() {
        saveActualSituationData(this.queryParams).then(response => {
          if (200 === response.code) {
            this.$modal.msgSuccess("保存成功");
            this.$emit('queryRangeList');
            this.close();
          } else {
            this.$modal.alertError(response.msg);
          }
        });
      }

    }
  }
</script>

<style lang="scss">
  .scope{
    .el-dialog__body{
      height: 80vh;
      overflow: auto;
    }
  }
</style>
