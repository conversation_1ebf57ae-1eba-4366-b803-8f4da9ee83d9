<template>
  <el-dialog
    :title="modalTitle"
    :visible.sync="dialogVisible"
    width="50%"
    :before-close="handleClose">
    <el-row :gutter="20">
      <el-col :span="15">
        <el-card class="box-card"  shadow="never" style=" height:64vh">
          <div slot="header" class="clearfix">
            <span>待选择组织</span>
            <el-row :gutter="10" style="margin-top: 10px; border-top: 1px solid #3333;">
              <el-col :span="18">
                <el-input
                  style="margin-top: 20px;"
                  placeholder="输入关键字进行过滤"
                  v-model="filterText">
                </el-input>
              </el-col>
              <el-col :span="4">
                <el-button  type="primary" icon="el-icon-search" size="mini" style="margin-top: 20px;" @click="handleQuery">搜索</el-button>
              </el-col>
            </el-row>
          </div>
          <div class="card_height ">
            <radioTree v-on:selectNode="selectNode" :treeList="treeList"></radioTree>
          </div>
        </el-card>
      </el-col>
      <el-col :span="9">
        <el-card class="box-card"  shadow="never">
          <div slot="header" class="clearfix">
            <span>已选择组织</span>
          </div>
          <div style="margin-top: 10px; border-top: 1px solid #3333;">
            <div style="margin-top: 20px;">{{selectNodeData.name}}</div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    <span slot="footer" class="dialog-footer">
      <el-button size="mini" @click="handleClose()">取 消</el-button>
      <el-button size="mini" type="primary" @click="sub()">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script lang="ts">
  import radioTree from "./radioTree";

  export default {
    name: "orgTreeModal",
    components: { radioTree },
    props: {
      treeList: {
        type: Array,
        default: ()=>{ return []}
      },
      dialogVisible: {
        type: Boolean,
        default: true
      },
      modalTitle: {
        type: String,
        default: ''
      },
      nodeData: {
        type: Object,
        default: ()=>{return {}}
      },
    },
    data() {
      return {
        filterText: '',
        defaultProps: {
          children: 'children',
          label: 'label'
        },
        selectNodeData:{},
        // selectIds:[],
      };
    },
    created() {
      this.selectNodeData = this.nodeData;
      // this.selectIds = [this.nodeData.id]
    },
    methods: {
      /**关闭模态框*/
      handleClose() {
        this.$emit("closeModal");

      },
      /**选择节点*/
      selectNode(data) {
        this.selectNodeData = data
      },
      handleQuery(){
        this.$emit("getTreeData", this.filterText);
      },
      sub(){
        this.$emit("getOrg", this.selectNodeData);
        this.handleClose();
      }
    }
  };
</script>

<style>
  .text {
    font-size: 14px;
  }

  .item {
    margin-bottom: 18px;
  }

  .clearfix:before,
  .clearfix:after {
    display: table;
    content: "";
  }
  .clearfix:after {
    clear: both
  }
  .el-card__body{
    height:calc(100% - 120px) !important;
    overflow:auto;
    padding-top: 0px !important;
  }
</style>
