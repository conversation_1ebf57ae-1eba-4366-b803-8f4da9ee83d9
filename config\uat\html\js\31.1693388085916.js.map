{"version": 3, "sources": ["webpack:///src/views/redirect.vue", "webpack:///./src/views/redirect.vue", "webpack:///./src/views/redirect.vue?040c"], "names": ["created", "_this$$route", "$route", "params", "query", "path", "$router", "replace", "render", "h"], "mappings": ";;;;;;;;;;;;;;;;;AACe;EACfA,OAAA,WAAAA,QAAA;IACA,IAAAC,YAAA,QAAAC,MAAA;MAAAC,MAAA,GAAAF,YAAA,CAAAE,MAAA;MAAAC,KAAA,GAAAH,YAAA,CAAAG,KAAA;IACA,IAAAC,IAAA,GAAAF,MAAA,CAAAE,IAAA;IACA,KAAAC,OAAA,CAAAC,OAAA;MAAAF,IAAA,QAAAA,IAAA;MAAAD,KAAA,EAAAA;IAAA;EACA;EACAI,MAAA,WAAAA,OAAAC,CAAA;IACA,OAAAA,CAAA;EACA;AACA,CAAC,E;;;;;;;;;;;;ACVD;AAAA;AAAA;AAAA;AAC4D;AACL;;;AAGvD;AAC0F;AAC1F,gBAAgB,2GAAU;AAC1B,EAAE,8EAAM;AACR;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA,IAAI,KAAU,EAAE,YAYf;AACD;AACe,gF;;;;;;;;;;;;ACjCf;AAAA;AAAA,wCAAqR,CAAgB,yUAAG,EAAC,C", "file": "js/31.1693388085916.js", "sourcesContent": ["<script>\r\nexport default {\r\n  created() {\r\n    const { params, query } = this.$route\r\n    const { path } = params\r\n    this.$router.replace({ path: '/' + path, query })\r\n  },\r\n  render: function(h) {\r\n    return h() // avoid warning message\r\n  }\r\n}\r\n</script>\r\n", "var render, staticRenderFns\nimport script from \"./redirect.vue?vue&type=script&lang=js&\"\nexport * from \"./redirect.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\n/* hot reload */\nif (module.hot) {\n  var api = require(\"E:\\\\workspace\\\\info\\\\trigram-ui\\\\node_modules\\\\vue-hot-reload-api\\\\dist\\\\index.js\")\n  api.install(require('vue'))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!api.isRecorded('62d4e7f6')) {\n      api.createRecord('62d4e7f6', component.options)\n    } else {\n      api.reload('62d4e7f6', component.options)\n    }\n    \n  }\n}\ncomponent.options.__file = \"src/views/redirect.vue\"\nexport default component.exports", "import mod from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./redirect.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./redirect.vue?vue&type=script&lang=js&\""], "sourceRoot": ""}