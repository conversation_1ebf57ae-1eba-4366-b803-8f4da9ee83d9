<template>
    <div>
      <div v-for="(item,index) in formData">
        <div class="right-report-detail-title">
        <p class="right-report-detail-span float-left">
          {{index+1}}、{{item.violationCase}}<span style="font-weight: normal;font-size: 14px;">（{{item.reportStageName}}）</span>
        </p>
        <div class="float-right" v-if="edit">
          <el-button
            type="danger"
            plain
            icon="el-icon-delete"
            size="mini"
            @click="deleteReport(item.realtimeReportId,index,item.reportSasacId,item.reportStageId)"
          >删除
          </el-button>
        </div>
      </div>
      <!-- 基本信息 -->
      <BlockCard title="基本信息">
        <el-form class="common-card padding10_0" size="medium"  label-width="160px">
          <el-row>
            <el-col :span="8">
              <el-form-item label="央企集团名称"><span>{{item.groupName}}</span></el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="集团简称">{{item.groupSortName}}</el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="统一社会信用代码">{{item.uscCode}}</el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="行业名称">{{item.industryName}}</el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="行业代码">{{item.industryCode}}</el-form-item>
            </el-col>
            <el-col :span="16">
              <el-form-item label="涉及企业名称">{{item.involveCompany}}</el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="涉及企业级次">{{item.involveCompanyLevel}}</el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="违规事项">{{item.violationCase}}</el-form-item>
            </el-col>

            <el-col :span="24">
              <el-form-item label="报告主题">{{item.reportTitle}}</el-form-item>
            </el-col>
            <el-col :span="16">
              <el-form-item label="实时报告阶段名称">{{item.reportStageName}}</el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="实时报告编码">{{item.realTimeReportCode}}</el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="预估损失风险（万元）">{{item.estimateLoss}}</el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="发现时间">{{item.occurrenceDate}}</el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="报送次数">{{item.reportNumber}}</el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="报告年份">{{item.reportYear}}</el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="报告时间">{{item.reportDate}}</el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </BlockCard>
      <!-- 5day -->
      <BlockCard title="5个工作日实时报告快报" v-if="item.reportStageCode==1">
        <el-form class="common-card padding10_0" size="medium"  label-width="160px">
          <el-row>
            <el-col :span="8">
              <el-form-item label="报告阶段名称"><span>{{item.reportStageName}}</span></el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="阶段码"><span>{{item.reportStageCode}}</span></el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="阶段上报时间"><span>{{item.reportStageDate}}</span></el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="问题和线索来源"><span>{{item.problemResource}}</span></el-form-item>
            </el-col>
            <el-col :span="16">
              <el-form-item label="问题类别"><span>{{item.problemType}}</span></el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="问题和线索描述"><span>{{item.problemDescription}}</span></el-form-item>
            </el-col>
            <el-col :span="16">
              <el-form-item label="其他严重不良后果"><span>{{item.badResult}}</span></el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="预估损失风险（万元）"><span>{{item.estimateLoss}}</span></el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="涉及企业联系人"><span>{{item.companyContacter}}</span></el-form-item>
            </el-col>
            <el-col :span="16">
              <el-form-item label="联系电话"><span>{{item.companyContacterTel}}</span></el-form-item>
            </el-col>

            <el-col :span="8">
              <el-form-item v-if="edit" label="央企联系人">
                <el-input @blur="myFunction" v-if="edit" v-model="item.groupContacter"></el-input>
                <p v-else class="layui-input-text">{{item.groupContacter}}</p>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="央企联系人电话">
                <el-input @blur="myFunction" v-if="edit" v-model="item.groupContacterTel"></el-input>
                <p v-else class="layui-input-text">{{item.groupContacterTel}}</p>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </BlockCard>
      <!-- 15day -->
      <BlockCard v-if="item.reportStageCode==2" title="15个工作日实时报告">
        <el-form class="common-card padding10_0" size="medium"  label-width="160px">
        <el-row>
          <el-col :span="8">
            <el-form-item label="报告阶段名称"><span>{{item.reportStageName}}</span></el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="阶段码"><span>{{item.reportStageCode}}</span></el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="阶段上报时间"><span>{{item.reportStageDate}}</span></el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="涉及企业基本情况"><span>{{item.involveCompany15}}</span></el-form-item>
          </el-col>

          <el-col :span="24">
            <el-form-item label="问题和线索来源描述"><span>{{item.problemDescription}}</span></el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="问题性质"><span>{{item.problemProperties}}</span></el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="主要原因"><span>{{item.principalReason}}</span></el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="初步核实违规违纪情况"><span>{{item.violationContent}}</span></el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="已采取的应对措施"><span>{{item.taskSteps}}</span></el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="已开展的应对处置、成效"><span>{{item.results}}</span></el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="下一步工作安排"><span>{{item.nextWork}}</span></el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="备注"><span>{{item.remark}}</span></el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="涉及企业联系人"><span>{{item.contactPerson}}</span></el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="联系电话"><span>{{item.contactTel}}</span></el-form-item>
          </el-col>
        </el-row>
        </el-form>
      </BlockCard>
      <!-- 30day -->
      <BlockCard v-if="item.reportStageCode==3" title="30 个工作日初核报告">
        <el-form class="common-card padding10_0" size="medium"  label-width="160px">
          <el-row>
            <el-col :span="8">
              <el-form-item label="报告阶段名称"><span>{{item.reportStageName}}</span></el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="阶段码"><span>{{item.reportStageCode}}</span></el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="阶段上报时间"><span>{{item.reportStageDate}}</span></el-form-item>
            </el-col>


            <el-col :span="24">
              <el-form-item label="工作开展情况"><span>{{item.workDevelopment}}</span></el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="资产损失及其他严重不良后果"><span>{{item.assetsLossBadResult}}</span></el-form-item>
            </el-col>

            <el-col :span="24">
              <el-form-item label="存在的主要问题"><span>{{item.principalProblem}}</span></el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="初步核实违规违纪情况"><span>{{item.violationContent}}</span></el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="初步核实是否属于责任追究范围"><span>{{item.accountabilityScope}}</span></el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="有关方面处置建议和要求"><span>{{item.disposalRequirements}}</span></el-form-item>
            </el-col>

            <el-col :span="24">
              <el-form-item label="已开展的应对处置、成效"><span>{{item.results}}</span></el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="提出处置意见、后期工作安排"><span>{{item.ideaNextWork}}</span></el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="备注"><span>{{item.remark}}</span></el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="涉及企业联系人"><span>{{item.contactPerson}}</span></el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="联系电话"><span>{{item.contactTel}}</span></el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </BlockCard>
      <!-- progress -->
      <BlockCard v-if="item.reportStageCode==4" title="后续工作进展情况报告">
        <el-form class="common-card padding10_0" size="medium"  label-width="160px">
          <el-row>
            <el-col :span="8">
              <el-form-item label="报告阶段名称"><span>{{item.reportStageName}}</span></el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="阶段码"><span>{{item.reportStageCode}}</span></el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="阶段上报时间"><span>{{item.reportStageDate}}</span></el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </BlockCard>
      <!-- check -->
      <!-- 核查处置结果报告 -->
      <BlockCard v-if="item.reportStageCode==100" title="核查处置结果报告">
        <el-form class="common-card padding10_0" size="medium"  label-width="160px">
          <el-row>
            <el-col :span="8">
              <el-form-item label="报告阶段名称"><span>{{item.reportStageName}}</span></el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="阶段码"><span>{{item.reportStageCode}}</span></el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="阶段上报时间"><span>{{item.reportStageDate}}</span></el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="处置完成时间"><span>{{item.handleCompleteDate}}</span></el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </BlockCard>
        <BlockCard title="附件列表">
          <FieldList
            :key="item.attachmentItems"
            :fileList="item.attachmentItems"
          ></FieldList>
        </BlockCard>
      </div>
    </div>
</template>

<script>import BlockCard from '@/components/BlockCard';
  import FieldList from '@/views/components/fileUpload/regular';
  import {actualReportData, deleteActualReportRecord, cancelConfirmedStatus} from '@/api/sasac/reportManagement/edit/detail/index';
    export default {
      components:{BlockCard,FieldList},
      name: "reportSummary",
      props: {
        problemId: {
          type: String
        },
        edit: {
          type: Boolean,
          default: true
        }
      },
      data(){
          return{
            formData:[],
            actualStageItems:[],
          }
      },
      created() {
        this.ActualReportData();
      },
      mounted() {
      },
      methods: {
        //修改了值
        myFunction(){
          this.actualStageItems = [];
          this.formData.forEach(row => {
            if(row.reportStageCode==1){
              this.actualStageItems.push({
                actualStageId: row.actualStageId,
                groupContacter: row.groupContacter,
                groupContacterTel: row.groupContacterTel,
                realtimeReportId: row.realtimeReportId,
                reportStageCode:row.reportStageCode,
                reportStageName: row.reportStageName,
                violationCase: row.violationCase,
              });
            }
          });
          this.$emit('actualStage',this.actualStageItems);
        },
        // 获取查询页数据
        ActualReportData() {
          actualReportData(this.problemId).then(response => {
            this.formData = response.data;
            //初始化赋实时报告的值
            this.formData.forEach(row => {
              if(row.reportStageCode==1){
                this.actualStageItems.push({
                  actualStageId: row.actualStageId,
                  groupContacter: row.groupContacter,
                  groupContacterTel: row.groupContacterTel,
                  realtimeReportId: row.realtimeReportId,
                  reportStageCode:row.reportStageCode,
                  reportStageName: row.reportStageName,
                  violationCase: row.violationCase,
                });
              }
            });
            this.$emit('actualStage',this.actualStageItems);
          });
        },

        // 删除
        deleteReport(realtimeReportId, index,reportSasacId,reportStageId) {
          this.$modal.confirm('是否确定删除该报告？').then(() => {
            this.DeleteActualReportRecord(realtimeReportId, index,reportSasacId,reportStageId);
          }).catch(() => {
          });
        },

        //取消上报状态
        cancelConfirmedStatus(){
          cancelConfirmedStatus({id: this.problemId, reportContent: 'ACTUAL_REPORT'}).then(response => {
            if (200 === response.code) {
              this.$modal.msgSuccess(response.msg);
              this.$emit('cancel');
            } else {
              this.$modal.alertError(response.msg);
            }
          });
        },

        //删除方法调用
        DeleteActualReportRecord(realtimeReportId, index,reportSasacId,reportStageId) {
          deleteActualReportRecord(realtimeReportId,reportSasacId,reportStageId).then(response => {
            if (200 === response.code) {
              this.cancelConfirmedStatus();
              this.formData.splice(index, 1);
              this.myFunction();
            } else {
              this.$modal.alertError(response.msg);
            }
          });
        },
        /** 下载附件 */
        downloadFile(row) {
          this.download('/sys/documentTemplate/downloadByAttachmentId', {id: row.id}, row.fileName)
        },
        //附件列表关闭
        close() {
          this.visible = false;
        }
      }
    }
</script>

<style scoped>
  .right-report-detail-title{
    width: 100%;
    height: 48px;
    line-height: 48px;
    padding:0 12px;
    box-sizing: border-box;
    background: #F9F9F9;
  }
  .right-report-detail-span{
    font-size: 16px;
    font-weight: bold;
    color: #333333;
  }
</style>
