<template>
  <div>
    <div class="vio-file-box">
      <el-row class="vio-file-div" v-for="item in fileList">
        <el-col :span="4" class="vio-file-type flex vio-file-border">
<!--          <span class="text-red" v-show="item.fileTypeCode!=='0'">*</span>-->
          <span>{{item.fileTypeName}}</span>
        </el-col>
        <el-col :span="20" class="vio-file-content">
          <ul class="vio-file-list">
            <el-row class="vio-file-li ry-row flex" v-for="obj in item.businessAttachments">
              <el-col :span="12"  class="vio-file-name">
                <i class="el-icon-tickets"></i>
                <span>{{obj.attachmentName}}</span>
              </el-col>
              <el-col :span="6" class="vio-file-user icon-grey">
                <span>{{obj.uploaderName}}</span>
              </el-col>
              <el-col :span="6" class="vio-file-del text-center">
                <a href="javascript:void(0);" @click="fileDownload(obj)" class="table-btn tip-edit" title="下载">
                  <i class="el-icon-bottom"></i>
                </a>
              </el-col>
            </el-row>
          </ul>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>

  export default {
    name: "demo",
    components: {
    },
    props: {
      fileList: {
        type: Array
      },
    },
    data(){
      return{
      }
    },
    mounted(){
    },
    methods:{
      /**下载模板*/
      handleDownload(fileTemplate,modelFileName){
        this.download('/colligate/violFile/downloadTemplate/'+fileTemplate, {fileTemplate
        },modelFileName)
      },
      /**下载文件*/
      fileDownload(obj){
        console.info(obj);
        this.download('/sys/attachment/downloadSysAttachment/'+obj.attachmentUuid, {
        },obj.attachmentName)
      },
    }
  }
</script>

<style scoped lang="scss">
  .flex{
    display: flex;
    align-items: center;
  }
  .vio-file-box{
    border: 1px solid #d9d9d9;
    .vio-file-div{
      display: flex;
      width: 100%;
      border-bottom: 1px solid #d9d9d9;
      .vio-file-border{
        border-right: 1px solid #d9d9d9;
      }
      .vio-file-type{
        background-color: #F4F8FC;
        color: #73777a;
        min-height: 48px;
        padding: 0 10px;
        box-sizing: border-box;
        .text-red{
          color: #f5222d !important;
        }
      }
      .vio-file-download{
        justify-content: center;
        .vio-file-down{
          padding: 0 4px;
          border-right: 1px solid #d9d9d9;
        }
        i{
          color: #f5222d;
        }
        .vio-file-down:last-child{
          border-right-width: 0;
        }
      }

      .vio-file-content{
        min-height: 48px;
        .vio-file-list{
          padding:0;
          margin:0;
          .vio-file-li{
            padding-left: 10px;
            box-sizing: border-box;
            border-bottom: 1px solid #d9d9d9;
            min-height: 48px;
            .vio-file-name{
              i{
                margin-right:6px;
              }
            }
            .vio-file-user,.vio-file-time{
              height: 48px;
              display: flex;
              align-items: center;
              color: #a9b0b4;
            }
            .vio-file-del{
              text-align: center;
              i{
                color:#f5222d;
                margin:0 6px;
              }
            }
          }
          :last-child {
            border-bottom-width: 0;
          }
        }
      }
    }
    :last-child {
      border-bottom-width: 0;
    }
  }
</style>
