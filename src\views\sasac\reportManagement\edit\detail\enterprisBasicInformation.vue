<!--企业基本信息查看页面-->
<template>
  <div style="height:calc(70vh - 220px);">
    <el-table
      border
      :data="tableList"
      ref="table"
      height="100%"
    >
      <el-table-column label="集团名称" prop="involOrgName" min-width="20%" show-overflow-tooltip/>
      <el-table-column label="集团简称" prop="involOrgNameBak" min-width="20%" show-overflow-tooltip/>
      <el-table-column label="社会信用代码" prop="socialCreditCode" min-width="20%" show-overflow-tooltip/>
      <el-table-column label="行业名称" prop="industryName" min-width="20%" show-overflow-tooltip/>
      <el-table-column label="行业代码" prop="industryCode" min-width="20%" show-overflow-tooltip/>
    </el-table>
  </div>
</template>

<script>
  import {getReportArea, refreshReportArea} from "@/api/sasac/reportManagement/edit/detail/index";

  export default {
    name: "enterprisBasicInformation",
    props: {
      problemId: {
        type: String
      },
    height:{
      type: String
    }
    },
    data() {
      return {
        tableList: [],
        total: 0,
        params: {
          pageNum: 1,
          pageSize: 10,
        }
      }
    },
    created() {
      this.GetReportArea();
    },
    mounted() {
    },
    methods: {
      // 获取查询页数据
      GetReportArea() {
        getReportArea({ reportId:this.problemId,...this.params}).then(response => {
          this.tableList = response.data;
        });
      },
      // 获取刷新数据
      onRefresh() {
        //接口：/colligate/baseInfo/report/refreshReportArea
        refreshReportArea({ reportId:this.problemId,...this.params}).then(response => {
          console.log("tableList: ", this.tableList)
          this.tableList = response.data;
        });
      },
    }
  }
</script>

<style scoped>

</style>
