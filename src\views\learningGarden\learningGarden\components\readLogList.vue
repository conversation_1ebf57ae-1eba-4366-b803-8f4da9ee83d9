<template>
  <div>
    <div class="learning-content-ul1"  v-loading="loading">
      <div style="height:calc(70vh - 100px);overflow: auto">
        <NoData v-if="dataList.length==0"></NoData>
        <div class="learning-content-ul2" v-else>
          <div class="learning-ul2-li cursor" v-for="(item,index) in dataList" :key="index" @click="learningGardens(item,item.module)">
            <div class="learning-ul2-title ovflowHidden">{{item.title}}</div>
            <div class="learning-ul2-info">
              <span class="learning-ul2-index">{{item.module | fromatComon(dict.type.MODULE)}}</span>
              <span class="learning-ul2-date">阅读时间：{{item.readTime}}</span>
            </div>
          </div>
        </div>
      </div>
      <pagination
        v-show="total>0"
        :total="total"
        :page-sizes='[10]'
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getLastReadLog"
      />
    </div>
    <!-- 制度机制详情页 -->
    <lawDetail v-if="open2" v-on:closeModal="closeModal2" :id="lawId"></lawDetail>
    <!-- 信息发布详情页 -->
    <table2Add v-if="open3" editType = "view" v-on:closeModal="closeModal3"  :informationId="informationId"></table2Add>
  </div>
</template>

<script>
import { isExternalUrlOpen } from '@/utils/index'
import { getLastReadLog,readLog } from '@/api/learningGarden/index'
import table2Add from "@/views/learningGarden/gardenManagement/components/table2Add";
import lawDetail from "@/views/base/basequery/law/lawDetail";
export default {
  name: "contextCollectionList",
  dicts: ['MODULE','INFORMATION_TYPE'],
  components:{table2Add,lawDetail},
  data() {
    return {
      open:false,
      collectionOpen:false,//我的收藏
      open2:false,//制度机制详情页
      open3:false,//信息发布详情页
      dataList:[],
      info:{//钻取信息存放
        module:'',
        tableId:'',
        title:'',
        url:''
      },
      lawId:'',
      informationId:'',
      total:0,//数据总条数
      queryParams: {
        pageNum: 1,
        pageSize: 10
      },//查询参数
      loading:false,
    };
  },
  created() {
    this.getLastReadLog()
    isExternalUrlOpen('http://www.sasac.gov.cn').then((res)=>{
      this.open = true;
    }).catch((res)=>{
    })
  },
  methods: {
    closeModal3(){
      this.open3 = false;
    },
    closeModal2(){
      this.open2 = false;
    },
    getLastReadLog(){
      this.loading = true
       getLastReadLog(this.queryParams).then(res=>{
      this.dataList = res.rows;
      this.loading = false;
      this.total = res.total;
       })
    },
    //打开页签
    openNewTab (url) {
      window.open(url)
    },
    //收藏 与 浏览记录  钻取
    learningGardens(item,type){
      let param = {
        module:type,
        tableId:item.tableId,
        title:item.title,
        url:item.url
      }
      if(type=='1'){//政策文章
        if(this.open&&item.url){
          this.openNewTab(item.url)
        }else{
          this.$message.error('该网址为外网地址，请确认是否联网。');
        }
      }
      if(type=='2'){//制度机制
        this.info = param;
        this.open2 = true;
        this.lawId = item.tableId;
      }
      if(type=='3'){//发布信息
        this.info = param;
        this.open3 = true;
        this.informationId = item.tableId;
      }
      //记录  阅读
      readLog(param).then(res=>{

      })
    },
  }
}
</script>

<style scoped lang="scss">
.learning-content-ul1{
  background: #fff;
  padding:10px 0;
  box-sizing: border-box;
  height: calc(70vh - 10px);
  .learning-content-ul2{
    .learning-ul2-li{
      border: 1px solid rgba(228,228,228,1);
      border-radius: 2px;
      height: 68px;
      margin-bottom:12px;
      width: 100%;
      padding:2px 16px;
      .learning-ul2-title{
        font-family: PingFangSC-Medium;
        font-size: 14px;
        color: #333333;
        letter-spacing: 0;
        line-height: 22px;
        font-weight: 500;
        padding:6px 0;
        box-sizing: border-box;
      }
      .learning-ul2-info{
        display: flex;
        .learning-ul2-index{
          background: #FFEEEE;
          padding:0 12px;
          height: 22px;
          line-height: 22px;
          text-align: center;
          font-size: 13px;
          color: #f5222d;
          letter-spacing: 0;
          font-weight: 400;
          margin-right:12px;
        }
        .learning-ul2-date{
          font-family: PingFangSC-Regular;
          font-size: 13px;
          color: #909499;
          letter-spacing: 0;
          line-height: 22px;
          font-weight: 400;
        }
      }
    }
  }
}
</style>
