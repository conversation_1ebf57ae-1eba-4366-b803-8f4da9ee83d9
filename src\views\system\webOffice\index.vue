<!--webOffice-->
<template>
  <div  style="width: 100%; height: 100vh">
    <div id="office" style="display: block; width: 100%; height: 100%"></div>
  </div>
</template>

<script>
let OpenSDK = require('@/utils/webOffice/open-jssdk-v0.0.13.umd.js')
// import {OpenSDK} from "@/utils/webOffice/open-jssdk-v0.0.13.umd.js";
import { cacheSession,getPreviewInfo } from "@/api/system/webOffice";
export default {
  props: ["catalogId"],
  components: {},
  data() {
    return {
      tokens:'',
      sysFileId: "",
      fileName: "",
      link: "",
    };
  },
  methods: {
    cacheSession() {
      const that = this;
      cacheSession().then((response) => {
        if (response.code == 200) {
          this.tokens = response.data;
          this.getPreviewInfo(this.sysFileId, this.fileName);
        }
      });
    },

    getPreviewInfo(id, name) {
      console.log(id !== "string",id)
      console.log(name !== "string",name)
      // 验证输入参数
      if (!id || typeof id !== "string" || !name || typeof name !== "string") {
        return false;
      }

      getPreviewInfo({fileId:id,fileName:encodeURI(name)}).then((response) => {
        if (response.code == 200) {
          this.link = response.data.link + "&_w_tokentype=1";
          this.onloads();
        }
      });
    },

    onloads() {
      // 配置刷新 token 函数

      const refreshToken = () => {
        // 可以返回 Promise 或者 return { token, timeout }
        return Promise.resolve({
          token: this.tokens, // 必需：你需要设置的 token
          timeout: 10 * 60 * 1000, //  必需：token 超时时间，单位为毫秒，示例为 10 分钟
        });
      };

      if (!this.link) {
        return;
      }
      console.log(OpenSDK);
      const instance = OpenSDK.config({
        url: this.link,
        mount: document.querySelector("#office"),
        refreshToken,
      });

      // 由于 JSSDK 会提前 5 分钟调用 refreshToken 方法，所以建议设置的 timeout 为 10 分钟以上，避免刷新过快的问题。
      instance.setToken({
        token: this.tokens, // 根据自身的业务需求，通过异步请求或者模板输出的方式，取得 token
        timeout: 6 * 60 * 1000, // token 超时时间，可配合 refreshToken 配置函数使用，在超时前自动调用 refreshToken 重新刷新 token
      });
    },
  },
  created() {
    //获取参数
    const options = this.$route.query;
    // 获取参数
    this.sysFileId = options.fileUrl;
    this.fileName =  decodeURIComponent(options.fileName);
    this.cacheSession();

  },
};
</script>


<style scoped lang="scss">
</style>
