<!--5:处理及申诉-经办-->
<template>
  <div>
    <div class="popopo">
      <BlockCard title="基本信息">
        <el-row>
          <el-form
            ref="elForm"
            :model="formData"
            :rules="rules"
            size="medium"
            label-width="138px"
          >
            <el-col id="popopo" :span="12">
              <el-form-item label="系统编号" prop="auditCode">
                <span>{{ formData.auditCode }}</span>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="问题编号" prop="problemCode">
                <span>{{ formData.problemCode }}</span>
              </el-form-item>
            </el-col>

            <el-col :span="24">
              <el-form-item label="违规事项" prop="problemTitle">
                <span>{{ formData.problemTitle }}</span>
              </el-form-item>
            </el-col>

            <el-col :span="24">
              <el-form-item label="问题线索描述" prop="problemDescribe">
                <span>{{ formData.problemDescribe }}</span>
              </el-form-item>
            </el-col>

            <el-col :span="24">
              <el-form-item label="涉及专业线" prop="specLists">
                <el-checkbox-group
                  :key="formData.specLists"
                  v-model="formData.specLists"
                  size="medium"
                >
                  <el-checkbox
                    v-for="item in specList"
                    :key="item.specCode"
                    border
                    disabled
                    :label="item.specCode"
                  >{{ item.specName }}</el-checkbox>
                </el-checkbox-group>
              </el-form-item>
            </el-col>

            <el-col :span="24">
              <el-form-item label="涉及单位/部门/人员" prop="spec">
                <PersList
                  ref="pers"
                  :edit="noEdit"
                  :problem-id="problemId"
                  :relevant-table-id="formData.id"
                  :relevant-table-name="formData.relevantTableName"
                /> </el-form-item></el-col>
          </el-form>
        </el-row>
      </BlockCard>
      <Remind
        :key="actualFlag"
        :actual-flag="actualFlag"
      />

      <BlockCard title="责任追究">
        <el-row>
          <el-form
            ref="elForm1"
            :model="formData"
            :rules="rules"
            size="medium"
            label-width="138px"
          >
            <el-col :span="formData.accountabilityFlag == '1' ? 12 : 24">
              <el-form-item label="是否追责" prop="accountabilityFlag">
                <el-radio-group
                  v-model="formData.accountabilityFlag"
                  size="medium"
                >
                  <el-radio
                    v-for="(item, index4) in seriousAdverseEffectsFlagOptions"
                    :key="index4"
                    :label="item.value"
                    :disabled="item.disabled"
                  >{{ item.label }}</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>

            <el-col v-if="formData.accountabilityFlag == '0'" :span="24">
              <el-form-item label="" prop="unInvestigateReason">
                <el-input
                  v-model="formData.unInvestigateReason"
                  type="textarea"
                  placeholder="请填写未追责原因"
                  :autosize="{ minRows: 4, maxRows: 4 }"
                  :style="{ width: '100%' }"
                />
              </el-form-item>
            </el-col>

            <el-col v-if="formData.accountabilityFlag == '1'" :span="12">
              <el-form-item label="责任追究时间" prop="investigateTime">
                <el-date-picker
                  v-model="formData.investigateTime"

                  format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd"
                  :style="{ width: '100%' }"
                  placeholder="请选择责任追究时间"
                  clearable
                />
              </el-form-item>
            </el-col>

            <el-col v-if="formData.accountabilityFlag == '1'" :span="24">
              <el-form-item label="责任追究处理方式" prop="problemDescribe">
                <el-button size="mini"
                  type="primary"
                  class="float-right"
                  @click="saveTable()"
                >保存</el-button>
              </el-form-item>
            </el-col>

            <el-col v-if="formData.accountabilityFlag == '1'" :span="24">
              <el-table :data="tableData" border stripe style="width: 100%">
                <el-table-column
                  label="序号"
                  type="index"
                  width="80"
                  align="center"
                  fixed
                />
                <el-table-column
                  label="涉及人员"
                  prop="userName"
                  width="180"
                  align="center"
                  fixed
                />
                <el-table-column
                  label="部门"
                  prop="involOrgName"
                  width="180"
                  align="center"
                  fixed
                />

                <el-table-column
                  label="职务"
                  prop="postName"
                  width="180"
                  align="center"
                  fixed
                />

                <el-table-column
                  label="追责总人次"
                  prop="accountabilitySum"
                  width="180"
                  align="center"
                  fixed
                />

                <el-table-column
                  align="center"
                  prop="enddate"
                  label="处理方式"
                  min-width="1100"
                >
                  <template slot-scope="scope">
                    <el-checkbox-group
                      class="checkbox-group-specLists"
                      v-if="scope.row.userName !== '合计'"
                      :key="scope.row.specLists"
                      v-model="scope.row.specLists"
                      size="medium"
                    >
                      {{ scope.row.specLists }}
                      <el-checkbox
                        v-for="item in scope.row.detailWidth"
                        :key="item.dictValue"
                        border
                        :disabled="item.disabled"
                        :label="item.dictValue"
                      >{{ item.dictLabel }}</el-checkbox>
                    </el-checkbox-group>
                    <el-checkbox-group
                      class="checkbox-group-specLists checkbox-group-processingOther"
                      v-if="scope.row.userName !== '合计'"
                      :key="scope.row.processingOther"
                      v-model="scope.row.processingOther"
                      size="medium"
                    >
                      <el-checkbox
                        v-for="dict in dict.type.other_handler_way"
                        :key="dict.value"
                        border
                        :label="dict.value"
                      >{{ dict.label }}</el-checkbox>
                    </el-checkbox-group>
                  </template>
                </el-table-column>

                <el-table-column
                  :show-overflow-tooltip="true"
                  align="center"
                  prop="enddate"
                  label="扣减金额（万元）"
                  width="250"
                >
                  <template
                    slot-scope="scope"
                  >
                    <el-input-number
                      v-if="scope.row.userName !== '合计'"
                      v-model="scope.row.deductionSalary"
                      :min="0"
                      :precision="2"
                      placeholder="扣减金额（万元）"
                      controls-position="right"
                    />
                    <span v-else>{{ scope.row.deductionSalary }}</span>
                  </template>
                </el-table-column>

                <el-table-column
                  label="组织处理+扣减薪酬"
                  type="handleDeductionCount"
                  width="180"
                  align="center"
                >
                  <template
                    slot-scope="scope"
                  >
                    <span>{{ scope.row.userName == '合计'?scope.row.handleDeductionCount:scope.row.handleDeductionCount=='1'?'是':'否' }}</span>
                  </template>
                </el-table-column>

                <el-table-column
                  label="政务处分+扣减薪酬"
                  type="governmentDeductionCount"
                  width="180"
                  align="center"
                >

                  <template
                    slot-scope="scope"
                  >
                    <span>{{ scope.row.userName == '合计'?scope.row.governmentDeductionCount:scope.row.governmentDeductionCount=='1'?'是':'否' }}</span>

                  </template>
                </el-table-column>

                <el-table-column
                  label="党纪处分+扣减薪酬"
                  type="partyDeductionCount"
                  width="180"
                  align="center"
                >
                  <template
                    slot-scope="scope"
                  >
                    <span>{{ scope.row.userName == '合计'?scope.row.partyDeductionCount:scope.row.partyDeductionCount=='1'?'是':'否' }}</span>
                  </template>
                </el-table-column>
              </el-table>
            </el-col>

            <el-col v-if="formData.accountabilityFlag == '1'" :span="24">
              <el-form-item label="禁入限制人员信息" />
              <el-table :data="tableData2" border stripe style="width: 100%">
                <el-table-column
                  label="序号"
                  type="index"
                  width="80"
                  align="center"
                />
                <el-table-column label="姓名" type="userName" prop="userName" align="center" />
                <el-table-column
                  label="单位"
                  prop="involAreaName"
                  align="center"
                />

                <el-table-column
                  label="部门"
                  prop="involOrgName"
                  align="center"
                />

                <el-table-column label="职务" prop="postName" align="center" />

                <el-table-column
                  label="操作"
                  fixed="right"
                  align="center"
                  class-name="small-padding fixed-width"
                >
                  <template slot-scope="scope">
                    <el-button
                      size="mini"
                      type="text"
                      title="编辑"
                      icon="el-icon-edit"
                      @click="editFun(scope.row)"
                    />
                  </template>
                </el-table-column>
              </el-table>
            </el-col>
          </el-form>
        </el-row>
      </BlockCard>

      <BlockCard title="申诉">
        <el-row>
          <el-form
            ref="elForm"
            :model="formData"
            :rules="rules"
            size="medium"
            label-width="138px"
          >
            <el-col :span="12">
              <el-form-item label="是否申诉" prop="appealFlag">
                <el-radio-group v-model="formData.appealFlag" size="medium">
                  <el-radio
                    v-for="(item, index6) in seriousAdverseEffectsFlagOptions"
                    :key="index6"
                    :label="item.value"
                    :disabled="item.disabled"
                  >{{ item.label }}</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>

            <el-col v-if="formData.appealFlag == '1'" :span="12">
              <el-form-item label="申诉处理结果" prop="appealResult">
                <el-select
                  v-model="formData.appealResult"
                  placeholder="请选择申诉处理结果"
                  clearable
                  :style="{ width: '100%' }"
                >
                  <el-option
                    v-for="(item, index5) in formData.appealResultList"
                    :key="index5"
                    :label="item.dictLabel"
                    :value="item.dictValue"
                  />
                </el-select>
              </el-form-item>
            </el-col>

            <el-col v-if="formData.appealFlag == '1'" :span="24">
              <el-form-item label="受理申诉组织" prop="relevorgList" class="input-btn">

                <div class="list1">
                  <div v-for="(item,index1) of formData.relevorgList" :key="index1" class="list1-one">
                    <span>{{ item.orgName||item.name }}</span>
                    <span class="close"><i class="el-icon-close icon iconfont" @click="deleteCheckGroupLeaderList(item,'1')" /></span>
                  </div>
                </div>
                <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="addCheckGroupLeaderList('1')">添加</el-button>
              </el-form-item>

            </el-col>

            <el-col v-if="formData.appealFlag == '1'" :span="24">
              <el-form-item label="复核人员" prop="" class="input-btn">
                <div class="list1">
                  <div v-for="(item,index2) of formData.reviewerList" :key="index2" class="list1-one">
                    <span>{{ item.userName||item.name }}</span>
                    <span class="close"><i class="el-icon-close icon iconfont" @click="deleteCheckGroupLeaderList(item,'2')" /></span>
                  </div>
                </div>
                <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="addCheckGroupLeaderList('2')">添加</el-button>
              </el-form-item>
            </el-col>

            <el-col v-if="formData.appealFlag == '1'" :span="24">
              <el-form-item label="申诉资料" prop="" class="input-btn">
                <div style="float:left;margin: 0 10px;">
                  <FileUpload
                    :is-show-tip="showTip"
                    file-url="/colligate/violFile/uploadViolFile"
                    btn-title="上传申诉相关证明资料"
                    :param="{
                      linkKey:'a009',
                      fileType: 'DAILY_APPEAL_EXPAND_1',
                      problemId:problemId,
                      busiTableId:formData.id,
                      busiTable:formData.relevantTableName,
                      flowKey:'SupervisionDailyReport'
                    }"
                    @handleUploadSuccess="queryInvolveCompanyAndPerson"
                  >
                    {{ '上传申诉相关证明资料' }}
                  </FileUpload>
                </div>
                <div style="float:left;margin: 0 10px;">
                  <FileUpload
                    :is-show-tip="showTip"
                    file-url="/colligate/violFile/uploadViolFile"
                    btn-title="上传申诉处理结果"
                    :param="{
                      linkKey:'a009',
                      fileType: 'DAILY_APPEAL_EXPAND_2',
                      problemId:problemId,
                      busiTableId:formData.id,
                      busiTable:formData.relevantTableName,
                      flowKey:'SupervisionDailyReport'
                    }"
                    @handleUploadSuccess="queryInvolveCompanyAndPerson"
                  >
                    {{ '上传申诉处理结果' }}
                  </FileUpload>

                </div>
              </el-form-item>
            </el-col>

            <el-col v-if="formData.appealFlag == '1'" :span="24">
              <el-table
                :data="tableData3"
                border
                stripe
                style="width: 100%;"
                fixed="right"
              >
                <el-table-column
                  label="序号"
                  type="index"
                  min-width="15%"
                  align="center"
                />
                <el-table-column
                  label="附件名称"
                  prop="fileName"
                  min-width="30%"
                  align="center"
                />
                <el-table-column
                  label="附件类型"
                  prop="fileTypeName"
                  min-width="20%"
                  align="center"
                />
                <el-table-column
                  align="center"
                  prop="uploaderName"
                  label="上传人员"
                  min-width="20%"
                />

                <el-table-column
                  label="操作"
                  fixed="right"
                  min-width="15%"
                  align="center"
                  class-name="small-padding fixed-width"
                >
                  <template slot-scope="scope">
                    <el-button
                      size="mini"
                      type="text"
                      title="下载"
                      icon="el-icon-bottom"
                      @click="fileDownload(scope.row)"
                    />
                    <el-button
                      size="mini"
                      type="text"
                      title="删除"
                      icon="el-icon-delete"
                      @click="DelFile(scope.row)"
                    />
                  </template>
                </el-table-column>
              </el-table>
            </el-col>
          </el-form>
        </el-row>
      </BlockCard>
      <BlockCard
        title="附件列表"
      >
        <File
          v-if="formData.id!=''&&formData.relevantTableName!=''"
          :key="problemId||formData.id||formData.relevantTableName"
          ref="file"
          edit="true"
          :problem-id="problemId"
          :relevant-table-id="formData.id"
          :relevant-table-name="formData.relevantTableName"
          flow-type="VIOL_DAILY"
          problem-status="5"
          flow-key="SupervisionDailyReport"
        />
      </BlockCard>
      <!--修改记录-->
      <el-dialog :visible.sync="visibleModify" width="80%" append-to-body title="修改记录">
        <modifyRecord
          v-if="visibleModify"
          ref="modify"
          :key="problemId||relevantTableId||relevantTableName"
          edit="true"
          :problem-id="problemId"
          :relevant-table-id="relevantTableId"
          :relevant-table-name="relevantTableName"
          :problem-status="5"
          @modifySave="modifySave"
        />
        <div slot="footer" class="dialog-footer">
          <el-button size="mini" type="primary" @click="modifyClose">保存</el-button>
        </div>
      </el-dialog>
    </div>
    <Process slot="footer" ref="process" @publicSave="publicSave" />

    <el-dialog :visible.sync="VisibleCheckTree" width="60%" append-to-body :title="title">
      <CheckTree
        v-if="VisibleCheckTree"
        :key="selectTree"
        ref="checkTree"
        :url="urls"
        :select-tree="selectTree"
        :params="params"
        @list="persList"
      />
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="savePers">保存</el-button>
      </div>
    </el-dialog>
    <el-dialog v-bind="$attrs" :visible.sync="visible" :before-close="close" width="90%" append-to-body title="编辑">
      <Edit
        v-if="rows.id"
        :id="rows.id"
        ref="editRef"
        :key="rows"
        :select-value="rows"
        :problem-id="problemId"
        :relevant-table-id="rows.id"
        relevant-table-name="T_COL_VIOL_DAILY_LIMIT_PERSON"
        @close="close"
      />
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="saveEdit">保存</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { submitHisData } from '@/api/daily/historyQuestionEnter/index'
import BlockCard from '@/components/BlockCard'
import Edit from '@/views/daily/persEdit/edit'// tree
import {
  queryHandleAppealInfo,
  queryInvolPersonList,
  saveTreatmentMethod,
  queryLimitedPersonList,
  saveRelevorg,
  saveCheckGroupMember,
  selectViolFilesPage,
  saveHandleAppealInfo,
  delrelevorg,
  delCheckGroupMember,
  queryRelevorgByType,
  queryRelevpersonByType,
  checkHandleAppealInfo
} from '@/api/daily/process/handlingAppealRecords'
import PersList from '@/views/daily/tree/persList' // tree
import Remind from '@/views/components/remind'
import CheckTree from '@/views/daily/tree/check2Tree'// checkTree
import { deleteViolFile } from '@/api/components/index'
import File from '@/views/components/fileUpload'// 附件
import {
  // 生成情形范围修改记录
  generateSituationRangeModifyRecord
  // 生成业务数据修改记录
  , generateBusinessModifyRecord } from '@/api/daily/modifyRecord/modifyRecord'// 修改记录js方法
import modifyRecord from '@/views/daily/modifyRecord'// 修改记录
export default {
  dicts: ['other_handler_way'],
  components: {
    Edit,
    BlockCard,
    PersList,
    CheckTree,
    File,
    Remind, modifyRecord
  },
  props: {
    edit: {
      type: Boolean,
      default: false
    },
    problemId: {
      type: String
    }
  },
  data() {
    return {
      relevantTableId: '',
      relevantTableName: '',
      selectTree: [],
      visibleModify: false,
      showTip: false,
      rules: {
        unInvestigateReason: [
          { min: 1, max: 2048, message: '长度在 1 到 2048 个字符', trigger: 'blur' }
        ]
      },
      noEdit: false,
      flag: false,
      visible: false,
      visibleTree: false,
      title: '', // 弹窗标题
      params: {
        relevorgType: '', // 弹窗参数
        problemId: '',
        relevantTableId: ''
      },
      index: 1,
      urls: '',
      perTypes: '1',
      rows: {},
      VisibleCheckTree: false, // 弹窗是否开启
      formData: {
        accountabilityFlag: '',
        actualFlag: '',
        appealFlag: '',
        appealResult: '',
        appealResultList: [],
        auditCode: '',
        createBy: '',
        createByOrg: '',
        createLoginName: '',
        createPostId: '',
        createTime: '',
        dealPostIdPre: '',
        deletedFlag: '',
        expandFileTypeOptions: [],
        id: '',
        investigateTime: '',
        isCompleted: '',
        problemAreaCode: '',
        problemAreaCodePre: '',
        problemAreaName: '',
        problemAreaNamePre: '',
        problemCode: '',
        problemDescribe: '',
        problemId: '',
        problemProvCode: '',
        problemProvCodePre: '',
        problemProvName: '',
        problemProvNamePre: '',
        problemTitle: '',
        relevantTableName: '',
        relevorgList: [],
        reviewerList: [],
        specSelectedList: [],
        specLists: [],
        unInvestigateReason: '',
        updateBy: '',
        updateByOrg: '',
        updateLoginName: '',
        updatePostId: '',
        updateTime: ''
      },
      tableData: [],
      tableData2: [],
      tableData3: [],

      seriousAdverseEffectsFlagOptions: [
        {
          label: '是',
          value: 1
        },
        {
          label: '否',
          value: 0
        }
      ],
      specList: []

    }
  },
  computed: {},
  watch: {},
  created() {

    this.queryHandleAppealInfo()
  },
  mounted() {},
  methods: {
    close() {
      this.queryLimitedPersonList()
      this.visible = false
    },
    saveEdit() {
      this.$refs.editRef.saveLimitedPersonInfo()
    },
    // 请求数据详情
    queryHandleAppealInfo() {
      queryHandleAppealInfo({ problemId: this.problemId }).then((response) => {
        const { code, data } = response
        if (code === 200) {
          this.formData = Object.assign(this.formData, data)

          this.specList = data.specSelectedList
          this.relevantTableId = this.formData.id
          this.actualFlag = this.formData.actualFlag
          this.relevantTableName = this.formData.relevantTableName
          const array = []
          const specSelectedList = data.specSelectedList
          for (let i = 0, len = specSelectedList.length; i < len; i++) {
            if (specSelectedList[i].specCode) {
              array.push(specSelectedList[i].specCode)
            }
          }
          this.formData.specLists = array;
          this.queryInvolPersonList()
          this.queryLimitedPersonList()
          this.$nextTick(() => {
            this.$refs.pers.DueryDepartmentSelectInfo()
            this.$refs.file.ViolationFileItems()
          })
          this.queryInvolveCompanyAndPerson()
          this.$emit('closeLoading')
        }
      })
    },
    // 责任追究处理方式
    queryInvolPersonList() {
      queryInvolPersonList({
        limit: 10,
        page: 1,
        problemId: this.problemId,
        relevantTableId: this.formData.id
      }).then((response) => {
        const { code, data } = response
        if (code === 200) {
          if (data.length > 0) {
            data.forEach((item) => {
              if (item.userName !== '合计') {
                item.specLists = []
                if (item.orgHandleFlag == '1') {
                  item.specLists.push('1')
                }
                if (item.deductionSalaryFlag == '1') {
                  item.specLists.push('2')
                }
                if (item.prohibitFlag == '1') {
                  item.specLists.push('3')
                }
                if (item.governmentPunishmentFlag == '1') {
                  item.specLists.push('4')
                }
                if (item.partyPunishmentFlag == '1') {
                  item.specLists.push('5')
                }
                if (item.transferAuthorityFlag == '1') {
                  item.specLists.push('6')
                }
                var provinceDisable = ''
                if(this.$store.getters.provinceCode == '001000'){
provinceDisable = false
                }else{
                  provinceDisable = true
                }
                item.detailWidth = [
                  {
                    dictValue: '1',
                    dictLabel: '组织处理',
                    disabled:false
                  },
                  {
                    dictValue: '2',
                    dictLabel: '扣减薪酬',
                     disabled:false
                  },
                  {
                    dictValue: '3',
                    dictLabel: '禁入限制',
                     disabled:provinceDisable
                  },
                  {
                    dictValue: '4',
                    dictLabel: '政务处分',
                     disabled:provinceDisable
                  },
                  {
                    dictValue: '5',
                    dictLabel: '党纪处分',
                     disabled:false
                  },
                  {
                    dictValue: '6',
                    dictLabel: '移送监察/司法机关',
                     disabled:false
                  }
                ]
              }
            })
          }
          this.tableData = data
        }
      })
    },
    // 禁入限制人员信息
    queryLimitedPersonList() {
      queryLimitedPersonList({
        limit: 10,
        page: 1,
        problemId: this.problemId,
        relevantTableId: this.formData.id
      }).then((response) => {
        const { code, data } = response
        if (code === 200) {
          this.tableData2 = data
        }
      })
    },
    // 添加申诉组织
    addCheckGroupLeaderList(type) {
      this.perTypes = type
      this.selectTree = []
      if (type == '1') {
        this.title = '受理申诉组织'
        this.params.relevorgType = 'ACCEPT_COMPLAINTS'
        this.params.problemId = this.problemId
        this.params.relevantTableId = this.formData.id
        this.urls = '/colligate/relevorg/queryRelevorgTree'
        this.selectTree = this.formData.relevorgList
      }
      if (type == '2') {
        this.title = '复核人员'
        this.params.relevorgType = 'REVIEWER'
        this.params.problemId = this.problemId
        this.params.relevantTableId = this.formData.id
        this.urls = '/colligate/violDailyRelevperson/checkStaffOrgTree'
        this.selectTree = this.formData.reviewerList
      }
      this.VisibleCheckTree = true
    },
    queryRelevorgByType() {
      queryRelevorgByType({
        problemId: this.problemId,
        relevantTableId: this.formData.id,
        relevorgType: 'ACCEPT_COMPLAINTS'
      }).then(
        response => {
          const { code, data } = response
          if (code === 200) {
            this.formData.relevorgList = data
          }
        }
      )
    },

    queryRelevpersonByType() {
      queryRelevpersonByType({
        problemId: this.problemId,
        relevantTableId: this.formData.id,
        personType: 'REVIEWER'
      }).then(
        response => {
          const { code, data } = response
          if (code === 200) {
            this.formData.reviewerList = data
          }
        }
      )
    },
    // 删除
    deleteCheckGroupLeaderList(item, type) {
      if (type === '1') {
        this.$confirm('是否确认删除该受理申诉组织？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          delrelevorg({
            orgId: item.orgId || item.id,
            problemId: this.problemId,
            relevantTableId: this.formData.id,
            relevorgType: 'ACCEPT_COMPLAINTS'
          }).then(
            response => {
              const { code, msg } = response
              if (code === 200) {
                this.queryRelevorgByType()
                this.$message({
                  type: 'success',
                  message: msg
                })
              }
            }
          )
        }).catch(() => {
        })
      }

      if (type === '2') {
        this.$confirm('是否确认删除该复核人员吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          delCheckGroupMember({
            postId: item.postId || item.id,
            problemId: this.problemId,
            relevantTableId: this.formData.id,
            personType: 'REVIEWER'
          }).then(
            response => {
              const { code, msg } = response
              if (code === 200) {
                this.queryRelevpersonByType()
                this.$message({
                  type: 'success',
                  message: msg
                })
              }
            }
          )
        }).catch(() => {
        })
      }
    },
    // 保存申请组织
    savePers() {
      this.$refs.checkTree.list()
    },
    // 返回申请组织
    persList(data) {
      if (this.perTypes === '1') {
        const list = []
        this.index++
        if (!data.length) { return false }
        for (let i = 0; i < data.length; i++) {
          list.push(data[i].id)
        }
        this.formData.relevorgList = data
        // 点击确定保存 申请组织
        const query = {
          problemId: this.problemId,
          relevantTableId: this.formData.id,
          relevantTableName: this.formData.relevantTableName,
          relevorgType: 'ACCEPT_COMPLAINTS',
          orgIds: list
        }
        saveRelevorg(query).then(
          response => {
            const { code, msg } = response
            if (code === 200) {
              this.$modal.msgSuccess(msg)
              this.VisibleCheckTree = false
            }
          }
        )
      }
      if (this.perTypes === '2') {
        const list = []
        this.index++
        if (!data.length) { return false }
        for (let i = 0; i < data.length; i++) {
          list.push(data[i].id)
        }
        this.formData.reviewerList = data
        // 点击确定保存 申请组织
        const query = {
          problemId: this.problemId,
          relevantTableId: this.formData.id,
          relevantTableName: this.formData.relevantTableName,
          personType: 'REVIEWER',
          postIds: list
        }
        saveCheckGroupMember(query).then(
          response => {
            const { code, msg } = response
            if (code === 200) {
              this.$modal.msgSuccess(msg)
              this.VisibleCheckTree = false
            }
          }
        )
      }
    },

    // 上传后的附件列表请求
    queryInvolveCompanyAndPerson() {
      selectViolFilesPage({
        problemId: this.problemId,
        busiTableId: this.formData.id,
        limit: 10,
        page: 1
      }).then((response) => {
        const { code, data } = response
        if (code === 200) {
          this.tableData3 = data
        }
      })
    },

    // 删除附件
    DelFile(data) {
      const title = '确认删除该附件吗？'
      this.$modal.confirm(title).then(function() {
        return deleteViolFile(data.id)
      }).then(() => {
        this.$modal.msgSuccess('删除成功')
        this.queryInvolveCompanyAndPerson()
      }).catch(() => {})
    },
    // 编辑禁入限制人员
    editFun(rows) {
      this.visible = true
      this.rows = rows
    },
    /** 提交数据*/
   async nextStep() {
       if(await this.saveTable('flow')){
             this.$refs['elForm1'].validate((valid) => {
        if (valid) {
          saveHandleAppealInfo(this.formData).then(
            response => {
              checkHandleAppealInfo(this.formData).then(
                response => {
                  if (response.data.validateFlag) {
                    if (response.data.validateMsg) {
                      this.$confirm(response.data.validateMsg + ',是否继续提交？', '提示', {
                        confirmButtonText: '是',
                        cancelButtonText: '否',
                        type: 'warning'
                      }).then(() => {
                        this.$modal.msgSuccess('保存成功')
                        this.Modify()
                      }).catch(() => {
                      })
                    } else {
                      this.$modal.msgSuccess('保存成功')
                      this.Modify()
                    }
                  } else {
                    this.$message.error(response.data.validateMsg)
                  }
                }
              )
            }
          )
        }
      })
       }

    },
    /** 保存数据*/
   async publicSave() {
     if(await this.saveTable('flow')){
      this.$refs['elForm1'].validate((valid) => {
        if (valid) {
          saveHandleAppealInfo(this.formData).then((response) => {
            this.$modal.msgSuccess('保存成功')
          })
        }
      })
     }else{
     }

    },
    resetForm() {
      this.$refs['elForm'].resetFields()
      this.$refs['elForm1'].resetFields()
    },

    // 保存责任追究处理方式
  async saveTable(flow) {
    var isPush = false
      const list = ['1', '2', '3', '4', '5', '6']
      if (this.tableData && this.tableData.length > 0) {
        this.tableData.forEach((item) => {
          if (item.specLists && item.specLists.length >= 0) {
            list.filter((item2, index2) => {
              if (item.specLists.indexOf(item2) > -1) {
                if (item2 == '1') {
                  item.orgHandleFlag = '1'
                }
                if (item2 == '2') {
                  item.deductionSalaryFlag = '1'
                }
                if (item2 == '3') {
                  item.prohibitFlag = '1'
                }
                if (item2 == '4') {
                  item.governmentPunishmentFlag = '1'
                }
                if (item2 == '5') {
                  item.partyPunishmentFlag = '1'
                }
                if (item2 == '6') {
                  item.transferAuthorityFlag = '1'
                }
              } else {
                if (item2 == '1') {
                  item.orgHandleFlag = '0'
                }
                if (item2 == '2') {
                  item.deductionSalaryFlag = '0'
                }
                if (item2 == '3') {
                  item.prohibitFlag = '0'
                }
                if (item2 == '4') {
                  item.governmentPunishmentFlag = '0'
                }
                if (item2 == '5') {
                  item.partyPunishmentFlag = '0'
                }
                if (item2 == '6') {
                  item.transferAuthorityFlag = '0'
                }
              }
            })
          }
        })
      }

     await saveTreatmentMethod(this.tableData).then(
        response => {
          const { code, msg } = response
          if (code === 200) {
            isPush = true
            if(!flow){
               this.$message({
              type: 'success',
              message: msg
            })
            }


            this.queryInvolPersonList()
            this.queryLimitedPersonList()
          }
        }
      ).catch(()=>{

      })
      return isPush
    },

    // 调用修改记录保存
    modifyClose() {
      this.$refs.modify.save()
    },
    // 修改记录保存后
    modifySave(type) {
      if (type) {
        this.visibleModify = false
        this.submitHisDataFun()
      } else {
        this.visibleModify = false
      }
    },
    // 修改记录
    Modify() {
      console.log('修改记录:' + this.relevantTableId, this.relevantTableName, this.formData.id, this.formData.relevantTableName)
      // 生成情形范围修改记录
      generateSituationRangeModifyRecord(this.problemId, this.formData.id)
      // 生成业务数据修改记录
      generateBusinessModifyRecord(this.problemId, this.formData.id, this.formData.relevantTableName).then(
        response => {
          const isExistDifferenceField = response.data.isExistDifferenceField
          console.info(isExistDifferenceField)
          if (isExistDifferenceField) {
            this.visibleModify = true
          } else {
            this.submitHisDataFun()
          }
        }
      )
    },
    /** 下载文件*/
    fileDownload(obj) {
      this.download('/sys/attachment/downloadSysAttachment/' + obj.attachmentId, {
      }, obj.fileName)
    },
    // 添加受理申诉组织
    addRelevorgList() {},
    // 添加复合人员
    addReviewerList() {},
    // 申诉材料
    uploadFileCompany() {},
    //最终提交
    submitHisDataFun(){
      this.$confirm('请确认填写内容，提交后不能再进行修改', '提示', {
        confirmButtonText: '继续提交',
        cancelButtonText: '返回确认',
        type: 'warning'
      }).then(() => {
        const loading = this.$loading({
          spinner: 'el-icon-loading', // 自定义加载图标类名
          text: '正在加载...', // 显示在加载图标下方的加载文案
          lock: false, // lock的修改符--默认是false
        });
        submitHisData(this.problemId).then(
          response => {
            loading.close();
            this.$modal.msgSuccess("提交成功");
            this.closeEmit();
          }
        ).catch(err => {
          loading.close();
        })
      })
    },
    //流程提交后推进到下一个环节
    closeEmit(){
      this.$emit('hisNext')
    }
  }
}
</script>
<style scoped lang="scss">
.input-btn {
  ::v-deep .el-form-item__content {
    display: flex;
    button {
      margin-left: 8px;
      height: 35px;
    }
  }
}
.float-right {
  float: right;
}
.edit-span {
  white-space: normal;
  overflow-y: auto;
  overflow-wrap: break-word;
  word-break: normal;
  height: 61px;
  line-height: 30px;
  text-align: left;
  padding: 0px 10px;
  display: block;
}
::v-deep .editStyle {
  padding: 0px !important;
}
::v-deep .editStyle div.cell {
  padding: 0px !important;
}

::v-deep .editStyle .el-input--mini .el-input__inner {
  height: 56px;
  line-height: 56px;
  border: 0px;
}
.list1 {
  overflow: hidden;
  .list1-one {
    background-color: #e6f7ff;
    color: #40a9ff;
    margin: 0 10px 10px 10px;
    float: left;
    height: 30px;
    line-height: 30px;
    padding: 0 12px 0 12px;
    border-radius: 2px;
    .close {
      padding: 8px;
      cursor: pointer;
    }
  }
}
.list2 {
  display: flex;
  align-items: center;
  .list2-one {
    width: 250px;
    height: 30px;
    line-height: 30px;
    margin: 0 0 12px 0;
    display: inline-block;
    padding: 0 6px 0 12px;
    border-radius: 2px;
    box-sizing: border-box;
    position: relative;
    margin-right: 35px;
    span {
      text-align: left;
      display: inline-block;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      -webkit-line-clamp: 1;
      -webkit-box-orient: vertical;
      word-break: break-all;
      max-width: 100%;
      padding-right: 6px;
      box-sizing: border-box;
    }
  }
  .list2-one-bg {
    background: #f4f4f4;
    border-radius: 4px;
    color: #000;
    width: 250px;
    height: 30px;
    line-height: 30px;
    margin: 0 0 12px 0;
    display: inline-block;
    padding: 0 6px 0 12px;
    border-radius: 2px;
    box-sizing: border-box;
    position: relative;
    margin-right: 35px;

    span {
      text-align: left;
      display: inline-block;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      -webkit-line-clamp: 1;
      -webkit-box-orient: vertical;
      word-break: break-all;
      max-width: 100%;
      padding-right: 6px;
      box-sizing: border-box;
    }
    &::before {
      position: absolute;
      content: "";
      right: -30px;
      top: 15px;
      width: 25px;
      height: 1px;
      background: #d9d9d9;
    }
  }
}
.bottom-line {
  padding: 10px 0;
  text-align: center;
  border-top: 1px solid #d9d9d9;
  color: #f5222d !important;
}
.checkbox-group-specLists{
  display: inline-block;
}
.checkbox-group-processingOther{
  margin-left:30px;
}
</style>
