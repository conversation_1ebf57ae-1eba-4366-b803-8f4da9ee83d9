<template>
    <el-dialog
        title="企业联系人详情"
        :visible.sync="dialogVisible"
        width="90%"
        :before-close="handleClose">
        
      <personDetailCommon :type="type" :dataDetailsProps="dataDetails" v-loading="loading"></personDetailCommon>
    </el-dialog>
</template>

<script lang="ts">
  import {getAreaPersonBaseInfoById} from "@/api/base/person";
  import personDetailCommon from "../../common/personDetailCommon";
  import { Loading } from 'element-ui';

  export default {
    name: "personDetail",
    components: { personDetailCommon },
    props: {
      dialogVisible: {
        type: Boolean,
        default: true
      },
      id: {
        type: String,
        default: ''
      },
    },
    data() {
      return {
        dataDetails: {},
        type: "detail",
        loading: false,
      };
    },
    created() {
      this.areaPersonBaseInfo();
    },
    methods: {
      /**查询企业基本信息详情*/
      areaPersonBaseInfo() {
        // this.loading = true;
        let loadingInstance = Loading.service({ fullscreen: true });
        getAreaPersonBaseInfoById({id:this.id}).then(
          response => {
            this.dataDetails = response.data;
            // this.loading = false;
            loadingInstance.close();
          }
        );
      },
      /**关闭模态框*/
      handleClose() {
        this.$emit("closeModal"); 
      },
      /* startLoading: function() {
        loading = Loading.service({
        lock: true,
        text: '加载中……',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      },
      endLoading: function() {
          loading.close()
      }, */
    }
  };
</script>

<style>
  .text {
    font-size: 14px;
  }

  .item {
    margin-bottom: 18px;
  }

  .clearfix:before,
  .clearfix:after {
    display: table;
    content: "";
  }
  .clearfix:after {
    clear: both
  }
</style>