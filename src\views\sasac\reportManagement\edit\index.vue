<template>
  <div class="height100">
    <el-form class="common-card" style="padding-top:10px;" :model="reportData" size="medium" ref="elForm"
             label-width="80px">
      <el-row>
        <el-col :span="8">
          <el-form-item label="上报年度"><span>{{reportData.reportYear}}</span></el-form-item>
        </el-col>
        <el-col :span="16">
          <el-form-item label="上报标题">{{reportData.reportTitle}}</el-form-item>
        </el-col>
        <el-col :span="23">
          <el-form-item label="上报内容">
            <el-select v-model="reportContents"
                       multiple
                       @change="reportContentsSelect"
                       :style="{width: '100%'}"
            >
              <el-option :label="item.reportTypeName" :value="item.reportTypeCode"
                         v-for="(item,index) in reportContentDictionaries"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div class=" bottom-content">
      <el-row class="height100">
        <el-col  class="height100" :span="24">
          <div class="float-left left-report-detail height100">
            <ul class="report-detail-ul">
              <li v-for="(item,i) in reportContentDictionaries" v-show="reportContents.indexOf(item.reportTypeCode)!='-1'"
                  :class="index==i?'report-detail-li cursor text-red':'report-detail-li cursor'" @click="report(i)">
                <span class="report-detail-title">{{item.reportTypeName}}</span>
                <i v-if="item.isConfirm==1" class="el-icon-success"></i>
              </li>
            </ul>
          </div>
          <div class="float-right right-report-detail position height100" v-if="reportContentDictionaries.length&&reportContents.length">
            <div class="right-report-detail-title">
              <span class="right-report-detail-span">{{reportContentDictionaries[index].reportTypeName}}</span>
              <div class="float-right">
                <el-button
                  type="danger"
                  plain
                  icon="el-icon-refresh"
                  size="mini"
                  v-if="reportContentDictionaries[index].isRefresh==1"
                  @click="onRefresh"
                >刷新
                </el-button>
                <el-button
                  size="mini"
                  plain
                  type="danger"
                  icon="el-icon-edit"
                  v-if="reportContentDictionaries[index].isEdit==1"
                  @click="editReport"
                >{{reportContentDictionaries[index].editTitle}}
                </el-button>
              </div>
            </div>
            <div class="report-detail-contents">
              <!--企业基本信息-->
              <areaInfo ref="info" v-if="reportContentDictionaries[index].reportTypeCode=='COMPANY_BASIC_INFO'" :problemId="problemId"></areaInfo>
              <!--企业联系人-->
              <personInfo ref="info" v-if="reportContentDictionaries&&reportContentDictionaries[index].reportTypeCode=='COMPANY_CONTACT_PERSON'"  :problemId="problemId"></personInfo>
              <!--规章制度-->
              <rulesInfo ref="info" v-if="reportContentDictionaries&&reportContentDictionaries[index].reportTypeCode=='RULES_AND_REGULATIONS'" :problemId="problemId"></rulesInfo>
              <!--禁入限制人员-->
              <restrictedInfo ref="info" v-if="reportContentDictionaries&&reportContentDictionaries[index].reportTypeCode=='RESTRICTED_PERSONNEL'" :problemId="problemId"></restrictedInfo>
              <!--发件箱-->
              <outboxInfo ref="info" v-if="reportContentDictionaries&&reportContentDictionaries[index].reportTypeCode=='SASAC_OUTBOX'" :problemId="problemId"></outboxInfo>
              <!--实时报告-->
              <reportSummary ref="info" v-if="reportContentDictionaries&&reportContentDictionaries[index].reportTypeCode=='ACTUAL_REPORT'" @cancel="cancel" @actualStage="actualStage"  :problemId="problemId"></reportSummary>
              <!--定期报告-->
              <periodicInfo  ref="info" v-if="reportContentDictionaries&&reportContentDictionaries[index].reportTypeCode=='REGULAR_REPORT'" :problemId="problemId"></periodicInfo>
            </div>
            <div class="body-btn">
              <div class="layui-row">
                <el-button
                  class="float-right"
                  size="mini"
                  plain
                  type="danger"
                  @click="ReportContent"
                >确认
                </el-button>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
    <!--编辑弹出层-->
    <div>
      <!--企业基本信息-->
      <areaEdit
        ref="edit"
      :problemId="problemId"
      v-if="reportContentDictionaries[index]&&reportContentDictionaries[index].reportTypeCode=='COMPANY_BASIC_INFO'"
        :key="index"
      @editClose="editClose"
      ></areaEdit>
      <!--企业联系人-->
      <personEdit
        ref="edit"
        :problemId="problemId"
        v-if="reportContentDictionaries[index]&&reportContentDictionaries[index].reportTypeCode=='COMPANY_CONTACT_PERSON'"
        :key="index"
        @editClose="editClose"
      >
      </personEdit>
      <!--规章制度-->
      <rulesEdit
        ref="edit"
        :problemId="problemId"
        v-if="reportContentDictionaries[index]&&reportContentDictionaries[index].reportTypeCode=='RULES_AND_REGULATIONS'"
        :key="index"
        @editClose="editClose"
      >
      </rulesEdit>
      <!--禁入限制人员-->
      <restrictedEdit
        ref="edit"
        :problemId="problemId"
        v-if="reportContentDictionaries[index]&&reportContentDictionaries[index].reportTypeCode=='RESTRICTED_PERSONNEL'"
        :key="index"
        @editClose="editClose"
      >
      </restrictedEdit>
      <!--发件箱-->
      <outboxEdit
        ref="edit"
        :problemId="problemId"
        v-if="reportContentDictionaries[index]&&reportContentDictionaries[index].reportTypeCode=='SASAC_OUTBOX'"
        :key="index"
        @editClose="editClose"
      >
      </outboxEdit>
      <!--定期报告-->
      <periodicEdit
        ref="edit"
        :problemId="problemId"
        v-if="reportContentDictionaries[index]&&reportContentDictionaries[index].reportTypeCode=='REGULAR_REPORT'"
        :key="index"
        @editClose="editClose"
      >
      </periodicEdit>


      <!--流程-->
      <Process
        :key="processIndex"
        ref="process"
        :refresh-assignee-url="flowInfo.refreshAssigneeUrl"
        :save-btn-type="flowInfo.saveBtnType"
        :tab-flag="flowInfo.tabFlag"
        :select-value="{
        busiKey:flowInfo.busiKey,
        title:flowInfo.title
      }"
        :center-variable="{}"
        @close="closeAdd"
      />
    </div>
  </div>
</template>
<script>
  import {
    reportDetailBasicData,
    reportContentConfirm,
    validEachContentConfirmedStatus,
    deleteReportContent,
    saveReportSasacRecord,
    reportStructuredDataPackage
    ,flowParams
    ,cancelConfirmedStatus
  } from "@/api/sasac/reportManagement/edit/index";

  import areaInfo from './detail/enterprisBasicInformation';//企业基本信息-详情页
  import areaEdit from './edit/area';//企业基本信息-编辑
  import personInfo from './detail/enterpriseContact';//企业联系人-详情页
  import personEdit from './edit/person';//企业联系人-编辑
  import rulesInfo from './detail/enterpriseRulesSystems';//规章制度-详情页
  import rulesEdit from './edit/rules';//规章制度-编辑
  import restrictedInfo from './detail/restrictedPersonnel';//禁入限制人员-详情页
  import restrictedEdit from './edit/restricted';//禁入限制人员-编辑
  import outboxInfo from './detail/outbox';//发件箱-详情页
  import outboxEdit from './edit/outbox';//发件箱-编辑
  import reportSummary from './detail/reportSummary';//实时报告-编辑
  import periodicInfo from './detail/periodicReport';//定期-详情页
  import periodicEdit from './edit/periodicReport';//定期-编辑

  import Process from "@/components/process-common/index";


  export default {
    inheritAttrs: false,
    components: {areaInfo,areaEdit,personInfo,personEdit,rulesInfo,rulesEdit,restrictedInfo
      ,restrictedEdit,outboxInfo,outboxEdit,reportSummary,periodicInfo,periodicEdit
    ,Process},
    props: {
      problemId: {
        type: String
      }
    },
    data() {
      return {
        index: 0,
        reportContents: [],//随时变更的数据
        originalData:[],//未变更的数据
        reportData: {},
        reportContentDictionaries: [],
        reportContent:[],//初始选中的值
        actualStageItems:[],
        //流程信息
        flowInfo: {
          processIndex:0,
          busiKey: '', // 业务中获取
          title: '', // 业务中获取
          saveBtnType: true, // 是否需要保存按钮
          tabFlag: true, // 表明是业务发起环节
          refreshAssigneeUrl: '/sasac/flow' // 下环节自定义业务url
        },
        flowKey:'',//流程key
      }
    },
    computed: {},
    watch: {},
    created() {
      this.queryFlowParams();
    },
    mounted() {
    },
    methods: {
      //刷新
      onRefresh(){
        this.$refs.info.onRefresh();
      },
      //点击编辑
      editReport(){
        this.$refs.edit.onShow();
      },
      //编辑关闭
      editClose(commitFlag){
        //更新状态
        this.cancel();
        if(commitFlag && commitFlag == 1){//提交操作，则刷新展示数据
          // 关闭了弹出层
          this.onRefresh();
        }
      },
      //修改actualStage
      actualStage(data){
        this.actualStageItems=data;
      },
      // 根据problemId获取页面初始数据
      ReportDetailBasicData() {
        reportDetailBasicData(this.problemId).then(response => {
          this.reportData = response.data;
          let selectIndexType = response.data.reportContentDictionaries[0]?response.data.reportContentDictionaries[0].reportTypeCode:''
          this.reportContentDictionaries = response.data.reportContentDictionariesAll;
          this.reportContent = response.data.reportContentDictionaries;
          for (var i = 0; i < this.reportContentDictionaries.length; i++) {
            for (var j = 0; j < this.reportContent.length; j++) {
              if(this.reportContentDictionaries[i].reportTypeCode == selectIndexType){
                this.index = i;
              }
              if(this.reportContent[j].reportTypeCode == this.reportContentDictionaries[i].reportTypeCode){
                this.reportContentDictionaries[i].isShow = true;
              }
            }
          }
          this.reportContent.forEach(row => {
            this.reportContents.push(row.reportTypeCode);
            this.originalData.push(row.reportTypeCode);
          });
          this.cancelConfirmedStatusApi()
        });
      },
      //取消全部的确认
      cancelConfirmedStatusApi(){
        let id =this.problemId;
        cancelConfirmedStatus({id:id,reportContent:(this.originalData).toString()}).then(response => {
        });
      },
      //点击左侧
      report(i) {
        this.index = i;
      },
      //点击确认
      ReportContent(){
        this.$modal.confirm('确认【' + this.reportContentDictionaries[this.index].reportTypeName + '】数据正确？').then(() => {
          this.ReportContentConfirm();
        }).catch(() => {
        });
      },
      //确认
      ReportContentConfirm(){

        let reportContentObj = {
          id: this.problemId,
          reportContent: this.reportContentDictionaries[this.index].reportTypeCode,
          actualStageItems: this.actualStageItems
        };
        reportContentConfirm(reportContentObj).then(response => {
          if (200 === response.code) {
            this.reportContentDictionaries[this.index].isConfirm = '1';
            this.$modal.msgSuccess("确认成功！");
          } else {
            this.$modal.alertError(response.msg);
          }
        });
      },
      //刷新状态确认
      cancel(){
        reportDetailBasicData(this.problemId).then(response => {
          let report = response.data.reportContentDictionaries;
          for (var i = 0; i < this.reportContentDictionaries.length; i++) {
            for (var j = 0; j< report.length; j++) {
              if(this.reportContentDictionaries[i].reportTypeCode==report[j].reportTypeCode){
                this.reportContentDictionaries[i].isConfirm = report[j].isConfirm;
              }
            }
          }
        });
      },
      //实时保存
      reportContentsSelect(e){
        let deleteresult = this.originalData.filter(item => !this.reportContents.includes(item));//删除
        let addresult = this.reportContents.filter(item => !this.originalData.includes(item));//新增
        this.originalData =  this.reportContents;
        if(deleteresult.length){//删除
          let selectIndexType = deleteresult[0]
          let deleteIndex = 0
          for (var i = 0; i < this.reportContentDictionaries.length; i++) {
              if(this.reportContentDictionaries[i].reportTypeCode == selectIndexType){
                deleteIndex = i;
              }
          }
          deleteReportContent({id: this.problemId, reportContents: deleteresult}).then(response => {
            if (200 === response.code) {
              if(this.reportContents.length==0){
                this.index = 9;
              }else if(this.reportContents.length==1){//如果是只有一个内容
                for (let i = 0; i < this.reportContentDictionaries.length; i++) {
                  if(this.reportContentDictionaries[i].reportTypeCode == this.reportContents[0]){
                    this.index = i;
                  }
                }
              }else if(deleteIndex==this.index){
                for (let i = 0; i < this.reportContentDictionaries.length; i++) {
                  if(this.reportContentDictionaries[i].reportTypeCode == this.reportContents[0]){
                    this.index = i;
                  }
                }
              }
            } else {
              this.$modal.alertError(response.msg);
            }
          });
        }
        if(addresult.length){//保存
          saveReportSasacRecord({id: this.problemId, reportContents: addresult}).then(response => {
            if (200 === response.code) {
              if(this.reportContents.length==1){//如果是只有一个内容
                for (let i = 0; i < this.reportContentDictionaries.length; i++) {
                  if(this.reportContentDictionaries[i].reportTypeCode == this.reportContents[0]){
                    this.index = i;
                  }
                }
              }
            } else {
              this.$modal.alertError(response.msg);
            }
          });
        }
      },
      //点击提交
      //第一步删除
      submitReport(){
        let result = this.originalData.filter(item => !this.reportContents.includes(item));
        if(result.length==0){//无删除
          this.validEachContentConfirmedStatus();//校验
        }else{
          deleteReportContent({id: this.problemId, reportContents: result}).then(response => {
            if (200 === response.code) {
              this.validEachContentConfirmedStatus();
            } else {
              this.$modal.alertError(response.msg);
            }
          });
        }
      },
      //第二步提交前的校验
      validEachContentConfirmedStatus() {
        validEachContentConfirmedStatus(this.problemId).then(response => {
          if (200 === response.code) {
            this.reportStructuredDataPackage();
          } else {
            this.$modal.alertError(response.msg);
          }
        });
      },
        //第三步点击提交
      reportStructuredDataPackage(){
        const that = this;
        const loading = this.$loading({
          lock: true,//lock的修改符--默认是false
          text: '正在提交中',//显示在加载图标下方的加载文案
          background: 'transparent',
          spinner: 'el-icon-loading',//自定义加载图标类名
        });
        this.flowInfo.processIndex++
        this.flowInfo.busiKey = this.problemId;
        this.flowInfo.title = this.reportData.reportTitle;
        //调用流程发起
        const loadProcessData = {
          businessKey: this.reportData.id,
          title: this.reportData.reportTitle,
          flowKey:this.flowKey
        }
        loading.close()
        this.$confirm('是否确认提交?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          that.$refs.process.handle(1, loadProcessData)
        }).catch(() => {
        });


        // reportStructuredDataPackage(this.problemId).then(response => {
        //   loading.close();
        //   if (200 === response.code) {
        //     this.$modal.msgSuccess('提交成功！');
        //     this.$emit('closeEdit');
        //   } else {
        //     this.$modal.alertError('提交失败！');
        //   }
        // }).catch(err => {
        //   loading.close();
        // });
      },

      closeAdd(){
        this.$emit('closeEdit');
      },

      //获取流程key
      queryFlowParams(){
        flowParams().then((res)=>{
          this.flowKey = res.data.processDefinitionKey;
        })
      },
    }

  }

</script>
<style lang="scss" scoped>
  .common-card {
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 0 10px 0 rgb(155 11 9 / 10%);
  }

  .bottom-content {
    padding-top: 16px;
    height: calc(100% - 95px);
    min-height: 400px;
  }

  .left-report-detail {
    width: 200px;
    background: #FCFAFB;
    opacity: 1;
  }

  .report-detail-ul {
    width: 100%;
    .report-detail-li {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 32px;
      height: 48px;
      line-height: 48px;
      border-bottom: 1px solid #D5DCE2;
    }
  }

  .report-detail-title {
    font-size: 16px;
  }

  .report-detail-li.icon-red {
    .iconfont {
      color: #f5222d;
    }
  }

  .right-report-detail {
    box-sizing: border-box;
    border: 8px solid #F4F4F4;
    padding: 8px;
    width: calc(100% - 214px);
    .right-report-detail-title {
      width: 100%;
      height: 42px;
      line-height: 42px;
      padding: 0 12px;
      box-sizing: border-box;
      background: #F9F9F9;
    }
    .right-report-detail-span {
      font-size: 16px;
      font-weight: bold;
      color: #333333;
    }
  }

  .report-detail-contents {
    overflow: auto;
    width: 100%;
    height: calc(100% - 72px);
  }
  .el-form-item {
    margin-bottom: 10px;
  }
  ::v-deep.el-form-item--medium .el-form-item__content{
    line-height: 28px;

  }
  ::v-deep.el-form-item--medium .el-form-item__label{
      line-height: 28px;
    }
</style>
