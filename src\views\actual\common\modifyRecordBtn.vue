<template>
  <div class="cursor modifyRecord" v-if="findDifferences">
    <img :src="require('@/assets/images/colligate/modifyRecord.png')" @click="Modify">
    <ModifyRecord
      ref="modify"
      :key="modifyData"
      :type="edit"
      @saveModify="saveModify"
    >
    </ModifyRecord>
  </div>
</template>

<script>
  import {actualAndDailyCompareData} from '@/api/actual/common/actualCompareResult';
  import ModifyRecord from './modifyRecord';// modifyRecord
  export default {
    components: {ModifyRecord},
    name: "btn",
    props: {
      businessData: {
        type: String
      },
    },
    data(){
      return{
        edit:false,
        visibleModify:false,
        findDifferences:false,
        modifyData:{}
      }
    },
    created(){
      this.ActualAndDailyCompareData();
    },
    methods:{
      saveModify(){},
      //是否存在修改记录
      ActualAndDailyCompareData(){
        console.log(this.businessData.actualProblemId,this.businessData.id);
        actualAndDailyCompareData({actualProblemId:this.businessData.actualProblemId, actualProblemBusinessId:this.businessData.id}).then(response => {
          if (200 === response.code && response.data.findDifferences) {
            this.modifyData = response.data;
          }
        });
      },
      Modify(){
        this.$refs.modify.show(this.modifyData);
      },
    }
  }
</script>

<style scoped>

</style>
