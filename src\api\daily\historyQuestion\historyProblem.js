import request from '@/utils/request'

/**
 * 查询2019年前日常报送历史问题
 * @param data
 * @param query
 */
export function dailyHistoryProblemBefore2019(data, query) {
  return request({
    url: "/colligate/dailyHistoryProblem/dailyHistoryProblemBefore2019",
    method: "post",
    data: data,
    params: query
  })
}

/**
 * 查询2019年后日常报送历史问题
 * @param data
 * @param query
 */
export function dailyHistoryProblemAfter2019(data, query) {
  return request({
    url: "/colligate/dailyHistoryProblem/dailyHistoryProblemAfter2019",
    method: "post",
    data: data,
    params: query
  })
}

/**
 * 保存2019年及以前日常历史问题的符合程度
 * @param data
 */
export function saveDailyHistoryProblemBefore2019AccordLevel(data) {
  return request({
    url: "/colligate/dailyHistoryProblem/saveDailyHistoryProblemBefore2019AccordLevel",
    method: "post",
    data: data
  })
}

/**
 * 保存2019年以后日常历史问题的符合程度
 * @param data
 */
export function saveDailyHistoryProblemAfter2019AccordLevel(data) {
  return request({
    url: "/colligate/dailyHistoryProblem/saveDailyHistoryProblemAfter2019AccordLevel",
    method: "post",
    data: data
  })
}

/**
 * 查询全部省份
 */
export function allProvinces() {
  return request({
    url: "/colligate/dailyHistoryProblem/allProvince",
    method: "post"
  })
}
