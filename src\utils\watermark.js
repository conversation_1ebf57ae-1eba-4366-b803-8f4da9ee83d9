let addWaterMark = (config) =>{
  //默认设置
  var defaultSettings = {
    watermark_txt: config.showString || "",
    watermark_x: 20, //水印起始位置x轴坐标
    watermark_y: 20, //水印起始位置Y轴坐标
    watermark_rows: 0, //水印行数
    watermark_cols: 0, //水印列数
    watermark_x_space: 10, //水印x轴间隔
    watermark_y_space: 120, //水印y轴间隔
    watermark_color: '#000000', //水印字体颜色
    watermark_alpha: Number(config.style.opacity) || 0.08, //水印透明度
    watermark_fontsize: '18px', //水印字体大小
    watermark_font: 'Vedana', //水印字体
    watermark_width:  Number(config.style.width) || 300, //水印宽度
    watermark_height:  Number(config.style.height) || 50, //水印长度
    watermark_angle:  Number(config.style.rotate) || 30 //水印倾斜度数
  };
  // if (arguments.length >= 1 && typeof arguments[0] === "object") {
  //   var src = arguments[0] || {};
  //   for (key in src) {
  //     if (src[key] && defaultSettings[key] && src[key] === defaultSettings[key]) continue;
  //     else if (src[key]) defaultSettings[key] = src[key];
  //   }
  // }
  let id = '1.23452384164.123412415'
  if (document.getElementById(id) !== null) {
    document.body.removeChild(document.getElementById(id))
  }
  var oTemp = document.createElement("div")
  oTemp.id = id
  //获取页面最大宽度
  var page_width = Math.max(document.body.scrollWidth, document.body.clientWidth);
  var cutWidth = page_width * 0.0150;
  var page_width = page_width - cutWidth;
  //获取页面最大高度
  var page_height = Math.max(document.body.scrollHeight, document.body.clientHeight)-200;
  page_height = Math.max(page_height, window.innerHeight - 80);
  //如果将水印列数设置为0，或水印列数设置过大，超过页面最大宽度，则重新计算水印列数和水印x轴间隔
  if (defaultSettings.watermark_cols == 0 ) {
    defaultSettings.watermark_cols = parseInt((page_width) / (defaultSettings.watermark_width));
    //defaultSettings.watermark_x_space = parseInt((page_width - defaultSettings.watermark_x - defaultSettings.watermark_width * defaultSettings.watermark_cols) / (defaultSettings.watermark_cols - 1));
  }
  //如果将水印行数设置为0，或水印行数设置过大，超过页面最大长度，则重新计算水印行数和水印y轴间隔
  if (defaultSettings.watermark_rows == 0) {
    defaultSettings.watermark_rows = parseInt((defaultSettings.watermark_y_space + page_height - defaultSettings.watermark_y) / (defaultSettings.watermark_height + defaultSettings.watermark_y_space));
    defaultSettings.watermark_y_space = parseInt(((page_height - defaultSettings.watermark_y) - defaultSettings.watermark_height * defaultSettings.watermark_rows) / (defaultSettings.watermark_rows - 1));
  }
  var x;
  var y;
  for (var i = 0; i < defaultSettings.watermark_rows; i++) {
    y = defaultSettings.watermark_y + (defaultSettings.watermark_y_space + defaultSettings.watermark_height) * i;
    for (var j = 0; j < defaultSettings.watermark_cols; j++) {
      x = defaultSettings.watermark_x + (defaultSettings.watermark_width + defaultSettings.watermark_x_space) * j;
      var mask_div = document.createElement('div');
      mask_div.id = 'mask_div' + i + j;
      mask_div.className = 'mask_div';
      mask_div.appendChild(document.createTextNode(defaultSettings.watermark_txt));
      //设置水印div倾斜显示
      mask_div.style.webkitTransform = "rotate(-" + defaultSettings.watermark_angle + "deg)";
      mask_div.style.MozTransform = "rotate(-" + defaultSettings.watermark_angle + "deg)";
      mask_div.style.msTransform = "rotate(-" + defaultSettings.watermark_angle + "deg)";
      mask_div.style.OTransform = "rotate(-" + defaultSettings.watermark_angle + "deg)";
      mask_div.style.transform = "rotate(-" + defaultSettings.watermark_angle + "deg)";
      mask_div.style.visibility = "";
      mask_div.style.position = "absolute";
      mask_div.style.left = x + 'px';
      mask_div.style.top = y + 'px';
      mask_div.style.overflow = "hidden";
      mask_div.style.zIndex = "9999";
      //让水印不遮挡页面的点击事件
      mask_div.style.pointerEvents = 'none';
      mask_div.style.opacity = defaultSettings.watermark_alpha;
      mask_div.style.fontSize = defaultSettings.watermark_fontsize;
      mask_div.style.fontFamily = defaultSettings.watermark_font;
      mask_div.style.color = defaultSettings.watermark_color;
      mask_div.style.textAlign = "center";
      mask_div.style.width = defaultSettings.watermark_width + 'px';
      mask_div.style.height = defaultSettings.watermark_height + 'px';
      mask_div.style.display = "block";
      oTemp.appendChild(mask_div);
    };
  };
  document.body.appendChild(oTemp);
}

let getNow = () => {
  var d = new Date();
  var year = d.getFullYear();
  var month = change(d.getMonth() + 1);
  var day = change(d.getDate());
  var hour = change(d.getHours());
  var minute = change(d.getMinutes());
  var second = change(d.getSeconds());

  function change(t) {
    if (t < 10) {
      return "0" + t;
    } else {
      return t;
    }
  }
  var time = year + '-' + month + '-' + day + '  ' + hour + ':' + minute + ':' + second + '';
  return time;
}


// let addWaterMark = (config) => {
//   let id = '1.23452384164.123412415'
//   if (document.getElementById(id) !== null) {
//     document.body.removeChild(document.getElementById(id))
//   }
//
//   //创建一个画布
//   let can = document.createElement('canvas')
//   // 设置canvas画布大小
//   can.width = config.style.width
//   can.height = config.style.height
//   let cans = can.getContext('2d')
//   cans.rotate(config.style.rotate * Math.PI / 180) // 水印旋转角度
//   cans.font = '15px Vedana'
//   cans.fillStyle = '#666666' //设置填充绘画的颜色、渐变或者模式
//   cans.textAlign = 'center' //设置文本内容的当前对齐方式
//   cans.textBaseline = 'Middle' //设置在绘制文本时使用的当前文本基线
//   cans.fillText(config.showString, can.width / 8, can.height/ 2) // 在画布上绘制填色的文本（输出的文本，开始绘制文本的X坐标位置，开始绘制文本的Y坐标位置）
//   // cans.fillText(str2, can.width / 2, can.height + 22)//根据需求可添加多行水印，在方法中添加str2..
//   let div = document.createElement('div')
//   div.id = id
//
//   const styleStr = `
//     pointer-events: none;
//     top: 60px;
//     left: 0px;
//     opacity: ${config.style.opacity};
//     position: fixed;
//     z-index: 100000;
//     width: ${document.documentElement.clientWidth}px;
//     height: ${document.documentElement.clientHeight}px;
//     background-image:url('${can.toDataURL("image/png")}');
//   `;
//   div.setAttribute('style', styleStr);
//
//   // if(container){
//   //   container.appendChild(div)
//   // }
//   // else {
//   //   document.body.appendChild(div)
//   // }
//   document.body.appendChild(div)
//
//   // const observer = new MutationObserver(() => {
//   //   const wmInstance = document.getElementById(id);
//   //   if ((wmInstance && wmInstance.getAttribute('style') !== styleStr) || !wmInstance) {
//   //     //如果标签在，只修改了属性，重新赋值属性
//   //     if (wmInstance) {
//   //       // 避免一直触发
//   //       // observer.disconnect();
//   //       // console.log('水印属性修改了');
//   //       wmInstance.setAttribute('style', styleStr);
//   //     } else {
//   //       /* 此处根据用户登录状态，判断是否终止监听，避免用户退出后登录页面仍然有水印 */
//   //       if (store.getters.token) {
//   //         //标签被移除，重新添加标签
//   //         // console.log('水印标签被移除了');
//   //         document.body.appendChild(watermark)
//   //       } else {
//   //         observer.disconnect();
//   //       }
//   //     }
//   //   }
//   // })
//   // observer.observe(document.body, {
//   //   attributes: true,
//   //   subtree: true,
//   //   childList: true,
//   // });
//
//   return id
// }

let loadDefaultConfiguration = (configAttr, loginInfo) => {
  let wmConfig = {
    showString : '',
    style: {
      width: 240,
      height: 50,
      rotate: 25,
      opacity: 0.08
    }
  }
  configAttr.forEach((attr) => {
    // 设置文字
    if(attr.attrType == '1' ){
      if(attr.attrId == '10001'){
        //IP
        wmConfig.showString += " " + loginInfo.loginIP
      }else if(attr.attrId == '10002'){
        //账号
        wmConfig.showString += " " + loginInfo.staff
      }else if(attr.attrId == '10003'){
        //日期
        wmConfig.showString += " " + getNow()
      }else if(attr.attrId == '10004'){
        //自定义文字
        wmConfig.showString += " " + attr.attrValue
      }
    }
    // 设置样式
    if(attr.attrType == '2' ){
      if(attr.attrId == '20001'){
        //水印宽度
        wmConfig.style.width = attr.attrValue
      }else if(attr.attrId == '20002'){
        //水印高度
        wmConfig.style.height = attr.attrValue
      }else if(attr.attrId == '20003'){
        //水印倾角
        wmConfig.style.rotate = attr.attrValue
      }else if(attr.attrId == '20004'){
        //水印透明度
        wmConfig.style.opacity = attr.attrValue
      }
    }
  })
  return wmConfig
}

// 添加水印
export function setWaterMark(configAttr, loginInfo) {

  let config = loadDefaultConfiguration(configAttr, loginInfo);
  let id = addWaterMark(config)
  if (document.getElementById(id) === null) {
    id = addWaterMark(config)
  }
  window.onresize = () => {
    addWaterMark(config)
  }
}
// 移除水印
export function clearWatermark() {
  let id = '1.23452384164.123412415'
  if (document.getElementById(id) !== null) {
    document.body.removeChild(document.getElementById(id))
  }
}
