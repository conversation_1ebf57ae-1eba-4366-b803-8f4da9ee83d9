<!--企业基本信息-->
<template>
  <div class="app-areaList padding_b10">
    <SearchList :searchList="searchList" @on-search="handleQuery" @on-reset="resetQuery"></SearchList>
    <el-table :data="tableList" :header-cell-style="{background:'#F4F8FC',color:'#606266','text-align':'center'}" :cell-style="{'text-align':'center'}">
      <el-table-column label="序号" type="index" min-width="4%" align="center">
        <template slot-scope="scope">
          <table-index
          :index="scope.$index"
          :pageNum="queryParams.pageNum"
          :pageSize="queryParams.pageSize"
          />
        </template>
      </el-table-column>
      <el-table-column label="省分" prop="involProvName" min-width="8%"/>
      <!--<el-table-column label="地市" prop="involAreaName"  min-width="12%"/>-->
      <el-table-column label="企业名称" prop="involOrgName"  min-width="20%"  show-overflow-tooltip/>
      <el-table-column label="企业简称" prop="involOrgNameBak" min-width="15%" show-overflow-tooltip/>
      <el-table-column label="社会信用代码" prop="socialCreditCode" min-width="15%"  show-overflow-tooltip/>
      <el-table-column label="所属行业名称" prop="industryName" min-width="15%"  show-overflow-tooltip/>
      <el-table-column label="行业代码" prop="industryCode" min-width="15%"  show-overflow-tooltip/>
      <el-table-column label="操作" fixed="right" width="100" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            v-if="scope.row.dispatchStatus != 0"
            size="mini"
            type="text"
            icon="el-icon-search"
            title="查看"
            @click="handleDetails(scope.row)"
          ></el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="areaBaseInfo"
    />
    <areaDetail v-if="dialogVisible" v-on:closeModal="closeModal" :id="id"></areaDetail>
  </div>
</template>

<script>
  import {getAreaBaseInfo} from "@/api/base/area";
  import areaDetail from "./areaDetail";
  import SearchList from "../../common/SearchList";

  export default {
    name: "areaList",
    components: { areaDetail, SearchList  },
    data() {
      return {
        // 显示搜索条件
        showSearch: true,
        // 总条数
        total: 0,
        // 表格数据
        tableList: [],
        // 是否显示弹出层
        dialogVisible: false,
        id:'',
        //新增主键
        //日常问题查询 参数
        queryParams: {
          pageNum: 1,
          pageSize: 10,
          involProvName:'',
          involOrgName:'',
        },
        searchList: [
          {
            label: "省分",
            name: "involProvName",
            value: null,
            type: "Input"
          },
          {
            label: "企业名称",
            name: "involOrgName",
            value: null,
            type: "Input"
          }
        ]
      };
    },
    created() {
      this.areaBaseInfo();
    },
    methods: {
      /**查询企業基本信息列表*/
      areaBaseInfo() {
       //this.loading = true;
        getAreaBaseInfo(this.queryParams).then(
          response => {
            this.tableList = response.rows;
            this.total = response.total;
            //this.loading = false;
          }
        );
      },
      /** 搜索按钮操作*/
      handleQuery(params) {
        this.queryParams={
          ...this.queryParams,
          ...params,
          pageNum: 1,
        }
        this.areaBaseInfo();
      },
      /**重置按钮操作*/
      resetQuery() {
        this.queryParams = {
          pageNum: 1,
          pageSize: 10,
        };
        this.areaBaseInfo();
      },
      /**查看按钮操作*/
      handleDetails(row) {
        this.dialogVisible = !this.dialogVisible;
        this.id = row.id;
      },
      /**关闭模态框*/
      closeModal(){
        this.dialogVisible = !this.dialogVisible;
      }
    }
  };
</script>
<style rel="stylesheet/scss" lang="scss">
</style>







