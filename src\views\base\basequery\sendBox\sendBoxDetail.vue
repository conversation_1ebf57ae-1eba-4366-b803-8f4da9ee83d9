<template>
    <el-dialog
        title="发件箱详情"
        :visible.sync="dialogVisible"
        width="70%"
        :before-close="handleClose">
        <div>
          <el-form class="common-card padding10_0" size="medium"  label-width="130px">
            <el-row>
              <el-col :span="24">
                <el-form-item label="收件人"><span>{{dataDetails.receivePerson}}</span></el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="发件人"><span>{{dataDetails.sendPerson}}</span></el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="标题"><span>{{dataDetails.title}}</span></el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="发送内容"><span>{{dataDetails.mailContent}}</span></el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
      <BlockCard
        title="附件列表"
      >
        <el-table
          border
          :data="dataDetails.files"
          ref="table2"
          :show-header="false"
          :cell-class-name="rowClass"
        >
          <el-table-column
            fixed
            align="center"
            label="序号"
            type="index"
            min-width="10%">
          </el-table-column>
          <el-table-column label="文档名称" prop="fileName" min-width="60%"/>
          <el-table-column label="上传人" prop="createLoginName" min-width="15%"/>
          <el-table-column label="上传时间" prop="createTime" :formatter="dateFormat" min-width="13%"/>
          <el-table-column label="操作" prop="del" min-width="15%" fixed="right"
                           align="center"
          >
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                title="下载"
                icon="el-icon-bottom"
                @click="downloadFile(scope.row)"
              >
              </el-button>
              <!-- <el-button
                size="mini"
                type="text"
                icon="el-icon-search"
              >预览
              </el-button> -->
            </template>
          </el-table-column>
        </el-table>
      </BlockCard>
    </el-dialog>
</template>

<script lang="ts">
  import {getSendBoxById} from "@/api/base/sendBox";
  import moment from "moment";
  import BlockCard from '@/components/BlockCard';

  export default {
    components:{BlockCard},
    name: "sendBoxDetail",
    props: {
      dialogVisible: {
        type: Boolean,
        default: true
      },
      id: {
        type: String,
        default: ''
      },
    },
    data() {
      return {
        dataDetails: {},
      };
    },
    created() {
      this.sendBoxInfo();
    },
    methods: {
      /**查询企业基本信息详情*/
      sendBoxInfo() {
       //this.loading = true;
        getSendBoxById({id: this.id}).then(
          response => {
            this.dataDetails = response.data;
            //this.loading = false;
          }
        );
      },
      /**关闭模态框*/
      handleClose() {
        this.$emit("closeModal");
      },
      /** 修改附件表样式 */
      rowClass ({ row, column, rowIndex, columnIndex }) {
        if (columnIndex === 1 || columnIndex === 2) {
          return 'no-right-border'
        }else if(columnIndex === 0){
          return 'cell-color'
        }
      },
      /*日期处理*/
      dateFormat:function(row,column){
        var date = row[column.property];
        if(date === undefined){
          return ''
        }
        return moment(date).format("YYYY-MM-DD")
      },
      // 文件下载处理
      handleDownload(row) {
        var name = row.fileName;
        var url = row.filePath;
        var suffix = url.substring(url.lastIndexOf("."), url.length);
        const a = document.createElement('a')
        a.setAttribute('download', name + suffix)
        a.setAttribute('target', '_blank')
        a.setAttribute('href', url)
        a.click()
      },
      /** 下载附件 */
      downloadFile(row) {
        this.download('/sys/documentTemplate/downloadByAttachmentId', { id: row.id }, row.fileName)
      },
    }
  };
</script>
