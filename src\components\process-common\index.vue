<template>
  <div>
    <div v-if="!tabFlag">
      <el-button type="primary" icon="el-icon-tickets" v-show="saveBtnType.saveBtn" @click="todoSave" size="mini" v-preventReClick plain>保存</el-button>
      <el-button type="primary" icon="el-icon-check" @click="todoPass(1)" size="mini" plain>下一步</el-button>
      <el-button type="primary" icon="el-icon-document-delete" @click="todoPass(4)" v-if="flowCfgLink.buttonBreak" size="mini" plain>中止</el-button>
      <el-button type="primary" icon="el-icon-sort" @click="todoPass(3)" size="mini" v-if="flowCfgLink.buttonTurn" plain>转派</el-button>
      <el-button type="primary" icon="el-icon-s-fold" @click="todoPass(2)" v-if="flowCfgLink.buttonBack" size="mini" plain>退回</el-button>
      <el-button @click="closeEmit" icon="el-icon-close" size="mini">关闭</el-button>
    </div>
    <el-dialog class="process" v-bind="$attrs"  :visible.sync="processVisible" @close="onClose" width="750" :title="processTitle" append-to-body="true">
      <el-form ref="elForm" :model="formData" :rules="rules" size="medium" label-width="115px">
        <el-form-item label="下一环节名称" prop="nextLinkKey" v-show="processType==1||processType==2">
          <el-select @change="changeLink($event)" v-model="formData.nextLinkKey" placeholder="请选择下一环节名称" clearable :style="{width: '100%'}">
            <el-option  label="--请选择--" value=""></el-option>
            <el-option  v-for="(item, index) in nextLinkKey" :key="index" :label="item.label"
                        :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="下环节处理人" prop="assignee"  v-show="processType!=4" >
          <div v-show="refreshNextAssigneeList.length==0" @click="jumpAssignee()" class="cursor">
            <el-input readonly  v-model="selectTree[0].name" placeholder="请选择下环节处理人" ></el-input>
          </div>
          <el-select  v-show="refreshNextAssigneeList.length!=0" v-model="formData.assignee" filterable placeholder="请选择下环节处理人" clearable :style="{width: '100%'}">
            <el-option  label="--请选择--" value=""></el-option>
            <el-option v-for="(item, index) in refreshNextAssigneeList" :key="index" :label="item.label"
                       :value="item.value" :disabled="!item.disable"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="处理意见" prop="processComment">
          <el-input v-model="formData.processComment" type="textarea" placeholder="请输入处理意见"
                    :autosize="{minRows: 4, maxRows: 4}" :style="{width: '100%'}"></el-input>
        </el-form-item>
        <!--<el-form-item >-->
        <!--<el-alert-->
        <!--title="温馨提示：双击可选择常用意见；选中意见内容，点击鼠标右键，可以将选中的内容添加到常用意见，或从常用意见中删除。"-->
        <!--type="warning"-->
        <!--show-icon>-->
        <!--</el-alert>-->
        <!--</el-form-item>-->
        <el-form-item label="发送短信"  v-show="processType!=3" >
          <el-switch v-model="formData.sendMsg"></el-switch>
        </el-form-item>
        <el-form-item label="发送邮箱"  v-show="processType!=3" >
          <el-switch v-model="formData.mailMsg"></el-switch>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button type="primary" icon="el-icon-tickets" @click="handelConfirm" v-preventReClick size="mini" plain>提交</el-button>
        <el-button @click="close" icon="el-icon-close" size="mini">关闭</el-button>
      </div>
    </el-dialog>
    <!--渲染无下一环节人情况-->
    <renderNoNextLinkUser ref="linkSelect" @nextLinkUser="nextLinkUser" :key="selectTree" :selectTreeData="selectTree"></renderNoNextLinkUser>
  </div>
</template>
<script>
  import { processLinkData,refreshNextAssignee,startAndSubmitProcess,tasklink,pushProcess,backAssignee,backProcess,flowParams,breakProcess,refreshTurnAssignee,transferProcess } from "@/api/components-new/index";
  import  renderNoNextLinkUser from '@/components/process-common/common/renderNoNextLinkUser';
  export default {
    inheritAttrs: false,
    components: {renderNoNextLinkUser},
    props: {
      refreshAssigneeUrl:'',
      saveBtnType: {
        type: Boolean,
        default: false
      },
      selectValue: {
        type: Object
      },
      centerVariable:{
        type: Object
      },
      // true 流程发起推进 false：待办待阅
      tabFlag: {
        type: Boolean,
        default: false
      },
      flowCfgLink:{
        type:Object,
        default: {
          buttonBack: 0,// 当前环节是否可回退
          buttonBreak: 0,// 当前环节是否可中止
          buttonLastBack: null,
          buttonQuick: 0,// 当前环节是否可进行简退操作（必然为非首环节）
          buttonTurn: 0,// 当前环节是否可转派
          flowLinkId: null,
          flowTypeId: null,
          grabPattern: null,
          histUrl: null,
          isCountersign: 0,// 据是否为会签节点处理按钮事件及显隐
          isHistoryBack: null
        }
      }
    },
    data() {
      return {
        selectTree:[{name:'',id:''}],
        processData:{},//额外参数
        processVisible:false,
        processTitle:'流程提交',
        processType:1,//1:下一步 2:退回 3:转派 4:中止
        formData: {
          nextLinkKey: undefined,
          nextLinkName:undefined,
          assignee:'',
          processComment: '',
          sendMsg: false,
          mailMsg:false,
        },
        rules: {
          nextLinkKey: [{
            required: true,
            message: '请选择下一环节名称',
            trigger: 'change'
          }],
          assignee: [{
            required: true,
            message: '请选择下环节处理人',
            trigger: 'change'
          }],
          processComment: [{
            required: true,
            message: '请输入处理意见',
            trigger: 'blur'
          }]
        },
        taskDefinitionKey:'',
        processDefinitionKey:'',
        nextLinkKey: [],
        refreshNextAssigneeList: [],
        loading : false
      }
    },
    computed: {},
    watch: {},
    created() {
    },
    mounted() {},
    methods: {
      jumpAssignee(){
        this.$refs.linkSelect.openSelect()
      },
      openFullScreen2() {
        this.loading = this.$loading({
          background: 'rgba(255,255,255,0)',
          spinner: 'el-icon-loading', // 自定义加载图标类名
          text: '正在加载...', // 显示在加载图标下方的加载文案
          lock: false // lock的修改符--默认是false
        });
      },
      onOpen() {},
      onClose() {
        this.processVisible=false;
      },
      close() {
        this.processVisible=false;
      },
      closeEmit() {
        this.$emit('close');
      },
      async FlowParams(type,object){
        if(this.centerVariable&&this.centerVariable.flowKey=="specialReport"){
          this.processDefinitionKey=this.centerVariable.flowKey;
          this.processFun(type,object);
        }else{
          await flowParams(this.refreshAssigneeUrl).then(
            response => {
              // this.processDefinitionKey='SupervisionDailyReport';
              this.processDefinitionKey=response.data.processDefinitionKey;
              this.processFun(type,object);
            }
          )
        }
      },

      /** 流程 1:下一步 2:退回 3:转派 4:中止*/
      handle(type,object) {
        this.FlowParams(type,object);
        this.processType = type;
      },

      //流程操作
      processFun(type,object){
        if(object){
          this.processData = object;
        }else{
          this.processData = {};
        }
        if(type===1){
          this.processTitle='流程提交';
          if(this.tabFlag){//流程发起并送审
            this.ProcessLinkData();
          }else{//流程推进
            this.Tasklink(1);
          }
        }else if(type===2){
          this.processTitle='流程退回';
          this.Tasklink(2);
        }else if(type===3){
          this.processTitle='流程转派';
          this.RefreshTurnAssignee();
        }else{
          this.processTitle='流程中止';
          this.processVisible=true;
        }
        this.resetForm('elForm');
      },
      /** 下一环节名称 */
      ProcessLinkData(){
        processLinkData(this.processDefinitionKey,this.processData).then(
          response => {
            this.nextLinkKey = response.data.dataRows;
            this.formData.nextLinkKey = response.data.dataRows[0].value;
            this.formData.nextLinkName = response.data.dataRows[0].label;
            this.taskDefinitionKey = response.data.dataRows[0].value;
            this.refreshNextData();
            this.processVisible=true;
          }
        );
      },
      /** 通过或者退回下一环节名称 */
      Tasklink(type){
        tasklink({processInstanceId:this.selectValue.processInstanceId,linkKey:this.selectValue.linkKey,processDefinitionKey:this.processDefinitionKey,flowKeyReV:this.centerVariable.flowKeyReV,handleType:type},this.processData).then(
          response => {
            this.nextLinkKey = response.data.dataRows;
            this.formData.nextLinkKey = response.data.dataRows[0].value;
            this.formData.nextLinkName = response.data.dataRows[0].label;
            this.taskDefinitionKey = response.data.dataRows[0].value;
            if(type===2){//回退
              if(response.data.dataRows.length>1){
                this.formData.nextLinkKey ='';
                this.formData.nextLinkName = '';
                this.taskDefinitionKey = '';
                this.taskDefinitionKey = this.selectValue.linkKey;
                //this.BackAssignee();
                this.processVisible=true;
              }else{
                this.nextLinkKey = response.data.dataRows;
                this.formData.nextLinkKey = response.data.dataRows[0].value;
                this.formData.nextLinkName = response.data.dataRows[0].label;
                this.taskDefinitionKey = response.data.dataRows[0].value;
                this.BackAssignee();
                this.processVisible=true;
              }
            }else{
              if(this.formData.nextLinkKey=='a999'){
                this.$confirm('是否结束流程？点击【确定】结束流程。', '提示', {
                  confirmButtonText: '确定',
                  cancelButtonText: '取消',
                  type: 'warning'
                }).then(() => {
                  this.handelConfirmEnd();
                }).catch(() => {});
              }else{
                this.aefreshNextData();
                this.processVisible=true;
              }
            }
          }
        );
      },
      /** 转派下一环节取人 */
      // /workflowRestController/refreshTurnAssignee
      RefreshTurnAssignee(){
        refreshTurnAssignee({processDefinitionKey:this.processDefinitionKey,taskDefinitionKey:this.taskDefinitionKey,processDefinitionId:this.processDefinitionKey,processInstanceId:this.selectValue.processInstanceId}).then(
          response => {
            this.refreshNextAssigneeList = response.data;
            this.processVisible=true;
            if(response.data.length==1){
              this.formData.assignee = response.data[0].value;
            }else{
              for (let i in response.data) {
                if(response.data[i].checkFlag=='1'){
                  this.formData.assignee = response.data[i].value;
                }
              }
            }

          }
        );
      },
      /** 下一环节选择 */
      changeLink(e){
        let obj = {};
        obj = this.nextLinkKey.find((item)=>{
          return item.value === e;
        });
        this.formData.nextLinkName=obj.label;
        if(this.processType==1){//下一步
          this.aefreshNextData();
        }else if(this.processType==2){//退回
          this.BackAssignee();
        }
      },
      /** 下一环节处理人 */
      refreshNextData(){
        let dataForm = {
          processDefinitionKey:this.processDefinitionKey,
          taskDefinitionKey:this.taskDefinitionKey,
          processDefinitionId:this.processDefinitionKey
        };
        refreshNextAssignee(this.refreshAssigneeUrl,dataForm).then(
          response => {
            this.refreshNextAssigneeList = response.data;
            if(response.data.length==1){
              this.formData.assignee = response.data[0].value;
            }else{
              for (let i in response.data) {
                if(response.data[i].checkFlag=='1'){
                  this.formData.assignee = response.data[i].value;
                }
              }
            }

          }
        );
      },

      //无下环节处理人时
      nextLinkUser(data){
        this.selectTree = data;
        this.formData.assignee = data[0].id;

      },
      someMethod() {
        return new Promise((resolve, reject) => {
          const callback = (data) => {
            resolve(data);
          };
          this.$emit('loadProcessData', callback); // 传递一个回调给父组件
        });
      },

      /** 推进下一环节处理人 */
      aefreshNextData(){
        let dataForm = {processDefinitionKey:this.processDefinitionKey,taskDefinitionKey:this.taskDefinitionKey,processDefinitionId:this.processDefinitionKey,processInstanceId:this.selectValue.processInstanceId};
        refreshNextAssignee(this.refreshAssigneeUrl,dataForm).then(
          response => {
            this.refreshNextAssigneeList = response.data;

            if(response.data.length==1){
              this.formData.assignee = response.data[0].value;
            }else{
              for (let i in response.data) {
                if(response.data[i].checkFlag=='1'){
                  this.formData.assignee = response.data[i].value;
                }
              }
            }
          }
        );
      },
      /** 退回下一环节处理人 */
      BackAssignee(){
        let dataForm = {processInstanceId:this.selectValue.processInstanceId,processDefinitionId:this.processDefinitionKey,processDefinitionKey:this.processDefinitionKey,taskDefinitionKey: this.formData.nextLinkKey};
        backAssignee(dataForm).then(
          response => {
            this.refreshNextAssigneeList = [];
            this.refreshNextAssigneeList.push(response);
            this.formData.assignee = response.value;
          }
        );
      },
      //发起
      handelConfirm() {
        if(this.processType!=1&&this.processType!=2){
          this.rules.nextLinkKey[0].required = false;
        }
        if(this.processType==4){
          this.rules.assignee[0].required = false;
        }
        this.$refs['elForm'].validate(valid => {
          if (!valid) return
          if(this.processType===1){//发起流程
            if(this.tabFlag){//流程发起并送审
              this.StartAndSubmitProcess();
            }else{//流程推进
              this.PushProcess();
            }
          }else if(this.processType===2){//退回
            this.BackProcess();
          }else if(this.processType===4){//中止
            this.rules.nextLinkKey[0].required = true;
            this.rules.assignee[0].required = true;
            this.BreakProcess();
          }else if(this.processType===3){//转派
            this.rules.nextLinkKey[0].required = true;
            this.TransferProcess();
          }
        })
      },
      //结束流程跳过校验
      handelConfirmEnd() {
        if(this.processType===1){//发起流程
          if(this.tabFlag){//流程发起并送审
            this.StartAndSubmitProcess();
          }else{//流程推进
            this.PushProcess();
          }
        }
      },
      //下一步的流程发起
      StartAndSubmitProcess(){
        this.openFullScreen2();
        let dataForm = {flowKey:this.processDefinitionKey,processDefinitionId:this.processDefinitionKey,businessKey:this.selectValue.busiKey,title:this.selectValue.title};
        var data = Object.assign(this.formData,dataForm);
        startAndSubmitProcess(this.refreshAssigneeUrl,data).then(
          response => {
            this.$modal.msgSuccess("发起成功");
            setTimeout(() => {
              this.loading.close();
              this.close();
              this.closeEmit();
            },1500);
          }
        ).catch(() => {
          this.loading.close();
        });
      },

      //下一步的流程推进
      PushProcess(){
        this.someMethod().then((res) => {
          let loadProcessData = res;
          this.openFullScreen2();
          let dataForm = {
            linkKey: this.selectValue.linkKey,
            flowKey: this.centerVariable.flowKey,
            processInstanceId: this.selectValue.processInstanceId,
            businessKey: this.centerVariable.busiKey,
            taskId: this.centerVariable.taskId,
            title: this.selectValue.title
          };
          var data = Object.assign(this.formData, dataForm);
          pushProcess(this.refreshAssigneeUrl, {...data,...loadProcessData}).then(
            response => {
              this.$modal.msgSuccess("操作成功");
              setTimeout(() => {
                this.loading.close();
                this.close();
                this.closeEmit();
              }, 1500);
            }
          ).catch(() => {
            this.loading.close();
          });
        })
      },

      //退回发起
      BackProcess(){
        this.someMethod().then((res) => {
          let loadProcessData = res;
          this.openFullScreen2();
          let dataForm = {
            linkKey: this.selectValue.linkKey,
            flowKey: this.centerVariable.flowKey,
            processInstanceId: this.selectValue.processInstanceId,
            businessKey: this.centerVariable.busiKey,
            taskId: this.centerVariable.taskId,
            title: this.selectValue.title
          };
          var data = Object.assign(this.formData, dataForm);
          backProcess(this.refreshAssigneeUrl, {...data,...loadProcessData}).then(
            response => {
              this.$modal.msgSuccess("退回成功");
              setTimeout(() => {
                this.loading.close();
                this.close();
                this.closeEmit();
              }, 1500);
            }
          ).catch(() => {
            this.loading.close();
          });
        })
      },

      //中止
      BreakProcess(){
        this.openFullScreen2();
        let dataForm = {linkKey:this.selectValue.linkKey,flowKey:this.centerVariable.flowKey,processInstanceId:this.selectValue.processInstanceId,businessKey:this.centerVariable.busiKey,taskId:this.centerVariable.taskId};
        var data = Object.assign(this.formData,dataForm);
        breakProcess(this.refreshAssigneeUrl,data).then(
          response => {
            this.$modal.msgSuccess("中止成功");
            setTimeout(() => {
              this.loading.close();
              this.close();
              this.closeEmit();
            },1500);
          }
        ).catch(() => {
          this.loading.close();
        });
      },
      //转派
      TransferProcess(){
        this.openFullScreen2();
        let dataForm = {linkKey:this.selectValue.linkKey,flowKey:this.centerVariable.flowKey,processInstanceId:this.selectValue.processInstanceId,businessKey:this.centerVariable.busiKey,taskId:this.centerVariable.taskId};
        var data = Object.assign(this.formData,dataForm);
        transferProcess(this.refreshAssigneeUrl,data).then(
          response => {
            this.$modal.msgSuccess("转派成功");
            setTimeout(() => {
              this.loading.close();
              this.close();
              this.closeEmit();
            },1500);
          }
        ).catch(() => {
          this.loading.close();
        });
      },
      /** 保存按钮 */
      todoSave(){
        // 调用子页面保存方法
        this.$emit("publicSave", this);
      },
      /** 流程 1:下一步 2:退回 3:转派 4:中止*/
      todoPass(y){
        if(y==1){
          this.$emit("nextStep", this);
        }else{
          this.handle(y);
        }

      },
    }
  }

</script>
<style scoped lang="scss">
  .process{
    ::v-deep.el-dialog__body{
      padding-top: 16px !important;
      height: auto  !important;
      background: #fff !important;
      padding-left:20px !important;
      padding-right:20px !important;
      padding-bottom:0 !important;
    }
  }
</style>
