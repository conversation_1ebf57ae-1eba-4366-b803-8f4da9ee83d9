<template>
  <div>
    <el-dialog v-bind="$attrs" :visible.sync="visible" v-on="$listeners" @open="onOpen" @close="onClose"  :modal-append-to-body="false" :title="title">
      <el-form ref="elForm" :model="formData" :rules="rules" size="medium" label-width="250px">
        <el-form-item label="是否产生资产损失" prop="lossStateAssetsFlag">
          <el-radio-group v-model="formData.lossStateAssetsFlag" size="medium">
            <el-radio v-for="(item, index) in whetherLossOptions" :key="index" :label="item.value" @change="radioChanged"
                      :disabled="item.disabled">{{item.label}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="预估损失金额（万元）" prop="lossAmount"  v-if="formData.lossStateAssetsFlag">
          <el-input-number v-model="formData.lossAmount"  :min="0"  placeholder="损失金额（万元）" :disabled="lossAmountDisabled"></el-input-number>
        </el-form-item>
        <el-form-item  label="预估损失风险（万元）" prop="lossRisk"  v-if="formData.lossStateAssetsFlag">
          <el-input-number v-model="formData.lossRisk" :min="0"  placeholder="损失风险（万元）"></el-input-number>
        </el-form-item>
        <el-form-item label="是否产生不良影响" prop="isAdverseEffect">
          <el-radio-group v-model="formData.isAdverseEffect" size="medium">
            <el-radio v-for="(item, index) in whetherEffectOptions" :key="index" :label="item.value" @change="radioEffectChanged"
                      :disabled="item.disabled">{{item.label}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="对应不良影响" prop="correspondingAdverseEffects" v-show="formData.isAdverseEffect">
          <el-select v-model="formData.correspondingAdverseEffects" :style="{width: '100%'}" clearable="clearable" multiple="multiple" value="">
            <el-option v-for="(item, index) in dict.type.corresponding_adverse_effect" :key="index" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>

        <!--v-show="showRangeFlag"-->
        <el-form-item
        label="是否违规经营投资责任追究范围" prop="violRangeFlag"  >
          <el-radio-group v-model="formData.violRangeFlag" size="medium">
            <el-radio  v-for="(item, index) in whetherRangeOptions" :key="index" :label="item.value" @change="radioViolRangeFlag"
                      :disabled="item.disabled">{{item.label}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-row  v-if="formData.violRangeFlag">
          <scopeSituationData
            :key="problemId"
            :edit='edit'
            :problemId="problemId"
            :relevantTableId = "relevantTableId"
            :relevantTableName = "relevantTableName"
            ref="scope"
            @scopeSituation="scopeSituation"
          ></scopeSituationData>
        </el-row>
      </el-form>
      <div slot="footer">
        <el-button size="mini" @click="close">取消</el-button>
        <el-button size="mini" type="primary" @click="handelConfirm">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
  import {insertProblemAndRelevantId,sureHandel} from "@/api/daily/add";
  import scopeSituationData from './scopeSituation/scopeSituationData';//范围情形展示

  export default {
    inheritAttrs: false,
    components: {
      scopeSituationData
    },
    dicts: ['corresponding_adverse_effect'],
    props: {},
    data() {
      return {
        radioDisabled:false,
        lossAmountDisabled: false,
        showRangeFlag: false,//无用
        showAdverseEffectFlag: false,
        edit:true,
        title:'新增问题',
        visible:false,//弹框
        scopeSituationData:[],//范围情形
        formData: {
          lossStateAssetsFlag: undefined,
          lossAmount: undefined,
          lossRisk: undefined,
          violRangeFlag: undefined,
          isAdverseEffect: undefined,
          correspondingAdverseEffects: []
        },
        rules: {},
        whetherLossOptions: [{
          "label": "是",
          "value": 1
        }, {
          "label": "否",
          "value": 0
        }],
        whetherRangeOptions: [{
          "label": "是",
          "value": 1,
          "disabled":false
        }, {
          "label": "否",
          "value": 0,
          "disabled":false
        }],
        whetherEffectOptions: [{
          "label": "是",
          "value": 1
        }, {
          "label": "否",
          "value": 0
        }],
        //新增的主键
        problemId:'',
        relevantTableId:'',
        relevantTableName:'',
        //范围情形数据
        queryRangeData:[]
      }
    },
    computed: {},
    watch: {},
    created() {},
    mounted() {},
    methods: {
      //范围情形有无数据
      scopeSituation(data){
        this.scopeSituationData=data;
      },
      // 是否是否违规经营投资责任追究范围必填
      radioChanged(){
        this.lossAmountDisabled = !this.formData.lossStateAssetsFlag;
        if (!this.formData.lossStateAssetsFlag) {
          this.formData.lossAmount = ''
          this.formData.lossRisk = ''
        }else{

        }

        if (this.formData.lossStateAssetsFlag !== undefined && this.formData.isAdverseEffect !== undefined) {
          if (!this.formData.lossStateAssetsFlag && !this.formData.isAdverseEffect) {
            this.showRangeFlag = false;
            this.$confirm('不满足问题受理要求，请修改后【重新填报】或【取消填报】！', '提示', {
              confirmButtonText: '取消填报',
              cancelButtonText: '重新填报',
              type: 'warning'
            }).then(() => {
              this.close();
            }).catch(() => {
            });
          } else {
            this.showRangeFlag = true;
          }

        }
      },
      // 是否是否违规经营投资责任追究范围必填
      radioViolRangeFlag(){
        if(this.formData.violRangeFlag=='1'){
          this.whetherRangeOptions[0]['disabled']=true
          this.whetherRangeOptions[1]['disabled']=true
        }
        if(!this.formData.violRangeFlag){
          this.$confirm('不满足问题受理要求，请修改后【重新填报】或【取消填报】！', '提示', {
            confirmButtonText: '取消填报',
            cancelButtonText: '重新填报',
            type: 'warning'
          }).then(() => {
            this.close();
          }).catch(() => {
          });
        }
      },
      radioEffectChanged() {
        this.showAdverseEffectFlag = this.formData.isAdverseEffect;
        if(!this.formData.isAdverseEffect){
          this.formData.correspondingAdverseEffects=undefined
        }
        if (this.formData.lossStateAssetsFlag !== undefined && this.formData.isAdverseEffect !== undefined) {
          if (!this.formData.lossStateAssetsFlag && !this.formData.isAdverseEffect) {
            this.showRangeFlag = false;
            this.$confirm('不满足问题受理要求，请修改后【重新填报】或【取消填报】！', '提示', {
              confirmButtonText: '取消填报',
              cancelButtonText: '重新填报',
              type: 'warning'
            }).then(() => {
              this.close();
            }).catch(() => {
            });
          } else {
            this.showRangeFlag = true;
          }
        }
      },
      /**新增获取主键*/
      onOpen() {
        insertProblemAndRelevantId().then(
            response => {
              this.problemId = response.data.problemId;
              this.relevantTableId = response.data.relevantTableId;
              this.relevantTableName = response.data.relevantTableName;
            }
            );
      },
      // 显示弹框
      show() {
        this.visible = true;
      },
      onClose() {
        this.$refs['elForm'].resetFields()
      },
      close() {
        this.visible = false;
        this.$emit('update:visible', false)
      },
      handelConfirm() {
        //1、是否产生资产损失为是，则校验输入预估损失金额或预估损失风险，至少填一个
        if(this.formData.lossStateAssetsFlag && !this.formData.lossRisk && !this.formData.lossAmount){
            this.$message.error('请输入预估损失金额或预估损失风险且不能同时为空或0！');
        }else if (this.formData.isAdverseEffect && !this.formData.correspondingAdverseEffects.length) {
          //2、是否产生不良影响，校验对应不良影响不能为空
            this.$message.error('请选择【对应不良影响】！');
        }else if (!this.scopeSituationData.length) {
          //3、违规经营投资责任追究范围不能为空
            this.$message.error('请选择【违规经营投资责任追究范围】！');
        }else if(!this.formData.lossRisk && !this.formData.lossAmount && !this.formData.correspondingAdverseEffects.length){
          //4、是否产生资产损失与是否产生不良影响至少有一个为是（此处用对应的内容不能为空来进行校验）
          this.$confirm('【是否产生资产损失】与【是否产生不良影响】至少有一个为是，不满足问题受理要求，请修改后【重新填报】或【取消填报】！', '提示', {
                confirmButtonText: '取消填报',
                cancelButtonText: '重新填报',
                type: 'warning'
              }).then(() => {
                this.close();
              }).catch(() => {
           });
        }else if(this.formData.isAdverseEffect == undefined){
          this.$message.error('请选择【是否产生不良影响】！');
        }else{
          this.formData.problemId=this.problemId;
            this.formData.relevantTableId=this.relevantTableId;
            this.formData.relevantTableName=this.relevantTableName;
            sureHandel(this.formData).then(
              response => {
                this.$emit('closeConfirm',this.formData);
                this.close()
              }
            );
        }

        // if (!this.formData.lossStateAssetsFlag && !this.formData.violRangeFlag) {
        //   this.$confirm('不满足问题受理要求，请修改后【重新填报】或【取消填报】！', '提示', {
        //     confirmButtonText: '取消填报',
        //     cancelButtonText: '重新填报',
        //     type: 'warning'
        //   }).then(() => {
        //     this.close();
        //   }).catch(() => {
        //   });
        // } else if (this.formData.lossStateAssetsFlag && !this.formData.lossRisk && !this.formData.lossAmount) {
        //   this.$message.error('请输入预估损失金额！');
        // } else if (!this.formData.lossRisk && this.formData.lossRisk !== 0) {
        //   this.$message.error('请输入预估损失风险！');
        // } else if (!this.formData.lossRisk && !this.formData.lossAmount) {
        //   this.$message.error('预估损失金额与预估损失风险不能同时为空或0！');
        // } else if (!this.scopeSituationData.length) {
        //   this.$message.error('请选择【违规经营投资责任追究范围】！');
        // } else if (this.formData.isAdverseEffect && !this.formData.correspondingAdverseEffects.length) {
        //   this.$message.error('请选择【对应不良影响】！');
        // } else {
        //   if (!this.formData.lossAmount) {
        //     this.formData.lossAmount = 0;
        //   }
        //     this.formData.problemId=this.problemId;
        //     this.formData.relevantTableId=this.relevantTableId;
        //     this.formData.relevantTableName=this.relevantTableName;
        //     sureHandel(this.formData).then(
        //       response => {
        //         this.$emit('closeConfirm',this.formData);
        //         this.close()
        //       }
        //     );
        // }
      },
      /** 新增范围情形*/
      scopeAdd() {
        this.$refs.select.show();
      },
    }
  }

</script>
<style lang="scss">
  .el-dialog__body{
    height: 70vh;
    overflow: auto;
  }
</style>
