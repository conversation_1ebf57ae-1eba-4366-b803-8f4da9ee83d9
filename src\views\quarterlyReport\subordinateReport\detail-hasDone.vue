<!--下级单位数据上报-已办-->
<template>
  <div class="scope">
    <el-dialog class="subordinateReport commons_popup" :visible.sync="visible" @close="close" :modal-append-to-body="false"
               append-to-body title="下级单位上报查看" width="90%">
      <div slot="title" class="el-popup-header-title">
        <svg-icon icon-class="edit_file" />
        <span class="el-dialog-header-name">{{ "下级单位上报查看" }}</span>
      </div>
      <BlockCard title="基本信息">
        <el-form ref="elForm" :model="infoData" size="medium" label-width="100px">
          <el-row>
            <el-col :span="8">
              <el-form-item label="上报年度">
                {{ infoData.reportYear }}
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="上报季度">
                {{ infoData.reportQuarter | fromatComon(dict.type.REPORT_QUARTER) }}
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="上报截止日期">
                <el-date-picker
                  format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd"
                  v-model="infoData.reportCloseTime"
                  type="datetime"
                  placeholder=""
                  readonly
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="上报标题">
                {{ infoData.reportTitle }}
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="上报要求">
                {{ infoData.reportRequire }}
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </BlockCard>
      <BlockCard title="附件列表">
        <el-table
          :data="filesData"
          border
          v-loading="tableLoading"
          style="width: 100%"
        >
          <el-table-column
            label="序号"
            type="index"
            min-width="5%"
            align="center"
          />
          <el-table-column label="文件名" prop="fileName" min-width="50%">
            <template slot-scope="scope">
              <div
                style="text-align: left"
                class="overflowHidden-1"
                :title="scope.row.fileName"
              >
                {{ scope.row.fileName || "" }}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            label="上传人"
            prop="createUserName"
            min-width="10%"
            align="center"
          />
          <el-table-column
            label="上传时间"
            prop="createTime"
            min-width="20%"
            align="center"
          />

          <el-table-column
            label="操作"
            fixed="right"
            min-width="15%"
            align="center"
            class-name="small-padding fixed-width"
          >
            <template slot-scope="scope">
              <a @click="fileDownload(scope.row)" class="table-btn">下载</a>
            </template>
          </el-table-column>
        </el-table>
      </BlockCard>
      <BlockCard title="下级单位上报" >
        <template slot="left">
          <div style="position: absolute;top: 0px;margin-left: 20px;">
            <el-tabs v-model="status" @tab-click="queryReportDepartmentList">
              <el-tab-pane :label="tabName0" name="0"></el-tab-pane>
              <el-tab-pane :label="tabName1" name="1"></el-tab-pane>
              <el-tab-pane :label="tabName2" name="2"></el-tab-pane>
            </el-tabs>
          </div>
        </template>
        <template v-slot:right>
          <el-button
            size="mini"
            type="primary"
            v-show="status=='2'"
            icon="el-icon-download"
            class="float-right margin-top-8"
            @click="detailedExport"
            plain
          >导出
          </el-button>
        </template>
        <!--全部-->
        <el-form v-if="status=='0'"  style="height:calc(71vh - 280px);min-height:440px">
          <el-table ref="table" v-loading="loading" border :data="tableList" height="100%" :key="tableList" >
            <el-table-column label="序号" type="index" min-width="4%" align="center"/>
            <el-table-column label="上报单位" prop="reportUnitName" show-overflow-tooltip align="center" min-width="30%"/>
            <el-table-column label="接口人" prop="nickName" align="center" min-width="15%"/>
            <el-table-column label="邮箱" prop="email" show-overflow-tooltip  align="center" min-width="20%"/>
            <el-table-column label="联系电话" prop="phoneNumber" show-overflow-tooltip align="center" min-width="20%"/>
            <el-table-column label="状态" prop="procStatusName" align="center" min-width="6%"/>
          </el-table>
        </el-form>
        <!--进行中-->
        <el-form v-if="status=='1'"  style="height:calc(71vh - 280px);min-height:440px">
          <el-table v-loading="loading" :data="tableList" border :key="tableList" height="100%">
            <el-table-column label="序号" type="index" min-width="4%" align="center"/>
            <el-table-column label="上报单位" show-overflow-tooltip prop="reportUnitName" align="center" min-width="20%"/>
            <el-table-column label="接口人" show-overflow-tooltip prop="nickName" align="center" min-width="15%"/>
            <el-table-column label="邮箱" show-overflow-tooltip prop="email" align="center" min-width="10%"/>
            <el-table-column label="联系电话" show-overflow-tooltip prop="phoneNumber" align="center" min-width="10%"/>
            <el-table-column label="所处阶段" show-overflow-tooltip prop="linkName" align="center" min-width="15%"/>
            <el-table-column label="处理人" show-overflow-tooltip prop="handleUserName" align="center" min-width="10%"/>

          </el-table>
        </el-form>
        <!--已完成-->
        <el-form v-if="status=='2'"  style="height:calc(71vh - 280px);min-height:440px">
          <el-table ref="table" border v-loading="loading" :data="tableList"  height="100%">
            <el-table-column label="序号" type="index" width="100" align="center"  fixed="left"/>
            <el-table-column label="公司名称" prop="reportUnitName" width="200" show-overflow-tooltip fixed="left"/>
            <el-table-column label="基本信息" show-overflow-tooltip>
              <el-table-column label="所属行业" prop="industryType" width="130" show-overflow-tooltip/>
              <el-table-column label="填报季度" prop="reportYearQuarter" align="center" width="130" show-overflow-tooltip>
                <template slot-scope="scope">
                  {{ scope.row.reportYear }}年{{ scope.row.reportQuarterName }}
                </template>
              </el-table-column>
            </el-table-column>

            <el-table-column label="工作部署情况" show-overflow-tooltip>
              <el-table-column label="季度数据" show-overflow-tooltip>
                <el-table-column label="本季度召开领导小组会议（次）" align="center" prop="quarterTeamMeetingTime" width="130"
                                 show-overflow-tooltip/>
                <el-table-column label="本季度召开领导小组办公室会议（次）" align="center" prop="quarterTeamOfficeMeetingTime" width="130"
                                 show-overflow-tooltip/>
                <el-table-column label="本季度召开专题会议（次）" align="center" prop="quarterSpecialMeetingTime" width="130"
                                 show-overflow-tooltip/>
              </el-table-column>
              <el-table-column label="当年累计数据" show-overflow-tooltip>
                <el-table-column label="当年累计召开领导小组会议（次）" align="center" prop="totalLeaderTeamMeetingTime" width="130"
                                 show-overflow-tooltip/>
                <el-table-column label="当年累计召开领导小组办公室会议（次）" align="center" prop="totalTeamOfficeMeetingTime" width="130"
                                 show-overflow-tooltip/>
                <el-table-column label="当年累计召开专题会议（次）" align="center" prop="totalSpecialMeetingTime" width="130"
                                 show-overflow-tooltip/>
              </el-table-column>
            </el-table-column>
            <el-table-column label="累计印发责任追究相关制度数量（项）" align="center" prop="totalAccountabilitySystemNumber" width="130"
                             show-overflow-tooltip/>
            <el-table-column label="体系建设情况" show-overflow-tooltip>
              <el-table-column label="当年累计新增配套制度（项）" align="center" prop="totalNewSupportingSystem" width="130"
                               show-overflow-tooltip/>
              <el-table-column label="新增配套制度名称" prop="newSupportingName" width="200" show-overflow-tooltip/>
              <el-table-column label="当年累计新增工作机制（项）" align="center" prop="totalNewWorkSystem" width="130"
                               show-overflow-tooltip/>
              <el-table-column label="新增工作机制名称" prop="newWorkName" width="200" show-overflow-tooltip/>
              <el-table-column label="累计专职人员数量（人）" align="center" prop="totalProfessionalNumber" width="130"
                               show-overflow-tooltip/>
              <el-table-column label="当年累计新增专职人员数量（人）" align="center" prop="totalNewSpecialPersonNumber" width="130"
                               show-overflow-tooltip/>
              <el-table-column label="主责部门" prop="groupMainDept" width="130" show-overflow-tooltip/>
            </el-table-column>

            <el-table-column label="违规问题线索查办情况" show-overflow-tooltip>
              <el-table-column label="全级次企业" show-overflow-tooltip>
                <el-table-column label="新受理问题线索情况（季度数据）" show-overflow-tooltip>
                  <el-table-column label="本季度新受理问题线索数量（件）" prop="quarterNewProblemNumber" align="center" width="130"
                                   show-overflow-tooltip/>
                  <el-table-column label="本季度涉及资产损失（万元）" prop="lossAmount" align="right" width="130" show-overflow-tooltip>
                    <template slot-scope="scope">
                      {{ scope.row.lossAmount | filterNum }}
                    </template>
                  </el-table-column>
                  <el-table-column label="本季度涉及资产损失风险（万元）" prop="lossRisk" align="right" width="130" show-overflow-tooltip>
                    <template slot-scope="scope">
                      {{ scope.row.lossRisk | filterNum }}
                    </template>
                  </el-table-column>
                </el-table-column>
                <el-table-column label="受理问题线索办理进展情况（当年累计数据）" show-overflow-tooltip>
                  <el-table-column label="当年累计受理问题线索数量（件）" align="center" prop="totalProblemSourceNumber" width="130"
                                   show-overflow-tooltip/>
                  <el-table-column label="上年结转问题线索数量（件）" align="center" prop="lastYearProblemSourceNumber" width="130"
                                   show-overflow-tooltip/>
                  <el-table-column label="其中" show-overflow-tooltip>
                    <el-table-column label="未启动核查（件）" align="center" prop="checkNoStartedNumber" width="200"
                                     show-overflow-tooltip/>
                    <el-table-column label="正在核查（件）" align="center" prop="checkInProcessNumber" width="200"
                                     show-overflow-tooltip/>
                    <el-table-column label="完成核查（件）" align="center" prop="checkCompletedNumber" width="200"
                                     show-overflow-tooltip/>
                  </el-table-column>
                </el-table-column>
              </el-table-column>
            </el-table-column>

            <el-table-column label="追责整改工作成效" show-overflow-tooltip>
              <el-table-column label="当年累计完成追责问题数量（件）" align="center" prop="totalCompletedProblemNumber" width="130"
                               show-overflow-tooltip/>
              <el-table-column label="当年累计追责总人数（人）" align="center" prop="totalAccountabilityPersonNumber" width="130"
                               show-overflow-tooltip/>
              <el-table-column label="其中" show-overflow-tooltip>
                <el-table-column label="中央企业负责人（人）" align="center" prop="enterpriseManagementNumber" width="130"
                                 show-overflow-tooltip/>
                <el-table-column label="集团管理干部（人）" align="center" prop="groupManagementNumber" width="130"
                                 show-overflow-tooltip/>
                <el-table-column label="子企业管理干部（人）" align="center" prop="subManagementNumber" width="130"
                                 show-overflow-tooltip/>
              </el-table-column>
              <el-table-column label="当年累计追责总人次（人次）" align="center" prop="totalAccountabilityPersonTime" width="130"
                               show-overflow-tooltip/>
              <el-table-column label="其中" show-overflow-tooltip>
                <el-table-column label="组织处理（人次）" align="center" prop="orgHandleTime" width="130" show-overflow-tooltip/>
                <el-table-column label="扣减薪酬（人次）" align="center" prop="deductionSalaryTime" width="130"
                                 show-overflow-tooltip/>
                <el-table-column label="党纪处分（人次）" align="center" prop="partyPunishmentTime" width="130"
                                 show-overflow-tooltip/>
                <el-table-column label="政务处分（人次）" align="center" prop="governmentPunishmentTime" width="130"
                                 show-overflow-tooltip/>
                <el-table-column label="禁入限制（人次）" align="center" prop="prohibitTime" width="130" show-overflow-tooltip/>
                <el-table-column label="移送监察机关或司法机关（人次）" align="center" prop="transferAuthorityTime" width="130"
                                 show-overflow-tooltip/>
                <el-table-column label="其他（人次）" align="center" prop="accountabilityOtherTime" width="130"
                                 show-overflow-tooltip/>
              </el-table-column>
              <el-table-column label="当年累计扣减薪酬金额（万元）" prop="totalDeductionSalary" align="right" width="130"
                               show-overflow-tooltip>
                <template slot-scope="scope">
                  {{ scope.row.totalDeductionSalary | filterNum }}
                </template>
              </el-table-column>
              <el-table-column label="责任约谈" show-overflow-tooltip>
                <el-table-column label="当年累计责任约谈次数（次）" align="center" prop="dutyInterviewNumber" width="130"
                                 show-overflow-tooltip/>
                <el-table-column label="当年累计责任约谈总人次（人次）" align="center" prop="dutyInterviewPersonTime" width="130"
                                 show-overflow-tooltip/>
              </el-table-column>
              <el-table-column label="当年累计挽回资产损失（万元）" prop="totalRetrieveLossAmount" align="right" width="130"
                               show-overflow-tooltip>
                <template slot-scope="scope">
                  {{ scope.row.totalRetrieveLossAmount | filterNum }}
                </template>
              </el-table-column>
              <el-table-column label="当年累计降低损失风险（万元）" prop="totalReduceLossRisk" align="right" width="130"
                               show-overflow-tooltip>
                <template slot-scope="scope">
                  {{ scope.row.totalReduceLossRisk | filterNum }}
                </template>
              </el-table-column>
              <el-table-column label="当年累计制修订管理制度（项）" align="center" prop="totalPerfectSystemNumber" width="130"
                               show-overflow-tooltip/>
              <el-table-column label="其他工作成效" align="center" prop="otherAchievement" width="300" show-overflow-tooltip/>
            </el-table-column>


            <el-table-column label="备注" align="left" prop="remark" width="200" show-overflow-tooltip/>
            <el-table-column label="追责部门填报人" align="center" prop="informantName" width="130" show-overflow-tooltip/>
            <el-table-column label="联系电话" align="center" prop="informantPhone" width="130" show-overflow-tooltip/>
            <el-table-column v-if="rolesDQJBJKR" label="操作" width="80px" align="center" fixed="right">
              <template slot-scope="scope">

                <el-button
                  size="mini"
                  type="text"
                  title="查看"
                  icon="el-icon-search"
                  @click="openViewItem(scope.row)"
                >查看</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-form>
      </BlockCard>
      <div slot="footer">
        <el-button size="mini" @click="close" >取消</el-button>
      </div>
    </el-dialog>
    <!-- 集团查看 -->
    <el-dialog
      class="commons_popup"
      v-bind="$attrs"
      :visible.sync="viewvisible"
      width="90%"
      title="查看"
      v-if="viewvisible"
      append-to-body
    >
      <div slot="title" class="el-popup-header-title">
        <svg-icon icon-class="edit_file" />
        <span class="el-dialog-header-name">{{ "查看" }}</span>
      </div>
      <viewGroup :rowData="groupItem"  showType="area"/>
    </el-dialog>
  </div>
</template>

<script>
import BlockCard from '@/components/BlockCard';
import {queryQuarterReportFileList, queryQuarterReportInfo} from '@/api/quarterly-report'
import personTree from '@/views/quarterlyReport/subordinateReport/components/personTree' // 上报单位接口人
import {
  saveAndStartProcess
  ,getReportUnitUserData
  ,queryProvList
  ,toUrgeProcess
  ,queryReportConfirmList
  ,reportLowerUnitValidateInfo
} from '@/api/quarterly-report/subordinateReport'
import bhInput from "@/views/quarterlyReport/bhInput";
import {mapGetters} from 'vuex'
export default {
  name: "specialReportEdit",
  components: {bhInput,BlockCard, personTree,viewGroup: (d) => import("@/views/quarterlyReport/view-province.vue")},
  computed: {
    ...mapGetters([
      'roles'
    ]),
  },
  dicts: ['REPORT_QUARTER'],
  props: {},
  data() {
    return {
      reportQuarterList:[],
      openLoading: {},
      id: '',
      viewvisible:false,
      groupItem:{},
      //上报单位接口人信息
      memberOpen: false,
      params: {},
      loading: false,
      visible: false,
      infoData: {},
      filesData: [],
      selection:[],//催办数据
      tableList: [],//下级单位上报确认 表格
      queryData: {},
      tabName0:'全部(0)',
      tabName1:'进行中(0)',
      tabName2:'已完成(0)',
      status:'0', //0为查询全部  1 进行中 2 已完成
      rolesDQJBJKR:false,//是否有编辑补录权限
      bhVisible:false, //驳回原因弹窗
      bhItem: {}, //驳回点击数据
    }
  }
  , created() {
  }
  , methods: {
    // 显示弹框
    open(id) {
      this.id = id;
      this.status = '0'
      this.queryQuarterReportInfo()
      this.visible = true;
    },
    //关闭弹窗
    close() {
      this.visible = false;
      this.$emit("close",'edit');
    },
    //打开loading
    getLoading() {
      this.openLoading = this.$loading({
        lock: true,//lock的修改符--默认是false
        text: '提交中',//显示在加载图标下方的加载文案
        spinner: 'el-icon-loading',//自定义加载图标类名
        background: 'rgba(0, 0, 0, 0.7)',//遮罩层颜色
        target: document.querySelector('#table')//loadin覆盖的dom元素节点
      });
    },
    //关闭loading
    closeLoading() {
      this.openLoading.close();
    },
    //查询上报内容
    queryQuarterReportInfo() {
      this.regularTreeUrl = '/colligate/violRegular/report/getReportUnitList/'+this.id
      let params = {
        reportProvId: this.id,
        operationType: 'edit'
      }
      queryQuarterReportInfo(params).then((response) => {
        this.infoData = response.data.reportProvInfo;
        this.reportProvCode = this.infoData.reportUnitCode;

        //下级单位发起确认列表
        this.getunitData();
        this.getReportUnitUserData();
        //查询附件列表
        this.queryFileList();
      })
    },
    //查询附件列表
    queryFileList() {
      this.tableLoading = true
      queryQuarterReportFileList(this.infoData.id).then((response) => {
        this.filesData = response.data;
        this.tableLoading = false
      })
    },
    /**下载文件*/
    fileDownload(obj) {
      this.download(
        "/sys/attachment/downloadSysAttachment/" + obj.attachmentId,
        {},
        obj.fileName
      );
    },

    /****************下级单位上报确认--开始**************/

    //加载各页签下上报单位列表
    queryReportDepartmentList(obj){
      this.status = obj.name;
      console.log(this.status)
      this.getReportUnitUserData();
    },

    //下级单位上报情况初始化查询列表
    getunitData() {
      this.tabName0='全部(0)';
      this.tabName1='进行中(0)';
      this.tabName2='已完成(0)';
      queryProvList({
        quarterReportId:this.infoData.quarterReportId,
        quarterAreaProvId:this.infoData.id,
        reportUnitCode:this.infoData.reportUnitCode
      }).then((response)=>{
        let sumProvData = response.data;
        // foreach 数组 sumProvData 判断 procStatus 等于谁 给tabName赋值
        sumProvData.forEach((ele) => {
          if (ele.procStatus == "") {
            this.tabName0 = "全部("+ele.provCount+")";
          }
          if (ele.procStatus == "1") {
            this.tabName1 = "进行中("+ele.provCount+")";
          }
          if (ele.procStatus == "2") {
            this.tabName2 = "已完成("+ele.provCount+")";
          }
        });

      })

    },
    //下级单位上报情况-更多
    getReportUnitUserData(){
      this.selection = [];
      this.loading = true;
      getReportUnitUserData({
        quarterReportId:this.infoData.quarterReportId,
        quarterAreaProvId:this.infoData.id,
        status:this.status=='0'?'':this.status,
      }).then((response)=>{
        this.tableList = response.rows;
        this.loading = false;
      })
    },



    /****************下级单位上报确认--结束**************/

    /****************进行中--开始**************/
    //进行中表格选中数据
    handleSelectionChange(selection) {
      this.selection = selection;
    },
    /****************进行中--结束**************/

    /****************已完成--开始**************/
    //导出
    detailedExport() {
      this.download(
        "/quarter/area/getAreaDataDetailExport",
        {quarterAreaProvId :this.infoData.id},
        this.getFileName()
      );
    },

    getFileName() {
      // 时间戳
      let date = new Date().getTime()
      let filename = '下级单位上报情况' + date + '.xlsx'
      return filename;
    },
    //地市查看
    openViewItem(row) {
      this.viewvisible = true;
      this.groupItem = row;
    },
    /****************已完成--结束**************/
    //关闭后调用
    closeBtnFub(){
      //下级单位发起确认列表
      this.getunitData();
      this.getReportUnitUserData();
    }
  }
}
</script>

<style lang="scss" scoped>
.subordinateReport {
  .margin-top-8 {
    margin-top: 8px;
  }

  ::v-deep .el-dialog__body {
    height: 70vh;
    padding: 10px;
    overflow: auto;
  }

  .sub-report-box {
    display: inline-block;
    width: 100%;
    .sub-report-detail {
      margin-bottom:10px;
      display: flex;
      justify-content: left;
      flex-wrap: wrap;
      .sub-report-type{
        text-align: center;
        width: 70px;
        height: 35px;
        font-size: 12px;
        border:1px solid #ddd;
        line-height: 35px;
        border-radius: 4px 0 0 4px;
      }

      .sub-report-num{
        text-align: center;
        width: 70px;
        height: 35px;
        font-size: 12px;
        border:1px solid #ddd;
        line-height: 35px;
        background-color: #f5f8fc;
      }
      .border-right{
        border-radius: 0 4px 4px 0;
        border-right: 1px solid #ddd;
      }

      .sub-report-li{
        text-align: center;
        padding:0 6px;
        min-width: 70px;
        height: 35px;
        font-size: 12px;
        border:1px solid #ddd;
        line-height: 35px;
      }

      .sub-report-core{
        cursor: pointer;
        text-align: center;
        width: 70px;
        color:#f5222d;
        height: 35px;
        font-size: 12px;
        border:1px solid #ddd;
        line-height: 35px;
        border-radius: 0 4px 4px 0;
      }
    }
  }
}
</style>
