import request from '@/utils/request';

export function queryActualSituationRange(data) {
  return request({
    url: '/colligate/violActualRange/actualRangeData',
    method: 'post',
    data: data
  });
}

export function situationCheckModalData(data) {
  return request({
    url: '/colligate/violActualRange/situationCheckModalData',
    method: 'post',
    data: data
  });
}

export function saveActualSituationData(data) {
  return request({
    url: '/colligate/violActualRange/saveActualSituationData',
    method: 'post',
    data: data
  });
}

export function deleteSituationRangeData(data) {
  return request({
    url: '/colligate/violActualRange/deleteSituationRangeData',
    method: 'post',
    data: data
  });
}
