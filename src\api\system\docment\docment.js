import request from '@/utils/request'

//1、主页面：查询模板配置列表
export function queryMainTemplateList(params,query) {
  return request({
    url: '/sys/documentTemplate/selectTemplateList',
    method: 'post',
    data: query,
    params: params
  })
}
//2、编辑：根据主键查询模板信息
export function queryMainTemplateById(id) {
  return request({
    url: '/sys/documentTemplate/selectTemplateById',
    method: 'post',
    data: {"id":id}
  })
}

//3、新增保存， 编辑保存
export function updateTemplateById(query) {
  return request({
    url: '/sys/documentTemplate/saveOrUpdateConfigMain',
    method: 'post',
    data: query
  })
}

//4、 删除模板配置信息
export function dropTemplateById(dropParams) {
  return request({
    url: '/sys/documentTemplate/saveOrUpdateConfigMain',
    method: 'post',
    data: dropParams
  })
}

// 5、查询附件列表
export function queryFileList(params,query) {
  return request({
    url: '/sys/documentTemplateFile/selectFileList',
    method: 'post',
    data: query,
    params: params
  })
}
// 6、附件上传
export function uploadFile(formData) {
  return request({
    url: '/sys/documentTemplateFile/uploadAndSaveFile',
    method: 'post',
    data: formData
  })
}
// 7、 删除附件
export function dropFilesById(dropParams) {
  return request({
    url: '/sys/documentTemplateFile/deleteFile',
    method: 'post',
    data: dropParams
  })
}



