import request from '@/utils/request'


// 下一环节名称
export function flowParams(url) {
  return request({
    url: url,
    method: 'post'
  })
}

// 下一环节处理人
export function refreshNextAssignee(data) {
  return request({
    url: '/colligate/violRegular/refreshNextAssignee',
    method: 'post',
    data: data
  })
}

// 流程推进
export function pushProcess(data) {
  return request({
    url: '/colligate/violRegular/pushProcess',
    method: 'post',
    data: data
  })
}

// 退回
export function backProcess(data) {
  return request({
    url: '/colligate/violRegular/backProcess',
    method: 'post',
    data: data
  })
}

