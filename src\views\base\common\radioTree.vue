<template>
<div>
  <el-tree :data="treeList" :props="defaultProps" 
    style="margin-top: 20px"
     :expand-on-click-node="false" 
     node-key="id"
     :default-expanded-keys="keyIdArray"

  >
   <span slot-scope="{ node, data }">
      <el-radio v-if="(onlyChild && !data.isParent) || !onlyChild" v-model="radio" :label="data.id"  @change="change(data)">
        {{ node.label }}
      </el-radio>
      <span v-if="onlyChild && data.isParent">{{ node.label }}</span>
    </span>
  </el-tree>
</div>
</template>

<script lang="ts">
  export default {
    name: "orgTree",
    props: {
      treeList: {
        type: Array,
        default: ()=>{ return []}
      },
      onlyChild: {
        type: Boolean,
        default: false
      },
    },
    data() {
      return {
        defaultProps: {
          children: 'children',
          label: 'label'
        },
        keyIdArray: [], // 默认展开的节点的 key 的数组
        checkedList: [],   //选中节点的数据
        radio: "",    //  这一行
      };
    },
    methods: {
      change(data) {
        this.checkedList = [];
        this.checkedList[0] = data;
        this.$emit("selectNode", {id: data.id, name: data.name});
      },
    }
  };
</script>

<style>
  .text {
    font-size: 14px;
  }

  .item {
    margin-bottom: 18px;
  }

  .clearfix:before,
  .clearfix:after {
    display: table;
    content: "";
  }
  .clearfix:after {
    clear: both
  }
</style>