<template>
  <div class="login">
    <!--  头部  -->
    <div class="login-top">
      <div class="login-top-logo">
        <img class="login-log" src="../assets/images/login/logo.png">
      </div>
    </div>
    <!--  底部  -->
    <!--<div class="login-bottom">-->
    <!--<span>@2021 版权所有©联通（山东）产业互联网有限公司</span>-->
    <!--<span>支撑方式：电子商城中心</span>-->
    <!--<span> 戚永强</span>-->
    <!--<span>18678881296</span>-->
    <!--</div>-->
    <div class="login-main">
      <el-form ref="loginForm" :model="loginForm" :rules="loginRules" class="login-form">
        <h3 class="title">Hi,欢迎登录</h3>
        <el-form-item prop="info">
          <el-input v-model="loginForm.info" type="text" auto-complete="off" placeholder="请输入账号" />
        </el-form-item>
        <el-form-item prop="word">
          <el-input
            v-model="loginForm.word"
            type="password"
            auto-complete="off"
            placeholder="请输入密码"
            @keyup.enter.native="handleLogin"
          />
        </el-form-item>
        <Verify
          ref="verify"
          :mode="'pop'"
          :captcha-type="'blockPuzzle'"
          :img-size="{ width: '330px', height: '155px' }"
          @success="capctchaCheckSuccess"
        />
        <el-checkbox v-model="loginForm.rememberMe" style="margin:10px 0px 10px 0px;">记住密码</el-checkbox>
        <el-form-item style="width:100%;">
          <el-button
            size="medium"
            type="primary"
            class="login-btn"
            @click.native.prevent="handleLogin"
          >
            <span v-if="!loading">登 录</span>
            <span v-else>登 录 中...</span>
          </el-button>
          <div v-if="register" style="float: right;">
            <router-link class="link-type" :to="'/register'">立即注册</router-link>
          </div>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script>
import Cookies from 'js-cookie'
import { encrypt, decrypt } from '@/utils/jsencrypt'
import Verify from '@/components/Verifition/Verify'
import { getCodeImg } from '@/api/login'
import { Message } from 'element-ui'
export default {
  name: 'Login',
  components: { Verify, getCodeImg },
  data() {
    return {
      codeUrl: '',
      cookiePassword: '',
      loginForm: {
        info: '',
        word: '',
        rememberMe: false,
        code: '',
        uuid: ''
      },
      loginRules: {
        info: [
          { required: true, trigger: 'blur', message: '请输入您的账号' }
        ],
        word: [
          { required: true, trigger: 'blur', message: '请输入您的密码' }
        ]
      },
      loading: false,
      // 验证码开关
      captchaOnOff: true,
      // 注册开关
      register: false,
      redirect: undefined
    }
  },
  watch: {
    $route: {
      handler: function(route) {
        this.redirect = route.query && route.query.redirect
        console.log('this.redirect',this.redirect)
      },
      immediate: true
    }
  },
  created() {
    this.getCode()
    this.getCookie()
  },
  methods: {
    getCode() {
      getCodeImg().then(res => {
        this.captchaOnOff = res.captchaOnOff === undefined ? true : res.captchaOnOff
      })
    },
    getCookie() {
      const info = Cookies.get('info')
      const word = Cookies.get('word')
      const rememberMe = Cookies.get('rememberMe')
      this.loginForm = {
        info: info === undefined ? this.loginForm.info : info,
        word: word === undefined ? this.loginForm.word : decrypt(word),
        rememberMe: rememberMe === undefined ? false : Boolean(rememberMe)
      }
    },
    capctchaCheckSuccess(params) {
      this.loginForm.code = params ? params.captchaVerification : ''
      this.loading = true
      if (this.loginForm.rememberMe) {
        Cookies.set('info', this.loginForm.info, { expires: 30 })
        Cookies.set('word', encrypt(this.loginForm.word), { expires: 30 })
        Cookies.set('rememberMe', this.loginForm.rememberMe, { expires: 30 })
      } else {
        Cookies.remove('info')
        Cookies.remove('word')
        Cookies.remove('rememberMe')
      }
      const loginData = JSON.parse(JSON.stringify(this.loginForm))
      loginData.word = this.$getRsaCode(loginData.word) // ras 加密 密码
      this.$store.dispatch('Login', loginData).then(() => {
        this.$router.push({ path:  '/index' }).catch(() => {})
      }).catch(() => {
        this.loading = false
      })
    },
    handleLogin() {
      if(!this.loginForm.info) {
        Message({
          message: '请输入您的账号',
          type: 'error'
        })
      }else if(!this.loginForm.word){
        Message({
          message: '请输入您的密码',
          type: 'error'
        })
      }else{
      if (this.captchaOnOff) {
        this.$refs.loginForm.validate((valid) => {
          if (valid) {
            this.$refs.verify.show()
          }
        })
      } else {
        this.$refs.loginForm.validate((valid) => {
          if (valid) {
            this.capctchaCheckSuccess()
          }
        })
      }
      }
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss">
.login {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  background-image: url("../assets/images/login/bg1.png");
  background-size: 100%;
  background-position-y: 85%;

.title {
  text-align: left;
  margin: 6.5% 0;
  height: 9%;
  font-family: MicrosoftYaHei-Bold;
  font-size: 30px;
  font-weight: bold;
  font-stretch: normal;
  letter-spacing: 2px;
  color: #181818;
  position: relative;
  padding-left: 8%;
}
.title:before {
  content: '';
  position: absolute;
  left: 0;
  top: 6px;
  width: 8px;
  height: 28px;
  background: #f5222d;
}
.login-top{
  width: 100%;
  height: 12%;
  display: flex;
  background: #fff;
  z-index: 1;
  position: fixed;
  top:0;
  .login-top-logo{
    width: 100%;
    padding-left: 6%;
    .login-log{
      height: 64%;
      margin-top: 1%;
    }
  }
}
.login-main {
  background: url('../assets/images/login/img-conter.png') no-repeat 0 center;
  width: 79%;
  position: absolute;
  left: 2%;
  height: 57%;
  top: 28%;
  background-size: 75% 100%;
  padding-left: 0;
  margin: 0;
}
.login-bottom{
  position: fixed;
  bottom: 0;
  width: 100%;
  height: 8%;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
    span{
    font-size: 12px;
    font-weight: normal;
    padding: 0 14px;
    color: #73777A;
  }
}
.login-form {
  float: right;
  border-radius: 4px;
  background: #ffffff;
  width: 25.3%;
  height: 100%;
  padding: 3.3%;
  .el-input {
    height: 100%;
    input {
      padding: 0;
      border-radius: 0;
      border:0;
      border-bottom: 1px solid #D9D9D9;
      height: 100%;
      line-height: 1;
    }
  }
  .input-icon {
    height: 39px;
    width: 14px;
    margin-left: 2px;
  }
}
.login-tip {
  font-size: 13px;
  text-align: center;
  color: #bfbfbf;
}
.login-code {
  width: 33%;
  height: 38px;
  float: right;
  img {
    cursor: pointer;
    vertical-align: middle;
  }
}
.el-login-footer {
  height: 40px;
  line-height: 40px;
  position: fixed;
  bottom: 0;
  width: 100%;
  text-align: center;
  color: #fff;
  font-family: Arial;
  font-size: 12px;
  letter-spacing: 1px;
}
.login-code-img {
  width: 100%;
}
  .login-btn{
    float: right;
    width: 41%;
    height: 54px;
    background: #f5222d;
    border-radius: 4px;
    font-size: 20px;
    border-color: #f5222d;
  }
.login-btn:hover, .login-btn:focus{
  background: #f5222d;
  border-color: #f5222d;
}
.el-form-item{
  padding-top: 4.5%;
  margin-bottom: 8%;
  height: 11%;
  .el-form-item__content{
    height: 100%;
    min-height: 36px;
    line-height: 100%;
  }
}
.el-checkbox__input.is-checked + .el-checkbox__label {
  color: #f5222d;
}
.el-checkbox__input.is-checked .el-checkbox__inner {
  background-color: #f5222d;
  border-color: #f5222d;
}
.el-checkbox__inner:hover {
  border-color: #f5222d;
}
}
</style>
