import request from '@/utils/request'

// 超时提醒
export function ordinaryTimeoutBlock() {
    return request({
        url: '/colligate/violOrdinaryHomepage/ordinaryTimeoutBlock',
        method: 'post',
    })
}




// 日历

export function ordinaryCalendarBlock(monthOfYear) {
    return request({
        url: '/colligate/violOrdinaryHomepage/ordinaryCalendarBlock/' + monthOfYear,
        method: 'post',
    })
}


// 问题处理进度

export function problemStatusNumsList(data) {
    return request({
        url: '/colligate/violQuery/problemStatusNumsList',
        method: 'post',
        data
    })
}




// 全部问题

export function ordinaryAllProblem() {
    return request({
        url: '/colligate/violOrdinaryHomepage/ordinaryAllProblem',
        method: 'post',
    })
}


