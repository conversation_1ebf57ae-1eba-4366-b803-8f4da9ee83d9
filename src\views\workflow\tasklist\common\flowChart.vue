<template>
  <div class="flowChat">
    <iframe
      id="flow-img-iframe"
      title="流程图"
      style="width: 100%;height:1100px"
      scrolling="auto"
      frameBorder="0"
    ></iframe>
  </div>
</template>
<script>
  import iFrame from "@/components/iFrame/flowFrame";
  import { flowChatData } from "@/api/components/process";

  export default {
    name: "flowChat",
    components: { iFrame },
    props: {
      selectValue: {
        type: Object
      },
    },
    data() {
      return {
        url:''
      }
    },
    computed: {},
    watch: {},
    created() {
      this.FlowChatData();
    },
    mounted() {},
    methods: {
      /**获取流程图*/
      FlowChatData(){
        flowChatData(this.selectValue).then(
            response => {
              this.url = response.msg
              let result = this.url.replaceAll("getQueryVariable('processInstID') !=", "1!=");
              let iframe = document.getElementById("flow-img-iframe");
              console.log(result)
              if(iframe) {
                let iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                let htmlStr = result; // response.msg中存放的就是流程图页面的html字符串
                iframeDoc.body.innerText = "";
                // 改造后点击返回主流程 会直接返回到本系统主页
                // todo:以下代码替换了原本流程中心html页面中返回主流程的代码，通过iframe间通信，将iframe中的tenant、processInstID两个数据传到外面，再执行getHtmlStr方法解决跳转问题
                iframeDoc.write(htmlStr && htmlStr.replace('reload.click();', `
        window.parent && window.parent.postMessage({
          tenant: tenantId,
          processInstID: encodeURIComponent(parentProcId)
        },"*");
      `));
              }
            }
          );
      }
    }
  }

</script>
<style scoped lang="scss">
  .flowChat{
    height: 100%;
  }
</style>

