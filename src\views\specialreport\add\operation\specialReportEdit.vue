<!--新增台账-->
<template>
  <div class="scope">
    <el-dialog :visible.sync="visible" @close="close" :modal-append-to-body="false" append-to-body :title="title" width="90%">
      <BlockCard title="项目基本信息">
        <el-form ref="elForm" :model="formData" size="medium" label-width="128px">
          <el-row>
          <el-col :span="16">
            <el-form-item label="项目名称">
              <el-input v-model="formData.projectName" placeholder="项目名称" clearable maxlength = '120' show-word-limit></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="项目编码">
              <el-input v-model="formData.projectNum" placeholder="项目编码" clearable maxlength = '100' ></el-input>
            </el-form-item>
          </el-col>
          </el-row>
          <el-row>
          <el-col :span="8">
            <el-form-item label="项目类型">
              <el-select
                v-model="formData.projectTypeEnumId"
                placeholder="项目类型"
                clearable
                size="medium"
              >
                <el-option
                  v-for="dict in dict.type.SPR_PROJECT_TYPE"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item label="审计对象" >
              <el-input v-model="formData.projectOrgId" placeholder="审计对象"
                        size="medium"v-show="false"></el-input>
              <el-input v-model="formData.projectOrgName" placeholder="审计对象" v-if="grade != 'P'" @focus="showAuditOrg()"
                        size="medium"></el-input>
              <el-input v-model="formData.projectOrgName" placeholder="审计对象" v-if="grade == 'P'" readonly
                        size="medium"></el-input>
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item label="项目年度">
              <el-date-picker v-model="formData.projectYear"   format="yyyy" value-format="yyyy" type="year"
                              :style="{width: '100%'}" placeholder="项目年度" clearable></el-date-picker>
            </el-form-item>
          </el-col>
          </el-row>
        </el-form>
      </BlockCard>

      <BlockCard
        title="审计报告信息"
      >
        <AuditReportInformationUpload
          :edit='edit'
          :key="id"
          :id="id"
          ref="file"
        ></AuditReportInformationUpload>

      </BlockCard>
      <BlockCard title="项目台账信息">
        <div class="position">
          <div class="position-select">
            <div class="float-right " style="margin-left:15px;">
              <el-button  type="primary" plain  size="mini" @click="addLedgerFun">新增台账</el-button>

              <el-button  type="primary" plain  size="mini" @click="handleDownload">模板下载</el-button>

             <FileUpload
               style="display: inline-block;margin-left: 10px;"
                :fileUrl="uploadUrl"
                btnTitle="导入台账"
                uploadPrompt="问题台账导入为增量导入，请注意处理重复数据"
                :isShowTip=showTip
                icon="el-icon-download"
                :param="{projectId:id}"
                @handleUploadSuccess="handleUploadSuccess"
              >

              </FileUpload>
            </div>
          </div>
        </div>
        <el-form>
          <el-table border v-loading="tableLoading" :data="ledgerTable">
            <el-table-column  type="index"  min-width="8%" align="center" label="序号" >
              <template slot-scope="scope">
                <table-index
                  :index="scope.$index"
                />
              </template>
            </el-table-column>
            <el-table-column label="问题编号" prop="problemNum"  min-width="15%" align="center" />
            <el-table-column label="发现问题业务类型" prop="problemTypeEnumName" min-width="30%" align="center" />
            <el-table-column label="审计发现问题" prop="problemAudit" min-width="25%" show-overflow-tooltip align="left" >
            </el-table-column>
            <el-table-column label="具体问题描述" prop="problemDescription" min-width="25%" show-overflow-tooltip align="left" >
            </el-table-column>
            <el-table-column label="是否上报告" prop="reportFlag"  min-width="15%" align="center" >
              <template slot-scope="scope" class="text-center">
                {{ scope.row.reportFlag==1?'是':scope.row.reportFlag==0?'否':'' }}
              </template>
            </el-table-column>
            <el-table-column label="是否追责" prop="ifDuty"  min-width="15%" align="center" >
              <template slot-scope="scope" v-if="projectUpdateMethod === '0'">
                <div>
                  <el-radio-group v-model="scope.row.ifDuty"  @change="toSaveLedgersList">
                    <el-radio label="1">是</el-radio>
                    <el-radio label="0">否</el-radio>
                  </el-radio-group>
                </div>
              </template>
              <template slot-scope="scope" v-if="projectUpdateMethod === '1'" align="center" >
                {{ scope.row.ifDuty==1?'是':scope.row.ifDuty==0?'否':'' }}
              </template>
            </el-table-column>
            <el-table-column label="是否移交纪检" prop="transferFlag"  min-width="20%" align="center" >
              <template slot-scope="scope">
                <div>
                  <el-radio-group v-model="scope.row.transferFlag">
                    <el-radio label="1">是</el-radio>
                    <el-radio label="0">否</el-radio>
                  </el-radio-group>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="日常问题" prop="problemNum"  min-width="13%" align="center" >
              <template slot-scope="scope" class="text-center">
                <span v-if="scope.row.ifDuty !='1'"></span>
                <a @click="showProblemInfo(scope.row.id)" class="table-btn" style='color: #c20000;' v-if="scope.row.ifDuty =='1'">
                  {{ scope.row.problemCount }}
                </a>
              </template>
            </el-table-column>
            <el-table-column
              label="操作"
              fixed="right"
              min-width="20%"
              align="center"
              class-name="small-padding fixed-width"
            >
              <template slot-scope="scope">
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-edit"
                  title="编辑"
                  @click="updateLedgerFun(scope.row.id)"
                >
                </el-button>
                <el-button
                  size="mini"
                  type="text"
                  title="删除"
                  icon="el-icon-delete"
                  @click="delLedgers(scope.row)"
                >
                </el-button>
                <el-button
                  size="mini"
                  type="text"
                  title="关联"
                  icon="el-icon-connection"
                  @click="relation(scope.row)"
                  v-if="scope.row.ifDuty == '1'"
                >
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-form>
      </BlockCard>
      <BlockCard
        title="初核专项报告"
      >
        <PreliminaryFileUpload
          :edit='edit'
          :key="id"
          :projectId = "id"
          ref="file"
        ></PreliminaryFileUpload>

      </BlockCard>
      <div slot="footer">
        <el-button size="mini" type="primary"  @click="saveSpecialReportInfo" plain>保存</el-button>
        <el-button size="mini" type="primary"  @click="submitSpecialReport">提交</el-button>
        <el-button size="mini" @click="close">关闭</el-button>
      </div>
    </el-dialog>

<!--台账-->
    <AddLedger
      ref="addLedger"
      :key="index1"
      :projectId="id"
      :ledgerId="ledgerId"
      :ledgerType="ledgerType"
      :projectUpdateMethod="projectUpdateMethod"
      @close="queryLedgersList"
    ></AddLedger>

    <el-dialog :visible.sync="auditOrgFlag" width="60%" append-to-body title="审计对象">
      <AuditOrgTree
        v-if="auditOrgFlag"
        :key="auditOrgIndex"
        ref="auditOrgTree"
        :url="auditOrgTreeUrl"
        :projectOrgId="formData.projectOrgId"
        @list="departLists"
      />
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="sureAuditOrg" >确认</el-button>
      </div>
    </el-dialog>

    <!--流程-->
    <Process
      :key="processIndex"
      ref="process"
      :refresh-assignee-url="flowInfo.refreshAssigneeUrl"
      :save-btn-type="flowInfo.saveBtnType"
      :tab-flag="flowInfo.tabFlag"
      :select-value="{
        busiKey: flowInfo.busiKey,
        title: flowInfo.title,
      }"
      :center-variable="{}"
      @close="closeAdd"
    />

    <!--台账关联日常问题列表页面-->
    <RelationDailyProblem
      ref="relation"
      :key="relationIndex"
      :pbInId="ledgerId"
      :projectId="id"
      :projectOrgId="formData.projectOrgId"
      @onClose="queryLedgersList"
    ></RelationDailyProblem>

    <!--已关联日常问题列表页面-->
    <AssociatedDailyProblem
      ref="associated"
      :key="associatedIndex"
      :pbInId="ledgerId"
      :projectOrgId="formData.projectOrgId"
      :edit="edit"
      @onClose="queryLedgersList"
    ></AssociatedDailyProblem>
  </div>
</template>

<script>
import {
  saveLedger
  ,queryLedgersList
  ,delLedgers
  ,getNewProjectId
  ,saveSpecialReportInfo
  ,queryProjectInfo
  ,validateInfo
  ,flowParams
  ,saveLedgersList} from '@/api/special-report'
import BlockCard from '@/components/BlockCard'
import AddLedger from '@/views/specialreport/flow/operation/addLedger';
import AuditReportInformationUpload from './auditReportInformationUpload';//审计报告信息
import PreliminaryFileUpload from '@/views/specialreport/flow/operation/fileUpload';//初核审计报告
import Process from "@/components/process-common/index";
import AuditOrgTree from './auditOrgTree'// 审计对象
import RelationDailyProblem from '@/views/specialreport/spledger/relationDailyProblem';//台账关联问题
import AssociatedDailyProblem from '@/views/specialreport/spledger/associatedDailyProblem';//已关联问题
export default {
  name: "specialReportEdit",
  components: {
    BlockCard
    ,AuditReportInformationUpload
    ,PreliminaryFileUpload
    ,AddLedger
    ,Process
    ,AuditOrgTree
    ,RelationDailyProblem
    ,AssociatedDailyProblem
  },
  dicts: ['SPR_PROJECT_TYPE']
  , props: {
  },
  data() {
    return {
      edit:true,
      auditOrgIndex:0,
      title: '',
      id: '',//主键
      visible: false,//弹框
      index1:0,
      formData: {
        projectName: ''
        , projectNum: ''
        , projectTypeEnumId: ''
        , projectYear: ''
        , projectOrgId: ''
        , projectOrgName: ''
      },
      tableLoading: false, // 表格loading
      ledgerTable:[],//台账信息
      showTip:false,
      ledgerId:'',//台账表主键
      ledgerType:'',//台账修改方式 add:新增；edit:编辑
      flowInfo: {
        processIndex:0,
        busiKey: "", // 业务中获取
        title: "", // 业务中获取
        saveBtnType: true, // 是否需要保存按钮
        tabFlag: true, // 表明是业务发起环节
        refreshAssigneeUrl: "/spr/flow", // 自定义业务url
      },
      flowKey:'',//流程key
      loading:{},
      projectUpdateMethod:'',//项目问题新增方式
      auditOrgFlag:false,//审计对象是否加载
      auditOrgTreeUrl:'/spr/getAuditOrgList',
      uploadUrl:'/spr/attachment/importSprLedger',//导入台账
      processData :{},//流程额外参数
      relationIndex:0,//关联问题
      associatedIndex:0,//已关联问题
      edit:'edit',//已关联问题可操作
      relationFlag:'1',//台账关联关系是否校验 0：否；1：是
      grade:'',//当前登录人层级
      saveFlag:false,//专项是否保存
      ledgerFlag:'1',//校验台账必填
    }
  }
  , created() {
  }
  , methods: {
    // 显示弹框
    show(id) {
      this.id = id;
      if (id) {
        this.title = '编辑'
        this.flowInfo.busiKey = this.id;
        this.saveFlag = true;
      } else {
        this.getNewProjectId();
        this.title = '新增'
      }
      this.visible = true;
      this.queryFlowParams()
      this.queryProjectInfo()
      this.queryLedgersList()
    },
    //关闭弹窗
    close() {
     this.visible = false;
      this.$emit("close");

    },
    //获取流程key
    queryFlowParams(){
      flowParams().then((res)=>{
        this.flowKey = res.data.processDefinitionKey;
      })
    },
    //新增获取新主键
    getNewProjectId(){
      getNewProjectId().then((res)=>{
        this.id = res.data.projectId;
        this.flowInfo.busiKey = this.id;
        this.grade = res.data.grade;
        this.formData.projectOrgName =res.data.projectOrgName
        this.formData.projectOrgId =res.data.projectOrgId
      })
    },
    //根据主键查询项目信息
    queryProjectInfo(){
      queryProjectInfo(this.id).then((res)=>{
        if(res.data){
          this.formData = res.data;
          if(res.data.updateMethod){
            this.projectUpdateMethod = res.data.updateMethod;
          }else{
            this.projectUpdateMethod = '1';
          }
        }else{
          this.projectUpdateMethod = '1';
        }
        this.$forceUpdate();
      })
    },
    //审计对象
    showAuditOrg(){
      this.auditOrgIndex ++;
      this.auditOrgFlag = true;
    },
    //保存上报单位(回调函数)
    departLists(data){
      this.formData.projectOrgName =data[0].name
      this.formData.projectOrgId =data[0].id
      this.auditOrgFlag = false;
    },
    //确认审计对象
    sureAuditOrg(){
      //获取选中信息
      this.$refs.auditOrgTree.list();
      this.saveFlag = false
    },
    //根据主键查询台账信息
    queryLedgersList(){
      queryLedgersList(this.id).then((res)=>{
        //台账信息
        this.ledgerTable = res.data.ledgerReturnList;
        this.$forceUpdate();
      })
    },
    //新增台账
    addLedgerFun(){
      if(!this.saveFlag){
        // this.$message.warning('请先保存专项报告！');
        // return false;
        //调用保存方法
        this.problemSaveSpInfo();
      }
      this.ledgerType = "add"
      this.index1++;
      this.$nextTick(()=> {
        this.$refs.addLedger.show();
      });
    },
    //编辑台账
    updateLedgerFun(id){
      this.ledgerType = "edit"
      this.ledgerId = id;
      this.index1++;
      this.$nextTick(()=> {
        this.$refs.addLedger.show();
      });
    },
    //删除台账
    delLedgers(obj){
      this.$modal.confirm("确认删除此条数据").then(function() {
        return delLedgers({id:obj.id,projectId:obj.projectId})
      }).then((response) => {
        this.$modal.msgSuccess("删除成功");
        //刷新台账列表
        this.queryLedgersList();
      }).catch(() => {});

    },
    //下载台账模板
    handleDownload(){
        this.download('/spr/attachment/downLoadSpReportAttachment/D_PRO_SP_PROJECT_INFO_LEDGER', {}, "初核专项报告问题台账模板.xlsx");
    },
    // 导入台账
    handleUploadSuccess(res, file) {
      this.$modal.msgSuccess('上传成功');
      this.queryLedgersList();
    },
    //保存报告信息
    saveSpecialReportInfo(){
      this.getLoading();
      saveSpecialReportInfo({...this.formData, ...{id: this.id}}).then((res)=>{
          saveLedgersList(this.ledgerTable).then(()=>{
            this.saveFlag = true;
            this.loading.close();
            this.$modal.msgSuccess('保存成功');
          })
      })
    },
    //关联日常问题、新增台账之前先保存专项报告
    problemSaveSpInfo(){
      saveSpecialReportInfo({...this.formData, ...{id: this.id}}).then((res)=>{
        saveLedgersList(this.ledgerTable).then(()=>{
          this.saveFlag = true;
        })
      })
    },
    //保存台账
    toSaveLedgersList(){
      saveLedgersList(this.ledgerTable).then(()=>{
        // this.queryLedgersList();
      })
    },
    getLoading(){
      this.loading = this.$loading({
        lock: true,//lock的修改符--默认是false
        text: '保存中',//显示在加载图标下方的加载文案
        spinner: 'el-icon-loading',//自定义加载图标类名
        background: 'rgba(0, 0, 0, 0.7)',//遮罩层颜色
        target: document.querySelector('#table')//loadin覆盖的dom元素节点
      });
    },
    //校验  保存
    submitSpecialReport() {
      if (!this.formData.projectName) {
        this.$modal.msgError("【项目名称】不能为空！");
        return false;
      }else if (!this.formData.projectNum) {
        this.$modal.msgError("【项目编号】不能为空！");
        return false;
      }else if (!this.formData.projectTypeEnumId) {
        this.$modal.msgError("【项目类型】不能为空！");
        return false;
      } else if (!this.formData.projectOrgId) {
        this.$modal.msgError("【审计对象】不能为空！");
        return false;
      } else if (!this.formData.projectYear) {
        this.$modal.msgError("【项目年度】不能为空！");
        return false;
      }/*else if(this.ledgerTable.length <1){
        this.$modal.msgError("项目台账信息不能为空！");
        return false;
      }*/
      const loading = this.$loading({
        lock: true,//lock的修改符--默认是false
        text: '正在提交中',//显示在加载图标下方的加载文案
        background: 'transparent',
        spinner: 'el-icon-loading',//自定义加载图标类名
      });
      //先保存，再校验
      this.getLoading()
      saveSpecialReportInfo({...this.formData, ...{id: this.id}}).then((res)=>{
        saveLedgersList(this.ledgerTable).then(()=>{
          this.loading.close();
          //后端校验
          let params = {
            projectId:this.id,
            relationFlag:this.relationFlag,
            ledgerFlag:this.ledgerFlag
          }
          //后端校验
          validateInfo(params).then((res)=>{
            loading.close();
            let validateType = res.data.validateType;
            if(res.data.flag){
              this.toStartFlow();
            }else if(validateType == 'sp' || validateType == 'ledger'){
              this.$confirm(res.data.msg, '提示', {
                confirmButtonText: '直接提交',
                cancelButtonText: '返回',
                type: 'warning'
              }).then(()=> {
                //继续提交，不再校验台账关联日常问题；新：继续提交后关闭下环节框，可再进行校验，所以注释掉
                // this.relationFlag = '0';
                this.toStartFlow();
              }).catch(() => {});
            }else{
              this.$message.error(res.data.msg);
            }
          })
        })
      })
    },
    //发起流程
    toStartFlow(){
      this.flowInfo.title = this.formData.projectName
      //调用流程发起
      const loadProcessData = {
        businessKey: this.id,
        title: this.formData.reportTitle,
        flowKey:this.flowKey
      }
      this.$refs.process.handle(1, loadProcessData)
    },
    //流程提交后关闭
    closeAdd(){
      this.close();
    },
    //专项台账关联问题
    relation(obj){
      if(!this.formData.projectOrgId){
        this.$message.warning("请先选择审计对象，并保存专项报告");
        return;
      }
      if(!this.saveFlag){
        // this.$message.warning('请先保存专项报告！');
        // return false;
        //调用保存方法
        this.problemSaveSpInfo();
      }
      this.ledgerId = obj.id;
      this.relationIndex++;
      this.$nextTick(()=> {
        this.$refs.relation.show();
      });
    },
    //攥取已关联问题列表
    showProblemInfo(id){
      this.ledgerId = id;
      this.associatedIndex++;
      this.$nextTick(()=> {
        this.$refs.associated.show();
      });
    },
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-select {
  width: 100%;
}
::v-deep .el-radio{
  margin-right:18px !important;
}
/* 对于屏幕宽度小于等于600px的设备 */
@media screen and (max-width: 1340px) {
  ::v-deep.el-radio{
    margin-right:6px !important;
  }
  ::v-deep .el-radio__label {
    font-size: 14px;
    padding-left: 4px;
  }
}
::v-deep .el-dialog__body {
  height: 70vh;
  overflow: auto;
  padding-top: 10px !important;
  background: #fff !important;
}
.position-select {
  position: absolute;
  width: 100%;
  text-align: right;
  top: -54px;
}
</style>
