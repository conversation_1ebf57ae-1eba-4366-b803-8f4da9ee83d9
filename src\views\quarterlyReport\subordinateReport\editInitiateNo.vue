<!--下级单位数据上报-已发起-->
<template>
  <div class="scope">
    <el-dialog class="subordinateReport commons_popup" :visible.sync="visible" @close="close" :modal-append-to-body="false"
               append-to-body :title="type=='1'?'下级单位上报':'补录下级单位上报'" width="90%">
      <div slot="title" class="el-popup-header-title">
        <svg-icon icon-class="edit_file" />
        <span class="el-dialog-header-name">{{ type=='1'?'下级单位上报':'补录下级单位上报' }}</span>
      </div>
      <BlockCard title="基本信息">
        <el-form ref="elForm" :model="infoData" size="medium" label-width="100px">
          <el-row>
            <el-col :span="8">
              <el-form-item label="上报年度">
                {{ infoData.reportYear }}
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="上报季度">
                {{ infoData.reportQuarter | fromatComon(dict.type.REPORT_QUARTER) }}
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="上报截止日期">
                <el-date-picker
                  format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd"
                  v-model="infoData.reportCloseTime"
                  type="datetime"
                  placeholder=""
                  readonly
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="上报标题">
                {{ infoData.reportTitle }}
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="上报要求">
                {{ infoData.reportRequire }}
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </BlockCard>
      <BlockCard title="附件列表">
        <el-table
          :data="filesData"
          border
          v-loading="tableLoading"
          style="width: 100%"
        >
          <el-table-column
            label="序号"
            type="index"
            min-width="5%"
            align="center"
          />
          <el-table-column label="文件名" prop="fileName" min-width="50%">
            <template slot-scope="scope">
              <div
                style="text-align: left"
                class="overflowHidden-1"
                :title="scope.row.fileName"
              >
                {{ scope.row.fileName || "" }}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            label="上传人"
            prop="createUserName"
            min-width="10%"
            align="center"
          />
          <el-table-column
            label="上传时间"
            prop="createTime"
            min-width="20%"
            align="center"
          />

          <el-table-column
            label="操作"
            fixed="right"
            min-width="15%"
            align="center"
            class-name="small-padding fixed-width"
          >
            <template slot-scope="scope">
              <a @click="fileDownload(scope.row)" class="table-btn">下载</a>
            </template>
          </el-table-column>
        </el-table>
      </BlockCard>
      <BlockCard title="下级单位上报" >
        <div slot="left">
          <el-button
            style="margin-left:10px;"
            type="primary"
            plain
            icon="el-icon-plus"
            size="mini"
            @click="addReportDepart"
          >添加上报单位
          </el-button>
        </div>

        <el-form>
          <el-table v-loading="loading" :data="tableList1" height="350px">
            <el-table-column label="序号" type="index" min-width="4%" align="center"/>
            <el-table-column label="上报单位" prop="reportUnitName" show-overflow-tooltip align="center" min-width="30%"/>
            <el-table-column label="接口人" prop="interfaceUserName" show-overflow-tooltip align="center" min-width="20%"/>
            <el-table-column label="邮箱" prop="interfaceUserMail" show-overflow-tooltip align="center" min-width="20%"/>
            <el-table-column label="联系电话" prop="interfaceUserPhone" show-overflow-tooltip align="center"
                             min-width="20%"/>
            <el-table-column label="操作"  width="200" align="center"
                             class-name="small-padding fixed-width">
              <template slot-scope="scope">
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-edit"
                  @click="clickPostName(scope.row)"
                >编辑
                </el-button>
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-delete"
                  @click="delReportUnitByIdFun(scope.row)"
                >删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-form>
      </BlockCard>
      <div slot="footer">
        <el-button size="mini" type="primary" @click="initiateReportingFun" >发起上报</el-button>
        <el-button size="mini" @click="close" >取消</el-button>
      </div>
    </el-dialog>
    <el-dialog
      v-if="memberOpen"
      :visible.sync="memberOpen"
      width="80%"
      class="changePeoples commons_popup"
      title="上报单位接口人"
      append-to-body
    >
        <div slot="title" class="el-popup-header-title">
          <svg-icon icon-class="edit_file" />
          <span class="el-dialog-header-name">{{ '上报单位接口人' }}</span>
        </div>
      <personTree :closeBtn="cancelPeople" :rowData="params"/>
    </el-dialog>

    <el-dialog :visible.sync="departmentFlag" class="treeOpen commons_popup" width="60%" append-to-body title="上报单位">
      <div slot="title" class="el-popup-header-title">
        <svg-icon icon-class="edit_file" />
        <span class="el-dialog-header-name">{{ '上报单位' }}</span>
      </div>
      <RegularTree
        :type="type"
        :regularReportId="id"
        v-if="departmentFlag"
        :key="computedKey"
        ref="regularTree"
        :url="regularTreeUrl"
        :selectTree="selectTree"
        @list="departLists"
      />
    </el-dialog>
  </div>
</template>

<script>
import BlockCard from '@/components/BlockCard';
import RegularTree from '@/views/quarterlyReport/tree/index'// 上报单位
import {queryQuarterReportFileList, queryQuarterReportInfo} from '@/api/quarterly-report'
import personTree from '@/views/quarterlyReport/subordinateReport/components/personTree' // 上报单位接口人
import {
  saveAndStartProcess
  ,queryReportConfirmList
  ,reportLowerUnitValidateInfo,
  saveQuarterAreaUnitInfo,
  delReportUnitById
} from '@/api/quarterly-report/subordinateReport'


export default {
  name: "specialReportEdit",
  components: {BlockCard, personTree,RegularTree},
  props: {
    type: {//1 新增 2 补录
      type: String,
      default: "1",
    },
  },
  dicts: ['REPORT_QUARTER'],
  data() {
    return {
      openLoading: {},
      id: '',
      //上报单位接口人信息
      memberOpen: false,
      params: {},

      loading: false,
      visible: false,
      infoData: {},
      filesData: [],
      tableLoading: false,
      tableList1: [],//下级单位上报确认 表格
      queryData: {},
      total: 0,
      reportProvCode:'',//下级单位所属省分

      selectTree:[]
      ,departmentFlag:false//添加上报单位树
      ,regularTreeUrl:'/quarter/area/getReportUnitList'
    }
  }
  , created() {
  }
  , methods: {
    // 显示弹框
    open(id) {
      this.id = id;
      this.queryQuarterReportInfo()
      this.visible = true;
    },
    computedKey() {
      return this.selectTree+'_'+this.id;
    },
    //关闭弹窗
    close() {
      this.visible = false;
       this.$emit("close",'edit');
    },
    //打开loading
    getLoading() {
      this.openLoading = this.$loading({
        lock: true,//lock的修改符--默认是false
        text: '提交中',//显示在加载图标下方的加载文案
        spinner: 'el-icon-loading',//自定义加载图标类名
        background: 'rgba(0, 0, 0, 0.7)',//遮罩层颜色
        target: document.querySelector('#table')//loadin覆盖的dom元素节点
      });
    },
    //关闭loading
    closeLoading() {
      this.openLoading.close();
    },
    //查询上报内容
    queryQuarterReportInfo() {
      this.regularTreeUrl = '/quarter/area/getReportUnitList'
      let params = {
        reportProvId: this.id,
        operationType: 'edit'
      }
      queryQuarterReportInfo(params).then((response) => {
        this.infoData = response.data.reportProvInfo;
        this.reportProvCode = this.infoData.reportUnitCode;

        //下级单位发起确认列表
        this.getunitData();
        //查询附件列表
        this.queryFileList();
      })
    },
    //查询附件列表
    queryFileList() {
      this.tableLoading = true
      queryQuarterReportFileList(this.infoData.id).then((response) => {
        this.filesData = response.data;
        this.tableLoading = false
      })
    },
    /**下载文件*/
    fileDownload(obj) {
      this.download(
        "/sys/attachment/downloadSysAttachment/" + obj.attachmentId,
        {},
        obj.fileName
      );
    },

    /****************下级单位上报确认--开始**************/
    //下级单位上报情况初始化查询列表
    getunitData() {
      this.loading = true;
      let params = {
        reportProvId: this.id,
        reportProvCode: this.reportProvCode
      }
      queryReportConfirmList(params).then((response)=>{
        this.tableList1 = response.data;
        this.loading = false;
      })
    },

    //选择上报单位接口人
    clickPostName(data) {
      this.memberOpen = true;
      this.params.id = data.id;
      this.params.reportUnitCode = data.reportUnitCode;
    },

    //保存上报单位接口人
    toSavePersonById() {
      //获取选中信息
      this.$refs.regularPersonTree.list();
    },

    // 人员选择完成
    cancelPeople(detail) {
      this.memberOpen = false;
      //刷新上报单位列表
      this.getunitData();
    },

    //添加上报单位
    addReportDepart(){
      this.selectTree = [];
      for(let i = 0;i<this.tableList1.length;i++){
        let departStr = {};
        departStr.id = this.tableList1[i].reportUnitCode+"";
        departStr.name = this.tableList1[i].reportUnitName;
        this.selectTree.push(departStr);
      }
      this.departmentFlag = true;
    },

    //保存上报单位(回调函数)
    departLists(data){
      let list=[];
      for (let i = 0; i < data.length; i++) {
        let item = data[i];
        list.push({reportUnitCode: item.id, reportUnitName: item.name})
      }
      //保存上报单位
      saveQuarterAreaUnitInfo({reportProvId:this.id,reportUnitList:list}).then(
        response => {
          const { code, data } = response;
          if (code === 200) {
            //关闭上报单位窗口
            this.departmentFlag = false;
            //刷新上报单位列表
            this.getunitData();
          }
        }
      )
    },

    // 删除上报单位信息
    delReportUnitByIdFun(row){

      this.$confirm('是否确认删除该上报单位？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        //删除上报单位
        delReportUnitById(row.id).then(
          response => {
            const {code, data} = response;
            if (code === 200) {
              this.$message({message: '删除成功', type: 'success'});
              //刷新上报单位列表
              this.getunitData();
            }
          })
        })
    },

    //发起上报按钮
    initiateReportingFun() {
      if (this.tableList1.length == 0) {
        this.$message.error("请选择需要上报的单位");
        return false;
      }
      // 发起上报按钮 提示
      this.$confirm('是否确认发起上报？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        //发起前校验
        let params = {
          reportProvId: this.id,
          reportProvCode: this.reportProvCode
        }
        reportLowerUnitValidateInfo(params).then(res => {
          if(res.code == 200){
            this.getLoading()
            saveAndStartProcess({
              reportProvId: this.id,
              reportProvCode: this.reportProvCode
            }).then((response) => {
              if (response.code == 200) {
                this.$message({message: '发起上报成功', type: 'success'});
                //发起上报成功 关闭当前页面
                this.closeLoading()
                this.close()
              }
            }).catch(() => {
              this.closeLoading()
            })
          }else{
            this.$message.error(res.msg);
          }
        })
      }).catch(() => {
      });
    },
    /****************下级单位上报确认--结束**************/
  }
}
</script>

<style lang="scss" scoped>
.subordinateReport {
  .margin-top-8 {
    margin-top: 8px;
  }

  ::v-deep .el-dialog__body {

    padding: 10px;
    overflow: auto;
  }

  .sub-report-box {
    display: inline-block;
    width: 100%;
    .sub-report-detail {
      margin-bottom:10px;
      display: flex;
      justify-content: left;
      flex-wrap: wrap;
      .sub-report-type{
        text-align: center;
        width: 70px;
        height: 35px;
        font-size: 12px;
        border:1px solid #ddd;
        line-height: 35px;
        border-radius: 4px 0 0 4px;
      }

      .sub-report-num{
        text-align: center;
        width: 70px;
        height: 35px;
        font-size: 12px;
        border:1px solid #ddd;
        line-height: 35px;
        background-color: #f5f8fc;
      }
      .border-right{
        border-radius: 0 4px 4px 0;
        border-right: 1px solid #ddd;
      }

      .sub-report-li{
        text-align: center;
        padding:0 6px;
        min-width: 70px;
        height: 35px;
        font-size: 12px;
        border:1px solid #ddd;
        line-height: 35px;
      }

      .sub-report-core{
        cursor: pointer;
        text-align: center;
        width: 70px;
        color:#f5222d;
        height: 35px;
        font-size: 12px;
        border:1px solid #ddd;
        line-height: 35px;
        border-radius: 0 4px 4px 0;
      }
    }
  }

}
.treeOpen ::v-deep.el-dialog__body{
  padding:0 20px;
}
.subordinateReport ::v-deep.el-dialog__body{
  height: 70vh;
}
</style>
