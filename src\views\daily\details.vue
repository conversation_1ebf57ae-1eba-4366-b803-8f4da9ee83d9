<template>
  <div>
    <el-tabs v-model="activeName">
      <el-tab-pane label="日常报送" name="0">
        <div class="process-x">
          <dailyDetail
            v-if="dailyProblemId||dailyProblemStatus"
            :dailyProblemId="dailyProblemId"
            :dailyProblemStatus="dailyProblemStatus"
            :selectValue="selectValue"
            :procInsId="procInsId"
          ></dailyDetail>
        </div>
      </el-tab-pane>
      <el-tab-pane label="实时报送" name="1" v-if="selectValue.actualFlag==='1'">
        <div class="process-x">
          <actualDetail
            :actualProblemId="actualProblemId"
            :actualProblemStatus="actualProblemStatus"
            v-if="actualProblemId&&actualProblemStatus"
            :selectValue="selectValue"
            :procInsId="procInsId"
          ></actualDetail>
        </div>
      </el-tab-pane>
    </el-tabs>
    <!--<opinion-->
      <!--:processInstanceId="procInsId"-->
      <!--:isShow="isShow"-->
    <!--/>-->
  </div>
</template>

<script>
  import dailyDetail from './detail';
  import actualDetail from '@/views/actual/detail';
  import {problemStatus} from '@/api/daily/index';
  import opinion from '@/views/daily/modifyRecord/opinion';

  export default {
    name: "details",
    props: {
      selectValue: {
        // type:Object
      },
      procInsId:{
        type:String
      },
      activeName:{
        type:String
      }
    },
    components: {
      dailyDetail,
      actualDetail,
      opinion
    },
    data() {
      return {
        dailyProblemId:'',
        dailyProblemStatus:'',
        actualProblemId:'',
        actualProblemStatus:'',
        isShow:'1'
      }
    },
    mounted() {
      this.ProblemStatus();
    },
    methods: {
      //获取流程环节
      ProblemStatus(){
        problemStatus({dailyProblemId: this.selectValue.id}).then(response => {
          this.dailyProblemId = response.data.dailyProblemId;
          // this.dailyProblemStatus = response.data.dailyProblemStatus;
          if(response.data.dailyProblemStatus=='10'){
            this.dailyProblemStatus = '2';
          }else if(response.data.dailyProblemStatus=='9'){
            this.dailyProblemStatus ='1';
          }else if(response.data.dailyProblemStatus=='8'){
            this.dailyProblemStatus ='7';
          }else{
            this.dailyProblemStatus = response.data.dailyProblemStatus;
          }
          this.actualProblemId = response.data.actualProblemId;
          this.actualProblemStatus = response.data.actualProblemStatus?response.data.actualProblemStatus:'1';
        });
      }
    }
  }
</script>

<style scoped lang="scss">
  .process-x {
    /*overflow: hidden;*/
    height: calc(80vh - 165px);
  }
  ::v-deep .el-tabs__content{
    overflow: inherit;
  }
</style>
