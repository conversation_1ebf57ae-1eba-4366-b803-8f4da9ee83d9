<template>
  <div class="app-container">
    <FileUpload
      ref="upload"
      :isShowTip=showTip
      :fileUrl="fileListUrl"
      btnTitle=""
      :key="param"
      :param="param"
      @handleUploadSuccess="handleUploadSuccess"
    >

    </FileUpload>
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="id" prop="fileName">
        <el-input
          v-model="queryParams.fileId"
          placeholder="id"
          clearable
          size="small"
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>


    <el-table v-loading="loading" :data="fileList" @selection-change="handleSelectionChange">
      <el-table-column
        fixed
        align="center"
        label="序号"
        type="index"
        min-width="10%">
        <template slot-scope="scope">
          <span>{{(queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1}}</span>
        </template>
      </el-table-column>
      <el-table-column label="id" align="center" prop="id" width="270" />
      <el-table-column label="文档名称" align="center" prop="fileName" :show-overflow-tooltip="true" width="200"/>
      <el-table-column label="业务主键" align="center" prop="businessId" width="270"/>
      <el-table-column label="业务表名" align="center" prop="businessTable" width="250"/>
      <el-table-column label="ossKey" align="center" prop="storageKey"  width="300"/>
      <el-table-column label="文档大小" align="center" prop="fileSize" width="100"/>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            title="下载"
            icon="el-icon-download"
            @click="downloadFile(scope.row)"
          >下载</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="replaceFile(scope.row)"
          >替换</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
  import { replaceList} from "@/api/system/file/index";
  import FileUpload from '@/views/components/fileUpload/file';

  export default {
    name: "FileName",
    components: {FileUpload},
    data() {
      return {
        param: {},//附件上传所需要信息
        showTip: false,
        // 遮罩层
        loading: true,
        // 非单个禁用
        single: true,
        // 非多个禁用
        multiple: true,
        // 显示搜索条件
        showSearch: true,
        // 总条数
        total: 0,
        // 字典表格数据
        fileList: [],
        // 弹出层标题
        title: "",
        // 是否显示弹出层
        open: false,
        // 查询参数
        queryParams: {
          pageNum: 1,
          pageSize: 10,
          fileId: undefined
        },

        fileListUrl: '/risk/files/replaceFile',
        // 表单参数
        form: {},
      };
    },
    created() {
      this.getList();
    },
    methods: {
      /** 查询字典类型列表 */
      getList() {
        this.loading = true;
        replaceList( {pageNum:this.queryParams.pageNum,pageSize:this.queryParams.pageSize}, this.queryParams).then(response => {
            this.fileList = response.rows;
            this.total = response.total;
            this.loading = false;
          }
        );
      },


      /** 重置按钮操作 */
      resetQuery() {
        this.queryParams.fileId = null;
        this.handleQuery();
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParams.pageNum = 1;
        this.getList();
      },

      //下载附件
      downloadFile(row){
        this.download('/sys/attachment/downloadSysAttachment/'+row.id,{}, row.fileName)
      },
      //替换附件
      replaceFile(row){
        if(!row.storageKey){
          this.$message.error('替换失败, ossKey为空')
        }
        this.param = {
          storageKey: row.storageKey
        }
        this.$nextTick(() => {
          this.$refs.upload.uploadClick();
        })
      },
      // 上传成功回调
      handleUploadSuccess(res, file) {
        this.getList();
        this.$modal.msgSuccess('上传成功');
      },



    }
  };
</script>
