import request from '@/utils/request'

// 查询页面信息
export function queryHandleAppealInfo(data) {
  return request({
    url: '/colligate/handleAppeal/queryHandleAppealInfo',
    method: 'post',
    data: data
  })
}

// 责任追究处理方式
export function queryInvolPersonList(data) {
  return request({
    url: '/colligate/supervisionInvolve/queryInvolPersonList',
    method: 'post',
    data: data
  })
}

// 保存责任追究处理方式

export function saveTreatmentMethod(data) {
  return request({
    url: '/colligate/supervisionInvolve/saveTreatmentMethod',
    method: 'post',
    data: data
  })
}

// 禁入限制人员信息

export function queryLimitedPersonList(data) {
  return request({
    url: '/colligate/limitedPerson/queryLimitedPersonList',
    method: 'post',
    data: data
  })
}

// 保存受理申诉组织

export function saveRelevorg(data) {
  return request({
    url: '/colligate/relevorg/saveRelevorg',
    method: 'post',
    data: data
  })
}
// 保存复合人员
export function saveCheckGroupMember(data) {
  return request({
    url: '/colligate/violDailyRelevperson/saveCheckGroupMember',
    method: 'post',
    data: data
  })
}
// 附件列表

export function selectViolFilesPage(data) {
  return request({
    url: '/colligate/violFile/selectViolFilesPage',
    method: 'post',
    data: data
  })
}

// 保存

export function saveHandleAppealInfo(data) {
  return request({
    url: '/colligate/handleAppeal/saveHandleAppealInfo',
    method: 'post',
    data: data
  })
}

// 删除申诉组织

export function delrelevorg(data) {
  return request({
    url: '/colligate/relevorg/delrelevorg',
    method: 'post',
    data: data
  })
}

// 删除复核人员

export function delCheckGroupMember(data) {
  return request({
    url: '/colligate/violDailyRelevperson/delCheckGroupMember',
    method: 'post',
    data: data
  })
}
// 查询已经选择的申诉组织

export function queryRelevorgByType(data) {
  return request({
    url: '/colligate/relevorg/queryRelevorgByType',
    method: 'post',
    data: data
  })
}

// 查询已经选择的复核人员

export function queryRelevpersonByType(data) {
  return request({
    url: '/colligate/violDailyRelevperson/queryRelevpersonByType',
    method: 'post',
    data: data
  })
}

// 校验

export function checkHandleAppealInfo(data) {
  return request({
    url: '/colligate/handleAppeal/checkHandleAppealInfo',
    method: 'post',
    data: data
  })
}

// 禁入限制人员详情查询
export function queryLimitedPersonInfoById(data) {
  return request({
    url: '/colligate/limitedPerson/queryLimitedPersonInfoById',
    method: 'post',
    data: data
  })
}

// 禁入限制人员保存
export function saveLimitedPersonInfo(data) {
  return request({
    url: '/colligate/limitedPerson/saveLimitedPersonInfo',
    method: 'post',
    data: data
  })
}
// 03 删除单个监督追责附件

export function deleteViolFile(id) {
  return request({
    url: '/colligate/violFile/deleteViolFile/' + id,
    method: 'post'
  })
}

// 责任追究处理方式
export function queryInvolPersonListByProblemId(data) {
  return request({
    url: '/colligate/violQuery/queryInvolPersonListByProblemId',
    method: 'post',
    data: data
  })
}

