import request from '@/utils/request'

//上报总览
export function selectReportStatistical(thisYear){
  return request({
    url: '/colligate/violRegularQuery/selectReportStatistical',
    method: 'post',
    data: JSON.stringify({thisYear:thisYear})
  })
}

//上报完成情况
export function selectReportCompletionStatistical(thisYear){
  return request({
    url: '/colligate/violRegularQuery/selectReportCompletionStatistical',
    method: 'post',
    data: JSON.stringify({thisYear:thisYear})
  })
}

//进行中报告
export function selectReportHandingStatistical(thisYear){
  return request({
    url: '/colligate/violRegularQuery/selectReportHandingStatistical',
    method: 'post',
    data: JSON.stringify({thisYear:thisYear})
  })
}

// 上报列表
export function violRegularList(query) {
  return request({
    url: '/colligate/violRegularQuery/violRegularList',
    method: 'post',
    data: query,
    params:query
  })
}

//上报状态下拉
export function regularStatus(){
  return request({
    url: '/colligate/violRegularQuery/regularStatus',
    method: 'post'
  })
}

//报告类型
export function regularReportType(){
  return request({
    url: '/colligate/violRegularQuery/regularReportType',
    method: 'post'
  })
}

//删除上报
export function deleteRegularReport(id){
  return request({
    url: '/colligate/violRegular/report/deleteRegularReport/'+id,
    method: 'post'
  })
}

