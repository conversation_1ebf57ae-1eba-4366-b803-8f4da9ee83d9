<template>
  <el-dialog
    title=""
    :visible.sync="visible"
    width="900px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :show-close="true"
    append-to-body
    custom-class="data-inconsistency-dialog"
  >
    <!-- 自定义标题 -->
    <div slot="title" class="dialog-title">
      <i class="el-icon-warning" style="color: #E6A23C; margin-right: 8px;"></i>
      <span>提示</span>
    </div>

    <div class="dialog-content">
      <!-- 温馨提示文字 -->
      <div class="warning-text">
        温馨提示：字段数据与基础模块数据不一致，请根据实际情况对往基础数据模块进行数据更新。
      </div>

      <!-- 数据不一致表格 -->
      <div v-for="(section, index) in inconsistentData" :key="index" class="data-section">
        <div class="section-title">{{ section.title }}</div>
        
        <el-table
          :data="section.data"
          border
          style="width: 100%; margin-bottom: 20px;"
          :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
        >
          <el-table-column prop="fieldName" label="字段" width="300" align="left">
            <template slot-scope="scope" >
              <span>{{ scope.row.fieldName }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="quarterValue" label="季度报告" width="260">
            <template slot-scope="scope">
              <span
                class="inconsistent-value"
                :style="{ textAlign: isNumber(scope.row.quarterValue) ? 'right' : 'left', display: 'block' }"
              >
                {{ scope.row.quarterValue }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="baseValue" :label="index === 1 ? '日常报送' : '基础数据'" width="260">
            <template slot-scope="scope">
              <span
                class="inconsistent-value"
                :style="{ textAlign: isNumber(scope.row.baseValue) ? 'right' : 'left', display: 'block' }"
              >
                {{ scope.row.baseValue }}
              </span>
            </template>
          </el-table-column>
        </el-table>

        <!-- 详细说明 -->
        <div v-if="section.description" class="section-description">
          {{ section.description }}
        </div>
      </div>

      <!-- 底部提示 -->
      <div class="bottom-warning">
        温馨提示：字段数据与日常报送模块数据不一致，请根据实际情况校准数据，并联系集团公司。（本季度需上报受理时间为2025-06-15至2025-09-14的日常报送数据）
      </div>
    </div>

    <!-- 底部按钮 -->
    <!-- <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose" size="medium">返回</el-button>
    </div> -->
  </el-dialog>
</template>

<script>
export default {
  name: 'DataInconsistencyDialog',
  props: {
    value: {
      type: Boolean,
      default: false
    },
    inconsistentData: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    visible: {
      get() {
        return this.value;
      },
      set(val) {
        this.$emit('input', val);
      }
    }
  },
  methods: {
    handleClose() {
      this.visible = false;
      this.$emit('close');
    },
    // 判断是否为数字
    isNumber(value) {
      if (!value && value !== 0) return false;
      // 去除空格后判断
      const trimmedValue = String(value).trim();
      // 判断是否为纯数字（包括小数）
      return /^\d+(\.\d+)?$/.test(trimmedValue);
    }
  }
};
</script>

<style scoped>
.data-inconsistency-dialog {
  border-radius: 8px;
}

.dialog-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

.dialog-content {
  padding: 0 20px;
}

.warning-text {
  background-color: #fdf6ec;
  border: 1px solid #faecd8;
  border-radius: 4px;
  padding: 12px 16px;
  margin-bottom: 20px;
  color: #e6a23c;
  font-size: 14px;
  line-height: 1.5;
}

.data-section {
  margin-bottom: 30px;
}

.section-title {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 10px;
  padding-left: 8px;
  border-left: 3px solid #409eff;
}

.inconsistent-value {
  color: #f56c6c;
  font-weight: 500;
}

.section-description {
  background-color: #f0f9ff;
  border: 1px solid #b3d8ff;
  border-radius: 4px;
  padding: 12px 16px;
  margin-top: 10px;
  color: #409eff;
  font-size: 13px;
  line-height: 1.4;
}

.bottom-warning {
  background-color: #fdf6ec;
  border: 1px solid #faecd8;
  border-radius: 4px;
  padding: 12px 16px;
  color: #e6a23c;
  font-size: 14px;
  line-height: 1.5;
  margin-top: 20px;
}

.dialog-footer {
  text-align: center;
  padding: 20px 0 10px 0;
}

/* 表格样式调整 */
::v-deep .el-table th {
  background-color: #f5f7fa !important;
}

::v-deep .el-table td {
  padding: 12px 0;
}

::v-deep .el-table--border td {
  border-right: 1px solid #ebeef5;
}

::v-deep .el-table--border th {
  border-right: 1px solid #ebeef5;
}
</style>
