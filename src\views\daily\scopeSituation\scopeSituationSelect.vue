<!--违规经营投资责任范围情形列表-->
<template>
  <div class="scope">
    <el-dialog v-bind="$attrs" :visible.sync="visible" width="80%" v-on="$listeners" @open="onOpen" @close="onClose" append-to-body :title="title">
      <Jscrollbar height="68vh">
         <el-form :model="queryParams" ref="queryForm" id="queryParams"  :inline="true" label-width="45px">
      <el-form-item label="方面" prop="status">
      <el-select
      v-model="queryParams.aspectCode"
      placeholder="方面"
      clearable
      size="small"
      >
      <el-option
      v-for="item in aspectList"
      :key="item.code"
      :label="item.codeText"
      :value="item.code"
      />
      </el-select>
      </el-form-item>
      <el-form-item label="情形">
        <el-input v-model="queryParams.situationName" placeholder=""  :style="{width: '100%'}">
        </el-input>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="QueryAspectSituateList">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
      <el-form   style="height: calc(100% - 60px)">
      <el-table v-loading="loading" :data="tableList" ref="table" @selection-change="handleSelectionChange" height="100%">
        <el-table-column type="selection" min-width="10%" fixed="left"/>
        <el-table-column label="序号" type="index" min-width="10%" align="center" />
        <el-table-column label="违规经营投资责任追究方面名称" prop="aspectName" min-width="30%"/>
        <el-table-column label="违规经营投资责任追究情形名称" prop="situationName"  min-width="50%"/>
      </el-table>
    </el-form>
      </Jscrollbar>
      <div slot="footer">
        <el-button size="mini" @click="close">取消</el-button>
        <el-button size="mini" type="primary" @click="handelConfirm">确定</el-button>
      </div>
  </el-dialog>
  </div>
</template>

<script>
  import {queryAspectSituateList,queryRangeAspectList,saveAspectSituate} from "@/api/daily/scopeSituation/index";

  export default {
    name: "report",
    props: {
      problemId:'',
      relevantTableId:'',
      relevantTableName:'',
    },
    data() {
      return {
        loading:false,
        title:'违规经营投资责任范围情形列表',
        visible:false,//弹框
        status:'',
        // 显示搜索条件
        showSearch: true,
        // 总条数
        total: 0,
        // 表格数据
        tableList: [],
        //已选中id
        hasSelectList:[],
        // 方面列表
        aspectList:[],
        //新增主键
        //日常问题查询 参数
        queryParams: {
          problemId:this.problemId,
          relevantTableId:this.relevantTableId,
          relevantTableName:this.relevantTableName,
          aspectCode:'',
          situationName:''
        },
      };
    },
    created() {
      this.QueryRangeAspectList();
    },
    filters: {
    },
    methods: {
      /**查询范围情形列表*/
      QueryAspectSituateList() {
        this.loading = true;
        queryAspectSituateList(this.queryParams).then(
          response => {
            this.tableList = response.data;
            this.total = response.data.length;
            this.$nextTick(()=> {
              this.tableList.forEach(row => {
                if (row.checked) {
                  this.$refs.table.toggleRowSelection(row, true);
                }
              });
            });
            this.loading = false;
          }
        );
      },
      /**方面*/
      QueryRangeAspectList(){
        queryRangeAspectList().then(
          response => {
            this.aspectList = response.data.aspectList;
          }
        );
      },
      /**重置按钮操作*/
      resetQuery() {
        this.queryParams.aspectCode = "";
        this.queryParams.situationName = "";
        this.QueryAspectSituateList();
      },
      // 显示弹框
      show() {
        this.visible = true;
        this.QueryAspectSituateList();
      },
      onClose() {

      },
      close() {
        this.visible = false;
      },
      handelConfirm() {
       let paramsData = JSON.parse(JSON.stringify(this.queryParams));
        paramsData.rangeEntityList = this.hasSelectList;
        saveAspectSituate(paramsData).then(
          response => {
            this.$modal.msgSuccess("保存成功");
            this.$emit('queryRangeList');
            this.close();
          })
      },
      //选中的值
      handleSelectionChange(val){
        let selectList = [];
        val.forEach(function (item) {
          selectList.push(item)
        });
        this.hasSelectList = selectList;
      },
      //关闭
      onOpen(){},
    }
  };
</script>

<style lang="scss">
  .height{
    height: 100%;
  }
</style>




