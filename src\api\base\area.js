import request from '@/utils/request'

// 基础数据查询--企业基本信息列表查询
export function getAreaBaseInfo(query) {
  return request({
    url: '/colligate/baseInfo/getAreaBaseInfo',
    method: 'post',
    data: query
  })
}

// 基础数据查询--企业基本信息详情查询
export function getAreaBaseInfoById(query) {
  return request({
    url: '/colligate/baseInfo/getAreaBaseInfoById',
    method: 'post',
    data: query
  })
}
// 基础数据维护--企业基本信息详情查询
export function getAreaBaseInfoOne(query) {
  return request({
    url: '/colligate/baseInfo/getAreaBaseInfoOne',
    method: 'post',
    data: query
  })
} 

// 基础数据维护--企业基本信息保存或提交
export function addAreaBaseInfo(query) {
  return request({
    url: '/colligate/baseInfo/addAreaBaseInfo',
    method: 'post',
    data: query
  })
}