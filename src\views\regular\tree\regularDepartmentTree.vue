<template>
  <div class="position">
    <el-row style="position: absolute;top:0;width: 100%;z-index: 99;">
      <el-col :span="12" class="text-right">
        <el-button size="mini" type="primary" @click="allChecked()">全选</el-button>
        <el-button size="mini" type="primary" @click="allClear()" plain>清空</el-button>
      </el-col>
      <el-col :span="12" class="text-right">
        <el-button size="mini" type="primary" @click="versionRecordOpen()" v-if="!supplementalAdmission" plain>版本记录</el-button>
        <el-button size="mini" type="primary" @click="listOpen()"  v-if="!supplementalAdmission">保存</el-button>
        <el-button size="mini" type="primary" @click="list('0')"  v-if="supplementalAdmission">保存</el-button>
      </el-col>
    </el-row>
    <el-row style="padding:40px 0 10px;">
      <el-col :span="12" class="tree-box">
        <el-tree ref="tree"
                 :data="data"
                 lazy
                 :key="treeIndex"
                 show-checkbox
                 :default-checked-keys="defaultTree"
                 node-key="id"
                 check-strictly
                 @check-change="checkChange"
                 :load="loadnode"
                 :props="defaultProps"
                 @node-click="nodeclick">
        </el-tree>
      </el-col>
      <el-col :span="12" class="tree-box">
        <TreeSelect
          :key="index"
          :selectTree="selectTreeData"
          @noCheck="noCheck"
        >
        </TreeSelect>
      </el-col>
    </el-row>

    <el-dialog class="versionRecord" :visible.sync="versionRecordFlag" width="80%" append-to-body title="已选字段历史">
      <VersionRecord
        v-if="versionRecordFlag"
        ref="versionRecord"
        @selectRecord="selectRecord"
      />
    </el-dialog>

    <el-dialog
      class="saveVisible"
      title="提示"
      :visible.sync="dialogVisible"
      width="400px"
      append-to-body  :before-close="visibleClose">
      <div class="version-name-box">
        <el-form class="version-form"  label-width="80px">
          <el-form-item label="版本名称">
            <el-input v-model="versionName" type="text" placeholder="版本名称"/>
          </el-form-item>
        </el-form>
        <span class="version-title">确认按照新模板保存并将当前模板替换成默认模板吗？</span>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="saveRecordClose('0')" plain>仅保存</el-button>
         <el-button type="primary" @click="saveRecordClose('1')">保存并替换</el-button>
      </span>
    </el-dialog>
<!--    <el-dialog-->
<!--      :visible.sync="dialogVisible"-->
<!--      :before-close="handleClose">-->
<!--      &lt;!&ndash; 对话框内容 &ndash;&gt;-->
<!--    </el-dialog>-->
  </div>

</template>

<script>
import {treeUrl} from "@/api/components/index";
import TreeSelect from '@/components/TreeSelect/checked';
import VersionRecord from '@/views/regular/tree/versionRecord';
import {

} from "@/api/regular/tree/regularTree"
import {getDefaultProvVersion,getRegularProvVersionId,saveProvVersion,updateRegularProvVersionId,updateLastUseTime} from "@/api/regular/tree/versionRecord";

export default {
  name: 'regularTree',
  components: {
    TreeSelect, VersionRecord
  },
  props: {
    selectTree: {
      type: Array,
      default: []
    },
    supplementalAdmission: {
      type: Boolean,
      default: false
    },
    url: {
      type: String,
      default: ''
    },
    regularReportId:{
      type: String,
      default: ''
    },
    params: {}
  },
  data() {
    return {
      versionName: '',//版本名称
      dialogVisible: false,//提示 保存版本记录
      versionRecordFlag: false,//版本记录
      data: [],
      defaultTree: [],
      index: 0,
      selectLength:0,
      treeIndex: 0,
      versionCode:'',
      query: {
        name: '',
        areaCode: '',
        isParent: '',
        provCode: '',
        checked: '',
        id: '',
        pId: '',
        isAll: false,
        open: false,
        nocheck: '',
        userId: '',
        selectName: ''
      },
      allclear: false,//清空
      allcheck: false,//全选
      defaultProps: {//树对象属性对应关系
        children: 'children',
        label: 'name',
        isLeaf: function (data, node) {
          return !data.isParent
        },
        disabled: function (data, node) {
          return data.nocheck
        }
      }
    }
  },
  created() {
    this.selectTreeData = this.selectTree;
    if (this.selectTreeData.length) {//有修改后的值
      for (let i = 0; i < this.selectTreeData.length; i++) {
        this.defaultTree[i] = this.selectTreeData[i].id;
      }
      if(!this.supplementalAdmission){
        this.getRegularProvVersionIdFun()
      }
    } else {//查询默认版本
      if(!this.supplementalAdmission){
        this.getDefaultProvVersionFun()
      }
    }
  },
  methods: {
    //查询默认版本
    getDefaultProvVersionFun(){
       getDefaultProvVersion().then(
         response => {
        if(response.data.length){//有默认版本
          this.defaultTree = [];
          this.selectTreeData = [];
          //{ "id": "0011", "name": "北京" },
          this.versionCode = response.data[0].regularProvVersionId;
          for (let i = 0; i < response.data.length; i++) {
            this.selectTreeData.push({id:response.data[i].unitCode,name:response.data[i].unitName})
            this.defaultTree[i] = response.data[i].unitCode;
          }
        }
        }
      );
    },
    //查询当前使用的版本code
    getRegularProvVersionIdFun(){
      getRegularProvVersionId({id:this.regularReportId}).then(response => {
        this.versionCode = response.data;
      })
    },
    handleClose(done) {
      // 在这里执行关闭前的逻辑
      // 例如: 确认用户想要关闭对话框
      this.$confirm('确认关闭？')
        .then(_ => {
          done();
        })
        .catch(_ => {
          // 如果用户选择不关闭，则不调用 done()
        });
    },
    //打开版本记录
    versionRecordOpen() {
      this.versionRecordFlag = true;
      this.$nextTick(() => {
        this.$refs.versionRecord.init();
      })
    },
    //获取数据返回
    list(type) {
      this.$emit('list', this.selectTreeData,type);
    },
    //点击保存，查询是否为新的需要添加记录
    listOpen() {
      if(this.selectTreeData.length == 0){
        this.$modal.msgError("请至少选择一个单位！");
        return false;
      }
      if(this.versionCode!==''){//使用的现有版本
        updateRegularProvVersionId({ "id":this.regularReportId,
          "regularProvVersionId":this.versionCode
        }).then(response => {
          if(response.code==200){
            updateLastUseTime({id:this.versionCode}).then(ret => {
              this.list('0')
            })
          }else{
            this.$modal.msgError(response.msg);
          }
        })
      }else{
        this.dialogVisible = true;
      }

    },
    visibleClose(){
      this.dialogVisible = false;
    },
    //提示 保存版本记录 直接关闭
    //0:直接关闭 2：保存并替换 1：仅保存
    saveVisibleClose() {
       this.list('0')
    },
    //提示 保存版本记录
    //1：保存并替换 0：仅保存
    saveRecordClose(type) {
      let list=[];
      for (let i = 0; i < this.selectTreeData.length; i++) {
        let item = this.selectTreeData[i];
        list.push({unitCode: item.id, unitName: item.name})
      }
      let versionName = this.versionName;
      //做保存操作
      saveProvVersion({versionName:versionName,subList:list,isDefault:type}).then(res => { //保存完成之后
        this.versionCode = res.data;
        updateRegularProvVersionId({ "id":this.regularReportId,
          "regularProvVersionId":res.data
        }).then(response => {
          if(response.code==200){
            updateLastUseTime({id:this.versionCode}).then(ret => {
              this.versionName = '';
              this.list(type)
              this.dialogVisible = false;
            })
          }else{
            this.$modal.msgError(response.msg);
          }
        })
      })
    },
    //查询人员姓名
    treeQuery() {
      treeUrl(this.url, {...this.query, ...this.params}).then(
        response => {
          this.data = response
        }
      );
    },
    loadnode(node, resolve) {
      //如果展开第一级节点，从后台加载一级节点列表
      if (node.level == 0) {
        this.index++;
        // this.selectTreeData = [];
        this.loadfirstnode(resolve);
      }
      //如果展开其他级节点，动态从后台加载下一级节点列表
      if (node.level >= 1) {
        this.loadchildnode(node, resolve);
      }
    },
    //加载第一级节点
    loadfirstnode(resolve) {
      treeUrl(this.url, {...this.query, ...this.params}).then(
        response => {

          if (this.allcheck) {//全选
            this.selectTreeData = [];
            this.defaultTree = [];
            response.forEach(item => {
              this.selectTreeData.push(item);
              this.defaultTree.push(item.id);
            })

            this.allclear = false;
            this.allcheck = false;
            resolve(response);
          } else if (this.allclear) {//清空
            this.selectTreeData = [];
            this.defaultTree = [];

            this.allclear = false;
            this.allcheck = false;
            resolve(response);
          } else {

            this.allclear = false;
            this.allcheck = false;
            resolve(response);
          }
        }
      );
    },
    //全选
    allChecked() {
      this.allclear = false;
      this.allcheck = true;
      this.versionCode=""
      this.$nextTick(() => {
        this.treeIndex++;
      })
    },
    //清空
    allClear() {
      this.selectTreeData = [];
      this.defaultTree = [];
      this.allcheck = false;
      this.allclear = true;
      this.$nextTick(() => {
        this.treeIndex++;
      })

    },
    //加载节点的子节点集合
    loadchildnode(node, resolve) {
      treeUrl(this.url, {...node.data, ...this.params}).then(
        response => {
          resolve(response);
        }
      );
    },
    //点击节点上触发的事件，传递三个参数，数据对象使用第一个参数
    nodeclick(data, dataObj, self) {
    },
    //修改状态
    checkChange(node, type) {
      if (type) {//选中
        if (!this.defaultTree.includes(node.id)) {
          this.defaultTree.push(node.id);
        }
        //如果已选中的长度不为0，则减少已选中的长度
        if(this.selectLength!=0){
          this.selectLength--;
          //否则，重置版本代码为空
        }else{
          this.versionCode=""
        }
        if (!this.selectTreeData.some(item => item.id === node.id)) {
          this.selectTreeData.push(node);
        }
      } else {
        this.versionCode=""
        this.selectTreeData.splice(this.selectTreeData.findIndex(item => item.id === node.id), 1);
        this.defaultTree.splice(this.defaultTree.findIndex(item => item === node.id), 1)
      }
      this.index++
      console.log('节点调整',this.selectTreeData)
    },
    //删除某节点
    noCheck(obj) {
      this.versionCode=""
      if (this.$refs.tree.getNode(obj.id)) {
        this.$refs.tree.setChecked(obj.id, false);
      } else {
        this.defaultTree.splice(this.defaultTree.findIndex(item => item === obj.id), 1);
        this.selectTreeData.splice(this.selectTreeData.findIndex(item => item.id === obj.id), 1);

      }
    },
    //选择
    selectRecord(item) {
      this.versionCode=item.id;
      this.allclear = false;
      this.allcheck = false;
      this.selectTreeData = [];
      this.defaultTree = [];
      let list = item.subList;
      for (let i = 0; i < list.length; i++) {
        this.selectTreeData.push({id: list[i].unitCode, name: list[i].unitName})
        this.defaultTree.push(list[i].unitCode);
      }
      this.selectLength = list.length;
      this.$nextTick(() => {
        this.treeIndex++;
        this.index++;
        this.versionRecordFlag = false;
      })
    }
  }
}
</script>
<style rel="stylesheet/scss" scoped lang="scss">
.is-disabled {
  display: none !important;
}

.position {
  position: relative;
}

.tree-box {
  height: calc(100vh - 400px);
  overflow: auto;

}

.versionRecord ::v-deep.el-dialog__body {
  height: 70vh;
  overflow: auto;
}

.saveVisible ::v-deep.el-dialog {
  margin-top: calc(50vh - 100px) !important;
}

.saveVisible ::v-deep.el-dialog__body {
  padding: 4px 20px;
  height: 100px;
}

.version-name-box{
  .version-title{
    width: 100%;
    display: inline-block;
    margin:10px 0  4px;
    font-size: 14px;
  }
  .version-form{
    .el-form-item{
      margin:0;
    }
  }
}
</style>
