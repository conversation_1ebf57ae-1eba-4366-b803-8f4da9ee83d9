import request from '@/utils/request'

// 翻页查找待办任务列表（待办列表）
export function taskToDo(param,data) {
  return request({
    url: '/workflowController/taskToDo',
    method: 'post',
    data: data,
    params: param
  })
}

// 翻页查找已办任务列表（已办列表）
export function taskHasDone(param,data) {
  return request({
    url: '/workflowController/taskHasDone',
    method: 'post',
    data: data,
    params: param
  })
}

  // 翻页查找待阅任务列表（待阅列表）
  export function taskToRead(param,data) {
    return request({
      url: '/workflowController/taskToRead',
      method: 'post',
      data: data,
      params: param
    })
}

// 翻页查找已阅任务列表（已阅列表）
export function taskHasRead(param,data) {
  return request({
    url: '/workflowController/taskHasRead',
    method: 'post',
    data: data,
    params: param
  })
}

// 流程待办环节树
export function taskToDoTree() {
  return request({
    url: '/workflowController/taskToDoTree',
    method: 'post'
  })
}
  // 流程已办环节树
  export function taskHasDoneTree() {
    return request({
      url: '/workflowController/taskHasDoneTree',
      method: 'post'
    })
}

