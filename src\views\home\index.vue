<template>
  <div class="home-container">
    <div class="top-container">
      <div class="left-checbox">
        <el-radio v-model="formData.reportType" label="1">日常报送</el-radio>
        <el-radio v-model="formData.reportType" label="2">实时报送</el-radio>
      </div>
      <div class="right-form">
        <div class="tagmodel-search-title">注意：金额单位为【万元】</div>
        <div class="tagmodel-search-condition">选择年度：</div>
        <div class="right-radio">
          <el-radio v-model="formData.yearSelection" label="2">去年</el-radio>
          <el-radio v-model="formData.yearSelection" label="1">今年</el-radio>
          <el-radio v-model="formData.yearSelection" label="3">自定义</el-radio>
          <el-date-picker
            v-model="formData.startYear"
            style="margin-right: 10px"
            type="year"
            :disabled="endyearDisatble"
            placeholder="开始日期"
            :picker-options="pickerStartAuditYear"
            format="yyyy"
            value-format="yyyy"
            @change="startYearChange"
          />
          至
          <el-date-picker
            v-model="formData.endYear"
            style="margin-left: 10px"
            :disabled="endyearDisatble"
            type="year"
            placeholder="结束日期"
            clearable
            :picker-options="pickerEndAuditYear"
            value-format="yyyy"
            format="yyyy"
            @change="endYearChange"
          />
        </div>
      </div>
    </div>
    <div class="mid-container">
      <el-row style="padding-bottom: 4px; padding-top: 8px">
        <el-col :span="6" class="padding-right-4px">
          <div class="el-common-card height-470">
            <div class="el-common-header">
              <div class="el-common-card-header border0">
                <div class="el-card-header-title">监督追责问题总览</div>
              </div>
            </div>
            <div class="el-content">
              <div
                class="
                  el-common-card
                  row-echars-block
                  margin-top-8-px
                  height-95-px
                  flex
                  align-items-center
                "
              >
                <div class="el-row-one-num el-after-line">
                  <div
                    id="A_problemNumber"
                    class="el-bottom-nums pointer"
                    @click="goQuestionSearch('wtsl')"
                  >
                    {{ problemOverviewData.problemNumber || "0" }}
                  </div>
                  <div class="el-top-text">问题数量</div>
                </div>
                <div class="el-one-num el-graphic_alphanumeric height">
                  <div
                    class="
                      el-one_graphic_alphanumeric
                      height
                      flex-dir-col-jus-cen
                      el-auto_padding
                    "
                  >
                    <div
                      class="
                        el-one_graphic
                        flex
                        align-items-center
                        margin-bottom-10-px
                      "
                    >
                      <i class="icon iconfont icon-jicha icon-color-1" />
                      <span class="el-graphic_text">核查</span>
                      <span
                        id="A_checkProblemNumber"
                        class="el-graphic_num pointer"
                        @click="goQuestionSearch('hc')"
                      >{{
                        problemOverviewData.checkProblemNumber || "0"
                      }}</span>
                    </div>

                    <div class="el-one_graphic flex align-items-center">
                      <i class="icon iconfont icon-jicha icon-color-1" />
                      <span class="el-graphic_text">整改</span>
                      <span
                        id="A_reformProblemNumber"
                        class="el-graphic_num pointer"
                        @click="goQuestionSearch('zg')"
                      >{{
                        problemOverviewData.reformProblemNumber || "0"
                      }}</span>
                    </div>
                  </div>
                </div>

                <div
                  class="
                    flex-1
                    height
                    flex-dir-col-jus-cen
                    padding-right-15px
                    flex-dir-col-jus-cen-style
                  "
                >
                  <el-progress
                    :percentage="problemOverviewData.checkProblemRate || 0"
                    class="el-progress margin-bottom-18-px"
                  />
                  <el-progress
                    :percentage="problemOverviewData.reformProblemRate || 0"
                  />
                </div>
              </div>

              <div
                class="
                  el-common-card
                  row-echars-block
                  margin-top-8-px
                  height-130-px
                  flex
                  align-items-center
                "
              >
                <div class="el-row-one-num graphic_alphanumeric height width">
                  <div
                    class="
                      el-one_graphic_alphanumeric
                      height
                      flex-dir-col-jus-cen
                    "
                  >
                    <div
                      class="
                        el-one_graphic
                        flex
                        align-items-center
                        margin-bottom-10-px
                        width
                      "
                    >
                      <i
                        class="icon iconfont icon-yly_qianzhuang icon-color-3"
                      />
                      <span class="el-graphic_text">损失金额</span>
                      <span
                        id="A_lossAmount"
                        class="el-graphic_num flex-1 text-right block"
                      >{{ problemOverviewData.lossAmount || 0 }}</span>
                    </div>

                    <div class="el-one_graphic flex align-items-center width margin-bottom-10-px">
                      <i
                        class="icon iconfont icon-yly_qianzhuang icon-color-3"
                      />
                      <span class="el-graphic_text">损失风险</span>
                      <span
                        id="A_lossRisk"
                        class="el-graphic_num flex-1 text-right block"
                      >{{ problemOverviewData.lossRisk || 0 }}</span>
                    </div>

                    <div class="el-one_graphic flex align-items-center width margin-bottom-10-px">
                      <i
                        class="icon iconfont icon-yly_qianzhuang icon-color-3"
                      />
                      <span class="el-graphic_text">挽回损失</span>
                      <span
                        id="A_retrieveLossAmount"
                        class="el-graphic_num flex-1 text-right block"
                      >{{ problemOverviewData.retrieveLossAmount || 0 }}</span>
                    </div>

                    <div class="el-one_graphic flex align-items-center width">
                      <i
                        class="icon iconfont icon-yly_qianzhuang icon-color-3"
                      />
                      <span class="el-graphic_text">降低风险</span>
                      <span
                        id="A_reduceLossRisk"
                        class="el-graphic_num flex-1 text-right block"
                      >{{ problemOverviewData.reduceLossRisk || 0 }}</span>
                    </div>
                  </div>
                </div>
              </div>

              <!--<div-->
                <!--class="-->
                  <!--el-common-card-->
                  <!--row-echars-block-->
                  <!--margin-top-8-px-->
                  <!--height-95-px-->
                  <!--flex-->
                  <!--align-items-center-->
                <!--"-->
              <!--&gt;-->
                <!--<div class="el-row-one-num el-after-line">-->
                  <!--<div id="A_perfectSystemNumber" class="el-bottom-nums">-->
                    <!--{{ problemOverviewData.perfectSystemNumber || 0 }}-->
                  <!--</div>-->
                  <!--<div class="el-top-text">完善制度</div>-->
                <!--</div>-->

                <!--<div class="el-row-one-num">-->
                  <!--<div id="A_retrieveLossAmount" class="el-bottom-nums">-->
                    <!--{{ problemOverviewData.retrieveLossAmount || 0 }}-->
                  <!--</div>-->
                  <!--<div class="el-top-text">挽回损失</div>-->
                <!--</div>-->

                <!--<div class="flex-1 height">-->
                  <!--<div id="echars-huan">-->
                    <!--<ehcarsHuan-->
                      <!--id="echars-huan-1"-->
                      <!--:chars-data="-->
                        <!--problemOverviewData.retrieveLossAmountRate || 0-->
                      <!--"-->
                    <!--/>-->
                  <!--</div>-->
                <!--</div>-->
              <!--</div>-->

              <div
                class="
                  el-common-card
                  row-echars-block
                  margin-top-8-px
                  height-95-px
                  flex
                  align-items-center
                "
              >
                <div class="el-row-one-num el-after-line">
                  <div id="A_involvePersonNumber" class="el-bottom-nums">
                    {{ problemOverviewData.involvePersonNumber || 0 }}
                  </div>
                  <div class="el-top-text">涉及人数</div>
                </div>

                <div class="el-row-one-num">
                  <div id="A_accountabilityPersonNumber" class="el-bottom-nums">
                    {{ problemOverviewData.accountabilityPersonNumber || 0 }}
                  </div>
                  <div class="el-top-text">追责人数</div>
                </div>

                <div class="flex-1 height">
                  <div id="echars-huan">
                    <ehcarsHuan
                      id="echars-huan-2"
                      :chars-data="
                        problemOverviewData.accountabilityPersonRate || 0
                      "
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-col>

        <el-col :span="12" class="padding-left-4px padding-right-4px">
          <div class="el-common-card height-470">
            <div class="el-common-header">
              <div v-if="orgGrade == 'G'" class="el-common-card-headers">
                <div
                  class="el-card-header-select"
                  :class="changeTabsIndex == '0' ? 'active' : ''"
                  @click="changeTabs('0')"
                >
                  涉及单位统计(
                  <span id="mapName1" class="mapName">{{
                    statisticsDataChina.mapType == "全国"
                      ? "31省"
                      : statisticsDataChina.mapType
                  }}</span>
                  )
                </div>

                <div
                  class="el-card-header-select"
                  :class="changeTabsIndex == '1' ? 'active' : ''"
                  @click="changeTabs('1')"
                >
                  涉及单位统计(子公司)
                </div>
              </div>

              <div
                v-if="orgGrade != 'G' && !querySubsidiaryData"
                class="el-common-card-headers"
              >
                <div
                  class="el-card-header-select"
                  :class="changeTabsIndex == '0' ? 'active' : ''"
                  @click="changeTabs('0')"
                >
                  涉及单位统计(
                  <span id="mapName1" class="mapName">{{
                    statisticsDataChina.mapType == "全国"
                      ? "31省"
                      : statisticsDataChina.mapType
                  }}</span>
                  )
                </div>
              </div>

              <div
                v-if="orgGrade != 'G' && querySubsidiaryData"
                class="el-common-card-headers"
              >
                <div
                  class="el-card-header-select"
                  :class="changeTabsIndex == '1' ? 'active' : ''"
                  @click="changeTabs('1')"
                >
                  涉及单位统计(子公司)
                </div>
              </div>
            </div>

            <div
              v-if="changeTabsIndex === '0'"
              class="el-common-card-content relactive"
            >
              <div>
                <el-select
                  v-model="formData.statisticsType"
                  placeholder="请选择"
                  @change="statisticsTypeChange"
                >
                  <el-option label="问题数量" value="problemNumber" />
                  <el-option
                    label="严重不良影响问题数量"
                    value="seriousProblemNumber"
                  />
                  <el-option label="损失金额" value="lossAmount" />
                  <el-option label="损失风险" value="lossRisk" />
                  <el-option
                    label="追责人数"
                    value="accountabilityPersonNumber"
                  />
                  <el-option label="完善制度" value="perfectSystemNumber" />
                  <el-option label="挽回损失" value="retrieveLossAmount" />
                </el-select>
              </div>
              <div class="height_36_px flex width relactive">
                <el-row class="width height">
                  <el-col :span="15" class="padding-right-4px height">
                    <echarsChina
                      v-if="statisticsDataChina"
                      id="china_echars"
                      :chars-data="statisticsDataChina"
                    />
                  </el-col>

                  <el-col :span="9" class="height echars-right-block">
                    <div class="titles">
                      <span
                        id="mapName2"
                        class="mapName"
                      >{{
                        statisticsDataChina.mapType == "全国"
                          ? "31省分"
                          : statisticsDataChina.mapType
                      }}总计</span>
                    </div>

                    <div class="echars-right-block_con flex-dir-col-jus-cen">
                      <div
                        class="one-echars-right-block flex align-items-center"
                      >
                        <i
                          class="
                            icon
                            iconfont
                            icon-changjianwentixiangguanwenti
                            margin-right-14-px
                          "
                        />
                        <span class="flex-1">问题数量</span>
                        <span
                          id="problemNumber"
                          class="right-nums"
                        >{{ provinceTotalList.problemNumber || "0" }}</span>
                      </div>
                      <div
                        class="one-echars-right-block flex align-items-center"
                      >
                        <i
                          class="
                            icon
                            iconfont
                            icon-yanzhongweifa
                            margin-right-14-px
                          "
                        />
                        <span class="flex-1">严重不良影响问题数量</span>
                        <span id="seriousProblemNumber" class="right-nums">{{
                          provinceTotalList.seriousProblemNumber || "0"
                        }}</span>
                      </div>
                      <div
                        class="one-echars-right-block flex align-items-center"
                      >
                        <i class="icon iconfont icon-A margin-right-14-px" />
                        <span class="flex-1">损失金额</span>
                        <span id="lossAmount" class="right-nums">{{
                          provinceTotalList.lossAmount || "0"
                        }}</span>
                      </div>
                      <div
                        class="one-echars-right-block flex align-items-center"
                      >
                        <i
                          class="
                            icon
                            iconfont
                            icon-fengxiantishi
                            margin-right-14-px
                          "
                        />
                        <span class="flex-1">损失风险</span>
                        <span id="lossRisk" class="right-nums">{{
                          provinceTotalList.lossRisk || "0"
                        }}</span>
                      </div>
                      <div
                        class="one-echars-right-block flex align-items-center"
                      >
                        <i
                          class="
                            icon
                            iconfont
                            icon-weixiuzhan-fuzeren
                            margin-right-14-px
                          "
                        />
                        <span class="flex-1">追责人数</span>
                        <span
                          id="accountabilityPersonNumber"
                          class="right-nums"
                        >{{
                          provinceTotalList.accountabilityPersonNumber || "0"
                        }}</span>
                      </div>

                      <div
                        class="one-echars-right-block flex align-items-center"
                      >
                        <i
                          class="
                            icon
                            iconfont
                            icon-anquanzhidux
                            margin-right-14-px
                          "
                        />
                        <span class="flex-1">完善制度</span>
                        <span id="perfectSystemNumber" class="right-nums">{{
                          provinceTotalList.perfectSystemNumber || "0"
                        }}</span>
                      </div>
                      <div
                        class="one-echars-right-block flex align-items-center"
                      >
                        <i
                          class="
                            icon
                            iconfont
                            icon-yly_qianzhuang
                            margin-right-14-px
                          "
                        />
                        <span class="flex-1">挽回损失</span>
                        <span id="retrieveLossAmount" class="right-nums">{{
                          provinceTotalList.retrieveLossAmount || "0"
                        }}</span>
                      </div>
                    </div>
                  </el-col>
                </el-row>
              </div>
            </div>
            <div
              v-if="changeTabsIndex === '1'"
              class="el-common-card-content relactive"
            >
              <el-scrollbar class="width height">
                <el-table :data="tableData" stripe style="width: 100%">
                  <el-table-column type="index" label="序号" width="60" fixed />
                  <el-table-column
                    prop="regionFullName"
                    label="涉及单位"
                    fixed
                    width="200"
                  />
                  <el-table-column
                    prop="problemNumber"
                    label="问题数量"
                    width="100"
                  />

                  <el-table-column
                    prop="seriousProblemNumber"
                    label="严重不良影响问题数量"
                    width="200"
                  />

                  <el-table-column
                    prop="lossAmount"
                    label="损失金额"
                    width="100"
                  >
                    <template slot-scope="scope">
                      <span>{{ scope.row.lossAmount | filterNum }}</span>
                    </template>
                  </el-table-column>

                  <el-table-column prop="lossRisk" label="损失风险" width="100">
                    <template slot-scope="scope">
                      <span>{{ scope.row.lossRisk | filterNum }}</span>
                    </template>
                  </el-table-column>

                  <el-table-column
                    prop="accountabilityPersonNumber"
                    label="追责人数"
                    width="100"
                  />

                  <el-table-column
                    prop="perfectSystemNumber"
                    label="完善制度"
                    width="100"
                  />

                  <el-table-column
                    prop="retrieveLossAmount"
                    label="挽回损失"
                    width="100"
                  >
                    <template slot-scope="scope">
                      <span>{{
                        scope.row.retrieveLossAmount | filterNum
                      }}</span>
                    </template>
                  </el-table-column>
                </el-table>
              </el-scrollbar>
            </div>
          </div>
        </el-col>

        <el-col :span="6" class="padding-left-4px height-470">
          <el-row class="width height-50-per">
            <el-col :span="24" class="width height" style="padding-bottom: 4px">
              <div class="el-common-card height">
                <div class="el-common-header">
                  <div class="el-common-card-header border0">
                    <div class="el-card-header-title">我的工作台</div>
                  </div>
                </div>
                <div class="el-content">
                  <el-row class="width height" style="padding: 15px 0px">
                    <el-col :span="12" class="one-after-line height relactive">
                      <div class="top-echars-con">
                        <div
                          class="el-row-one-num width-50 pointer"
                          @click="goWork('1')"
                        >
                          <div id="Count1" class="el-bottom-nums">
                            {{ userTaskCountData.todoNum || "0" }}
                          </div>
                          <div class="el-top-text">我的待办</div>
                        </div>

                        <div
                          class="el-row-one-num width-50 pointer"
                          @click="goWork('2')"
                        >
                          <div id="Count2" class="el-bottom-nums">
                            {{ userTaskCountData.hasdnoeNum || "0" }}
                          </div>
                          <div class="el-top-text">我的已办</div>
                        </div>
                      </div>
                      <div class="bottom-echars-con">
                        <el-progress
                          :percentage="userTaskCountData.todoProportion"
                          color="#fd8487"
                        />
                      </div>
                    </el-col>
                    <el-col :span="12" class="height">
                      <div class="top-echars-con">
                        <div
                          class="el-row-one-num width-50 pointer"
                          @click="goWork('3')"
                        >
                          <div id="Count3" class="el-bottom-nums">
                            {{ userTaskCountData.readNum || "0" }}
                          </div>
                          <div class="el-top-text">我的待阅</div>
                        </div>

                        <div
                          class="el-row-one-num width-50 pointer"
                          @click="goWork('4')"
                        >
                          <div id="Count4" class="el-bottom-nums">
                            {{ userTaskCountData.toreadNum || "0" }}
                          </div>
                          <div class="el-top-text">我的已阅</div>
                        </div>
                      </div>
                      <div class="bottom-echars-con">
                        <el-progress
                          :percentage="userTaskCountData.readProportion"
                          color="#8ca4fa"
                        />
                      </div>
                    </el-col>
                  </el-row>
                </div>
              </div>
            </el-col>
          </el-row>
          <el-row class="width height-50-per">
            <el-col :span="24" class="width height" style="padding-top: 4px">
              <div class="el-common-card height">
                <div class="el-common-header">
                  <div class="el-common-card-header border0">
                    <div class="el-card-header-title">超时提醒</div>
                  </div>
                </div>
                <div class="el-content flex align-items-center">
                  <div v-if="warnData" class="echars-right-con width-60">
                    <div class="titles">超时数量</div>
                    <div class="red-block">
                      今日+
                      <span id="todayWarnNumbers">{{
                        warnData.todayWarnNumbers || "0"
                      }}</span>
                    </div>
                    <div
                      id="warnNumbers"
                      class="black-num pointer"
                      @click="goTimeList"
                    >
                      {{ warnData.warnNumbers || "0" }}
                    </div>
                    <div class="bottom-list scroll-box">
                      <el-carousel
                        height="64px"
                        direction="vertical"
                        indicator-position="none"
                        :autoplay="true"
                        interval="5000"
                      >
                        <el-carousel-item
                          v-for="(item1, index1) in warnDataNewArr"
                          :key="index1"
                        >
                          <div
                            v-for="(item2, index2) in item1"
                            :key="index2"
                            class="msg-item ovflowHidden"
                          >
                            <a href="javascript:void(0)" class="item">{{
                              item2.problemTitle
                            }}</a>
                          </div>
                        </el-carousel-item>
                      </el-carousel>
                    </div>
                  </div>
                  <div class="width-40 height">
                    <echarsHuan3
                      v-if="warnData"
                      id="echars-huan-5"
                      :chars-data="warnData"
                    />
                  </div>
                </div>
              </div>
            </el-col>
          </el-row>
        </el-col>
      </el-row>

      <el-row style="padding-top: 4px">
        <el-col :span="6" class="padding-right-4px">
          <div class="el-common-card height-303">
            <div class="el-common-header flex align-items-center">
              <div class="el-common-card-header border0 flex-1">
                <div class="el-card-header-title">问题分布统计</div>
              </div>
              <el-select
                v-model="statistics"
                placeholder="请选择"
                class="widht-45-per"
                @change="statisticsChange"
              >
                <el-option label="按专业线统计" value="problemSpecSort" />
                <el-option label="按情形统计" value="problemRangeSort" />
                <el-option label="按状态统计" value="problemStatusSort" />
              </el-select>
            </div>
            <div class="el-content">
              <echarsHuan2
                v-if="statisticsData.statisticsList"
                id="echars-huan-3"
                :chars-data="statisticsData"
              />
            </div>
          </div>
        </el-col>

        <el-col :span="18" class="padding-left-4px">
          <div class="el-common-card height-303">
            <div class="el-common-header flex align-items-center">
              <div class="el-common-card-header border0 flex-1">
                <div class="el-card-header-title">年度趋势变化分析</div>
              </div>
            </div>
            <div class="el-content">
              <curve
                v-if="yearTrendData"
                id="curve"
                :chars-data="yearTrendData"
              />
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <el-dialog
      v-if="dialogVisible"
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="90%"
      :append-to-body="true"
      :before-close="handleClose"
      :close-on-click-modal="false"
      class="no-header"
    >
      <questionSearch
        v-if="sendHomeData.yearSelection && formData.reportType == '1'"
        :send-home-data-base="sendHomeData"
      />
      <realTimequestionSearch
        v-if="sendHomeData.yearSelection && formData.reportType == '2'"
        :send-home-data-base="sendHomeData"
      />
    </el-dialog>

    <el-dialog
      v-if="dialogVisible2"
      :title="dialogTitle"
      :visible.sync="dialogVisible2"
      width="90%"
      :append-to-body="true"
      :before-close="handleClose"
      :close-on-click-modal="false"
      class="no-header"
    >
      <timeList :warn-data-new-arr="warnDataNewArrCopy" />
    </el-dialog>
  </div>
</template>

<script>
import pubSub from 'pubsub-js'
import ehcarsHuan from '@/components/echars/echars-huan'
import echarsChina from '@/components/echars/echars-china'
import echarsHuan2 from '@/components/echars/echars-huan-2'
import echarsHuan3 from '@/components/echars/echars-huan-3'
import curve from '@/components/echars/curve'
import questionSearch from './components/questionSearch'
import realTimequestionSearch from './components/realTimequestionSearch'
import timeList from './components/timeList'
import { getFullYear } from '@/utils/index'
import {
  violationProblemOverviewData,
  violationLeaderHomePage,
  chinaMapStatisticsData,
  provinceTotalData,
  yearTrendChangeData,
  involveSubsidiaryStatisticsData,
  userTaskCount,
  selectWarnData,
  querySubsidiary
} from '@/api/home/<USER>'
export default {
  name: 'IndexHome',
  components: {
    ehcarsHuan,
    echarsChina,
    echarsHuan2,
    echarsHuan3,
    curve,
    questionSearch,
    realTimequestionSearch,
    timeList
  },

  data() {
    return {
      endyearDisatble: true,
      endyear: '',
      reportType: '1',
      radioDate: '2',
      payment: '',
      changeTabsIndex: '0',
      orgGrade: '',
      querySubsidiaryData: '',
      region: '',
      tableData: [],
      formData: {
        reportType: '1',
        yearSelection: '1',
        startYear: getFullYear(),
        endYear: getFullYear(),
        statisticsType: 'problemNumber'
      },

      statistics: 'problemSpecSort',
      problemOverviewData: '',
      statisticsData: {
        statisticsList: '',
        sum: 0
      },
      statisticsDataChina: '',
      provinceTotalList: '',
      yearTrendData: '',
      userTaskCountData: '',
      CountDone1: 0,
      CountDone2: 0,
      warnData: '',
      warnDataNewArr: [],
      warnDataNewArrCopy: [],
      dialogVisible: false,
      dialogVisible2: false,
      dialogTitle: '',
      sendHomeData: {},
      pickerStartAuditYear: {
        disabledDate: (time) => {
          if (this.formData.endYear) {
            return time.getFullYear() > this.formData.endYear
          }
        }
      },
      pickerEndAuditYear: {
        disabledDate: (time) => {
          return time.getFullYear() < this.formData.startYear
        }
      }
    }
  },
  watch: {
    'formData.yearSelection': {
      handler(val) {
        if (val == '1') {
          this.endyearDisatble = true
          this.formData.startYear = getFullYear()
          this.formData.endYear = getFullYear()
        }

        if (val == '2') {
          this.endyearDisatble = true
          this.formData.startYear = Number(getFullYear()) - 1
          this.formData.endYear = Number(getFullYear()) - 1
        }

        if (val == '3') {
          this.endyearDisatble = false
          this.formData.startYear = getFullYear()
          this.formData.endYear = getFullYear()
        }
        if (val != '3') {
          this.yearTrendChangeData()
          this.provinceTotalData()
          this.chinaMapStatisticsData()
          this.violationProblemOverviewData()

          if (this.statistics === 'problemSpecSort') {
            this.violationLeaderHomePage('problemSpecSort')
          }
          if (this.statistics === 'problemRangeSort') {
            this.violationLeaderHomePage('problemRangeSort')
          }
          if (this.statistics === 'problemStatusSort') {
            this.violationLeaderHomePage('problemStatusSort')
          }
          this.involveSubsidiaryStatisticsData()
        }
      },
      deep: true
    },
    'formData.reportType': {
      handler(val) {
        this.yearTrendChangeData()
        this.provinceTotalData()
        this.chinaMapStatisticsData()
        this.violationProblemOverviewData()
        if (this.statistics === 'problemSpecSort') {
          this.violationLeaderHomePage('problemSpecSort')
        }
        if (this.statistics === 'problemRangeSort') {
          this.violationLeaderHomePage('problemRangeSort')
        }
        if (this.statistics === 'problemStatusSort') {
          this.violationLeaderHomePage('problemStatusSort')
        }
        this.involveSubsidiaryStatisticsData()
      },
      deep: true
    }
  },
  created() {
    this.orgGrade = this.$store.getters.orgGrade
  },
  mounted() {
    this.querySubsidiary()
    this.violationProblemOverviewData()
    this.violationLeaderHomePage('problemSpecSort')
    this.provinceTotalData()
    this.yearTrendChangeData()
    this.userTaskCount()
    this.selectWarnData()

    if (this.orgGrade === 'G') {
      this.chinaMapStatisticsData()
    }
    // 订阅 子页面传来的数据
    pubSub.subscribe('echars_huan', (msg, data) => {
      this.goQuestionSearch(data.type, data.code)
    })
  },
  methods: {
    // 打开日常问题查询
    goQuestionSearch(type, code) {
      this.sendHomeData = {}
      if (this.formData.reportType == '1') {
        this.dialogTitle = '日常问题查询'
      }
      if (this.formData.reportType == '2') {
        this.dialogTitle = '实时问题查询'
      }
      this.sendHomeData.yearSelection = this.formData.yearSelection
      this.sendHomeData.startYear = this.formData.startYear
      this.sendHomeData.endYear = this.formData.endYear

      if (type == 'hc') {
        this.sendHomeData.checkStageFlag = '1'
      }
      if (type == 'zg') {
        this.sendHomeData.reformStageFlag = '1'
      }
      if (type == 'wttj') {
        if (this.statistics == 'problemSpecSort') {
          this.sendHomeData.specCode = code
        }
        if (this.statistics == 'problemRangeSort') {
          this.sendHomeData.aspectCode = code
        }

        if (this.statistics == 'problemStatusSort') {
          this.sendHomeData.status = code
        }
      }
      this.dialogVisible = true
    },
    // 我的工作台跳转
    goWork(type) {
      if (type == '1') {
        this.$router.push({ path: '/workflow/task-todo' })
      }
      if (type == '2') {
        this.$router.push({ path: '/workflow/task-hasdone' })
      }
      if (type == '3') {
        this.$router.push({ path: '/workflow/task-toread' })
      }
      if (type == '4') {
        this.$router.push({ path: '/workflow/task-read' })
      }
    },
    // 打开超时提醒数量列表
    goTimeList() {
      this.dialogTitle = '超时提醒'
      this.dialogVisible2 = true
    },
    // 关闭日常问题查询
    handleClose() {
      this.dialogVisible = false
      this.dialogVisible2 = false
    },
    querySubsidiary() {
      querySubsidiary().then((response) => {
        const { code, data } = response
        if (code == 200) this.querySubsidiaryData = data

        if (this.orgGrade !== 'G' && this.querySubsidiaryData) {
          this.changeTabsIndex = '1'
          this.involveSubsidiaryStatisticsData()
        }
        if (this.orgGrade !== 'G' && !this.querySubsidiaryData) {
          this.chinaMapStatisticsData()
        }
      })
    },
    startYearChange(val) {
      if (val) {
        this.yearTrendChangeData()
        this.provinceTotalData()
        this.chinaMapStatisticsData()
        this.violationProblemOverviewData()

        if (this.statistics === 'problemSpecSort') {
          this.violationLeaderHomePage('problemSpecSort')
        }
        if (this.statistics === 'problemRangeSort') {
          this.violationLeaderHomePage('problemRangeSort')
        }
        if (this.statistics === 'problemStatusSort') {
          this.violationLeaderHomePage('problemStatusSort')
        }
        this.involveSubsidiaryStatisticsData()
      }
    },

    endYearChange(val) {
      if (val) {
        this.yearTrendChangeData()
        this.provinceTotalData()
        this.chinaMapStatisticsData()
        this.violationProblemOverviewData()

        if (this.statistics === 'problemSpecSort') {
          this.violationLeaderHomePage('problemSpecSort')
        }
        if (this.statistics === 'problemRangeSort') {
          this.violationLeaderHomePage('problemRangeSort')
        }
        if (this.statistics === 'problemStatusSort') {
          this.violationLeaderHomePage('problemStatusSort')
        }
        this.involveSubsidiaryStatisticsData()
      }
    },

    changeTabs(changeTabsIndex) {
      this.changeTabsIndex = changeTabsIndex
      this.$forceUpdate()

      if (changeTabsIndex == '0') {
        this.chinaMapStatisticsData()
        this.provinceTotalData()
      }

      if (changeTabsIndex == '1') {
        this.involveSubsidiaryStatisticsData()
      }
    },

    statisticsTypeChange(val) {
      if (val) {
        this.yearTrendChangeData()
        this.provinceTotalData()
        this.chinaMapStatisticsData()
        this.violationProblemOverviewData()

        if (this.statistics === 'problemSpecSort') {
          this.violationLeaderHomePage('problemSpecSort')
        }
        if (this.statistics === 'problemRangeSort') {
          this.violationLeaderHomePage('problemRangeSort')
        }
        if (this.statistics === 'problemStatusSort') {
          this.violationLeaderHomePage('problemStatusSort')
        }
        this.involveSubsidiaryStatisticsData()
      }
    },

    statisticsChange(val) {
      if (val === 'problemSpecSort') {
        this.violationLeaderHomePage('problemSpecSort')
      }
      if (val === 'problemRangeSort') {
        this.violationLeaderHomePage('problemRangeSort')
      }
      if (val === 'problemStatusSort') {
        this.violationLeaderHomePage('problemStatusSort')
      }
    },
    violationProblemOverviewData() {
      violationProblemOverviewData(this.formData).then((response) => {
        const { code, data } = response
        if (code == 200) this.problemOverviewData = data
      })
    },

    violationLeaderHomePage(url) {
      this.statisticsData.statisticsList = ''
      violationLeaderHomePage(this.formData, url).then((response) => {
        const { code, data } = response
        if (code == '200') {
          var sum = 0
          for (var i = 0; i < data.length; i++) {
            sum += Number(data[i].value)
          }
          this.statisticsData.statisticsList = data
          this.statisticsData.sum = sum

          if (data[0].value == 0) {
            this.statisticsData.percent = 0
          } else {
            this.statisticsData.percent =
              (Number(data[0].value) / Number(sum)) * 100
          }
        }
      })
    },

    chinaMapStatisticsData() {
      var sendData = {
        yearSelection: this.formData.yearSelection,
        startYear: this.formData.startYear,
        endYear: this.formData.endYear,
        statisticsType: this.formData.statisticsType,
        reportType: this.formData.reportType
      }
      chinaMapStatisticsData(sendData).then((response) => {
        const { code, data } = response
        if (code == '200') {
          var bigValue = 0
          for (var i = 0; i < data.chinaMapItems.length; i++) {
            if (bigValue < data.chinaMapItems[i].statisticsValue) {
              bigValue = data.chinaMapItems[i].statisticsValue
            }
            if (data.mapType == '全国') {
              data.chinaMapItems[i].name =
                data.chinaMapItems[i].regionAbbreviationName
            } else {
              data.chinaMapItems[i].name =
                data.chinaMapItems[i].regionAbbreviationName + '市'
            }
            data.chinaMapItems[i].value =
              data.chinaMapItems[i].statisticsValue || '0'
          }

          data.chinaMapItems.push({
            name: '台湾',
            regionFullName: '台湾',
            value: '0'
          })

          data.chinaMapItems.push({
            name: '南海诸岛',
            regionFullName: '南海诸岛',
            value: '0'
          })
          this.statisticsDataChina = {
            data: data,
            bigValue: bigValue,
            mapType: data.mapType
          }
        }
      })
    },

    provinceTotalData() {
      provinceTotalData(this.formData).then((response) => {
        const { code, data } = response

        if (code == 200) this.provinceTotalList = data
      })
    },

    yearTrendChangeData() {
      yearTrendChangeData(this.formData).then((response) => {
        const { code, data } = response
        if (code == 200) {
          const problemYear = [2017, 2018, 2019, 2020]
          const problemNumber = [0, 0, 0, 0]
          const lossAmount = [0, 0, 0, 0]
          var flag = true
          for (var i = 0; i < data.length; i++) {
            if (data[i].problemYear == 2021) {
              flag = false
            }
          }
          if (flag) {
            problemYear.push(2021)
            problemNumber.push(0)
            lossAmount.push(0)
          }
          for (var i = 0; i < data.length; i++) {
            problemYear.push(data[i].problemYear)
            problemNumber.push(data[i].problemNumber)
            lossAmount.push(data[i].lossAmount)
          }
          this.yearTrendData = {
            problemYear: problemYear,
            problemNumber: problemNumber,
            lossAmount: lossAmount
          }
        }
      })
    },
    involveSubsidiaryStatisticsData() {
      var sendData = { ...this.formData, page: 1, limit: 999 }
      involveSubsidiaryStatisticsData(sendData).then((response) => {
        const { code, data } = response
        if (code == 200) this.tableData = data
      })
    },

    userTaskCount() {
      userTaskCount({}).then((response) => {
        const { code, data } = response
        if (code == 200) {
          this.userTaskCountData = data
        }
      })
    },
    selectWarnData() {
      selectWarnData({}).then((response) => {
        const { code, data } = response
        if (code == 200) {
          this.warnDataNewArrCopy = data.list
          this.warnDataNewArr = []
          for (var i = 0; i < data.list.length; i += 2) {
            this.warnDataNewArr.push(data.list.slice(i, i + 2))
          }

          this.warnData = data
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.top-container {
  display: flex;
  align-items: center;
  padding-bottom: 4px;
  .left-checbox {
    flex: 1;
  }
  .right-form {
    display: flex;
    align-items: center;
    .tagmodel-search-title {
      float: left;
      color: hsl(202, 7%, 68%);
      border-right: 1px solid #a9b0b4;
      padding-right: 6px;
      margin-right: 14px;
      white-space: nowrap;
    }
    .tagmodel-search-condition {
      display: inline-block;
      margin-right: 0px;
      font-size: 14px;
      color: rgb(34, 34, 34);
      position: relative;
      cursor: pointer;
      float: left;
      white-space: nowrap;
    }
    .right-radio {
      display: flex;
      align-items: center;
      position: relative;
      top: 2px;
    }
  }
}
.mid-container {
  .height-470 {
    height: 470px;
  }
  .height-303 {
    height: 303px;
  }
  .margin-top-8-px {
    margin-top: 8px;
  }
  .margin-bottom-10-px {
    margin-bottom: 10px;
  }
  .height-95-px {
    height: 95px;
  }
  .height-130-px {
    height: 150px;
  }
  .margin-bottom-18-px {
    margin-bottom: 18px;
  }
  .padding-right-15px {
    padding-right: 15px;
  }
  .padding-right-4px {
    padding-right: 4px;
  }
  .padding-left-4px {
    padding-left: 4px;
  }
  .height_36_px {
    height: calc(100% - 36px);
  }
  .widht-45-per {
    width: 45%;
  }
  .height-50-per {
    height: 50%;
  }
  .width-60 {
    width: 60%;
  }
  .width-40 {
    width: 40%;
  }
}
::v-deep .el-progress {
  display: flex;
  align-items: center;
}
::v-deep .el-progress__text {
  margin-right: 15px;
}
::v-deep .el-progress-bar {
  width: 90% !important;
}

::v-deep .el-dialog__headerbtn {
  z-index: 999;
}
::v-deep .el-dialog__body {
  padding: 10px 20px;
}
@media screen and (max-width: 1400px) {
  .el-content {
    padding: 0px 10px;
  }
  .el-bottom-nums {
    font-size: 16px;
  }
  .el-top-text {
    font-size: 12px;
  }
  .el-one_graphic_alphanumeric {
    padding: 0px 3px;
  }
  .el-graphic_text {
    font-size: 12px;
    margin-right: 6px;
  }
  .el-graphic_num {
    font-size: 18px;
  }
  .flex-dir-col-jus-cen-style {
    padding-left: 5px !important;
    padding-right: 0px !important;
  }
  .el-radio {
    margin-right: 8px;
  }
  .one-echars-right-block .flex-1 {
    font-size: 12px;
  }
  .echars-right-block
    .echars-right-block_con
    .one-echars-right-block
    span:last-child {
    font-size: 15px;
  }
  .echars-right-block .echars-right-block_con {
    padding: 30px 10px;
  }
}
::v-deep .mid-container .el-scrollbar__wrap {
  overflow: hidden;
}
// 设置滚动条的背景色和圆角
::v-deep .el-table__body-wrapper::-webkit-scrollbar-thumb {
  background-color: #eee;
  border-radius: 8px;
}

::v-deep .el-table__empty-block {
  height: 320px !important;
  border: none;
}
</style>
