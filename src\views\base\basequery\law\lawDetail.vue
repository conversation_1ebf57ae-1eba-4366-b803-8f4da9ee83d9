<template>
    <el-dialog
        title="企业规章制度详情"
        :visible.sync="dialogVisible"
        width="90%"
        append-to-body
        :before-close="handleClose">

        <div>
          <el-form class="common-card padding10_0" size="medium"  label-width="108px">
            <el-row>
              <el-col :span="24">
                <el-form-item label="标题"><span>{{dataDetails.title}}</span></el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="文号"><span>{{dataDetails.issueCode}}</span></el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="印发日期"><span>{{dateFormat(dataDetails.publishDate)}}</span></el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="施行日期"><span>{{dateFormat(dataDetails.implementDate)}}</span></el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="制度类型"><span>{{dataDetails.classifyText}}</span></el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="类别"><span>{{dataDetails.categoryText}}</span></el-form-item>
              </el-col>
            </el-row>
          </el-form>
          <BlockCard
            title="附件列表"
          >
            <el-table
              border
              :data="dataDetails.files"
              ref="table2"
              :show-header="false"
              :cell-class-name="rowClass"
            >
              <el-table-column
                fixed
                align="center"
                label="序号"
                type="index"
                min-width="10%">
              </el-table-column>
              <el-table-column label="文档名称" prop="fileName" min-width="60%"/>
              <el-table-column label="上传人" prop="createLoginName" min-width="15%"/>
              <el-table-column label="上传时间" prop="time" :formatter="dateFormat1" min-width="13%"/>
              <el-table-column label="操作" prop="del" min-width="15%" fixed="right"
                               align="center"
              >
                <template slot-scope="scope">
                  <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-bottom"
                    title="下载"
                    @click="downloadFile(scope.row)"
                  >
                  </el-button>
                  <!-- <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-search"
                  >预览
                  </el-button> -->
                </template>
              </el-table-column>
            </el-table>
          </BlockCard>
        </div>
    </el-dialog>
</template>

<script lang="ts">
  import {getBaseLaw} from "@/api/base/law";
  import moment from "moment";
  import BlockCard from '@/components/BlockCard';

  export default {
    name: "lawDetail",
    components:{BlockCard},
    props: {
      dialogVisible: {
        type: Boolean,
        default: true
      },
      id: {
        type: String,
        default: ''
      },
    },
    data() {
      return {
        dataDetails: {},
      };
    },
    created() {
      this.lawInfo();
    },
    methods: {
      /**查询企业基本信息详情*/
      lawInfo() {
       //this.loading = true;
        getBaseLaw({id: this.id}).then(
          response => {
            this.dataDetails = response.data;
            //this.loading = false;
          }
        );
      },
      /** 修改附件表样式 */
      rowClass ({ row, column, rowIndex, columnIndex }) {
        if (columnIndex === 1 || columnIndex === 2) {
          return 'no-right-border'
        }else if(columnIndex === 0){
          return 'cell-color'
        }
      },
      /**关闭模态框*/
      handleClose() {
        this.$emit("closeModal");
      },
      /*日期处理*/
      dateFormat:function(date){
        if(date === undefined){
          return ''
        }
        return moment(date).format("YYYY-MM-DD")
      },
      dateFormat1:function(data){
        if(data.time === undefined){
          return ''
        }
        return moment(data.time).format("YYYY-MM-DD")
      },
      /** 下载附件 */
      downloadFile(row) {
        this.download('/sys/documentTemplate/downloadByAttachmentId', { id: row.id }, row.fileName)
      },
    }
  };
</script>
