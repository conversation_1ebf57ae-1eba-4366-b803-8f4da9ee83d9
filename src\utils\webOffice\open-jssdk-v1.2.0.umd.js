!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports.OpenSDK=t():e.OpenSDK=t()}(self,(function(){return function(){var e={669:function(e,t,n){n(609)},448:function(e,t,n){"use strict";var r=n(867),o=n(26),i=n(372),a=n(327),s=n(97),c=n(109),u=n(985),l=n(61);e.exports=function(e){return new Promise((function(t,n){var f=e.data,d=e.headers,p=e.responseType;r.isFormData(f)&&delete d["Content-Type"];var h=new XMLHttpRequest;if(e.auth){var m=e.auth.username||"",v=e.auth.password?unescape(encodeURIComponent(e.auth.password)):"";d.Authorization="Basic "+btoa(m+":"+v)}var b=s(e.baseURL,e.url);function g(){if(h){var r="getAllResponseHeaders"in h?c(h.getAllResponseHeaders()):null,i={data:p&&"text"!==p&&"json"!==p?h.response:h.responseText,status:h.status,statusText:h.statusText,headers:r,config:e,request:h};o(t,n,i),h=null}}if(h.open(e.method.toUpperCase(),a(b,e.params,e.paramsSerializer),!0),h.timeout=e.timeout,"onloadend"in h?h.onloadend=g:h.onreadystatechange=function(){h&&4===h.readyState&&(0!==h.status||h.responseURL&&0===h.responseURL.indexOf("file:"))&&setTimeout(g)},h.onabort=function(){h&&(n(l("Request aborted",e,"ECONNABORTED",h)),h=null)},h.onerror=function(){n(l("Network Error",e,null,h)),h=null},h.ontimeout=function(){var t="timeout of "+e.timeout+"ms exceeded";e.timeoutErrorMessage&&(t=e.timeoutErrorMessage),n(l(t,e,e.transitional&&e.transitional.clarifyTimeoutError?"ETIMEDOUT":"ECONNABORTED",h)),h=null},r.isStandardBrowserEnv()){var w=(e.withCredentials||u(b))&&e.xsrfCookieName?i.read(e.xsrfCookieName):void 0;w&&(d[e.xsrfHeaderName]=w)}"setRequestHeader"in h&&r.forEach(d,(function(e,t){void 0===f&&"content-type"===t.toLowerCase()?delete d[t]:h.setRequestHeader(t,e)})),r.isUndefined(e.withCredentials)||(h.withCredentials=!!e.withCredentials),p&&"json"!==p&&(h.responseType=e.responseType),"function"==typeof e.onDownloadProgress&&h.addEventListener("progress",e.onDownloadProgress),"function"==typeof e.onUploadProgress&&h.upload&&h.upload.addEventListener("progress",e.onUploadProgress),e.cancelToken&&e.cancelToken.promise.then((function(e){h&&(h.abort(),n(e),h=null)})),f||(f=null),h.send(f)}))}},609:function(e,t,n){"use strict";var r=n(867),o=n(849),i=n(321),a=n(185);function s(e){var t=new i(e),n=o(i.prototype.request,t);return r.extend(n,i.prototype,t),r.extend(n,t),n}var c=s(n(655));c.Axios=i,c.create=function(e){return s(a(c.defaults,e))},c.Cancel=n(263),c.CancelToken=n(972),c.isCancel=n(502),c.all=function(e){return Promise.all(e)},c.spread=n(713),c.isAxiosError=n(268),e.exports=c,e.exports.default=c},263:function(e){"use strict";function t(e){this.message=e}t.prototype.toString=function(){return"Cancel"+(this.message?": "+this.message:"")},t.prototype.__CANCEL__=!0,e.exports=t},972:function(e,t,n){"use strict";var r=n(263);function o(e){if("function"!=typeof e)throw new TypeError("executor must be a function.");var t;this.promise=new Promise((function(e){t=e}));var n=this;e((function(e){n.reason||(n.reason=new r(e),t(n.reason))}))}o.prototype.throwIfRequested=function(){if(this.reason)throw this.reason},o.source=function(){var e;return{token:new o((function(t){e=t})),cancel:e}},e.exports=o},502:function(e){"use strict";e.exports=function(e){return!(!e||!e.__CANCEL__)}},321:function(e,t,n){"use strict";var r=n(867),o=n(327),i=n(782),a=n(572),s=n(185),c=n(875),u=c.validators;function l(e){this.defaults=e,this.interceptors={request:new i,response:new i}}l.prototype.request=function(e){"string"==typeof e?(e=arguments[1]||{}).url=arguments[0]:e=e||{},(e=s(this.defaults,e)).method?e.method=e.method.toLowerCase():this.defaults.method?e.method=this.defaults.method.toLowerCase():e.method="get";var t=e.transitional;void 0!==t&&c.assertOptions(t,{silentJSONParsing:u.transitional(u.boolean,"1.0.0"),forcedJSONParsing:u.transitional(u.boolean,"1.0.0"),clarifyTimeoutError:u.transitional(u.boolean,"1.0.0")},!1);var n=[],r=!0;this.interceptors.request.forEach((function(t){"function"==typeof t.runWhen&&!1===t.runWhen(e)||(r=r&&t.synchronous,n.unshift(t.fulfilled,t.rejected))}));var o,i=[];if(this.interceptors.response.forEach((function(e){i.push(e.fulfilled,e.rejected)})),!r){var l=[a,void 0];for(Array.prototype.unshift.apply(l,n),l=l.concat(i),o=Promise.resolve(e);l.length;)o=o.then(l.shift(),l.shift());return o}for(var f=e;n.length;){var d=n.shift(),p=n.shift();try{f=d(f)}catch(e){p(e);break}}try{o=a(f)}catch(e){return Promise.reject(e)}for(;i.length;)o=o.then(i.shift(),i.shift());return o},l.prototype.getUri=function(e){return e=s(this.defaults,e),o(e.url,e.params,e.paramsSerializer).replace(/^\?/,"")},r.forEach(["delete","get","head","options"],(function(e){l.prototype[e]=function(t,n){return this.request(s(n||{},{method:e,url:t,data:(n||{}).data}))}})),r.forEach(["post","put","patch"],(function(e){l.prototype[e]=function(t,n,r){return this.request(s(r||{},{method:e,url:t,data:n}))}})),e.exports=l},782:function(e,t,n){"use strict";var r=n(867);function o(){this.handlers=[]}o.prototype.use=function(e,t,n){return this.handlers.push({fulfilled:e,rejected:t,synchronous:!!n&&n.synchronous,runWhen:n?n.runWhen:null}),this.handlers.length-1},o.prototype.eject=function(e){this.handlers[e]&&(this.handlers[e]=null)},o.prototype.forEach=function(e){r.forEach(this.handlers,(function(t){null!==t&&e(t)}))},e.exports=o},97:function(e,t,n){"use strict";var r=n(793),o=n(303);e.exports=function(e,t){return e&&!r(t)?o(e,t):t}},61:function(e,t,n){"use strict";var r=n(481);e.exports=function(e,t,n,o,i){var a=new Error(e);return r(a,t,n,o,i)}},572:function(e,t,n){"use strict";var r=n(867),o=n(527),i=n(502),a=n(655);function s(e){e.cancelToken&&e.cancelToken.throwIfRequested()}e.exports=function(e){return s(e),e.headers=e.headers||{},e.data=o.call(e,e.data,e.headers,e.transformRequest),e.headers=r.merge(e.headers.common||{},e.headers[e.method]||{},e.headers),r.forEach(["delete","get","head","post","put","patch","common"],(function(t){delete e.headers[t]})),(e.adapter||a.adapter)(e).then((function(t){return s(e),t.data=o.call(e,t.data,t.headers,e.transformResponse),t}),(function(t){return i(t)||(s(e),t&&t.response&&(t.response.data=o.call(e,t.response.data,t.response.headers,e.transformResponse))),Promise.reject(t)}))}},481:function(e){"use strict";e.exports=function(e,t,n,r,o){return e.config=t,n&&(e.code=n),e.request=r,e.response=o,e.isAxiosError=!0,e.toJSON=function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:this.config,code:this.code}},e}},185:function(e,t,n){"use strict";var r=n(867);e.exports=function(e,t){t=t||{};var n={},o=["url","method","data"],i=["headers","auth","proxy","params"],a=["baseURL","transformRequest","transformResponse","paramsSerializer","timeout","timeoutMessage","withCredentials","adapter","responseType","xsrfCookieName","xsrfHeaderName","onUploadProgress","onDownloadProgress","decompress","maxContentLength","maxBodyLength","maxRedirects","transport","httpAgent","httpsAgent","cancelToken","socketPath","responseEncoding"],s=["validateStatus"];function c(e,t){return r.isPlainObject(e)&&r.isPlainObject(t)?r.merge(e,t):r.isPlainObject(t)?r.merge({},t):r.isArray(t)?t.slice():t}function u(o){r.isUndefined(t[o])?r.isUndefined(e[o])||(n[o]=c(void 0,e[o])):n[o]=c(e[o],t[o])}r.forEach(o,(function(e){r.isUndefined(t[e])||(n[e]=c(void 0,t[e]))})),r.forEach(i,u),r.forEach(a,(function(o){r.isUndefined(t[o])?r.isUndefined(e[o])||(n[o]=c(void 0,e[o])):n[o]=c(void 0,t[o])})),r.forEach(s,(function(r){r in t?n[r]=c(e[r],t[r]):r in e&&(n[r]=c(void 0,e[r]))}));var l=o.concat(i).concat(a).concat(s),f=Object.keys(e).concat(Object.keys(t)).filter((function(e){return-1===l.indexOf(e)}));return r.forEach(f,u),n}},26:function(e,t,n){"use strict";var r=n(61);e.exports=function(e,t,n){var o=n.config.validateStatus;n.status&&o&&!o(n.status)?t(r("Request failed with status code "+n.status,n.config,null,n.request,n)):e(n)}},527:function(e,t,n){"use strict";var r=n(867),o=n(655);e.exports=function(e,t,n){var i=this||o;return r.forEach(n,(function(n){e=n.call(i,e,t)})),e}},655:function(e,t,n){"use strict";var r=n(867),o=n(16),i=n(481),a={"Content-Type":"application/x-www-form-urlencoded"};function s(e,t){!r.isUndefined(e)&&r.isUndefined(e["Content-Type"])&&(e["Content-Type"]=t)}var c,u={transitional:{silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},adapter:(("undefined"!=typeof XMLHttpRequest||"undefined"!=typeof process&&"[object process]"===Object.prototype.toString.call(process))&&(c=n(448)),c),transformRequest:[function(e,t){return o(t,"Accept"),o(t,"Content-Type"),r.isFormData(e)||r.isArrayBuffer(e)||r.isBuffer(e)||r.isStream(e)||r.isFile(e)||r.isBlob(e)?e:r.isArrayBufferView(e)?e.buffer:r.isURLSearchParams(e)?(s(t,"application/x-www-form-urlencoded;charset=utf-8"),e.toString()):r.isObject(e)||t&&"application/json"===t["Content-Type"]?(s(t,"application/json"),function(e,t,n){if(r.isString(e))try{return(0,JSON.parse)(e),r.trim(e)}catch(e){if("SyntaxError"!==e.name)throw e}return(0,JSON.stringify)(e)}(e)):e}],transformResponse:[function(e){var t=this.transitional,n=t&&t.silentJSONParsing,o=t&&t.forcedJSONParsing,a=!n&&"json"===this.responseType;if(a||o&&r.isString(e)&&e.length)try{return JSON.parse(e)}catch(e){if(a){if("SyntaxError"===e.name)throw i(e,this,"E_JSON_PARSE");throw e}}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,validateStatus:function(e){return e>=200&&e<300},headers:{common:{Accept:"application/json, text/plain, */*"}}};r.forEach(["delete","get","head"],(function(e){u.headers[e]={}})),r.forEach(["post","put","patch"],(function(e){u.headers[e]=r.merge(a)})),e.exports=u},849:function(e){"use strict";e.exports=function(e,t){return function(){for(var n=new Array(arguments.length),r=0;r<n.length;r++)n[r]=arguments[r];return e.apply(t,n)}}},327:function(e,t,n){"use strict";var r=n(867);function o(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}e.exports=function(e,t,n){if(!t)return e;var i;if(n)i=n(t);else if(r.isURLSearchParams(t))i=t.toString();else{var a=[];r.forEach(t,(function(e,t){null!=e&&(r.isArray(e)?t+="[]":e=[e],r.forEach(e,(function(e){r.isDate(e)?e=e.toISOString():r.isObject(e)&&(e=JSON.stringify(e)),a.push(o(t)+"="+o(e))})))})),i=a.join("&")}if(i){var s=e.indexOf("#");-1!==s&&(e=e.slice(0,s)),e+=(-1===e.indexOf("?")?"?":"&")+i}return e}},303:function(e){"use strict";e.exports=function(e,t){return t?e.replace(/\/+$/,"")+"/"+t.replace(/^\/+/,""):e}},372:function(e,t,n){"use strict";var r=n(867);e.exports=r.isStandardBrowserEnv()?{write:function(e,t,n,o,i,a){var s=[];s.push(e+"="+encodeURIComponent(t)),r.isNumber(n)&&s.push("expires="+new Date(n).toGMTString()),r.isString(o)&&s.push("path="+o),r.isString(i)&&s.push("domain="+i),!0===a&&s.push("secure"),document.cookie=s.join("; ")},read:function(e){var t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove:function(e){this.write(e,"",Date.now()-864e5)}}:{write:function(){},read:function(){return null},remove:function(){}}},793:function(e){"use strict";e.exports=function(e){return/^([a-z][a-z\d\+\-\.]*:)?\/\//i.test(e)}},268:function(e){"use strict";e.exports=function(e){return"object"==typeof e&&!0===e.isAxiosError}},985:function(e,t,n){"use strict";var r=n(867);e.exports=r.isStandardBrowserEnv()?function(){var e,t=/(msie|trident)/i.test(navigator.userAgent),n=document.createElement("a");function o(e){var r=e;return t&&(n.setAttribute("href",r),r=n.href),n.setAttribute("href",r),{href:n.href,protocol:n.protocol?n.protocol.replace(/:$/,""):"",host:n.host,search:n.search?n.search.replace(/^\?/,""):"",hash:n.hash?n.hash.replace(/^#/,""):"",hostname:n.hostname,port:n.port,pathname:"/"===n.pathname.charAt(0)?n.pathname:"/"+n.pathname}}return e=o(window.location.href),function(t){var n=r.isString(t)?o(t):t;return n.protocol===e.protocol&&n.host===e.host}}():function(){return!0}},16:function(e,t,n){"use strict";var r=n(867);e.exports=function(e,t){r.forEach(e,(function(n,r){r!==t&&r.toUpperCase()===t.toUpperCase()&&(e[t]=n,delete e[r])}))}},109:function(e,t,n){"use strict";var r=n(867),o=["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"];e.exports=function(e){var t,n,i,a={};return e?(r.forEach(e.split("\n"),(function(e){if(i=e.indexOf(":"),t=r.trim(e.substr(0,i)).toLowerCase(),n=r.trim(e.substr(i+1)),t){if(a[t]&&o.indexOf(t)>=0)return;a[t]="set-cookie"===t?(a[t]?a[t]:[]).concat([n]):a[t]?a[t]+", "+n:n}})),a):a}},713:function(e){"use strict";e.exports=function(e){return function(t){return e.apply(null,t)}}},875:function(e,t,n){"use strict";var r=n(696),o={};["object","boolean","number","function","string","symbol"].forEach((function(e,t){o[e]=function(n){return typeof n===e||"a"+(t<1?"n ":" ")+e}}));var i={},a=r.version.split(".");function s(e,t){for(var n=t?t.split("."):a,r=e.split("."),o=0;o<3;o++){if(n[o]>r[o])return!0;if(n[o]<r[o])return!1}return!1}o.transitional=function(e,t,n){var o=t&&s(t);function a(e,t){return"[Axios v"+r.version+"] Transitional option '"+e+"'"+t+(n?". "+n:"")}return function(n,r,s){if(!1===e)throw new Error(a(r," has been removed in "+t));return o&&!i[r]&&(i[r]=!0,console.warn(a(r," has been deprecated since v"+t+" and will be removed in the near future"))),!e||e(n,r,s)}},e.exports={isOlderVersion:s,assertOptions:function(e,t,n){if("object"!=typeof e)throw new TypeError("options must be an object");for(var r=Object.keys(e),o=r.length;o-- >0;){var i=r[o],a=t[i];if(a){var s=e[i],c=void 0===s||a(s,i,e);if(!0!==c)throw new TypeError("option "+i+" must be "+c)}else if(!0!==n)throw Error("Unknown option "+i)}},validators:o}},867:function(e,t,n){"use strict";var r=n(849),o=Object.prototype.toString;function i(e){return"[object Array]"===o.call(e)}function a(e){return void 0===e}function s(e){return null!==e&&"object"==typeof e}function c(e){if("[object Object]"!==o.call(e))return!1;var t=Object.getPrototypeOf(e);return null===t||t===Object.prototype}function u(e){return"[object Function]"===o.call(e)}function l(e,t){if(null!=e)if("object"!=typeof e&&(e=[e]),i(e))for(var n=0,r=e.length;n<r;n++)t.call(null,e[n],n,e);else for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.call(null,e[o],o,e)}e.exports={isArray:i,isArrayBuffer:function(e){return"[object ArrayBuffer]"===o.call(e)},isBuffer:function(e){return null!==e&&!a(e)&&null!==e.constructor&&!a(e.constructor)&&"function"==typeof e.constructor.isBuffer&&e.constructor.isBuffer(e)},isFormData:function(e){return"undefined"!=typeof FormData&&e instanceof FormData},isArrayBufferView:function(e){return"undefined"!=typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer&&e.buffer instanceof ArrayBuffer},isString:function(e){return"string"==typeof e},isNumber:function(e){return"number"==typeof e},isObject:s,isPlainObject:c,isUndefined:a,isDate:function(e){return"[object Date]"===o.call(e)},isFile:function(e){return"[object File]"===o.call(e)},isBlob:function(e){return"[object Blob]"===o.call(e)},isFunction:u,isStream:function(e){return s(e)&&u(e.pipe)},isURLSearchParams:function(e){return"undefined"!=typeof URLSearchParams&&e instanceof URLSearchParams},isStandardBrowserEnv:function(){return("undefined"==typeof navigator||"ReactNative"!==navigator.product&&"NativeScript"!==navigator.product&&"NS"!==navigator.product)&&"undefined"!=typeof window&&"undefined"!=typeof document},forEach:l,merge:function e(){var t={};function n(n,r){c(t[r])&&c(n)?t[r]=e(t[r],n):c(n)?t[r]=e({},n):i(n)?t[r]=n.slice():t[r]=n}for(var r=0,o=arguments.length;r<o;r++)l(arguments[r],n);return t},extend:function(e,t,n){return l(t,(function(t,o){e[o]=n&&"function"==typeof t?r(t,n):t})),e},trim:function(e){return e.trim?e.trim():e.replace(/^\s+|\s+$/g,"")},stripBOM:function(e){return 65279===e.charCodeAt(0)&&(e=e.slice(1)),e}}},696:function(e){"use strict";e.exports=JSON.parse('{"name":"axios","version":"0.21.4","description":"Promise based HTTP client for the browser and node.js","main":"index.js","scripts":{"test":"grunt test","start":"node ./sandbox/server.js","build":"NODE_ENV=production grunt build","preversion":"npm test","version":"npm run build && grunt version && git add -A dist && git add CHANGELOG.md bower.json package.json","postversion":"git push && git push --tags","examples":"node ./examples/server.js","coveralls":"cat coverage/lcov.info | ./node_modules/coveralls/bin/coveralls.js","fix":"eslint --fix lib/**/*.js"},"repository":{"type":"git","url":"https://github.com/axios/axios.git"},"keywords":["xhr","http","ajax","promise","node"],"author":"Matt Zabriskie","license":"MIT","bugs":{"url":"https://github.com/axios/axios/issues"},"homepage":"https://axios-http.com","devDependencies":{"coveralls":"^3.0.0","es6-promise":"^4.2.4","grunt":"^1.3.0","grunt-banner":"^0.6.0","grunt-cli":"^1.2.0","grunt-contrib-clean":"^1.1.0","grunt-contrib-watch":"^1.0.0","grunt-eslint":"^23.0.0","grunt-karma":"^4.0.0","grunt-mocha-test":"^0.13.3","grunt-ts":"^6.0.0-beta.19","grunt-webpack":"^4.0.2","istanbul-instrumenter-loader":"^1.0.0","jasmine-core":"^2.4.1","karma":"^6.3.2","karma-chrome-launcher":"^3.1.0","karma-firefox-launcher":"^2.1.0","karma-jasmine":"^1.1.1","karma-jasmine-ajax":"^0.1.13","karma-safari-launcher":"^1.0.0","karma-sauce-launcher":"^4.3.6","karma-sinon":"^1.0.5","karma-sourcemap-loader":"^0.3.8","karma-webpack":"^4.0.2","load-grunt-tasks":"^3.5.2","minimist":"^1.2.0","mocha":"^8.2.1","sinon":"^4.5.0","terser-webpack-plugin":"^4.2.3","typescript":"^4.0.5","url-search-params":"^0.10.0","webpack":"^4.44.2","webpack-dev-server":"^3.11.0"},"browser":{"./lib/adapters/http.js":"./lib/adapters/xhr.js"},"jsdelivr":"dist/axios.min.js","unpkg":"dist/axios.min.js","typings":"./index.d.ts","dependencies":{"follow-redirects":"^1.14.0"},"bundlesize":[{"path":"./dist/axios.min.js","threshold":"5kB"}]}')}},t={};function n(r){var o=t[r];if(void 0!==o)return o.exports;var i=t[r]={exports:{}};return e[r](i,i.exports,n),i.exports}n.d=function(e,t){for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var r={};return function(){"use strict";function e(t){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e(t)}function t(e){return t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},t(e)}n.r(r),n.d(r,{config:function(){return Ce}}),n(669),function(){var e={},t=window.navigator.userAgent,n=/AppleWebKit.(\d+\.\d+)/i.exec(t);if((n=/Chrome.(\d+\.\d+)/i.exec(t))||window.chrome?e.chrome=n?parseFloat(n[1],10):"2.0":((n=/Version.(\d+\.\d+)/i.exec(t))||window.safariHandler)&&(e.safari=n?parseFloat(n[1],10):"3.3"),window.ActiveXObject||window.msIsStaticHTML){if(e.ie=6,(window.XMLHttpRequest||t.indexOf("MSIE 7.0")>-1)&&(e.ie=7),(window.XDomainRequest||t.indexOf("Trident/4.0")>-1)&&(e.ie=8),t.indexOf("Trident/5.0")>-1&&(e.ie=9),t.indexOf("Trident/6.0")>-1&&(e.ie=10),t.indexOf("Trident/7.0")>-1&&(e.ie=11),(t.indexOf("Trident/5.0")>-1||t.indexOf("MSIE 9.0")>-1)&&(e.ie9=!0),e.isBeta=navigator.appMinorVersion&&navigator.appMinorVersion.toLowerCase().indexOf("beta")>-1,e.ie<7)try{document.execCommand("BackgroundImageCache",!1,!0)}catch(e){}}else if(document.getBoxObjectFor||void 0!==window.mozInnerScreenX){var r=/(?:Firefox|GranParadiso|Iceweasel|Minefield).(\d+\.\d+)/i;e.firefox=parseFloat((r.exec(t)||r.exec("Firefox/3.3"))[1],10)}else if(navigator.taintEnabled)window.opera&&(e.opera=parseFloat(window.opera.version(),10));else{var o=/AppleWebKit.(\d+\.\d+)/i.exec(t);e.webkit=o?parseFloat(o[1],10):document.evaluate?document.querySelector?525:420:419}e.isMiui=t.indexOf("MiuiBrowser")>-1,e.isQQ=t.indexOf("QQTheme")>-1,e.isQQBrowser=t.indexOf("MQQBrowser")>-1,e.isIE=!!window.ActiveXObject||"ActiveXObject"in window,e.weixin=t.indexOf("MicroMessenger")>-1,e.isiPod=t.indexOf("iPod")>-1,e.isiPad=t.indexOf("iPad")>-1,e.isiPhone=t.indexOf("iPhone")>-1,e.android=t.indexOf("Android")>-1,e.air=t.indexOf("AdobeAIR")>-1?1:0,e.isMobile=e.weixin||e.isiPod||e.isiPad||e.isiPhone||e.android,e.isIphoneMobile=e.air||e.isiPod||e.isiPad||e.isiPhone}();var o=function(){return(o=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function i(e,t,n,r){return new(n||(n=Promise))((function(o,i){function a(e){try{c(r.next(e))}catch(e){i(e)}}function s(e){try{c(r.throw(e))}catch(e){i(e)}}function c(e){var t;e.done?o(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(a,s)}c((r=r.apply(e,t||[])).next())}))}function a(e,t){var n,r,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(i){return function(s){return function(i){if(n)throw new TypeError("Generator is already executing.");for(;a;)try{if(n=1,r&&(o=2&i[0]?r.return:i[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,i[1])).done)return o;switch(r=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return a.label++,{value:i[1],done:!1};case 5:a.label++,r=i[1],i=[0];continue;case 7:i=a.ops.pop(),a.trys.pop();continue;default:if(!(o=(o=a.trys).length>0&&o[o.length-1])&&(6===i[0]||2===i[0])){a=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){a.label=i[1];break}if(6===i[0]&&a.label<o[1]){a.label=o[1],o=i;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(i);break}o[2]&&a.ops.pop(),a.trys.pop();continue}i=t.call(e,a)}catch(e){i=[6,e],r=0}finally{n=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,s])}}}var s=function(){function e(){}return e.add=function(t){e.HANDLE_LIST.push(t),window.addEventListener("message",t,!1)},e.remove=function(t){var n=e.HANDLE_LIST.indexOf(t);n>=0&&e.HANDLE_LIST.splice(n,1),window.removeEventListener("message",t,!1)},e.empty=function(){for(;e.HANDLE_LIST.length;)window.removeEventListener("message",e.HANDLE_LIST.shift(),!1)},e.parse=function(e){try{return"object"==t(e)?e:e?JSON.parse(e):e}catch(t){return console.log("Message.parse Error:",t),e}},e.HANDLE_LIST=[],e}();function c(e){return"[object Function]"==={}.toString.call(e)}var u,l,f,d,p,h={origin:""};function m(e,t){h[e]=t}function v(e){return h[e]}function b(e){var t=v("origin");return!!function(e,t){return e!==t&&(e.replace(/www\./i,"").toLowerCase()!==t.replace(/www\./i,"").toLowerCase()||(e.match("www.")?void 0:(m("origin",t),!1)))}(t,e.origin)&&(console.warn("postMessage 域名检查不通过",{safeOrigin:t,eventOrigin:e.origin}),!0)}(p=u||(u={})).unknown="unknown",p.spreadsheet="s",p.writer="w",p.presentation="p",p.pdf="f",function(e){e.wps="w",e.et="s",e.presentation="p",e.pdf="f"}(l||(l={})),function(e){e.nomal="nomal",e.simple="simple"}(f||(f={})),function(e){e[e.requestFullscreen=1]="requestFullscreen",e[e.exitFullscreen=0]="exitFullscreen"}(d||(d={}));var g,w,y,x=(g=0,function(){return g+=1}),k=function(e,t,n){void 0===n&&(n=!0);var r=t;if(!w){var o=function e(t){var n=t.clientHeight,r=t.clientWidth;0!==n||0!==r||y?0===n&&0===r||!y||(y.disconnect(),y=null):window.ResizeObserver&&(y=new ResizeObserver((function(n){e(t)}))).observe(t),w.style.cssText+="height: "+n+"px; width: "+r+"px"}.bind(null,r);(w=document.createElement("iframe")).classList.add("web-office-iframe");var i={id:"office-iframe",src:e,scrolling:"no",frameborder:"0",allowfullscreen:"allowfullscreen",webkitallowfullscreen:"true",mozallowfullscreen:"true",allow:"clipboard-read; clipboard-write"};for(var a in r?(i.style="width: "+r.clientWidth+"px; height: "+r.clientHeight+"px;",n&&window.addEventListener("resize",o)):((r=document.createElement("div")).classList.add("web-office-default-container"),function(e){var t=document.createElement("style");document.head.appendChild(t);var n=t.sheet;n.insertRule(".web-office-default-container {position: absolute; padding: 0;  margin: 0; width: 100%; height: 100%; left: 0; top: 0;}",n.cssRules.length)}(),document.body.appendChild(r),i.style="position: fixed; top: 0; right: 0; bottom: 0; left: 0; width: 100%; height: 100%;"),i)w.setAttribute(a,i[a]);r.appendChild(w),w.destroy=function(){w.parentNode.removeChild(w),w=null,window.removeEventListener("resize",o),y&&(y.disconnect(),y=null)}}return w},O=function(e){k().contentWindow&&k().contentWindow.postMessage(JSON.stringify(e),v("origin"))};function E(e,t,n){return new Promise((function(r){var o=x();s.add((function e(t){if(!b(t)){var i=s.parse(t.data);i.eventName===n&&i.msgId===o&&(r(i.data),s.remove(e))}})),O({data:e,msgId:o,eventName:t})}))}var j=function(e){return E(e,"wps.jssdk.api","wps.api.reply")},S=function(e){return E(e,"api.basic","api.basic.reply")},I={idMap:{}};function T(e){return i(this,void 0,void 0,(function(){var t,n,r,o,i,c,u,l,f,d;return a(this,(function(a){switch(a.label){case 0:return b(e)?[2]:(t=s.parse(e.data),n=t.eventName,r=t.callbackId,o=t.data,r&&(i=I.idMap[r])?(c=i.split(":"),u=c[0],l=c[1],"api.callback"===n&&I[u]&&I[u][l]?[4,(d=I[u][l]).callback.apply(d,o.args)]:[3,2]):[3,2]);case 1:f=a.sent(),O({result:f,callbackId:r,eventName:"api.callback.reply"}),a.label=2;case 2:return[2]}}))}))}var C=function(e){return i(void 0,void 0,void 0,(function(){function t(){return Object.keys(I.idMap).find((function(e){return I.idMap[e]===r+":"+n}))}var n,r,o,i,c,u,l,f,d;return a(this,(function(a){switch(a.label){case 0:return n=e.prop,r=e.parentObjId,[4,N([o=e.value])];case 1:return i=a.sent(),c=i[0],u=i[1],e.value=c[0],l=Object.keys(u)[0],f=I[r],null===o&&f&&f[n]&&((d=t())&&delete I.idMap[d],delete f[n],Object.keys(f).length||delete I[r],Object.keys(I.idMap).length||s.remove(T)),l&&(Object.keys(I.idMap).length||s.add(T),I[r]||(I[r]={}),I[r][n]={callbackId:l,callback:u[l]},(d=t())&&delete I.idMap[d],I.idMap[l]=r+":"+n),[2]}}))}))},P=function(e,t,n,r){return i(void 0,void 0,void 0,(function(){var c,u,l,f,d,p,h,m;return a(this,(function(v){switch(v.label){case 0:return c=x(),f=new Promise((function(e,t){u=e,l=t})),d={},t.args?[4,N(t.args)]:[3,2];case 1:p=v.sent(),h=p[0],m=p[1],t.args=h,d=m,v.label=2;case 2:return"api.setter"!==e?[3,4]:[4,C(t)];case 3:v.sent(),v.label=4;case 4:return function(e){var t=e[0],n=e[1];"function"==typeof(t=o({},t)).data&&(t.data=t.data()),n(),O(t)}([{eventName:e,data:t,msgId:c},function(){var t=this;return s.add((function o(f){return i(t,void 0,void 0,(function(){var t,i,p;return a(this,(function(a){switch(a.label){case 0:return b(f)?[2]:"api.callback"===(t=s.parse(f.data)).eventName&&t.callbackId&&d[t.callbackId]?[4,d[t.callbackId].apply(d,t.data.args)]:[3,2];case 1:i=a.sent(),O({result:i,eventName:"api.callback.reply",callbackId:t.callbackId}),a.label=2;case 2:return t.eventName===e+".reply"&&t.msgId===c&&(t.error?((p=new Error("")).stack=t.error+"\n"+n,r&&r(),l(p)):u(t.result),s.remove(o)),[2]}}))}))})),f}]),[2,f]}}))}))};function N(e){return i(this,void 0,void 0,(function(){var t,n,r,o,i,s,c,u,l,f,d;return a(this,(function(a){switch(a.label){case 0:t={},n=[],r=e.slice(0),a.label=1;case 1:return r.length?(o=void 0,[4,r.shift()]):[3,13];case 2:return(i=a.sent())&&i.done?[4,i.done()]:[3,4];case 3:a.sent(),a.label=4;case 4:if(!function(e){if(!e)return!1;for(var t=e;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t}(o))return[3,11];for(c in o={},s=[],i)s.push(c);u=0,a.label=5;case 5:return u<s.length?(l=s[u],f=i[l],/^[A-Z]/.test(l)?f&&f.done?[4,f.done()]:[3,7]:[3,8]):[3,10];case 6:a.sent(),a.label=7;case 7:f&&f.objId?f={objId:f.objId}:"function"==typeof f&&(d=x(),t[d]=f,f={callbackId:d}),a.label=8;case 8:o[l]=f,a.label=9;case 9:return u++,[3,5];case 10:return[3,12];case 11:i&&i.objId?o={objId:i.objId}:"function"==typeof i&&void 0===i.objId?(d=x(),t[d]=i,o={callbackId:d}):o=i,a.label=12;case 12:return n.push(o),[3,1];case 13:return[2,[n,t]]}}))}))}var A=function(e,n){void 0===n&&(n=!0);var r=o({},e),i=r.headers,a=void 0===i?{}:i,s=r.subscriptions,c=void 0===s?{}:s,u=r.mode,l=void 0===u?f.nomal:u,d=r.commonOptions,p=a.backBtn,h=void 0===p?{}:p,m=a.shareBtn,v=void 0===m?{}:m,b=a.otherMenuBtn,g=void 0===b?{}:b,w=function(e,t){e.subscribe&&"function"==typeof e.subscribe&&(e.callback=t,c[t]=e.subscribe,n&&delete e.subscribe)};if(w(h,"wpsconfig_back_btn"),w(v,"wpsconfig_share_btn"),w(g,"wpsconfig_other_menu_btn"),g.items&&Array.isArray(g.items)){var y=[];g.items.forEach((function(e,t){switch(void 0===e&&(e={}),e.type){case"export_img":e.type=1,e.callback="export_img";break;case"export_pdf":e.type=1,e.callback="export_pdf";break;case"save_version":e.type=1,e.callback="save_version";break;case"about_wps":e.type=1,e.callback="about_wps";break;case"split_line":e.type=2;break;case"custom":e.type=3,w(e,"wpsconfig_other_menu_btn_"+t),y.push(e)}})),y.length&&(_||B)&&(g.items=y)}r.url=r.url||r.wpsUrl;var x=[];if((l===f.simple||d&&!1===d.isShowTopArea)&&x.push("simple","hidecmb"),r.debug&&x.push("debugger"),r.url&&x.length&&(r.url=r.url+(r.url.indexOf("?")>=0?"&":"?")+x.join("&")),d&&(d.isParentFullscreen||d.isBrowserViewFullscreen)&&(document.addEventListener("fullscreenchange",F),document.addEventListener("webkitfullscreenchange",F),document.addEventListener("mozfullscreenchange",F)),r.wordOptions&&(r.wpsOptions=r.wordOptions),r.excelOptions&&(r.etOptions=r.excelOptions),r.pptOptions&&(r.wppOptions=r.pptOptions),"object"==t(c.print)){var k="wpsconfig_print";"function"==typeof c.print.subscribe&&(c[k]=c.print.subscribe,r.print={callback:k},void 0!==c.print.custom&&(r.print.custom=c.print.custom)),delete c.print}return"function"==typeof c.exportPdf&&(c[k="wpsconfig_export_pdf"]=c.exportPdf,r.exportPdf={callback:k},delete c.exportPdf),r.commandBars&&D(r.commandBars,!1),o(o({},r),{subscriptions:c})},L=function(e){void 0===e&&(e="");var t="";if(!t&&e){var n=e.toLowerCase();-1!==n.indexOf("/office/s/")&&(t=u.spreadsheet),-1!==n.indexOf("/office/w/")&&(t=u.writer),-1!==n.indexOf("/office/p/")&&(t=u.presentation),-1!==n.indexOf("/office/f/")&&(t=u.pdf)}if(!t){var r=e.match(/[\?&]type=([a-z]+)/)||[];t=l[r[1]]||""}return t};function D(e,t){void 0===t&&(t=!0);var n=e.map((function(e){var t=e.attributes;if(!Array.isArray(t)){var n=[];for(var r in t)if(t.hasOwnProperty(r)){var o={name:r,value:t[r]};n.push(o)}e.attributes=n}return e}));return t&&O({data:n,eventName:"setCommandBars"}),n}var R=window.navigator.userAgent.toLowerCase(),_=/Android|webOS|iPhone|iPod|BlackBerry|iPad/i.test(R),B=function(){try{return-1!==window._parent.location.search.indexOf("from=wxminiprogram")}catch(e){return!1}}();function F(){var e={status:d.requestFullscreen},t=document,n=t.fullscreenElement||t.webkitFullscreenElement||t.mozFullScreenElement;e.status=n?d.requestFullscreen:d.exitFullscreen,O({data:e,eventName:"fullscreenchange"})}var M=function(){I.idMap={}};function U(){console.group("JSSDK 事件机制调整说明"),console.warn("jssdk.on、jssdk.off 和 jssdk.Application.Sub 将在后续版本中被弃用，建议使用改进后的 ApiEvent"),console.warn("具体请参考：https://wwo.wps.cn/docs/front-end/basic-usage/events/intro/"),console.groupEnd()}var q=0,H=new Set;function V(e){return q+=1,!e&&function(e){H.forEach((function(t){return t(e)}))}(q),q}function W(){var e=new Error("");return(e.stack||e.message||"").split("\n").slice(2).join("\n")}function z(e,t){var n,r=this,c=t.Events,l=t.Enum,f=t.Props,d=f[0],p=f[1],h={objId:q};switch(function e(t,n,r){for(var i=n.slice(0),a=function(){var n=i.shift();!n.alias&&~J.indexOf(n.prop)&&i.push(o(o({},n),{alias:n.prop+"Async"})),Object.defineProperty(t,n.alias||n.prop,{get:function(){var i=this,a=1===n.cache,s=a&&this["__"+n.prop+"CacheValue"];if(!s){var c=W(),u=V(a),l=function e(){for(var i,a=[],s=0;s<arguments.length;s++)a[s]=arguments[s];return void 0!==n.caller?function e(t,n,r){for(var i=n.slice(0),a=function(){var n=i.shift();!n.alias&&~J.indexOf(n.prop)&&i.push(o(o({},n),{alias:n.prop+"Async"})),Object.defineProperty(t,n.alias||n.prop,{get:function(){var o=this,i=1===n.cache,a=i&&this["__"+n.prop+"CacheValue"];if(!a){var s=W(),c=V(i),u=function o(){for(var i,a=[],c=0;c<arguments.length;c++)a[c]=arguments[c];return void 0!==n.caller?e(i={objId:V()},r[n.caller],r):i={},K(o,i,"api.caller",{obj:o,args:a,parentObjId:t.objId,objId:i.objId,prop:n.prop},s),i};return u.objId=-1,void 0!==n.getter&&(u.objId=c,e(u,r[n.getter],r)),K(t,u,"api.getter",{parentObjId:t.objId,objId:u.objId,prop:n.prop},s,(function(){delete o["__"+n.prop+"CacheValue"]})),i&&(this["__"+n.prop+"CacheValue"]=u),u}return a},set:function(e){var r=W();return K(t,{},"api.setter",{value:e,parentObjId:t.objId,objId:-1,prop:n.prop},r)}})};i.length;)a()}(i={objId:V()},r[n.caller],r):i={},K(e,i,"api.caller",{obj:e,args:a,parentObjId:t.objId,objId:i.objId,prop:n.prop},c),i};return l.objId=-1,void 0!==n.getter&&(l.objId=u,e(l,r[n.getter],r)),K(t,l,"api.getter",{parentObjId:t.objId,objId:l.objId,prop:n.prop},c,(function(){delete i["__"+n.prop+"CacheValue"]})),a&&(this["__"+n.prop+"CacheValue"]=l),l}return s},set:function(e){var r=W();return K(t,{},"api.setter",{value:e,parentObjId:t.objId,objId:-1,prop:n.prop},r)}})};i.length;)a()}(h,d,p),h.Events=c,h.Enum=l,e.Enum=h.Enum,e.Events=h.Events,e.Props=f,L(e.url)){case u.writer:e.WordApplication=e.WpsApplication=function(){return h};break;case u.spreadsheet:e.ExcelApplication=e.EtApplication=function(){return h};break;case u.presentation:e.PPTApplication=e.WppApplication=function(){return h};break;case u.pdf:e.PDFApplication=function(){return h}}e.Application=h,e.Free=function(e){return P("api.free",{objId:e},"")},e.Stack=h.Stack=(n=function(t){e&&e.Free(t)},function(){var e=[],t=function(t){e.push(t)};return H.add(t),{End:function(){n(e),H.delete(t)}}});var m={};s.add((function(e){return i(r,void 0,void 0,(function(){var t,n,r,o,i;return a(this,(function(a){switch(a.label){case 0:return b(e)?[2]:"api.event"===(t=s.parse(e.data)).eventName&&t.data?(n=t.data,r=n.eventName,o=n.data,(i=m[r])?[4,i(o)]:[3,2]):[3,2];case 1:a.sent(),a.label=2;case 2:return[2]}}))}))})),h.Sub={};var v=function(e){var t=c[e];Object.defineProperty(h.Sub,t,{set:function(e){U(),m[t]=e,O({eventName:"api.event.register",data:{eventName:t,register:!!e,objId:q+=1}})}})};for(var g in c)v(g)}var J=["ExportAsFixedFormat","GetOperatorsInfo","ImportDataIntoFields","ReplaceText","ReplaceBookmark","GetBookmarkText","GetComments"];function K(e,t,n,r,o,i){var a,s=(e.done?e.done():Promise.resolve()).then((function(){return a||(a=P(n,r,o,i)),a}));t.done=function(){return s},t.then=function(e,n){return r.objId>=0?(t.then=null,t.catch=null,s.then((function(){e(t)})).catch((function(e){return n(e)}))):s.then(e,n)},t.catch=function(e){return s.catch(e)},t.Destroy=function(){return P("api.free",{objId:t.objId},"")}}var X={},G=null,Q="fullscreenChange",$="api.getToken",Z="event.toast",Y="event.hyperLinkOpen",ee="api.getClipboardData";function te(e,t,n,r,c,u,l){var f=this;void 0===n&&(n={}),s.add((function(d){return i(f,void 0,void 0,(function(){var i,f,p,h,m,v,g,w,y,x,k,E,j,S,I,T,C,P,N;return a(this,(function(a){switch(a.label){case 0:return b(d)?[2]:(i=s.parse(d.data),f=i.eventName,p=void 0===f?"":f,h=i.data,m=void 0===h?null:h,v=i.url,g=void 0===v?null:v,-1!==["wps.jssdk.api"].indexOf(p)?[2]:"ready"!==p?[3,1]:(c.apiReadySended&&function(e){var t=[];Object.keys(X).forEach((function(n){X[n].forEach((function(r){var o=n;e.off(o,r),t.push({handle:r,eventName:o})})),delete X[n]})),t.forEach((function(e){var t=e.eventName,n=e.handle;null==G||G.ApiEvent.AddApiEventListener(t,n)}))}(t),O({eventName:"setConfig",data:o(o({},n),{version:e.version})}),e.tokenData&&e.setToken(o(o({},e.tokenData),{hasRefreshTokenConfig:!!n.refreshToken})),e.iframeReady=!0,[3,15]));case 1:return"error"!==p?[3,2]:(t.emit("error",m),[3,15]);case 2:return"open.result"!==p?[3,3]:(void 0!==(null===(C=null==m?void 0:m.fileInfo)||void 0===C?void 0:C.officeVersion)&&(e.mainVersion=m.fileInfo.officeVersion,console.log("WebOfficeSDK Main Version: V"+e.mainVersion)),t.emit("fileOpen",m),[3,15]);case 3:return"api.scroll"!==p?[3,4]:(window.scrollTo(m.x,m.y),[3,15]);case 4:if(p!==$)return[3,9];w={token:!1},a.label=5;case 5:return a.trys.push([5,7,,8]),[4,c.refreshToken()];case 6:return w=a.sent(),[3,8];case 7:return y=a.sent(),console.error("refreshToken: "+(y||"fail to get")),[3,8];case 8:return O({eventName:$+".reply",data:w}),[3,15];case 9:if(p!==ee)return[3,14];x={text:"",html:""},a.label=10;case 10:return a.trys.push([10,12,,13]),[4,c.getClipboardData()];case 11:return x=a.sent(),[3,13];case 12:return k=a.sent(),console.error("getClipboardData: "+(k||"fail to get")),[3,13];case 13:return O({eventName:ee+".reply",data:x}),[3,15];case 14:p===Z?c.onToast(m):p===Y?c.onHyperLinkOpen(m):"stage"===p?t.emit("stage",m):"event.callback"===p?(E=m.eventName,j=m.data,S=E,"fullScreenChange"===E&&(S=Q),"file.saved"===E&&(S="fileStatus"),((null===(P=n.commonOptions)||void 0===P?void 0:P.isBrowserViewFullscreen)||(null===(N=n.commonOptions)||void 0===N?void 0:N.isParentFullscreen))&&"fullscreenchange"===S&&(I=j.status,T=j.isDispatchEvent,n.commonOptions.isBrowserViewFullscreen?function(e,t,n,r){0===e?t.style="position: static; width: "+n.width+"; height: "+n.height:1===e&&(t.style="position: absolute; width: 100%; height: 100%"),r&&function(e){["fullscreen","fullscreenElement"].forEach((function(t){Object.defineProperty(document,t,{get:function(){return!!e.status},configurable:!0})}));var t=new CustomEvent("fullscreenchange");document.dispatchEvent(t)}({status:e})}(I,u,l,T):n.commonOptions.isParentFullscreen&&function(e,t,n){var r=document.querySelector(n),o=r&&1===r.nodeType?r:t;if(0===e){var i=document;(i.exitFullscreen||i.mozCancelFullScreen||i.msExitFullscreen||i.webkitCancelFullScreen||i.webkitExitFullscreen).call(document)}else 1===e&&(o.requestFullscreen||o.mozRequestFullScreen||o.msRequestFullscreen||o.webkitRequestFullscreen).call(o)}(I,u,n.commonOptions.isParentFullscreen)),t.emit(S,j)):"api.ready"===p&&z(e,m),a.label=15;case 15:return"function"==typeof r[p]&&r[p](e,g||m),[2]}}))}))}))}function ne(e){return new Promise((function(t){s.add((function n(r){b(r)||s.parse(r.data).eventName===e&&(t(),s.remove(n))}))}))}function re(e){var t,n=this;void 0===e&&(e={}),G&&G.destroy();try{var r=A(e),o=r.subscriptions,u=void 0===o?{}:o,l=r.mount,f=void 0===l?null:l,d=r.url,p=r.refreshToken,h=r.onToast,v=r.onHyperLinkOpen,b=r.getClipboardData;m("origin",(d.match(/https*:\/\/[^\/]+/g)||[])[0]);var g=k(d,f),w=ne("ready"),y=ne("open.result"),x=ne("api.ready"),E=f?{width:f.clientWidth+"px",height:f.clientHeight+"px"}:{width:"100vw",height:"100vh"};delete r.mount,d&&delete r.url,delete r.subscriptions;var I=(t=t||Object.create(null),{on:function(e,n){(t[e]||(t[e]=[])).push(n)},off:function(e,n){t[e]&&t[e].splice(t[e].indexOf(n)>>>0,1)},emit:function(e,n){(t[e]||[]).slice().map((function(e){e(n)})),(t["*"]||[]).slice().map((function(t){t(e,n)}))}}),T={apiReadySended:!1},C=function(e,t,r){return i(n,void 0,void 0,(function(){return a(this,(function(n){switch(n.label){case 0:return function(e,t,n){if(X[e]){var r=!!X[e].find((function(e){return e===t}));return r&&"off"===n?(I.off(e,t),X[e]=X[e].filter((function(e){return e!==t})),!!X[e].length||(X[e]=void 0,!1)):(r||"on"!==n||(X[e].push(t),I.on(e,t)),!0)}return"on"===n?(X[e]=[],X[e].push(t),!1):"off"===n||void 0}(e,t,r)?[3,2]:[4,w];case 1:n.sent(),function(e,t){var n=e.eventName,r=e.type,o=e.handle;"on"===t?I.on(n,o):I.off(n,o),"base.event"===r&&O({eventName:"basic.event",data:{eventName:n,action:t}}),U()}(function(e,t){var n=e,r="base.event";switch(n){case"fileSaved":console.warn("fileSaved事件监听即将弃用， 推荐使用fileStatus进行文件状态的监听"),n="fileStatus";break;case Q:n="fullscreenchange";break;case"error":case"fileOpen":r="callback.event"}return{eventName:n,type:r,handle:t}}(e,t),r),n.label=2;case 2:return[2]}}))}))};return G={url:d,iframe:g,version:"1.1.19",iframeReady:!1,tokenData:null,commandBars:null,tabs:{getTabs:function(){return i(this,void 0,void 0,(function(){return a(this,(function(e){switch(e.label){case 0:return[4,w];case 1:return e.sent(),[2,S({api:"tab.getTabs"})]}}))}))},switchTab:function(e){return i(this,void 0,void 0,(function(){return a(this,(function(t){switch(t.label){case 0:return[4,w];case 1:return t.sent(),[2,S({api:"tab.switchTab",args:{tabKey:e}})]}}))}))}},setCooperUserColor:function(e){return i(this,void 0,void 0,(function(){return a(this,(function(t){switch(t.label){case 0:return[4,w];case 1:return t.sent(),[2,S({api:"setCooperUserColor",args:e})]}}))}))},setToken:function(e){return i(this,void 0,void 0,(function(){return a(this,(function(t){switch(t.label){case 0:return[4,w];case 1:return t.sent(),G.tokenData=e,O({eventName:"setToken",data:e}),[2]}}))}))},ready:function(){return i(this,void 0,void 0,(function(){return a(this,(function(e){switch(e.label){case 0:return T.apiReadySended?[3,2]:[4,y];case 1:e.sent(),T.apiReadySended=!0,O({eventName:"api.ready"}),e.label=2;case 2:return[4,x];case 3:return e.sent(),[2,new Promise((function(e){return setTimeout((function(){return e(null==G?void 0:G.Application)}),0)}))]}}))}))},destroy:function(){g.destroy(),s.empty(),G=null,H=new Set,q=0,document.removeEventListener("fullscreenchange",F),M()},save:function(){return i(this,void 0,void 0,(function(){return a(this,(function(e){switch(e.label){case 0:return[4,w];case 1:return e.sent(),[2,j({api:"save"})]}}))}))},setCommandBars:function(e){return i(this,void 0,void 0,(function(){return a(this,(function(t){switch(t.label){case 0:return[4,w];case 1:return t.sent(),D(e),[2]}}))}))},updateConfig:function(e){return void 0===e&&(e={}),i(this,void 0,void 0,(function(){return a(this,(function(t){switch(t.label){case 0:return[4,w];case 1:return t.sent(),e.commandBars?(console.warn("Deprecated: `updateConfig()` 方法即将废弃，请使用`setCommandBars()`代替`updateConfig()`更新`commandBars`配置。"),[4,D(e.commandBars)]):[3,3];case 2:t.sent(),t.label=3;case 3:return[2]}}))}))},executeCommandBar:function(e){return i(this,void 0,void 0,(function(){return a(this,(function(t){switch(t.label){case 0:return[4,w];case 1:return t.sent(),D([{cmbId:e,attributes:[{name:"click",value:!0}]}]),[2]}}))}))},on:function(e,t){return i(this,void 0,void 0,(function(){return a(this,(function(n){return[2,this.ApiEvent.AddApiEventListener(e,t)]}))}))},off:function(e,t){return i(this,void 0,void 0,(function(){return a(this,(function(n){return[2,this.ApiEvent.RemoveApiEventListener(e,t)]}))}))},ApiEvent:{AddApiEventListener:function(e,t){return i(this,void 0,void 0,(function(){return a(this,(function(n){switch(n.label){case 0:return[4,C(e,t,"on")];case 1:return[2,n.sent()]}}))}))},RemoveApiEventListener:function(e,t){return i(this,void 0,void 0,(function(){return a(this,(function(n){switch(n.label){case 0:return[4,C(e,t,"off")];case 1:return[2,n.sent()]}}))}))}}},function(e,t,n,r,o,i){t&&c(t)&&(o.refreshToken=t,e.refreshToken={eventName:$}),i&&c(i)&&(o.getClipboardData=i,e.getClipboardData={eventName:ee}),n&&c(n)&&(o.onToast=n,e.onToast={eventName:Z}),r&&c(r)&&(o.onHyperLinkOpen=r,e.onHyperLinkOpen={eventName:Y})}(r,p,h,v,T,b),te(G,I,r,u,T,g,E),G}catch(e){console.error(e)}}console.log("WebOfficeSDK JS-SDK V1.1.19");var oe=Object.freeze({__proto__:null,listener:te,config:re});window.WPS=oe;var ie={config:re},ae=function e(t){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.filename=t.filename,this.format=t.format,this.user_acl=t.user_acl};try{new window.CustomEvent("T")}catch(p){var se=function(e,t){t=t||{bubbles:!1,cancelable:!1,detail:void 0};var n=document.createEvent("CustomEvent");return n.initCustomEvent(e,t.bubbles,t.cancelable,t.detail),n};se.prototype=window.Event.prototype,window.CustomEvent=se}var ce={},ue={};function le(e,t){window.addEventListener(e,(function(e){return t(e.detail)})),ce[e]=!0,Array.isArray(ue[e])&&(ue[e].forEach((function(t){fe(e,t)})),ue[e]=null)}function fe(e,t){if(!0===ce[e]){var n=new CustomEvent(e,{detail:t});window.dispatchEvent(n)}else ue[e]=[].concat(ue[e]||[],[t])}var de=null,pe=null,he=!0,me=null;function ve(e){var t;t={eventName:he?"setToken":"setTokenRefresh",data:e},(me.formatInstanceCollect.previewInstance.iframe.contentWindow||me.formatInstanceCollect.previewInstance.iframe.window).postMessage(t,"*"),he=!1,de=e,pe=(new Date).getTime()}function be(e,t){fe("opendocStage",{eventName:"opendocStage",data:{name:"opendoc.loading.start",stageTime:t-e,time:t-e,ts:t}})}function ge(e){fe("opendocStage",{eventName:"opendocStage",data:{name:"opendoc.loading.end",stageTime:Date.now()-e,time:Date.now()-e,ts:Date.now()}})}function we(e,t){fe("opendocStage",{eventName:"opendocStage",data:{name:"opendoc.render.error",stageTime:Date.now()-e,time:Date.now()-e,ts:Date.now(),error:t}})}function ye(e){fe("opendocStage",{eventName:"opendocStage",data:{name:"opendoc.render.end",stageTime:Date.now()-e,time:Date.now()-e,ts:Date.now()}})}function xe(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function ke(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var Oe=new RegExp(/\/docs\/viewweb\/reader\//),Ee=new RegExp(/\/weboffice\/office\//),je=window.addEventListener?"addEventListener":"attachEvent",Se=window[je],Ie="attachEvent"==je?"onmessage":"message",Te=function(){function t(e){var n=this;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t),ke(this,"on",le),ke(this,"formatInstanceCollect",{}),console.log("OpenSDK JS-SDK ".concat("v1.2.0")),new Promise((function(t,r){if(!e)return r(console.error("初始化sdk失败，缺失config参数"));if(!e.url||!Oe.test(e.url)&&!Ee.test(e.url))return r(console.error("请设置文档中台预览编辑链接"));e.mount&&e.mount instanceof Node||(e.mount=document.getElementsByTagName("body")[0]);var o,i=document.createElement("div");i.style.position="relative",i.style.width="100%",i.style.height="100%",e.mount.appendChild(i),e.mount=i,n.opendocLocation=function(e){var t=document.createElement("a");t.href=e;var n=t.hostname,r="80"===t.port||"443"===t.port?"":t.port,o=n+(r?":".concat(r):"");return{href:e,protocol:t.protocol||"",host:o,hostname:n,port:r,search:t.search.replace(t.hash,"")||"",hash:t.hash||"",pathname:0===(t.pathname||"").indexOf("/")?t.pathname||"":"/"+(t.pathname||""),relative:(e.match(/tps?:\/\/[^\/]+(.+)/)||[,""])[1]||"",segments:t.pathname.replace(/^\//,"").split("/")||[],origin:t.protocol+"//"+o||""}}(e.url),e.webpath=(o=n.opendocLocation.pathname||window.location.pathname||"").substring(0,o.lastIndexOf("/docs/viewweb/"))||"",n.sourceLocation=window.location,n.opendocMount=e.mount,n.config=e,n.initTime=Date.now(),fe("opendocStage",{eventName:"opendocStage",data:{name:"opendoc.html.start",stageTime:0,time:0,ts:n.initTime}}),n.listenStage(),e.url&&Ee.test(e.url)?(n.renderWebofficeEdit(),t(n)):n.loadMiddle().then((function(e){return t(e)}))}))}var n,r;return n=t,r=[{key:"listenOpendocMessage",value:function(e){var t=this;Se(Ie,(function(n){n.origin===t.opendocLocation.origin&&e(n)}))}},{key:"listenStage",value:function(){var t=this;this.listenOpendocMessage((function(n){"opendoc.loading.start"===n.data.eventName&&be(t.initTime,n.data.data.time),"opendoc.render.error"===n.data.eventName&&we(t.initTime,n.data.data.error),"previewStage"===n.data.eventName&&("preview.loading.end"===n.data.data.name&&(t.middleIframe&&t.opendocMount.contains(t.middleIframe)&&t.opendocMount.removeChild(t.middleIframe),ge(t.initTime)),"preview.render.error"===n.data.data.name&&we(t.initTime,n.data.data.error),"preview.render.end"===n.data.data.name&&(ye(t.initTime),t.formatInstanceCollect.previewInstance.renderEnd=!0)),function(t){if("string"!=typeof t)return!1;try{var n=JSON.parse(t);return!("object"!=e(n)||!n)}catch(e){return console.log("error："+t+"!!!"+e),!1}}(n.data)&&"web_remove_loading"===JSON.parse(n.data).eventName&&(t.middleIframe&&t.opendocMount.contains(t.middleIframe)&&setTimeout((function(){t.opendocMount.removeChild(t.middleIframe)}),100),ge(t.initTime))}))}},{key:"renderWebofficeEdit",value:function(){var e=this,t=ie.config(this.config);t.print=function(){t.executeCommandBar("Print")},t.on("fileOpen",(function(t){t.success&&ye(e.initTime),"Fail"===t.result&&we(e.initTime,t.msg)})),t.on("error",(function(t){we(e.initTime,t.reason||t)})),t.on("stage",(function(t){t.performance&&be(e.initTime,t.performance.timing.domLoading)})),this.config.setToken&&t.setToken(this.config.setToken),this.formatInstanceCollect.webofficeInstance=t}},{key:"loadMiddle",value:function(){var e=this;return new Promise((function(t){var n=document.createElement("iframe");n.style.position="absolute",n.style.width="100%",n.style.height="100%",n.style.top=0,n.style.zIndex=99,n.frameBorder="none",n.src=e.opendocLocation.origin+e.config.webpath+"/docs/viewweb/reader/middle"+(e.config.setToken?"?token="+encodeURIComponent(e.config.setToken.token)+"&":"?")+"opendocUrl="+encodeURIComponent(e.config.url),e.middleIframe=n,e.listenOpendocMessage((function(n){if("getPreviewTypeAndUrl"===n.data.eventName)return e.file=new ae(n.data.data.fileInfo),"weboffice"===n.data.data.previewType?(e.renderWeboffice(n.data.data.previewUrl),t(e)):(e.renderOpendoc(n.data.data.previewUrl),t(e))})),e.opendocMount.appendChild(n)}))}},{key:"renderWeboffice",value:function(e){var t=this,n=ie.config(Object.assign(this.config,{url:e,mode:"simple"}));n.print=function(){n.executeCommandBar("Print")},n.on("fileOpen",(function(e){e.success&&ye(t.initTime),"Fail"===e.result&&we(t.initTime,e.msg)})),n.on("error",(function(e){we(t.initTime,e.reason||e)})),this.config.setToken&&n.setToken(this.config.setToken),this.formatInstanceCollect.webofficeInstance=n}},{key:"renderOpendoc",value:function(e){var t=this,n=document.createElement("iframe");n.src=e,n.frameBorder="none",n.style.width="100%",n.style.height="100%",this.config.mount.appendChild(n);var r={iframe:n,destroy:function(){t.config.mount.removeChild(n)},print:function(){(n.contentWindow||n.window).postMessage("wpsPreviewPrint",t.opendocLocation.origin)},ready:function(){return new Promise((function(e){if(t.formatInstanceCollect.previewInstance.renderEnd)return e();window.addEventListener("opendocStage",(function(t){if("opendoc.render.end"===t.detail.data.name)return e()}))}))}};this.formatInstanceCollect.previewInstance=r,this.config.setToken&&function(e){if(!e.config.setToken||!e.config.setToken.token)return console.error("请按照文档规范设置token格式");window.addEventListener("message",(function(t){"wpsPreviewDidMount"===t.data&&(de=null,pe=null,he=!0,ve((me=e).config.setToken),"function"==typeof me.config.refreshToken&&function(e){window.document.addEventListener("visibilitychange",(function(){if("hidden"!==document.visibilityState){var e=(new Date).getTime();if(de&&e-pe>de.timeout){var t=me.config.refreshToken();"[object Promise]"===t.toString()?t.then((function(e){ve(e)})):ve(t)}}}));var t=function(e){ve(e),e.timeout&&n(e.timeout)},n=function(e){var n,r=e-3e5;setTimeout((function(){var o=(new Date).getTime(),i=me.config.refreshToken();if("[object Promise]"===i.toString())i.then((function(i){n=i;var a=(new Date).getTime();setTimeout((function(){t(n)}),r>0?3e5-(a-o):e-(a-o))}));else{n=i;var a=(new Date).getTime();setTimeout((function(){t(n)}),r>0?3e5-(a-o):e-(a-o))}}),r)};n(e)}(me.config.setToken.timeout))}))}(this)}},{key:"destroy",value:function(){for(var e in this.formatInstanceCollect)Object.hasOwnProperty.call(this.formatInstanceCollect,e)&&this.formatInstanceCollect[e]&&this.formatInstanceCollect[e].destroy instanceof Function&&this.formatInstanceCollect[e].destroy()}},{key:"ready",value:function(){var e=[];for(var t in this.formatInstanceCollect)Object.hasOwnProperty.call(this.formatInstanceCollect,t)&&this.formatInstanceCollect[t]&&this.formatInstanceCollect[t].ready instanceof Function&&e.push(this.formatInstanceCollect[t].ready());return Promise.all(e)}},{key:"print",value:function(){for(var e in this.formatInstanceCollect)console.log(this.formatInstanceCollect[e]),Object.hasOwnProperty.call(this.formatInstanceCollect,e)&&this.formatInstanceCollect[e]&&this.formatInstanceCollect[e].print instanceof Function&&this.formatInstanceCollect[e].print()}},{key:"getWebofficeInstance",value:function(){return this.formatInstanceCollect.webofficeInstance}},{key:"getPreviewInstance",value:function(){return this.formatInstanceCollect.previewInstance}}],r&&xe(n.prototype,r),t}(),Ce=function(e){return new Te(e)}}(),r}()}));