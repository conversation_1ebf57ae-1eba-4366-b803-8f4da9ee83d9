import request from '@/utils/request'

//新增初始化数据
export function getRegularReport(){
  return request({
    url: '/colligate/violRegular/report/getRegularReport',
    method: 'post'
  })
}

//编辑初始化数据
export function selectReportInfo(regularReportId){
  return request({
    url: '/colligate/violRegular/report/selectReportInfo/'+regularReportId,
    method: 'post'
  })
}

//校验是否存在同一年度区间的上报数据
export function checkReportTime(regularReportId,reportStartTime,reportType,reportYear,reportEndTime){
  return request({
    url: '/colligate/violRegular/report/checkReportTime',
    method: 'post',
    data: JSON.stringify({
      regularReportId: regularReportId,
      id: regularReportId,
      reportStartTime: reportStartTime,
      reportType: reportType,
      reportYear: reportYear,
      reportEndTime: reportEndTime
    })
  })
}

//保存数据
export function saveReportData(regularReport,unitList){
  return request({
    url: '/colligate/violRegular/report/saveReportData',
    method: 'post',
    data: JSON.stringify({
      report: regularReport,
      unitList: unitList
    })
  })
}
//校验接口
export function checkReportData(regularReportId){
  return request({
    url: '/colligate/violRegular/report/checkReportData/' + regularReportId,
    method: 'post',
    data: JSON.stringify({
      regularReportId: regularReportId
    })
  })
}

//调用提交发起上报流程
export function submitRegularReport(regularReportId) {
  return request({
    url: '/colligate/violRegular/submitRegularReport/' + regularReportId,
    method: 'post'
  })
}
//保存补录数据
export function saveRegularSupplementary(regularReportId,unitList){
  return request({
    url: '/colligate/violRegular/report/saveAddReportUnitByEdit/' + regularReportId,
    method: 'post',
    data: JSON.stringify(unitList)
  })
}

//退回
export function backUnitDetail(regularReportId,detailList){
  return request({
    url: '/colligate/violRegular/report/backUnitDetail',
    method: 'post',
    data: JSON.stringify({
      regularReportId:regularReportId,
      detailList:detailList
    })
  })
}

//显示历史数据
export function showBackHistoryList(regularReportId,reportUnitCode,orgGrade){
  return request({
    url: '/colligate/violRegular/report/showBackHistoryList',
    method: 'post',
    data: JSON.stringify({
      regularReportId:regularReportId,
      reportUnitCode:reportUnitCode,
      orgGrade:orgGrade
    })
  })
}
