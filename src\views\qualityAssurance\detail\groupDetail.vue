<!--集团 -》 详情页-->
<template>
  <div class="group-index">
    <div class="group-index-box" v-if="auditInfo.differentFieldList.length">
      <div class="group-header" >
        {{auditInfo.auditYear}}年{{auditInfo.auditQuarterName}}季度报送数据存在异常。同日常报送、基础数据模块核对校验时发现以下省分存在不一致情况：
      </div>
      <div class="group-prov" >
        <div class="group-prov-box" v-for="(item,index) in auditInfo.differentFieldList" :key="index">
          <div class="group-prov-title">{{ item.differentCn }}：</div>
          <div class="group-prov-name">{{item.provNames}}</div>
          {{index+1==auditInfo.differentFieldList.length?'。':'，'}}
        </div>
      </div>
    </div>
    <div class="group-index-box" v-else>
      <div class="group-header" >
        {{auditInfo.auditYear}}年{{auditInfo.auditQuarterName}}季度报送数据无异常。
      </div>
    </div>
    <div class="group-table">
      <BlockCard  title="数据不一致的省分" v-if="auditInfo.differentFieldList.length">
        <template v-slot:left>
          <el-radio-group v-model="tabPosition1" style="margin-left: 30px;" @input="queryNextList1">
            <el-radio-button label="1">实时数据</el-radio-button>
            <el-radio-button label="2">稽核数据</el-radio-button>
          </el-radio-group>
        </template>
        <el-form   style="height:380px">

          <el-table  ref="table" border v-loading="loading1" :data="tableList1"  height="100%">
            <el-table-column label="序号" type="index" min-width="6%" align="center" >
              <!--<template slot-scope="scope">-->
              <!--<table-index-->
              <!--:index="scope.$index"-->
              <!--:page-num="queryParams.pageNum"-->
              <!--:page-size="queryParams.pageSize"-->
              <!--/>-->
              <!--</template>-->
            </el-table-column>
            <el-table-column label="省分" prop="provName" width="140"  show-overflow-tooltip align="center">
              <template slot-scope="scope" >
                <p :class="scope.row.realDifferentFlag=='1'?'text-red':''" class="ovflowHidden">{{ scope.row.provName}}</p>
              </template>
            </el-table-column>
            <el-table-column label="当年累计新增配套制度"  align="center">
              <el-table-column label="基础数据" prop="totalNewSupportingSystem" width="120"   show-overflow-tooltip align="center"/>
              <el-table-column label="季度报告" prop="totalNewSupportingSystemQ" width="120"   show-overflow-tooltip align="center"/>
            </el-table-column>
            <el-table-column label="当年累计新增工作机制"  align="center">
              <el-table-column label="基础数据" prop="totalNewWorkSystem" width="120"  show-overflow-tooltip align="center"/>
              <el-table-column label="季度报告" prop="totalNewWorkSystemQ" width="120"  show-overflow-tooltip align="center"/>
            </el-table-column>
            <el-table-column label="本季度新受理问题线索数量"  align="center">
              <el-table-column label="日常报送" prop="quarterNewProblemNumber" width="120"  show-overflow-tooltip align="center"/>
              <el-table-column label="季度报告" prop="quarterNewProblemNumberQ" width="120"  show-overflow-tooltip align="center"/>
            </el-table-column>
            <el-table-column label="本季度涉及资产损失（万元）"  align="center">
              <el-table-column label="日常报送" prop="lossAmount" width="120"  show-overflow-tooltip align="right">
                <template slot-scope="scope">
                  {{ scope.row.lossAmount | filterNum() }}
                </template>
              </el-table-column>
              <el-table-column label="季度报告" prop="lossAmountQ" width="120"  show-overflow-tooltip align="right">
                <template slot-scope="scope">
                  {{ scope.row.lossAmountQ | filterNum }}
                </template>
              </el-table-column>
            </el-table-column>
            <el-table-column label="本季度涉及资产损失风险（万元）" prop="problemTitle" align="center">
              <el-table-column label="日常报送" prop="lossRisk" width="120"  show-overflow-tooltip align="right">
                <template slot-scope="scope">
                  {{ scope.row.lossRisk | filterNum }}
                </template>
              </el-table-column>
              <el-table-column label="季度报告" prop="lossRiskQ" width="120"  show-overflow-tooltip align="right">
                <template slot-scope="scope">
                  {{ scope.row.lossRiskQ | filterNum }}
                </template>
              </el-table-column>
            </el-table-column>
            <!--<el-table-column label="当年累计受理问题线索数量"  align="center">-->
            <!--<el-table-column label="日常报送" prop="totalProblemSourceNumber" min-width="10%"  show-overflow-tooltip align="left"/>-->
            <!--<el-table-column label="季度报告" prop="totalProblemSourceNumberQ" min-width="10%"  show-overflow-tooltip align="left"/>-->
            <!--</el-table-column>-->
            <el-table-column label="当年累计受理问题线索数量"  align="center">
              <el-table-column label="日常报送" prop="totalProblemSourceNumber" width="120"  show-overflow-tooltip align="center"/>
              <el-table-column label="季度报告" prop="totalProblemSourceNumberQ" width="120" show-overflow-tooltip align="center"/>
            </el-table-column>

            <el-table-column label="上年结转问题线索数量"  align="center">
              <el-table-column label="日常报送" prop="lastYearProblemSourceNumber" width="120"  show-overflow-tooltip align="center"/>
              <el-table-column label="季度报告" prop="lastYearProblemSourceNumberQ" width="120"  show-overflow-tooltip align="center"/>
            </el-table-column>

            <el-table-column label="当年累计完成追责问题数量（件）"  align="center">
              <el-table-column label="日常报送" prop="totalCompletedProblemNumber" width="120"  show-overflow-tooltip align="center"/>
              <el-table-column label="季度报告" prop="totalCompletedProblemNumberQ" width="120"  show-overflow-tooltip align="center"/>
            </el-table-column>

            <el-table-column label="当年累计追责总人数（人）"  align="center">
              <el-table-column label="日常报送" prop="totalAccountabilityPersonNumber" width="120"  show-overflow-tooltip align="center"/>
              <el-table-column label="季度报告" prop="totalAccountabilityPersonNumberQ" width="120"  show-overflow-tooltip align="center"/>
            </el-table-column>

            <el-table-column label="当年累计追责总人次"  align="center">
              <el-table-column label="日常报送" prop="totalAccountabilityPersonTime" width="120"  show-overflow-tooltip align="center"/>
              <el-table-column label="季度报告" prop="totalAccountabilityPersonTimeQ" width="120"  show-overflow-tooltip align="center"/>
            </el-table-column>

            <el-table-column label="当年累计追责：组织处理（人次）"  align="center">
              <el-table-column label="日常报送" prop="orgHandleTime" width="120"  show-overflow-tooltip align="center"/>
              <el-table-column label="季度报告" prop="orgHandleTimeQ" width="120"  show-overflow-tooltip align="center"/>
            </el-table-column>

            <el-table-column label="当年累计追责：扣减薪酬（人次）"  align="center">
              <el-table-column label="日常报送" prop="deductionSalaryTime" width="120"  show-overflow-tooltip align="center"/>
              <el-table-column label="季度报告" prop="deductionSalaryTimeQ" width="120"  show-overflow-tooltip align="center"/>
            </el-table-column>

            <el-table-column label="当年累计追责：党纪处分（人次）"  align="center">
              <el-table-column label="日常报送" prop="partyPunishmentTime" width="120"  show-overflow-tooltip align="center"/>
              <el-table-column label="季度报告" prop="partyPunishmentTimeQ" width="120" show-overflow-tooltip align="center"/>
            </el-table-column>

            <el-table-column label="当年累计追责：移送监察机关或司法机关（人次）"  align="center">
              <el-table-column label="日常报送" prop="transferAuthorityTime" width="120"  show-overflow-tooltip align="center"/>
              <el-table-column label="季度报告" prop="transferAuthorityTimeQ" width="120"  show-overflow-tooltip align="center"/>
            </el-table-column>

            <el-table-column label="当年累计追责：其他（人次）"  align="center">
              <el-table-column label="日常报送" prop="processingOtherItem" width="120"  show-overflow-tooltip align="center"/>
              <el-table-column label="季度报告" prop="processingOtherItemQ" width="120"  show-overflow-tooltip align="center"/>
            </el-table-column>

            <el-table-column label="当年累计扣减薪酬金额（万元）"  align="center">

              <el-table-column label="日常报送" prop="totalDeductionSalary" width="120"  show-overflow-tooltip align="right">
                <template slot-scope="scope">
                  {{ scope.row.totalDeductionSalary | filterNum }}
                </template>
              </el-table-column>
              <el-table-column label="季度报告" prop="totalDeductionSalaryQ" width="120"  show-overflow-tooltip align="right">
                <template slot-scope="scope">
                  {{ scope.row.totalDeductionSalaryQ | filterNum }}
                </template>
              </el-table-column>
            </el-table-column>

            <el-table-column label="当年累计挽回资产损失（万元）"  align="center">
              <el-table-column label="日常报送" prop="totalRetrieveLossAmount" width="120"  show-overflow-tooltip align="right">
                <template slot-scope="scope">
                  {{ scope.row.totalRetrieveLossAmount | filterNum }}
                </template>
              </el-table-column>
              <el-table-column label="季度报告" prop="totalRetrieveLossAmountQ" width="120"  show-overflow-tooltip align="right">
                <template slot-scope="scope">
                  {{ scope.row.totalRetrieveLossAmountQ | filterNum }}
                </template>
              </el-table-column>
            </el-table-column>

            <el-table-column label="当年累计降低损失风险（万元）"  align="center">
              <el-table-column label="日常报送" prop="totalReduceLossRisk" width="120"  show-overflow-tooltip align="right">
                <template slot-scope="scope">
                  {{ scope.row.totalReduceLossRisk | filterNum }}
                </template>
              </el-table-column>
              <el-table-column label="季度报告" prop="totalReduceLossRiskQ" width="120"  show-overflow-tooltip align="right">
                <template slot-scope="scope">
                  {{ scope.row.totalReduceLossRiskQ | filterNum }}
                </template>
              </el-table-column>
            </el-table-column>

            <el-table-column label="当年累计制修订管理制度（项）"  align="center">
              <el-table-column label="日常报送" prop="totalPerfectSystemNumber" width="120"  show-overflow-tooltip align="center"/>
              <el-table-column label="季度报告" prop="totalPerfectSystemNumberQ" width="120"  show-overflow-tooltip align="center"/>
            </el-table-column>

            <el-table-column label="子企业管理干部"  align="center">
              <el-table-column label="日常报送" prop="subManagementNumber" width="120"  show-overflow-tooltip align="center"/>
              <el-table-column label="季度报告" prop="subManagementNumberQ" width="120"  show-overflow-tooltip align="center"/>
            </el-table-column>

            <el-table-column label="问题状态统计" prop="problemStatusCollect" width="240"  show-overflow-tooltip align="left"/>
            <el-table-column label="不一致原因" prop="differentReason" width="240"  show-overflow-tooltip align="left"/>
            <el-table-column label="处理人" prop="handledUserName" width="120"  show-overflow-tooltip align="center"/>

            <el-table-column label="操作"  width="80" fixed="right"  align="center" class-name="small-padding fixed-width">
              <template slot-scope="scope">
                <el-button
                  v-if="scope.row.differentReason==''||scope.row.differentReason==null"
                  size="mini"
                  v-preventReClick
                  type="text"
                  icon="el-icon-bell"
                  title="催办"
                  @click="urging(scope.row.auditId)"
                ></el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-form>
      </BlockCard>

      <BlockCard  title="数据一致的省分">
        <template v-slot:left>
          <el-radio-group v-model="tabPosition2" style="margin-left: 30px;" @input="queryNextList2">
            <el-radio-button label="1">实时数据</el-radio-button>
            <el-radio-button label="2">稽核数据</el-radio-button>
          </el-radio-group>
        </template>
        <el-form  :style="{height:auditInfo.differentFieldList.length?'360px':'650px'}">
          <el-table  ref="table" border v-loading="loading2" :data="tableList2"  height="100%">
            <el-table-column label="序号" type="index" min-width="6%" align="center" >
              <!--<template slot-scope="scope">-->
              <!--<table-index-->
              <!--:index="scope.$index"-->
              <!--:page-num="queryParams.pageNum"-->
              <!--:page-size="queryParams.pageSize"-->
              <!--/>-->
              <!--</template>-->
            </el-table-column>
            <el-table-column label="省分" prop="provName" width="140"  show-overflow-tooltip align="center">
              <template slot-scope="scope" >
                <p :class="scope.row.realDifferentFlag=='1'?'text-red':''" class="ovflowHidden">{{ scope.row.provName}}</p>
              </template>
            </el-table-column>
            <el-table-column label="当年累计新增配套制度"  align="center">
              <el-table-column label="基础数据" prop="totalNewSupportingSystem" width="120"   show-overflow-tooltip align="center"/>
              <el-table-column label="季度报告" prop="totalNewSupportingSystemQ" width="120"   show-overflow-tooltip align="center"/>
            </el-table-column>
            <el-table-column label="当年累计新增工作机制"  align="center">
              <el-table-column label="基础数据" prop="totalNewWorkSystem" width="120"  show-overflow-tooltip align="center"/>
              <el-table-column label="季度报告" prop="totalNewWorkSystemQ" width="120"  show-overflow-tooltip align="center"/>
            </el-table-column>
            <el-table-column label="本季度新受理问题线索数量"  align="center">
              <el-table-column label="日常报送" prop="quarterNewProblemNumber" width="120"  show-overflow-tooltip align="center"/>
              <el-table-column label="季度报告" prop="quarterNewProblemNumberQ" width="120"  show-overflow-tooltip align="center"/>
            </el-table-column>
            <el-table-column label="本季度涉及资产损失（万元）"  align="center">
              <el-table-column label="日常报送" prop="lossAmount" width="120"  show-overflow-tooltip align="right">
                <template slot-scope="scope">
                  {{ scope.row.lossAmount | filterNum() }}
                </template>
              </el-table-column>
              <el-table-column label="季度报告" prop="lossAmountQ" width="120"  show-overflow-tooltip align="right">
                <template slot-scope="scope">
                  {{ scope.row.lossAmountQ | filterNum }}
                </template>
              </el-table-column>
            </el-table-column>
            <el-table-column label="本季度涉及资产损失风险（万元）" prop="problemTitle" align="center">
              <el-table-column label="日常报送" prop="lossRisk" width="120"  show-overflow-tooltip align="right">
                <template slot-scope="scope">
                  {{ scope.row.lossRisk | filterNum }}
                </template>
              </el-table-column>
              <el-table-column label="季度报告" prop="lossRiskQ" width="120"  show-overflow-tooltip align="right">
                <template slot-scope="scope">
                  {{ scope.row.lossRiskQ | filterNum }}
                </template>
              </el-table-column>
            </el-table-column>
            <!--<el-table-column label="当年累计受理问题线索数量"  align="center">-->
            <!--<el-table-column label="日常报送" prop="totalProblemSourceNumber" min-width="10%"  show-overflow-tooltip align="left"/>-->
            <!--<el-table-column label="季度报告" prop="totalProblemSourceNumberQ" min-width="10%"  show-overflow-tooltip align="left"/>-->
            <!--</el-table-column>-->
            <el-table-column label="当年累计受理问题线索数量"  align="center">
              <el-table-column label="日常报送" prop="totalProblemSourceNumber" width="120"  show-overflow-tooltip align="center"/>
              <el-table-column label="季度报告" prop="totalProblemSourceNumberQ" width="120" show-overflow-tooltip align="center"/>
            </el-table-column>

            <el-table-column label="上年结转问题线索数量"  align="center">
              <el-table-column label="日常报送" prop="lastYearProblemSourceNumber" width="120"  show-overflow-tooltip align="center"/>
              <el-table-column label="季度报告" prop="lastYearProblemSourceNumberQ" width="120"  show-overflow-tooltip align="center"/>
            </el-table-column>

            <el-table-column label="当年累计完成追责问题数量（件）"  align="center">
              <el-table-column label="日常报送" prop="totalCompletedProblemNumber" width="120"  show-overflow-tooltip align="center"/>
              <el-table-column label="季度报告" prop="totalCompletedProblemNumberQ" width="120"  show-overflow-tooltip align="center"/>
            </el-table-column>

            <el-table-column label="当年累计追责总人数（人）"  align="center">
              <el-table-column label="日常报送" prop="totalAccountabilityPersonNumber" width="120"  show-overflow-tooltip align="center"/>
              <el-table-column label="季度报告" prop="totalAccountabilityPersonNumberQ" width="120"  show-overflow-tooltip align="center"/>
            </el-table-column>

            <el-table-column label="当年累计追责总人次"  align="center">
              <el-table-column label="日常报送" prop="totalAccountabilityPersonTime" width="120"  show-overflow-tooltip align="center"/>
              <el-table-column label="季度报告" prop="totalAccountabilityPersonTimeQ" width="120"  show-overflow-tooltip align="center"/>
            </el-table-column>

            <el-table-column label="当年累计追责：组织处理（人次）"  align="center">
              <el-table-column label="日常报送" prop="orgHandleTime" width="120"  show-overflow-tooltip align="center"/>
              <el-table-column label="季度报告" prop="orgHandleTimeQ" width="120"  show-overflow-tooltip align="center"/>
            </el-table-column>

            <el-table-column label="当年累计追责：扣减薪酬（人次）"  align="center">
              <el-table-column label="日常报送" prop="deductionSalaryTime" width="120"  show-overflow-tooltip align="center"/>
              <el-table-column label="季度报告" prop="deductionSalaryTimeQ" width="120"  show-overflow-tooltip align="center"/>
            </el-table-column>

            <el-table-column label="当年累计追责：党纪处分（人次）"  align="center">
              <el-table-column label="日常报送" prop="partyPunishmentTime" width="120"  show-overflow-tooltip align="center"/>
              <el-table-column label="季度报告" prop="partyPunishmentTimeQ" width="120" show-overflow-tooltip align="center"/>
            </el-table-column>

            <el-table-column label="当年累计追责：移送监察机关或司法机关（人次）"  align="center">
              <el-table-column label="日常报送" prop="transferAuthorityTime" width="120"  show-overflow-tooltip align="center"/>
              <el-table-column label="季度报告" prop="transferAuthorityTimeQ" width="120"  show-overflow-tooltip align="center"/>
            </el-table-column>

            <el-table-column label="当年累计追责：其他（人次）"  align="center">
              <el-table-column label="日常报送" prop="processingOtherItem" width="120"  show-overflow-tooltip align="center"/>
              <el-table-column label="季度报告" prop="processingOtherItemQ" width="120"  show-overflow-tooltip align="center"/>
            </el-table-column>

            <el-table-column label="当年累计扣减薪酬金额（万元）"  align="center">

              <el-table-column label="日常报送" prop="totalDeductionSalary" width="120"  show-overflow-tooltip align="right">
                <template slot-scope="scope">
                  {{ scope.row.totalDeductionSalary | filterNum }}
                </template>
              </el-table-column>
              <el-table-column label="季度报告" prop="totalDeductionSalaryQ" width="120"  show-overflow-tooltip align="right">
                <template slot-scope="scope">
                  {{ scope.row.totalDeductionSalaryQ | filterNum }}
                </template>
              </el-table-column>
            </el-table-column>

            <el-table-column label="当年累计挽回资产损失（万元）"  align="center">
              <el-table-column label="日常报送" prop="totalRetrieveLossAmount" width="120"  show-overflow-tooltip align="right">
                <template slot-scope="scope">
                  {{ scope.row.totalRetrieveLossAmount | filterNum }}
                </template>
              </el-table-column>
              <el-table-column label="季度报告" prop="totalRetrieveLossAmountQ" width="120"  show-overflow-tooltip align="right">
                <template slot-scope="scope">
                  {{ scope.row.totalRetrieveLossAmountQ | filterNum }}
                </template>
              </el-table-column>
            </el-table-column>

            <el-table-column label="当年累计降低损失风险（万元）"  align="center">
              <el-table-column label="日常报送" prop="totalReduceLossRisk" width="120"  show-overflow-tooltip align="right">
                <template slot-scope="scope">
                  {{ scope.row.totalReduceLossRisk | filterNum }}
                </template>
              </el-table-column>
              <el-table-column label="季度报告" prop="totalReduceLossRiskQ" width="120"  show-overflow-tooltip align="right">
                <template slot-scope="scope">
                  {{ scope.row.totalReduceLossRiskQ | filterNum }}
                </template>
              </el-table-column>
            </el-table-column>

            <el-table-column label="当年累计制修订管理制度（项）"  align="center">
              <el-table-column label="日常报送" prop="totalPerfectSystemNumber" width="120"  show-overflow-tooltip align="center"/>
              <el-table-column label="季度报告" prop="totalPerfectSystemNumberQ" width="120"  show-overflow-tooltip align="center"/>
            </el-table-column>

            <el-table-column label="子企业管理干部"  align="center">
              <el-table-column label="日常报送" prop="subManagementNumber" width="120"  show-overflow-tooltip align="center"/>
              <el-table-column label="季度报告" prop="subManagementNumberQ" width="120"  show-overflow-tooltip align="center"/>
            </el-table-column>

            <el-table-column label="问题状态统计" prop="problemStatusCollect" width="240"  show-overflow-tooltip align="left"/>

            <!--<el-table-column label="不一致原因" prop="problemTitle" min-width="10%"  show-overflow-tooltip align="left"/>-->
          </el-table>
        </el-form>
      </BlockCard>
    </div>
    <div></div>
  </div>
</template>

<script>

import BlockCard from "@/components/BlockCard";
import {queryAllRealList,queryAllAuditList,urging,queryDataAuditInfo } from "@/api/qualityAssurance/index";
export default {
  name: "groupDetail",
  components: {
    BlockCard
  },
  props: {
    selectValue: {
      type: Object
    }
  },
  data() {
    return {
      auditInfo:{},
      tabPosition1: '1',
      tabPosition2:'1',
      loading1:false,
      loading2:false,
      rows:{},
      // 总条数
      total: 0,
      // 表格数据
      tableList1: [],
      tableList2: [],
      //查询 参数
      queryParams: {
        pageNum: 1,
        pageSize: 5
      }
    };
  },
  created() {
    // this.getArticleList();
    this.queryDataAuditInfo();
  },
  methods: {
    queryDataAuditInfo(){
      queryDataAuditInfo({
        auditYear:this.selectValue.auditYear,
        auditQuarter:this.selectValue.auditQuarter
        // auditQuarter:3
      }).then(response => {
        this.auditInfo = response.data;
        if(this.auditInfo.differentFieldList.length){
          this.queryNextList1()
        }
        this.queryNextList2()
      });
    },
    //不一致数据查询
    queryNextList1(){
      if(this.tabPosition1=='1'){//实时数据
        this.queryAllRealList('1')
      }else{//稽核数据
        this.queryAllAuditList('1')
      }
    },
    //一致数据查询
    queryNextList2(){
      if(this.tabPosition2=='1'){//实时数据
        this.queryAllRealList('0')
      }else{//稽核数据
        this.queryAllAuditList('0')
      }
    },
    /**实时数据(一致、不一致)*/
    // type：是否不一致0：否；1：是
    queryAllRealList(type) {

      if(type=='0'){//一致
        this.loading2 = true;
      }else{//不一致
        this.loading1 = true;
      }
      queryAllRealList({
      auditYear:this.selectValue.auditYear,
      auditQuarter:this.selectValue.auditQuarter,
      differentFlag:type
      }).then(
        response => {
          if(type=='0'){//一致
            this.tableList2 = response.rows;
            this.loading2 = false;
          }else{//不一致
            this.tableList1 = response.rows;
            this.loading1 = false;
          }
        }
      );
    },
    /**稽核汇总数据(一致、不一致)*/
    // type：是否不一致0：否；1：是
    queryAllAuditList(type) {
      if(type=='0'){//一致
        this.loading2 = true;
      }else{//不一致
        this.loading1 = true;
      }
      queryAllAuditList({
        auditYear:this.selectValue.auditYear,
        auditQuarter:this.selectValue.auditQuarter,
        differentFlag:type
      }).then(
        response => {
          if(type=='0'){//一致
            this.tableList2 = response.rows;
            this.loading2 = false;
          }else{//不一致
            this.tableList1 = response.rows;
            this.loading1 = false;
          }
        }
      );
    },
    /** 催办 */
    urging(id) {
      let  title = '确认催办提醒？';
      this.$modal.confirm(title).then(function() {
        return urging(id);
      }).then(() => {
        this.$modal.msgSuccess("催办提醒成功");
      }).catch(() => {});
    },
  }
}
</script>

<style scoped lang="scss">
  .group-index{
    .group-index-box{
      padding:0 20px;
      box-sizing: border-box;
      .group-header{
        display: inline-block;
        padding:7px 100px 7px 25px;
        background-image: linear-gradient(90deg, #FFE5E5 0%, #FFFFFF 100%);
        position: relative;
        font-size: 15px;
        color: #333333;
        letter-spacing: 0;
        font-weight: 500;
        &::before{
          position: absolute;
          content: "";
          left: 8px;
          top: 14px;
          width: 7px;
          height: 7px;
          border-radius: 50%;
          background: #F5222D;
        }
      }
      .group-prov{
        background: #FFFFFF;
        text-align: justify;
        box-shadow: 0px 0px 7px 0px rgba(0,0,0,0.09);
        border-radius: 2px;
        margin:12px 0;
        padding:7px 16px;
        box-sizing: border-box;
        .group-prov-box{
          display: inline;
          font-family: PingFangSC-Semibold;
          font-size: 14px;
          color: #333333;
          letter-spacing: 0;
          line-height: 24px;
          .group-prov-title{
            display: inline;
            font-weight: 600;
          }
          .group-prov-name{
            display: inline;
          }
        }
      }
    }
    .group-table{
      ::v-deep .el-radio-button--medium .el-radio-button__inner {
        padding: 6px 20px;
        margin-right:12px;
        border-left:1px solid #ddd;
        border-radius:4px;
      }
    }
  }
</style>
