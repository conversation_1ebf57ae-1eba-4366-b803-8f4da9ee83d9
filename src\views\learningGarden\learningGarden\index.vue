<template>
    <div class="learning-home grayBackground">
        <div class="learning-header">
         <div class="learning-header-bg">
           <div class="learning-header-left">
             <div  class="learning-header-tab  cursor" :class="item.value==type?'active':''" v-for="(item,index) in dict.type.MODULE" @click="selectType(item.value)" :key="index">{{item.label}}</div>
           </div>
           <div class="learning-header-center">
             <div class="learning-center-box">
               <el-input class="learning-center-input" v-model="queryParams.title" placeholder="文章标题"></el-input>
               <div  class="learning-center-btn cursor" @click="getList"><i class="el-icon-search"></i></div>
             </div>
           </div>
           <div  class="learning-header-right"></div>
         </div>
        </div>
        <div class="learning-content row">
          <div class="learning-content-left">
            <div class="learning-content-ul1"  v-loading="loading">
              <div style="height: 726px">
                <NoData v-if="dataList.length==0"></NoData>
               <div v-else>
                 <div class="learning-ul1-li" v-for="(item,index) in dataList" v-if="type=='1'" :key="index">
                   <div class="learning-ul1-li-title ovflowHidden cursor" :title="item.title" @click="learningGarden(item,1)">{{item.title}}</div>
                   <div class="learning-ul1-li-bottom">
                     <span class="learning-ul1-li-index width1">来源站点：{{item.siteCode|fromatComon(siteCodeOption1)}}</span>
                     <span class="learning-ul1-li-index width2">发布时间：{{item.publishDate}}</span>
                     <span class="learning-ul1-li-index width4 cursor"  @click="clickContextCollection(item,1,index)">
                    <i class="el-icon-star-on " :class="item.isCollection=='1'?'active':''"></i>
                    收藏
                  </span>
                   </div>
                 </div>
                 <div class="learning-ul1-li" v-for="(item,index) in dataList" v-if="type=='2'" :key="index">
                   <div class="learning-ul1-li-title ovflowHidden cursor" :title="item.title" @click="learningGarden(item,2)">{{item.title}}</div>
                   <div class="learning-ul1-li-bottom">
                     <span class="learning-ul1-li-index width1-2 ovflowHidden" :title="item.involProvName">省分：{{item.involProvName}}</span>
                     <span class="learning-ul1-li-index width2">施行时间：{{item.implementDate}}</span>
                     <span class="learning-ul1-li-index width3">制度数：{{item.lawNum}}</span>
                     <span class="learning-ul1-li-index width4 cursor"  @click="clickContextCollection(item,2,index)">
                    <i class="el-icon-star-on " :class="item.isCollection=='1'?'active':''"></i>
                    收藏
                  </span>
                   </div>
                 </div>
                 <div class="learning-ul1-li" v-for="(item,index) in dataList" v-if="type=='3'" :key="index">
                   <div class="learning-ul1-li-title ovflowHidden cursor" :title="item.informationTitle" @click="learningGarden(item,3)">{{item.informationTitle}}</div>
                   <div class="learning-ul1-li-bottom">
                     <span class="learning-ul1-li-index width1-3">信息类型：{{item.informationType | fromatComon(dict.type.INFORMATION_TYPE)}}</span>
                     <span class="learning-ul1-li-index width2">发布时间：{{item.publishTime}}</span>
                     <span class="learning-ul1-li-index width3 cursor" @click="clickContextCollection(item,3,index)">
                    <i class="el-icon-star-on " :class="item.isCollection=='1'?'active':''"></i>
                    收藏
                  </span>
                   </div>
                 </div>
               </div>
              </div>
              <pagination
                v-show="total>0"
                :total="total"
                :page-sizes='[10]'
                :page.sync="queryParams.pageNum"
                :limit.sync="queryParams.pageSize"
                @pagination="pageList"
              />
            </div>
          </div>
          <div class="learning-content-right">
            <div class="learning-right-box">
              <div class="learning-right-padding">
                <div class="learning-ul2-header">
                  <span class="learning-ul2-left">我的收藏</span>
                  <span class="learning-ul2-right cursor" @click="collectionOpen = !collectionOpen">更多</span>
                </div>
                <div class="learning-content-ul2">
                  <NoData v-if="contextCollectionList.length==0"></NoData>
                  <div v-else class="learning-ul2-li cursor" v-for="(item,index) in contextCollectionList" :key="index" @click="learningGardens(item,item.module)">
                    <div class="learning-ul2-title ovflowHidden">{{item.title}}</div>
                    <div class="learning-ul2-info">
                      <span class="learning-ul2-index">{{item.module | fromatComon(dict.type.MODULE)}}</span>
                      <span class="learning-ul2-date">收藏时间：{{item.collectionTime}}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="learning-right-box">
              <div class="learning-right-padding">
                <div class="learning-ul2-header">
                  <span class="learning-ul2-left">最近阅读</span>
                  <span class="learning-ul2-right cursor" @click="readLogOpen = !readLogOpen">更多</span>
                </div>
                <div class="learning-content-ul2">
                  <NoData v-if="readLogList.length==0"></NoData>
                  <div v-else class="learning-ul2-li cursor" v-for="(item,index) in readLogList" :key="index"   @click="learningGardens(item,item.module)">
                    <div class="learning-ul2-title ovflowHidden">{{item.title}}</div>
                    <div class="learning-ul2-info">
                      <span class="learning-ul2-index">{{item.module | fromatComon(dict.type.MODULE)}}</span>
                      <span class="learning-ul2-date">阅读时间：{{item.readTime}}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      <!-- 制度机制详情页 -->
      <lawDetail v-if="open2" v-on:closeModal="closeModal2" :id="lawId"></lawDetail>
      <!-- 信息发布详情页 -->
      <table2Add v-if="open3" editType = "view" v-on:closeModal="closeModal3"  :informationId="informationId"></table2Add>
      <!-- 我的收藏 -->
      <el-dialog title="我的收藏" :visible.sync="collectionOpen" width="80%"  append-to-body class="scrollbar" @close="collectionClose">
        <contextCollectionList :key="collectionOpen"></contextCollectionList>
      </el-dialog>

      <!-- 最近阅读 -->
      <el-dialog title="最近阅读" :visible.sync="readLogOpen" width="80%"  append-to-body class="scrollbar" @close="readLogClose">
        <readLogList :key="readLogOpen"></readLogList>
      </el-dialog>
    </div>
</template>

<script>
import { isExternalUrlOpen } from '@/utils/index'
import { getArticleListCollection,institutionalMechanism,getInformationListCollection,cancelContextCollection,contextCollection,
  readLog,getLastReadLog,getContextCollection
} from '@/api/learningGarden/index'


import contextCollectionList from '@/views/learningGarden/learningGarden/components/contextCollectionList'
import readLogList from '@/views/learningGarden/learningGarden/components/readLogList'
import {getSiteData} from "@/api/learningGarden/gardenManagement/gardenManagement";
import table2Add from "@/views/learningGarden/gardenManagement/components/table2Add";
import lawDetail from "@/views/base/basequery/law/lawDetail";
export default {
  name: "learningGarden",
  components: {contextCollectionList,readLogList,table2Add,lawDetail},
  dicts: ['MODULE','INFORMATION_TYPE'],
  data() {
    return {
      collectionOpen:false,//我的收藏
      readLogOpen:false,//浏览记录
      loading:true,
      open:false,//是否能打开外链
      type:1,//字典值
      total:0,
      open2:false,//制度机制详情页
      open3:false,//信息发布详情页
      dataList:[],
      info:{//钻取信息存放
        module:'',
        tableId:'',
        title:'',
        url:''
      },
      contextCollectionList:[],//我的收藏
      readLogList:[],//阅读记录
      isOpen:false,  // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        title:''
      },
      siteCodeOption1:[],
      informationId:'',
      lawId:'',
    };
  },
  created() {
    this.getList();
    this.getContextCollection();
    this.getLastReadLog();
    // 判断
    isExternalUrlOpen('http://www.sasac.gov.cn').then((res)=>{
      this.open = true;
    }).catch((res)=>{
    })
  },
  methods: {
    collectionClose(){
      this.getLastReadLog()
    },
    readLogClose(){
      this.getLastReadLog()
    },
    closeModal3(){
      this.open3 = false;
      this.getLastReadLog()
    },
    closeModal2(){
      this.open2 = false;
      this.getLastReadLog()
    },
    getSiteData(){
      getSiteData().then((response) => {
        if (response.code === 200) {
          let siteCodeOption = response.data
          for (let i = 0; i < siteCodeOption.length; i++){
            this.siteCodeOption1.push({label: siteCodeOption[i].siteName,value: siteCodeOption[i].siteCode})
          }
          this.getArticleListCollection()
        }else {
          this.$message.error(response.msg)
        }
      })
    },
    getList(){
      this.queryParams.pageNum = 1
      if(this.type==1){//政策文章
        this.getSiteData()

      }else if(this.type=='2'){//制度机制
        this.institutionalMechanism()
      }else if(this.type=='3'){//发布信息
        this.getInformationListCollection()
      }
    },
    pageList(){
      if(this.type==1){//政策文章
        this.getSiteData()
      }else if(this.type=='2'){//制度机制
        this.institutionalMechanism()
      }else if(this.type=='3'){//发布信息
        this.getInformationListCollection()
      }
    },
    //指标选择
    selectType(type){
      this.type = type;
      this.getList();
    },
    //政策文章
    getArticleListCollection(){
      this.loading = true;
      getArticleListCollection(this.queryParams,{title:this.queryParams.title}).then(res=>{

        this.total = res.total;
        this.dataList = res.rows;
        this.loading = false;
      })
    },

    //制度机制
    institutionalMechanism(){
      this.loading = true;
     institutionalMechanism(this.queryParams,{title:this.queryParams.title}).then(res=>{

      this.total = res.total;
      this.dataList = res.rows;
      this.loading = false;
      })
    },
    //发布信息
    getInformationListCollection(){
      this.loading = true;
      getInformationListCollection(this.queryParams,{title:this.queryParams.title}).then(res=>{

      this.total = res.total;
      this.dataList = res.rows;
      this.loading = false;
      })
    },
    //钻取
    learningGarden(item,type){
      let param = {
        module:type,
        tableId:item.id,
        title:type=='1'?item.title:type=='2'?item.title:item.informationTitle,
        url:type=='1'?item.url:''
      }
      if(type=='1'){//政策文章
       if(this.open&&item.url){
         this.openNewTab(item.url)
       }else{
         this.$message.error('该网址为外网地址，请确认是否联网。');
       }
      }
      if(type=='2'){//制度机制
          this.info = param;
          this.open2 = true;
        this.lawId = item.id;
      }
      if(type=='3'){//发布信息
        this.info = param;
        this.open3 = true;
        this.informationId = item.id;
      }
      //记录  阅读
      readLog(param).then(res=>{
        this.getLastReadLog()
      })
    },
    //收藏 与 浏览记录  钻取
    learningGardens(item,type){
      let param = {
        module:type,
        tableId:item.tableId,
        title:item.title,
        url:item.url
      }
      if(type=='1'){//政策文章
        if(this.open&&item.url){
          this.openNewTab(item.url)
        }else{
          this.$message.error('该网址为外网地址，请确认是否联网。');
        }
      }
      if(type=='2'){//制度机制
        this.info = param;
        this.open2 = true;
        this.lawId = item.tableId;
      }
      if(type=='3'){//发布信息
        this.info = param;
        this.open3 = true;
        this.informationId = item.tableId;
      }
      //记录  阅读
      readLog(param).then(res=>{
        this.getLastReadLog()
      })
    },
    //打开页签
    openNewTab (url) {
      window.open(url)
    },
    //点击收藏按钮
    clickContextCollection(item,type,index){
      let param = {
        module:type,
        tableId:item.id,
        title:type=='1'?item.title:type=='2'?item.title:item.informationTitle,
        url:type=='1'?item.url:''
      }
      if(item.isCollection=='0'){//为收藏 点击 收藏
        this.contextCollection(param,index)
      }else{//已收藏 点击 取消收藏
        this.cancelContextCollection(param,index)
      }
    },
    //收藏
    contextCollection(data,index){
    contextCollection(data).then(res=>{
      this.dataList[index].isCollection = '1'
      this.getContextCollection()
     })
    },
    //取消收藏
    cancelContextCollection(data,index){
     cancelContextCollection(data).then(res=>{
      this.dataList[index].isCollection = '0'
      this.getContextCollection()
     })
    },
    //我的收藏
    getContextCollection(){
      getContextCollection({pageNum:1,pageSize:4}).then(res=>{
      this.contextCollectionList = res.rows;
      })
    },
    //阅读记录
    getLastReadLog(){
       getLastReadLog({pageNum:1,pageSize:4}).then(res=>{
         this.readLogList = res.rows;
      })
    }
  }
};
</script>

<style scoped lang="scss">
  .learning-home{
    padding:4px;
    box-sizing: border-box;
    .learning-header{
      padding:4px;
      width: 100%;
      height:112px;
      .learning-header-bg{
        display: flex;
        align-items: center;
        justify-content: space-around;
        height: 100%;
        width: 100%;
        background: url("../../../assets/images/learningGarden.png") 50% 50% no-repeat;
        background-size: 100% 100%;
        .learning-header-left{
          display: flex;
          .learning-header-tab{
            position: relative;
            width: 96px;
            border-radius: 14px;
            text-align: center;
            height: 32px;
            line-height: 32px;
            font-family: PingFangSC-Medium;
            font-size: 15px;
            color: #FFFFFF;
            font-weight: 500;
            margin:0 10px;
            &:before{
              content: "";
              display: block;
              width: 1px;
              height: 30px;
              background-color: #fff;
              position: absolute;
              /* margin: auto; */
              /* left: 0; */
              right: -10px;
              top: 0px;
              bottom: 0;
            }
            &:last-child:before{
              width: 0px;
              height: 0px;
            }
            &.active{
              background: #FFFFFF;
              color: #F5222D;
            }
          }

        }
        .learning-header-center{
          .learning-center-box{
            display: flex;
            .learning-center-input{
              width: 370px;
              border-radius: 6px 0px 0px 6px;
              line-height: 52px;
              height: 52px;
              background-color: #fff;
              ::v-deep .el-input__inner{
                border:0;
              }
            }
            .learning-center-btn{
              display: inline-block;
              width: 75px;
              text-align: center;
              line-height: 57px;
              height: 52px;
              background: #FFC809;
              border-radius: 0px 6px 6px 0px;
              i{
                color:#fff;
                font-size: 24px;
              }
            }
          }
        }
        .learning-header-right{
          width: 340px;

        }
      }
    }
    .learning-content{
      padding:4px;
      width: 100%;
      .learning-content-left{
        width: 60%;
        float: left;
        padding: 0 4px 0 0;
        .learning-content-ul1{
          background: #fff;
          padding:10px 24px;
          box-sizing: border-box;
          height: 810px;
          .learning-ul1-li{
            border-bottom:1px solid #ECEDEF;
            padding:4px 0;
            .learning-ul1-li-title{
              height: 38px;
              line-height: 38px;
              font-family: PingFangSC-Regular;
              font-size: 16px;
              color: #333333;
              letter-spacing: 0;
              font-weight: 400;
            }
            .learning-ul1-li-bottom{
              display: flex;
              .learning-ul1-li-index{
                font-family: PingFangSC-Regular;
                font-size: 14px;
                color: #848F9E;
                letter-spacing: 0;
                line-height: 22px;
                font-weight: 400;
                margin-right:60px;
                display: flex;
                justify-items: center;
                i{
                  margin-right:6px;
                  font-size: 22px;
                  &.active{
                    color:#f5222d;
                  }
                }
                &.width1{
                  min-width: 270px;
                }
                &.width1-2{
                  min-width: 190px;
                }
                &.width1-3{
                  min-width: 160px;
                }
                &.width2{
                  min-width: 150px;
                }
                &.width3{
                  min-width:90px;
                }
                &.width4{
                  width: 60px;
                  margin-right:0;
                }
              }
            }
          }
        }
      }
      .learning-content-right{
        width: 40%;
        float: left;
        padding: 0 0 0 4px;
        .learning-right-box{
          padding-bottom: 8px;
          &:last-child{
            padding:0;
          }
          .learning-right-padding{
            height: 400px;
            background: #fff;
            padding:0 28px;
            box-sizing: border-box;
            .learning-ul2-header{
              height: 64px;
              line-height: 64px;
              display: flex;
              align-items: center;
              justify-content: space-between;
              .learning-ul2-left{
                font-family: PingFangSC-Semibold;
                font-size: 17px;
                color: #333333;
                letter-spacing: 0;
                font-weight: 600;
              }
              .learning-ul2-right{
                font-family: PingFangSC-Regular;
                font-size: 14px;
                color: #787F83;
                letter-spacing: 0;
                font-weight: 400;
              }
            }
            .learning-content-ul2{
              height: 336px;
              .learning-ul2-li{
                border: 1px solid rgba(228,228,228,1);
                border-radius: 2px;
                height: 68px;
                margin-bottom:12px;
                width: 100%;
                padding:2px 16px;
                .learning-ul2-title{
                  font-family: PingFangSC-Medium;
                  font-size: 14px;
                  color: #333333;
                  letter-spacing: 0;
                  line-height: 22px;
                  font-weight: 500;
                  padding:6px 0;
                  box-sizing: border-box;
                }
                .learning-ul2-info{
                  display: flex;
                  .learning-ul2-index{
                    background: #FFEEEE;
                    padding:0 12px;
                    height: 22px;
                    line-height: 22px;
                    text-align: center;
                    font-size: 13px;
                    color: #f5222d;
                    letter-spacing: 0;
                    font-weight: 400;
                    margin-right:12px;
                  }
                  .learning-ul2-date{
                    font-family: PingFangSC-Regular;
                    font-size: 13px;
                    color: #909499;
                    letter-spacing: 0;
                    line-height: 22px;
                    font-weight: 400;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
  /* 样式应用于屏幕宽度最大为 1400px 的情况下 */
  @media screen and (max-width: 1400px) {
    .learning-home {
      .learning-content{
        .learning-content-left{
          .learning-content-ul1{
            .learning-ul1-li{
              .learning-ul1-li-bottom{
                .learning-ul1-li-index{
                  margin-right:40px;
                  &.width4{
                    margin:0
                  }
                }
              }
            }
          }
        }
      }
    }
  }
</style>
