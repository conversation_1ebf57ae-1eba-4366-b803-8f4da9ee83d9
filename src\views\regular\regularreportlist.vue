<!--上报管理首页-->
<template>
  <div class="grayBackground padding4 regular">
    <el-row>
      <el-col :span="8">
        <BlockCard
          title="上报总览"
        >
          <el-form>
            <div class="position">
              <div class="position-select">
                <el-row>
                  <el-col :span="24">
                    <el-form-item>
                      <div class="float-right">
                        <el-radio-group v-model="timeType" @change="changeTimeType">
                          <el-radio label="0">本年</el-radio>
                          <el-radio label="1">累计</el-radio>
                        </el-radio-group>
                      </div>
                    </el-form-item>
                  </el-col>
                </el-row>
              </div>
            </div>
          </el-form>

          <div class="ry-common-card-content" style="height: 90px;">
            <div class="early-warning-data">
              <div class="early-warning-data-li ">
                <el-row>
                  <div class="ewd-li ewd-li-5">
                    <div class="ewd-li-box">
                      <el-col :span="6">
                        <div class="ewd-li-5-left">
                          <p class="ewd-li-5-top text-center ewd-li-5-num problemNums margin-t10">
                            {{reportStaticData.totalNum||0}}</p>
                          <p class="text-center">上报数</p>
                        </div>
                      </el-col>
                      <el-col :span="18">
                        <div class="ewd-li-5-right">
                          <el-row class="ewd-li-5-box">
                          <p class="ewd-li-5-percentage">定期报告</p>
                            <b class="float-left ewd-li-5-box-b">{{reportStaticData.reportTypeNum1}}</b>
                            <div class="ewd-li-5-per">
                              <span class="ewd-li-5-span ewd-li-5-span-1"
                                    :style="{ width:reportStaticData.reportTypeRate1 + '%' }"></span>
                            </div>
                            <p class="ewd-li-5-percentage">{{reportStaticData.reportTypeRate1||0.00}}%</p>
                          </el-row>
                          <el-row class="ewd-li-5-box">
                          <p class="ewd-li-5-percentage">其他报告</p>
                            <b class="float-left ewd-li-5-box-b">{{reportStaticData.reportTypeNum2}}</b>
                            <div class="ewd-li-5-per">
                              <span class="ewd-li-5-span ewd-li-5-span-1"
                                    :style="{ width:reportStaticData.reportTypeRate2 + '%' }"></span>
                            </div>
                            <p class="ewd-li-5-percentage">{{reportStaticData.reportTypeRate2||0.00}}%</p>
                          </el-row>
                        </div>
                      </el-col>
                    </div>
                  </div>
                </el-row>
              </div>
            </div>
          </div>
        </BlockCard>
        <BlockCard title="上报完成情况" >
          <el-form>
            <div class="position">
              <div id="canvas" class="canvas"></div>
              <div class="echart-text-box">
                <p class="echart-text-num">{{echartName}}</p>
                <p class="echart-text-num">{{echartPercent}}</p>
              </div>
              <div class="right-legend-box">
                <ul id="echartsList">
                  <li class="cursor" v-for="(item,index) in reportCompletedData">
                    <div class="li-1 ovflowHidden">
                      <span class="span-radius"></span>
                      <span class="question-li-text">{{item.name}}</span>
                    </div>
                    <div class="li-2 text-center">{{item.value}}</div>
                    <div class="li-3 text-right">{{item.percent}}%</div>
                  </li>
                </ul>
              </div>
            </div>
          </el-form>
        </BlockCard>
        <BlockCard title="进行中报告" height="400">
          <Jscrollbar height="100%">
          <el-form ref="elForm" size="medium" label-width="0">
            <el-row>
              <el-col :span="24">
                <ul id="rankList">
                  <li v-for="(item,index) in reportHandingData">
                    <div class="periodic-report-1 flex flex-between">
                      <div class="ovflowHidden">
                        <i class="text-red el-icon-document"></i>
                        <span>{{item.reportTitle}}</span>
                      </div>
                      <span>{{item.reportTypeName}}</span>
                    </div>
                    <el-row class="periodic-report-2 layui-row">
                      <el-col :span="8" class="text-left">
                        <span class="span-title">已完成上报单位数：</span>
                        <span class="span-value">{{item.statusNum2}}</span>
                      </el-col>
                      <el-col :span="8" class="text-center">
                        <span class="span-title">进行中上报单位数：</span>
                        <span class="span-value">{{item.statusNum1}}</span>
                      </el-col>
                    </el-row>
                  </li>
                </ul>
              </el-col>
            </el-row>
          </el-form>
          </Jscrollbar>
        </BlockCard>
      </el-col>
      <el-col :span="16">
        <BlockCard
          title="上报问题列表"
          :height="850"
        >

          <el-form :model="queryParams" size="medium" ref="queryForm"
                   label-width="105px">
            <el-row>
              <el-col :span="8">
                <el-form-item label="上报年度">
                  <el-date-picker :style="{width: '100%'}" v-model="queryParams.reportYear" value-format="yyyy"
                    type="year" placeholder="上报年度">
                  </el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="上报状态">
                  <el-select v-model="queryParams.status"
                             :style="{width: '100%'}"
                             clearable
                             filterable>
                    <el-option :label="item.dictLabel" :value="item.dictValue"
                               v-for="(item,index) in statusList"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="报告类型">
                  <el-select v-model="queryParams.reportType"
                             :style="{width: '100%'}"
                             clearable
                             filterable>
                    <el-option :label="item.dictLabel" :value="item.dictValue"
                               v-for="(item,index) in reportTypeList"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
          <el-row>
            <div class="float-left">
              <el-radio-group v-model="queryParams.statusFlag" style="margin-bottom: 30px;" size="small" @change="ViolRegularList">
                <el-radio-button label="1">已上报</el-radio-button>
                <el-radio-button label="0">草稿中</el-radio-button>
              </el-radio-group>
            </div>
            <div class="float-right">
              <el-button
                type="primary"

                icon="el-icon-plus"
                size="mini"
                @click="toAddRegular"
                v-if="orgGrade !='A'"
              >新增</el-button>
              <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
              <el-button icon="el-icon-refresh" size="mini"  @click="resetQuery">重置</el-button>
            </div>
          </el-row>

          <el-form :style="{height: 'calc(100% - 200px)'}">
            <el-table v-loading="loading" :data="tableList" height="100%">
              <el-table-column label="" type="index" width="50" align="center">
                <template slot-scope="scope">
                  <table-index
                    :index="scope.$index"
                    :pageNum="queryParams.pageNum"
                    :pageSize="queryParams.pageSize"
                  />
                </template>
              </el-table-column>
              <el-table-column label="标题" prop="reportTitle" width="150" show-overflow-tooltip/>
              <el-table-column label="上报年度" prop="reportYear" width="150" align="center"/>
              <el-table-column label="上报区间" prop="reportTime" width="150" align="center"/>
              <el-table-column label="报告类型" prop="reportTypeName" width="150" align="center"/>
              <el-table-column label="上报发起时间"prop="startReportTime" width="280" align="center"/>
              <el-table-column label="上报截止日期" prop="reportCloseTime" width="280" align="center"/>
              <el-table-column label="上报单位个数" prop="reportUnitNum" width="200" align="center"/>
              <el-table-column label="上报状态" prop="statusName" width="150" align="center"/>
              <el-table-column label="经办人" prop="createUserName" width="150" align="center"/>
              <el-table-column label="操作" fixed="right" width="150" align="center"
                               class-name="small-padding fixed-width">
                <template slot-scope="scope">
                  <el-button
                    v-if="scope.row.status === '1' || scope.row.status === '2'"
                    size="mini"
                    type="text"
                    title="查看"
                    icon="el-icon-search"
                    @click="handleDetail(scope.row)"
                  >
                  </el-button>
                  <el-button
                    v-if="scope.row.status === '0'"
                    size="mini"
                    type="text"
                    title="编辑"
                    icon="el-icon-edit"
                    @click="toEditRegular(scope.row)"
                  >
                  </el-button>
                  <el-button
                    v-if="scope.row.status === '0'"
                    size="mini"
                    type="text"
                    title="删除"
                    icon="el-icon-delete"
                    @click="toDelRegular(scope.row)"
                  >
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-form>
          <pagination
            v-show="total>0"
            :total="total"
            :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize"
            @pagination="ViolRegularList"
          />
        </BlockCard>
      </el-col>
    </el-row>

    <!--新增-->
    <addRegular
      :key="index"
      ref="add"
      v-on:ViolRegularList="refreshList"
      :title="openTitle"
    ></addRegular>
    <!--编辑-->
    <editRegular
      :key="index"
      ref="edit"
      v-on:ViolRegularList="refreshList"
      :regularReportId="regularReportId"
      :title="openTitle"
    ></editRegular>
    <!--查看-->
    <showRegular
      :key="index"
      ref="showInfo"
      :status="status"
      v-on:ViolRegularList="refreshList"
      :regularReportId="regularReportId"
      :title="openTitle"
    ></showRegular>

  </div>
</template>

<script>
  import BlockCard from '@/components/BlockCard';
  import echarts from 'echarts';//图表
  import {
    selectReportStatistical
    ,selectReportCompletionStatistical
    ,selectReportHandingStatistical
    ,violRegularList
    ,regularStatus
    ,regularReportType
    ,deleteRegularReport
  } from "@/api/regular/regularreportlist.js";
  import addRegular from './add/addAndEditRegular';
  import editRegular from './add/addAndEditRegular';
  import showRegular from './details/regularDetail';


  export default {
    name: "report",
    components: {
      BlockCard
      ,addRegular
      ,editRegular
      ,showRegular
    },
    dicts: [],
    data() {
      return {
        rows:{},
        echartName: '',
        echartPercent: '',
        chart: null,
        status:'',
        orderFlag: '1',//排序
        loading: false,
        //遮罩层
        visible: false,
        // 总条数
        total: 0,
        // 表格数据
        tableList: [],
        statusList:[],//上报状态下拉
        reportTypeList: [],//报告类型下拉
        reportStaticData:{},//上报总览
        reportCompletedData:{},//已完成情况
        reportHandingData:{},//进行中的报告
        timeType:'0',
        thisYear:new Date().getFullYear(),//本年、累计
        //上报列表查询 参数
        queryParams: {
          pageNum: 1,
          pageSize: 10,
          reportYear: '',
          status: '',
          reportType: '',
          statusFlag : '1'//已上报：1；草稿中：0
        },
        regularReportId:''
        ,index:0
        ,openTitle:'编辑上报',
        orgGrade:''
      };
    },
    created() {
      this.orgGrade = this.$store.getters.orgGrade
      this.refreshList();
    },
    mounted() {
    },
    filters: {},
    methods: {
      close(){
        this.visible=false;
      },
      //加载数据
      refreshList(){
        //左侧
        this.SelectReportStatistical();
        this.SelectReportCompletionStatistical();
        this.SelectReportHandingStatistical();
        //列表
        this.ViolRegularList();
        this.RegularStatus();
        this.RegularReportType();
      },
      //选择本年、累计
      changeTimeType(value){
        if(value === '0'){
          this.thisYear = new Date().getFullYear();
        }else{
          this.thisYear = '';
        }
        //刷新左侧
        this.SelectReportStatistical();
        this.SelectReportCompletionStatistical();
        this.SelectReportHandingStatistical();
      },
      //上报总览
      SelectReportStatistical(){
        this.loading = true;
        selectReportStatistical(this.thisYear).then(
          response => {
            this.reportStaticData = response.data;
            this.loading = false;
          }
        )
      },
      // 自适应字体大小
      fontSizeFun(res) {
        let docEl = document.documentElement,
          clientWidth =
            window.innerWidth ||
            document.documentElement.clientWidth ||
            document.body.clientWidth;
        if (!clientWidth) return;
        let fontSize = 100 * (clientWidth / 1920);
        return res * fontSize;
      },
      //echarts上报完成情况
      SelectReportCompletionStatistical(){
        this.loading = true;
        selectReportCompletionStatistical(this.thisYear).then(
          response => {
            this.loading = false;

            this.echartName = response.data[0].name;
            this.echartPercent = response.data[0].percent+'%';
            this.reportCompletedData = response.data;
            const chartDom = document.getElementById("canvas");
            const commonMyChart = echarts.init(chartDom);
            const colorlist = [
              "#ffc069",
              "#69c0ff",
            ];
            let option = {
              color: colorlist,
              tooltip: {
                trigger: "item",
              },
              grid: {
                top: this.fontSizeFun(0.20)
              },
              calculable: true,
              series: [
                {
                  name: "上报完成情况",
                  type: "pie",
                  radius: ["50%", "75%"], //环形饼状图
                  center: ["23%", "50%"],
                  label: {
                    show: false,
                  },
                  data: this.reportCompletedData,
                },
              ],
            };
            option && commonMyChart.setOption(option);
            commonMyChart.on('click', (e) => {
              this.echartName = e.name;
              this.echartPercent = e.percent + '%';
            })
          }
        )
      },
      //进行中报告
      SelectReportHandingStatistical(){
        this.loading = true;
        selectReportHandingStatistical(this.thisYear).then(
          response => {
            this.reportHandingData = response.data;
            this.loading = false;
          }
        )
      },
      /**查询上报问题列表*/
      ViolRegularList() {
        this.loading = true;
        violRegularList(this.queryParams).then(
          response => {
            this.tableList = response.rows;
            this.total = response.total;
            this.loading = false;
          }
        );
      },
      //上报状态
      RegularStatus() {
        regularStatus().then(
          response => {
            this.statusList = response.data;
          }
        );
      },
      //报告类型
      RegularReportType() {
        regularReportType().then(
          response => {
            this.reportTypeList = response.data;
          }
        );
      },
      /** 搜索按钮操作*/
      handleQuery() {
        this.queryParams.pageNum = 1;
        this.ViolRegularList();
      },
      /**重置按钮操作*/
      resetQuery() {
        this.queryParams = {
          pageNum: 1,
          pageSize: 10,
          reportYear: '',
          status: '',
          reportType:'',
          statusFlag:this.queryParams.statusFlag
        };
        this.ViolRegularList();
      },
      //新增上报
      toAddRegular(){
        this.index++;
        this.openTitle = "新增上报";
        this.$nextTick(()=> {
          this.$refs.add.show();
        })
      },
      //编辑
      toEditRegular(row){
        this.regularReportId = row.id;
        this.openTitle = "编辑上报";
        this.index++;
        this.$nextTick(()=>{
            this.$refs.edit.show();
          }
        )
      },
      //删除
      toDelRegular(row){
        this.regularReportId = row.id;
        this.$modal.confirm('是否删除该条上报吗？').then( ()=> {
          deleteRegularReport(row.id).then(
            response => {
              console.info(response.code === 200);
              if(response.code === 200){
                this.$modal.msgSuccess(response.msg);
                this.queryParams.pageNum = 1;
                this.ViolRegularList();
              }else{
                this.$modal.msgError(response.msg);
              }
            }
          );
        }).catch(function () {
          this.$modal.msgError("操作失败，请联系管理员");
        });
      },
      /** 查看操作 */
      handleDetail(row) {
        this.regularReportId = row.id;
        this.status = row.status;
        this.openTitle = "上报详情";
        this.index++;
        this.$nextTick(()=>{
            this.$refs.showInfo.show();
          }
        )
      },

    }
  };
</script>
<style rel="stylesheet/scss"  lang="scss">
  .regular{
    .el-dialog__body{
      height: 71vh;
    }

    .scope-chao-shi{
      margin-right:5px;
      margin-bottom: 0;
    }
    .canvas {
      width: 100%;
      height: 194px;
      z-index: 10;
    }

    .echart-text-box {
      position: absolute;
      width: calc((100% - 40px) / 2);
      height: 100px;
      top: 64px;
      z-index: 9;
      text-align: center;
      p {
        text-align: center;
        line-height: 32px;
      }
      .echart-text-p {
        font-size: 12px;
      }
    }

    .early-warning-data {
      margin-bottom: 10px;
    }

    .early-warning-data-li {
      width: 100%;
      margin: 4px 0;
    }

    .ewd-li {
      float: left;
      padding: 4px;
      box-sizing: border-box;
      height: 80px;
    }

    .ewd-li-box {
      box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
      border-radius: 4px;
      border: solid 1px #eeeeee;
      height: 100%;
      padding: 8px;
      box-sizing: border-box;
    }

    .ewd-li-1 {
      width: 83px;
    }

    .ewd-li-1 .ewd-li-1-circular {
      width: 68px;
      height: 68px;
      display: inline-block;
      line-height: 68px;
      border-radius: 50%;
      text-align: center;
      font-size: 18px;
      color: #ffffff;
      margin-top: 8px;
    }

    .circular-1 {
      background-image: linear-gradient(-30deg,
        #ff4d4e 0%,
        #ffa39d 100%);
    }

    .circular-2 {
      background-image: linear-gradient(-30deg,
        #40a9ff 0%,
        #91d5ff 100%);
    }

    .ewd-li-right {
      float: right;
      width: calc(100% - 83px);
    }

    .ewd-li-2 {
      width: 30%;
    }

    .ewd-li-3 {
      width: 50%;
    }

    .ewd-li-4 {
      width: 20%;
    }

    .ewd-li-5 {
      width: 100%;
      padding: 0;
    }

    .ewd-li-6 {
      padding: 0;
    }

    .ewd-li-5 .ewd-li-box, .ewd-li-6 .ewd-li-box {
      padding: 10px 20px 10px 0px;
    }

    .ewd-li-5-left {
      height: 100%;
      border-right: 1px solid #d9d9d9;
    }

    .ewd-li-5-right {
      height: 100%;
      padding-left: 20px;
      box-sizing: border-box;
    }

    .ewd-li-5-top {
      width: 100%;
      font-size: 14px;
      line-height: 24px;
      color: #73777a;
      margin-bottom:0;
    }

    .ewd-li-5-num {
      font-size: 20px;
      line-height: 24px;
      color: #181818;
    }

    .ewd-li-6-num {
      font-size: 18px;
      line-height: 24px;
      color: #333333;
    }

    .ewd-li-6-title {
      width: 100%;
      font-size: 14px;
      line-height: 24px;
      color: #898D8F;
      margin-bottom: 10px;
    }

    .ewd-li-5-box-img {
      margin-right: 10px;
    }

    .ewd-li-5-box-b {
      padding-left: 10px;
      font-size: 18px;
      margin-right:0;
      width: 50px;
    }

    .ewd-li-top {
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;
      flex-wrap: wrap;
      height: 40px;
    }

    .ewd-li-5-box {
      margin:5px 0;
      line-height: 24px;
    }

    .ewd-li-5-percentage {
      float: left;
      font-size: 14px;
    }

    .ewd-li-5-per {
      float: left;
      width: 26%;
      margin: 3px 10px 0 10px;
      height: 18px;
      background-color: #f5f5f5;
      border-radius: 2px;
    }

    .ewd-li-5-span {
      height: 18px;
      display: inline-block;
      border-radius: 2px;
    }

    .ewd-li-5-span-1 {
      background-color: #ff4d4e;
    }

    .ewd-li-5-span-2 {
      background-color: #ffa940;
    }

    .ewd-li-5-span-3 {
      background-color: #ff8787;
    }

    .ewd-li-5-span-4 {
      background-color: #8da2fe;
    }

    .ewd-li-top .ewd-li-top-left .iconfont {
      font-size: 20px;
    }

    .ewd-li-top-left {
      display: flex;
      align-items: center;
    }

    .nodara {
      width: 100%;
      height: 100%;
      min-height: 130px;
      color: #b5b5b5;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .ewd-li-top .ewd-li-top-left .icon-processed {
      font-size: 16px;
    }

    .ewd-li-top .ewd-li-top-left span {
      font-size: 14px;
      color: #000;
      margin-left: 4px;
      line-height: 14px;
    }

    .ewm-conter-li-box {
      display: flex;
      border-radius: 2px;
      overflow: hidden;
    }

    .ewd-li-box .ry-form-radio, .early-warning-model .ry-form-radio {
      margin: 0;
    }

    .ry-form-radioed > i, .ry-form-radio > i:hover {
      color: #f5222d
    }

    .right-btn-box .ry-form-radio {
      margin-top: 0;
      padding: 0;
    }

    .ewd-li-top-right span {
      font-size: 22px;
      font-weight: bold;
      color: #333333;
    }

    .ewd-li-bottom {
      width: 100%;
      display: flex;
      height: 28px;
      justify-content: space-between;
      align-items: center;
    }

    .ewd-li-bottom .ewd-li-bottom-li-label {
      font-size: 12px;
      color: #888888;
    }

    .ewd-li-bottom .ewd-li-bottom-li-num {
      font-size: 14px;
      color: #f5212d;
    }

    .ewd-li-bottom .ewd-li-bottom-li-model {
      font-size: 12px;
      background-color: #eeeeee;
      border-radius: 4px;
      border: solid 1px #cccccc;
      color: #888888;
      padding: 2px 12px;
    }

    .ewd-li-bottom .ewd-li-bottom-li-value {
      font-size: 14px;
      color: #03ac2b;
    }

    .ry-quarantine {
      width: 100%;
      height: 13px;
      background-color: #eeeeee;
    }

    .early-warning-model-box {
      width: 100%;
      height: 466px;
      position: relative;
    }

    .early-warning-model-list {
      width: 100%;
      height: 466px;
      position: relative;
    }

    .early-warning-model-box:before {
      position: absolute;
      content: '\7cbe\51c6\5ea6';
      height: calc(100% - 20px);
      left: 50%;
      top: 10px;
      width: 1px;
      background: #cccccc;
      display: flex;
      justify-content: center;
      padding-top: 30px;
      box-sizing: border-box;
      color: #888888;
    }

    .early-warning-model-box:after {
      position: absolute;
      content: "\91cd\8981\6027";
      width: calc(100% - 20px);
      top: 50%;
      left: 10px;
      height: 1px;
      background: #cccccc;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      padding-right: 30px;
      box-sizing: border-box;
      color: #888888;
    }

    .ewm-box-ul {
    }

    .ewm-box-ul-li {
      height: 233px;
      box-sizing: border-box;
      position: relative;
    }

    .ewm-box-ul-li1 {
      padding: 20px 0 20px 20px;
    }

    .ewm-box-ul-li2 {
      padding: 20px 10px 20px 0;
      position: relative;
    }

    .ewm-box-ul-li3 {
      padding: 35px 0 20px 20px;
    }

    .ewm-box-ul-li4 {
      padding: 35px 10px 20px 0;
    }

    .ewm-box-ul-li1:after {
      content: "";
      position: absolute;
      left: -7px;
      top: 5px;
      width: 0;
      height: 0;
      border-width: 0 8px 8px;
      border-style: solid;
      border-color: transparent transparent #888888;
    }

    .ewm-box-ul-li2:after {
      content: "";
      width: 8px;
      height: 8px;
      background: #fff;
      display: inline-block;
      position: absolute;
      left: 0;
      bottom: -5px;
      border-radius: 50%;
      border: 1px solid #888888;
    }

    .ewm-box-ul-li3:after {
      content: "";
      position: absolute;
      right: -6px;
      top: -7px;
      width: 0;
      height: 0;
      border-width: 8px 8px 8px;
      border-style: solid;
      border-color: transparent transparent transparent #888888;
    }

    .ewm-box-ul-li4:after {
      content: "";
      width: 8px;
      height: 8px;
      background: #fff;
      display: inline-block;
      position: absolute;
      right: -5px;
      bottom: 0;
      border-radius: 50%;
      border: 1px solid #888888;
    }

    .ewm-conter-list {
      height: 180px;
      overflow: auto;
      padding-right: 10px;
    }

    .ewm-conter-li {
      width: 100%;
      height: 20px;
      margin: 10px 0;
      cursor: pointer;
    }

    .ewm-conter-li.active .ewm-conter-li-title {
      color: #f5222d;
    }

    .ewm-conter-li-title {
      padding: 0;
      float: left;
      line-height: 20px;
      width: 32%;
      font-size: 13px;
    }

    .ewm-conter-li-right {
      float: right;
      width: 68%;
    }

    .ewm-conter-li-data {
      width: 46px;
      float: left;
      line-height: 20px;
      padding-left: 2px;
      box-sizing: border-box;
    }

    .ewm-conter-li-data .iconfont {
      font-size: 14px;
      vertical-align: middle;
    }

    .ewm-conter-li-num {
      font-size: 14px;
      color: #333333;
      vertical-align: top;
    }

    .ewm-conter-li-speed {
      float: right;
      width: calc(100% - 46px);
    }

    .ewm-conter-li-value {
      box-sizing: border-box;
      float: left;
      height: 20px;
      color: #ffffff;
      font-size: 14px;
      line-height: 20px;
    }

    /*******************  */
    .ewm-conter-li-value1 {
      text-align: center;
      background-image: linear-gradient(90deg,
        rgba(3, 172, 43, 0.8) 0%,
        rgba(3, 172, 43, 0.3) 100%);
      border-radius: 2px 0px 0px 2px;
    }

    .ewm-conter-li-value2 {
      text-align: center;
      background-image: linear-gradient(90deg,
        rgba(250, 139, 22, 0.8) 0%,
        rgba(250, 139, 22, 0.3) 100%);
      border-radius: 0px 2px 2px 0px;
    }

    .drill-model-list {
    }

    .drill-model-li {
      display: flex;
      align-items: center;
      height: 38px;
    }

    .drill-model-text {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 40px;
    }

    .drill-model-span1 {
      color: #73777a;
    }

    .drill-model-span2 {
      color: #181818;
      margin-right: 8px;
    }

    .ewm-conter-li-values1 {
      text-align: left;
      background: #ffb180;
      padding-left: 6px;
    }

    .ewm-conter-li-values2 {
      padding-left: 8px;
      text-align: left;
      background: #5ad8a6;
    }

    /*******************  */
    .ewm-conter-fixed {
      position: absolute;
    }

    .position-select {
      position: absolute;
      width: 100%;
      text-align: right;
      top: -54px;
    }

    .ewm-conter-fixed-title {
      width: 24px;
      height: 24px;
      line-height: 24px;
      text-align: center;
      background-color: #555555;
      border: solid 2px #cccccc;
      display: inline-block;
      border-radius: 50%;
      font-size: 14px;
      color: #ffffff;
      position: relative;
      z-index: 999;
    }

    .ewm-conter-fixed:hover .ewm-conter-fixed-data {
      display: inline-block;
    }

    .ewm-conter-fixed-data {
      display: none;
      height: 20px;
      background-color: #888888;
      border-radius: 10px 9px 9px 10px;
      border: solid 1px #cccccc;
      width: 110px;
      text-align: center;
      position: absolute;
      right: 15px;
      top: 4px;
      z-index: 0;
      line-height: 20px;
    }

    .ewm-conter-fixed1 {
      left: 10px;
      bottom: 10px;
    }

    .ewm-conter-fixed1 .ewm-conter-fixed-data {
      left: 15px;
      top: 4px;
    }

    .ewm-conter-fixed2 {
      right: 10px;
      bottom: 10px;
    }

    .ewm-conter-fixed2 .ewm-conter-fixed-data {
      right: 15px;
      top: 4px;
    }

    .ewm-conter-fixed3 {
      left: 10px;
      top: 10px;
    }

    .ewm-conter-fixed3 .ewm-conter-fixed-data {
      left: 15px;
      top: 4px;
    }

    .ewm-conter-fixed4 {
      right: 10px;
      top: 10px;
    }

    .ewm-conter-fixed4 .ewm-conter-fixed-data {
      right: 15px;
      top: 4px;
    }

    .ewm-conter-fixed-data .iconfont {
      color: #fff;
      font-size: 10px;
      margin-left: 5px;
    }

    .right-btn-box .ry-form-switch {
      margin-top: -2px;
    }

    .ewm-conter-fixed-data span {
      color: #fff;
    }



    .right-legend-box {
      width: calc(50% - 20px);
      position: absolute;
      right: 20px;
      top: 0;
      height: 100%;
      overflow: auto;
      display: flex;
      align-items: center;
      z-index: 10;
    }

    .right-legend-box ul {
      width: 100%;
    }

    .right-legend-box li {
      width: 100%;
      display: inline-block;
      line-height: 24px;
      color: #73777a;
    }

    .right-legend-box li .li-1 {
      float: left;
      width: 40%;
    }

    .right-legend-box li .li-1 .span-radius {
      display: inline-block;
      width: 8px;
      height: 8px;
      background-color: #ffb180;
      border-radius: 50%;
      margin-right: 6px;
    }

    .right-legend-box li .li-2 {
      float: left;
      width: 30%;
    }

    .right-legend-box li .li-3 {
      float: left;
      width: 30%;
    }
    .cursor{
      cursor: pointer;
    }

    .periodic-report-1, .periodic-report-2 {
      line-height: 32px;
    }

    .periodic-report-2 .span-title {
      color: #A8A4A4;
    }

    .flex {
      display: flex;
      align-items: center;
    }

    .flex-between {
      justify-content: space-between;
    }
  }
</style>







