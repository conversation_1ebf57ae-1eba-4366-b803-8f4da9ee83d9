<!--规章制度编辑-->
<template>
  <el-container>
    <el-dialog  :visible.sync="visible" width="90%" append-to-body  @close="close" title="规章制度">
      <div style="height:calc(70vh - 100px)">
      <el-table
        border
        :data="tableList"
        ref="table"
        height="100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column
          fixed
          align="center"
          type="selection"
          width="40">
        </el-table-column>
        <el-table-column
          fixed
          align="center"
          label="序号"
          type="index"
          width="50">
        </el-table-column>
        <el-table-column label="中央企业名称" prop="groupName" width="250"/>
        <el-table-column label="集团简称" prop="groupSortName" width="200"/>
        <el-table-column label="统一社会信用代码" prop="uscCode" width="200"/>
        <el-table-column label="标题" prop="title" width="250"/>
        <el-table-column label="文号" prop="issueCode" width="250" align="center"/>
        <el-table-column label="制度类型" prop="classifyText" width="150" align="center"/>
        <el-table-column label="印发日期" prop="publishDate" :formatter="dateFormat1" width="150" align="center"/>
        <el-table-column label="施行日期" prop="implementDate" :formatter="dateFormat2" width="150" align="center"/>
        <el-table-column label="类别" prop="classifyText" width="150" align="center"/>
        <el-table-column label="制度文件" prop="personPostalCode" align="center" width="100">
          <template slot-scope="scope">
          <span class="text-red cursor underline" @click="fileDialog( scope.row.files)">
            {{ scope.row.files.length?scope.row.files.length:0}}
          </span>
          </template>
        </el-table-column>
        <el-table-column label="状态" prop="del" width="140" fixed="right"
                         align="center"
        >
          <template slot-scope="scope">
            <span v-if="scope.row.del=='2'">新增</span>
            <div v-else>
              <el-select v-model="scope.row.del"
                         :style="{width: '100%'}"
              >
                <el-option label="编辑" key="3" :value="3"></el-option>
                <el-option label="删除" key="1" :value="1"></el-option>
              </el-select>
            </div>
          </template>
        </el-table-column>
      </el-table>
      </div>
      <div slot="footer">
        <el-button size="mini" @click="close()">取消</el-button>
        <el-button size="mini" type="primary" @click="save()">保存</el-button>
      </div>
    </el-dialog>
    <el-dialog  :visible.sync="visibleFile" width="800px"  append-to-body  @close="closeFile" title="附件列表">
      <el-table
        border
        :data="fileList"
        ref="table2"
        height="100%"
      >
        <el-table-column
          fixed
          align="center"
          label="序号"
          type="index"
          width="50">
        </el-table-column>
        <el-table-column label="文档名称" prop="fileName" width="225"/>
        <el-table-column label="上传时间" prop="time" :formatter="dateFormat" width="225" align="center"/>
        <el-table-column label="文档类型" prop="fileDocumentType" width="100"/>
        <el-table-column label="操作" prop="del" width="150" fixed="right"
                         align="center"
        >
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-bottom"
              title="下载"
              @click="downloadFile(scope.row)"
            >
            </el-button>
            <!-- <el-button
              size="mini"
              type="text"
              icon="el-icon-search"
            >预览
            </el-button> -->
          </template>
        </el-table-column>
      </el-table>
      <div slot="footer">
        <el-button size="mini" @click="closeFile()">取消</el-button>
      </div>
    </el-dialog>
  </el-container>
</template>

<script>
  import {getReportLawAll, saveReportLaw} from "@/api/sasac/reportManagement/edit/detail/index";
  import moment from "moment";

  export default {
    name: "rules",
    props: {
      problemId: {
        type: String
      }
    },
    data() {
      return {
        visibleFile:false,
        fileList:[],
        hasSelectList:[],//选中的值
        tableList:[],
        visible:false,
        commitFlag: 0,
      };
    },
    created() {
    },
    methods: {
      /**查询规章制度*/
      onShow() {
        this.visible = true;
        //this.loading = true;
        //接口：/colligate/baseInfo/report/getReportLawAll
        getReportLawAll({reportId: this.problemId}).then(
          response => {
            this.tableList = response.data;
            let tableChecked = response.data;
            this.hasSelectList = [];
            this.$nextTick(()=> {
              tableChecked.forEach(row => {
                if (row.checked) {
                  this.hasSelectList.push(row);
                  this.$refs.table.toggleRowSelection(row, true);
                }
              });
            });
            // this.loading = false;
          }
        );
      },
      //关闭弹框
      close(){
        this.visible = false;
        this.$emit('editClose', this.commitFlag);
        this.commitFlag = 0;
      },
      //弹出附件
      fileDialog(files){
        this.fileList = files;
        this.$nextTick(()=>{
          this.visibleFile=true;
        })
      },
      //关闭附件
      closeFile(){
        this.visibleFile=false;
      },
      //选中的值
      handleSelectionChange(val){
        let selectList = [];
        val.forEach(function (item) {
          selectList.push(item)
        });
        this.hasSelectList = selectList;
      },
      /** 下载附件 */
      downloadFile(row) {
        this.download('/sys/documentTemplate/downloadByAttachmentId', { id: row.id }, row.fileName)
      },
      /**保存或提交*/
      save(){
        this.commitFlag = 1;
        const params = {
          reportId:this.problemId,
          lawList:this.hasSelectList
        };
        //接口：/baseInfo/report/saveReportLaw
        saveReportLaw(params).then(
          response => {
            if(response.code === 200){
              this.$modal.msgSuccess('保存成功！');
              this.close();
            }else{
              this.$message.error(response.msg);
            }
          }
        );
      },
      /*日期处理*/
      dateFormat:function(row){
        if(row.time === undefined){
          return ''
        }
        return moment(row.time).format("YYYY-MM-DD HH:mm:ss")
      },
      dateFormat1:function(row){
        if(row.publishDate === undefined){
          return ''
        }
        return moment(row.publishDate).format("YYYY-MM-DD")
      },
      dateFormat2:function(row){
        if(row.implementDate === undefined){
          return ''
        }
        return moment(row.implementDate).format("YYYY-MM-DD")
      },
    }
  };
</script>

<style>
  .text-red {
    color: #f5222d;
  }

  .icon-orange {
    color: #fa8b16;
  }
</style>
