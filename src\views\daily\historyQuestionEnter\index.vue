<!--日常问题录入-->
<template>
  <div class="app-container app-report">
    <el-form :model="queryParams" ref="queryForm" id="queryParams" v-show="showSearch" :inline="true" label-width="120px">
      <el-form-item label="问题涉及单位">
        <el-input v-model="queryParams.involCompanyName" placeholder="问题涉及单位"  :style="{width: '100%'}">
        </el-input>
      </el-form-item>
      <el-form-item label="受理日期" prop="queryParams.acceptTime">
        <el-date-picker v-model="queryParams.acceptTime" format="yyyy-MM-dd" value-format="yyyy-MM-dd" placeholder="受理日期" clearable></el-date-picker>
      </el-form-item>
      <el-form-item label="违规事项">
        <el-input v-model="queryParams.problemTitle" placeholder="违规事项"  :style="{width: '100%'}">
        </el-input>
      </el-form-item>
        <el-form-item label="问题状态">
          <el-select
            v-model="queryParams.inclusionPhases"
            :style="{width: '100%'}"
            clearable
            multiple
          >
            <el-option label="请选择" value="">--请选择--</el-option>
            <el-option
              v-for="(item,index) in dailyStatusList"
              :key="index"
              :label="item.dictLabel"
              :value="item.dictValue"
            />
          </el-select>
        </el-form-item>
      <div class="float-right">
        <el-form-item>
          <el-button
            type="primary"

            icon="el-icon-plus"
            size="mini"
            @click="handleAdd"
          >新增</el-button>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
      </div>

    </el-form>
    <el-form   style="height: calc(100vh - 335px)">
      <el-table border ref="table" v-loading="loading" :data="tableList"  height="100%">
        <el-table-column label="序号" type="index" min-width="5%" align="center" >
          <template slot-scope="scope">
            <table-index
              :index="scope.$index"
              :page-num="queryParams.pageNum"
              :page-size="queryParams.pageSize"
            />
          </template>
        </el-table-column>
        <el-table-column label="违规事项" prop="problemTitle" min-width="30%"  show-overflow-tooltip/>
        <el-table-column label="问题涉及单位" prop="involCompanyNames" show-overflow-tooltip  min-width="20%"/>
        <el-table-column label="问题线索来源" prop="problemSourceName" show-overflow-tooltip  min-width="15%"/>
        <el-table-column label="关联专项台账" prop="problemAudit"  min-width="15%">
          <template slot-scope="scope">
            <a @click="relationLedger(scope.row.relationId)" :title="scope.row.problemAudit" class="table-btn ovflowHidden" style='color: #c20000;'>
              {{ scope.row.problemAudit }}
            </a>
          </template>
        </el-table-column>
        <el-table-column label="发现时间" prop="findTime" align="center"  show-overflow-tooltip min-width="15%"/>
        <el-table-column label="受理日期" prop="acceptTime" align="center" show-overflow-tooltip min-width="15%"/>
        <el-table-column label="问题状态" prop="statusName" align="center"  show-overflow-tooltip min-width="15%"/>
        <el-table-column label="操作"  min-width="20%"  align="center" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button
              size="mini"
              v-preventReClick
              type="text"
              icon="el-icon-edit"
              title="编辑"
              @click="handleDetail(scope.row,1)"
              v-if="scope.row.status == 'T' || scope.row.status < 8"
            >
            </el-button>
            <el-button
              size="mini"
              v-preventReClick
              type="text"
              icon="el-icon-search"
              title="查看"
              @click="detail(scope.row)"
            ></el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDel(scope.row)"
              title="删除"
              v-if="scope.row.status == 'T' || scope.row.status < 8"
            ></el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-form>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="ViolDailyDraftList"
    />
    <!--填报-->
    <dailyBox
      :key="problemId"
      :id="problemId"
      :title="title"
      :relevantTableName="relevantTableName"
      :relevantTableId="relevantTableId"
      ref="accept"
      @close="ViolDailyDraftList"
    >
    </dailyBox>
    <!--台账攥取-->
    <RelationUnSpLedger
      ref="relation"
      :key="index"
      :relationId="relationId"
      :queryType="queryType"
    ></RelationUnSpLedger>
<!--   查看-->
    <el-dialog v-bind="$attrs" :visible.sync="visible" width="90%" :title="'日常问题-'+rows.problemTitle">
      <Details
        v-if="rows.id"
        :key="rows.id"
        :select-value="rows"
        :active-name="activeName"
        proc-ins-id=""
      />
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="close">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {violDailyDraftList,violDailyDraftDelete,insertDailyProblemInfo} from "@/api/daily/enter";
import {dailyStatus} from '@/api/daily/index'
import {queryNewDailyId} from "@/api/daily/historyQuestionEnter/index";
 import dailyBox from '@/views/daily/historyQuestionEnter/dailyBox';//新增
import RelationUnSpLedger from '@/views/daily/spledger/relationUnSpLedger';//攥取台账页面
import Details from './details'// tree
export default {
  name: "historyQuestionEnter",
  components: {
     dailyBox,
    Details,
    RelationUnSpLedger
  },
  dicts: [],
  data() {
    return {
      keyAdd:0,
      title:'编辑',
      loading:false,
      //填报遮罩层
      visible:false,
      activeName: '0',
      rows:{},
      dailyStatusList: [],
      //选中的数据
      problemId:'',
      relevantTableId:'',
      relevantTableName:'',
      //查看与编辑
      edit:true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 表格数据
      tableList: [],
      //新增主键
      //日常问题查询 参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        involCompanyName:'',
        acceptTime:'',
        problemTitle:'',
        hisDataFlag:'1',
        inclusionPhases:[]
      },
      index:0,
      queryType:'relation',
      relationId:'',
    };
  },
  created() {
    this.ViolDailyDraftList();
    this.DailyStatus();
  },
  activated() {
    this.$nextTick(() => {
      this.$refs.table.doLayout(); //解决表格错位
    });
  },
  filters: {
  },
  methods: {
    // 问题状态
    DailyStatus() {
      dailyStatus({}).then(
        response => {
          this.dailyStatusList = response.data
        }
      )
    },
    /**查询日常问题列表*/
    ViolDailyDraftList() {
      this.loading = true;
      let queryParamsData  = JSON.parse(JSON.stringify(this.queryParams))
      if(queryParamsData.inclusionPhases.indexOf('1')>-1 && queryParamsData.inclusionPhases.indexOf('T') == -1){
        queryParamsData.inclusionPhases[queryParamsData.inclusionPhases.length] = 'T'
      }
      violDailyDraftList(queryParamsData).then(
        response => {
          this.tableList = response.rows;
          this.total = response.total;
          this.loading = false;
        }
      );
    },
    /** 新增按钮操作*/
    handleAdd() {
      queryNewDailyId().then(
        response => {
          this.title = '新增'
          this.problemId = response.data.problemId;
          this.relevantTableId = response.data.relevantTableId;
          this.relevantTableName = response.data.relevantTableName;
          this.$nextTick(() => {
            this.$refs.accept.show();
          });
        }
      );
    },
    /** 新增关闭触发操作*/
    closeAdd() {
      console.log('子组件关闭了弹出层');
    },
    /** 搜索按钮操作*/
    handleQuery() {
      this.queryParams.pageNum = 1;

      this.ViolDailyDraftList();
    },
    /** 查看 */
    detail(row) {
      this.rows = row
      this.rows.problemTitle = this.rows.problemTitle?this.rows.problemTitle:''
      this.visible = true
    },
    //关闭查看
    close(){
      this.visible = false
    },
    /**重置按钮操作*/
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        involCompanyName:'',
        acceptTime:'',
        problemTitle:'',
        hisDataFlag:'1',
        inclusionPhases:[]
      };
      this.ViolDailyDraftList();
    },

    /** 删除按钮操作 */
    handleDel(row) {
      this.$modal.confirm('是否确定删除该数据？').then(function() {
        return violDailyDraftDelete({id:row.id});
      }).then(() => {
        this.$modal.msgSuccess("删除成功");
        this.ViolDailyDraftList();
      }).catch(() => {});
    },
    /** 编辑与查看操作 */
    handleDetail(row,type){
      this.problemId = '';
      this.title = '编辑'
      this.problemId = row.id;
      this.relevantTableId = row.relevantTableId,
      this.$nextTick(() => {
        this.$refs.accept.show();
      });
    },
    /** 新增成功 */
    closeConfirm(formData){
      this.problemId = formData.problemId;
      this.relevantTableId=formData.relevantTableId;
      this.relevantTableName=formData.relevantTableName;
      this.edit = true;
      this.$nextTick(() => {
        this.$refs.accept.show();
      });
    },
    /**填报关闭*/
    onClose(){
      console.log('关闭填报');
    },
    /**填报提交*/
    submitForm(){
      console.log('调用子页面方法提交');
    },
    //专项台账攥取
    relationLedger(relationId){
      this.relationId = relationId
      this.index++;
      this.$nextTick(()=> {
        this.$refs.relation.show();
      });
    }
  }
};
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
</style>







