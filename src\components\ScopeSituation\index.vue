<template>
  <div class="de-list">
    <div class="de-li ry-row" v-for="item in scopeSituationData">
      <div class="depart_list left">
        <div class="depart_li_width">
          <li class="depart_li depart_li_blue1" style="width: 100%" :title="item.aspectName">
            <span class="depart_li_text">{{item.aspectName}}</span>
            <i class="el-icon-close icon iconfont" v-show="edit" @click="deleteScope(item.aspectCode,2)"></i>
          </li>
        </div>
      </div>
      <div class="ment_list right">
        <div class="depart_li_width">
          <li v-for="obj in item.rangeList" class="depart_li depart_li_blue1"  :title="obj.situationName">
            <span class="float-left">{{obj.situationName}}</span>
            <i class="el-icon-close icon iconfont" v-show="edit" @click="deleteScope(obj.id,1)"></i>
          </li>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  export default {
    name: "ScopeSituation",
    props: {
      title: {
        type: String,
        default: ''
      },
      edit: {
        type: Boolean,
        default: false,
      },
      scopeSituationData:{
        type:Array,
        default:[]
      }
    },
      data(){
        return{

        }
      },
      methods:{
        /** 删除操作 */
        deleteScope(id,type) {
          this.$emit('deleteScope',{id,type});
        },
      }
  }
</script>

<style rel="stylesheet/scss" lang="scss">
  .depart_li_width {
    width: 100%;
    max-width: 100%;
    padding-right: 10px;
    box-sizing: border-box;
    float: left;
    .depart_li {
      height: auto;
      position: relative;
      background-color: #e6f7ff;
      color: #40a9ff;
      line-height: 30px;
      margin: 0 0 12px 0;
      display: inline-block;
      padding: 0 30px 0 12px;
      border-radius: 2px;
      box-sizing: border-box;
      .icon {
        float: right;
        cursor: pointer;
        position: absolute;
        right: 8px;
        top: 6px;
        font-size: 16px;
      }
    }
  }
  .left{
    float:left;
    min-width: 250px;
    position: relative;
    &:before{
      content: " ";
      position: absolute;
      right: -36px;
      top: 14px;
      z-index: 2;
      width: 36px;
      height: 1px;
      border-bottom: 2px dotted #ddd;
      opacity: 1;
    }
  }
  .right{
    float:right;
    width: calc(100% - 300px);
  }
  .ry-row:before, .ry-row:after {
    content: '';
    display: block;
    clear: both;
  }
</style>
