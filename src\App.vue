
<template>
  <div id="app">
    <!-- <router-view :key="$route.fullPath"/> -->

    <router-view></router-view>
  </div>
</template>

<script>
import { getInfo } from '@/api/login.js'
export default {
  name: 'App',
  metaInfo() {
    return {
      title: this.$store.state.settings.dynamicTitle && this.$store.state.settings.title,
      titleTemplate: title => {
        return title ? `${title} - ${process.env.VUE_APP_TITLE}` : process.env.VUE_APP_TITLE
      }
    }
  },
  watch: {
    $route() {
      this.fetchData()
    }
  },
  methods: {
    fetchData() {
      const appMain = document.querySelector('.app-main')
      console.log('appMain',appMain)
      if(!appMain){
        // appMain.style.opacity = 1
      }else
      //判断当前是不是在cancellation 和 404页面
      if (this.$route.path === '/login'||this.$route.path === '/sign'  || this.$route.path === '/404'|| this.$route.path === '/system/webOffice') {

        appMain.style.opacity = 1
      } else {
        //将页面body的opacity设置为0
        appMain.style.opacity = 0
        // 调用接口逻辑
        getInfo().then(response => {
          //将页面body的opacity设置为1
          appMain.style.opacity = 1
        })
      }
    }
  }
}
</script>
