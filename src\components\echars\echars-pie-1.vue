<template>
    <div
      :id="id"
      ref="chart"
      :class="className"
      :style="{ height: height, width: width }"
    />
  </template>
  <script>
  import echarts from 'echarts'
  import { fontSizeEchars } from './mixins/fontSizeEchars'
  import resize from './mixins/resize'
  require('echarts/theme/macarons')
  import pubSub from 'pubsub-js'
  export default {
    mixins: [resize],
    props: {
      charsData: {
        type: Array,
        default: () => []
      },
      id: {
        type: String,
        default: 'myChart'
      },
      className: {
        type: String,
        default: 'chart'
      },
      width: {
        type: String,
        default: '100%'
      },
      height: {
        type: String,
        default: '100%'
      }
    },
    data() {
      return {
        chart: null
      }
    },
    watch: {
      charsData: {
        handler(val, oldVal) {
          this.chart.clear()
          setTimeout(() => {
            this.initChart()
          }, 1000)
        },
        deep: true
      }
    },
    mounted() {
      this.initChart()
    },
    beforeDestroy() {
      if (!this.chart) {
        return
      }
      this.chart.clear()
      this.chart.dispose()
      this.chart = null
    },
    methods: {
      initChart() {
        let name1 = this.charsData[0].name2
        let value1 = this.charsData[0].value 
        this.chart = echarts.init(this.$refs.chart, 'macarons')
        var option = {
    //       title: {
    //         text: [`{a|${value1}}\n{b|${name1}}`],
    //     left:'20%',
    //      top: '42%',
    //     textStyle: {
    //       rich: {
    //           a: {
    //             display:'block',
    //             fontSize: fontSizeEchars(0.14),
    //             color: '#021c25',
    //             padding:[0,0,0,15]
    //           },
    //           b: {
    //             display:'block',
    //             fontSize: fontSizeEchars(0.14),
    //             color: '#6a7474',
    //             padding:[10,0,0,0]
    //           }
    //         }

    //     },
    // },
            legend: {
                        type: "scroll",
                        orient: 'vertical',
                        left: '2%',
                        top: '5%',
                        textStyle: {
                            color: '#8C8C8C'
                        },
                    },
                    tooltip: {
                        trigger: 'item',
                        formatter: function (parms) {
                            var str =

                                "数量：" + parms.data.value + "</br>" +
                                "占比：" + parms.percent + "%";
                            return str;
                        }
                    },

                    series: [
                        {
                            name: '标题',
                            type: 'pie',
                            center: ['50%', '55%'],
                            radius: ['38%', '58%'],
                            clockwise: false, //饼图的扇区是否是顺时针排布
                            avoidLabelOverlap: false,
                            label: {
                                normal: {
                                    show: true,
                                    position: 'outter',
                                    formatter: function (parms) {
                                        return parms.data.legendname
                                    }
                                }
                            },
                            labelLine: {
                                normal: {
                                    length: fontSizeEchars(0.05),
                                    length2: fontSizeEchars(0.15),
                                    smooth: false,
                                }
                            },
                            data: this.charsData
                        }
                    ]
        }
        this.chart.setOption(
          option,
          true
        )
  
        // this.chart.on('click', (e) => {
        //       name1 = e.data.name2
        //       value1 = e.data.value
        //       option.title.text =  [`{a|${value1}}\n{b|${name1}}`],
        //       this.chart.setOption(
        //   option,
        //   true
        // )
        //     })
     
      }
    }
  }
  </script>
  <style lang="scss" scoped>
  #myChart {
    width: 100%;
    height: 100%;
    div {
      width: 100%;
      height: 100%;
    }
  }
  </style>
  