<!--规章制度-->
<template>
  <div   style="height:calc(70vh - 254px);">
    <el-table
      border
      :data="tableList"
      ref="table"
      height="100%"
    >
      <el-table-column
        align="center"
        type="index"
        width="50">
      </el-table-column>
      <el-table-column label="中央企业名称" prop="groupName" width="250" show-overflow-tooltip/>
      <el-table-column label="集团简称" prop="groupSortName" width="200" show-overflow-tooltip/>
      <el-table-column label="统一社会信用代码" prop="uscCode" width="200" show-overflow-tooltip/>
      <el-table-column label="标题" prop="title" width="250" show-overflow-tooltip/>
      <el-table-column label="文号" prop="issueCode" width="250" show-overflow-tooltip align="center"/>
      <el-table-column label="制度类型" prop="classifyText" width="150" show-overflow-tooltip align="center"/>
      <el-table-column label="印发日期" prop="publishDate" :formatter="dateFormat1" width="150" show-overflow-tooltip align="center"/>
      <el-table-column label="施行日期" prop="implementDate" :formatter="dateFormat2" width="150" show-overflow-tooltip align="center"/>
      <el-table-column label="类别" prop="classifyText" width="150" show-overflow-tooltip align="center"/>
      <el-table-column label="制度文件" prop="personPostalCode" align="center" width="100">
        <template slot-scope="scope">
          <span class="text-red cursor underline" @click="fileDialog( scope.row.files)">
            {{ scope.row.files.length?scope.row.files.length:0}}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="状态" prop="del" width="60" align="center">
        <template slot-scope="scope">
          {{ scope.row.del==1?'已删除': scope.row.del==2?'新增':'编辑'}}
        </template>
      </el-table-column>
    </el-table>
    <el-dialog  :visible.sync="visible" width="800px"  append-to-body  @close="close" title="附件列表">
      <el-table
        border
        :data="fileList"
        ref="table2"
        height="100%"
      >
        <el-table-column
          fixed
          align="center"
          label="序号"
          type="index"
          width="50">
        </el-table-column>
        <el-table-column label="文档名称" prop="fileName" width="225"/>
        <el-table-column label="上传时间" :formatter="dateFormat" prop="createTime" width="225" align="center"/>
        <el-table-column label="文档类型" prop="fileDocumentType" width="100"/>
        <el-table-column label="操作" prop="del" width="150" fixed="right"
                         align="center"
        >
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              title="下载"
              icon="el-icon-bottom"
              @click="downloadFile(scope.row)"
            >
            </el-button>
            <!-- <el-button
              size="mini"
              type="text"
              icon="el-icon-search"
            >预览
            </el-button> -->
          </template>
        </el-table-column>
      </el-table>
      <div slot="footer">
        <el-button size="mini" @click="close()">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import {getReportLaw,getReportedLaw} from "@/api/sasac/reportManagement/edit/detail/index";
  import moment from "moment";

  export default {
    name: "enterpriseRulesSystems",
    props: {
      problemId: {
        type: String
      },
      height:{
        type: String
      }
    },
    data() {
      return {
        tableList: [],
        total: 0,
        visible:false,
        fileList:[],//附件列表
        hasSelectList:[],//选中的值
        params: {
          pageNum: 1,
          pageSize: 10,
        }
      }
    },
    created() {
      this.GetReportAreaPerson();
    },
    mounted() {
    },
    methods: {
      // 获取查询页数据
      GetReportAreaPerson() {
        //接口：/colligate/baseInfo/report/getReportLaw
        getReportedLaw({ reportId:this.problemId,...this.params}).then(response => {
          this.tableList = response.data;
        });
      },
      // 获取刷新数据
      onRefresh() {
        this.GetReportAreaPerson();
      },
      //弹出附件
      fileDialog(files){
        this.fileList = files;
        this.$nextTick(()=>{
          this.visible=true;
        })
      },
      /** 下载附件 */
      downloadFile(row) {
        this.download('/sys/documentTemplate/downloadByAttachmentId', { id: row.id }, row.fileName)
      },
      //附件列表关闭
      close(){
        this.visible=false;
      },
      /*日期处理*/
      dateFormat:function(row){
        if(row.createTime === undefined){
          return ''
        }
        return moment(row.createTime).format("YYYY-MM-DD HH:mm:ss")
      },
      dateFormat1:function(row){
        if(row.publishDate === undefined){
          return ''
        }
        return moment(row.publishDate).format("YYYY-MM-DD")
      },
      dateFormat2:function(row){
        if(row.implementDate === undefined){
          return ''
        }
        return moment(row.implementDate).format("YYYY-MM-DD")
      },
    }
  }
</script>

<style scoped>

</style>
