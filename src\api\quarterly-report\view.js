import request from '@/utils/request'

//02 季度报告-查看，根据省分数据主键或流程主键查询季度报告省份数据表数据
export function queryQuarterReportProv(data) {
  return request({
    url: '/quarterReport/queryQuarterReportProv',
    method: 'post',
    data: data,
  })
}
//04查看汇总数据 根据季度报告表主键 获取查询未删除非驳回的省分汇总数据
export function getSumQuarterReportProvData(data) {
  return request({
    url: '/quarterReport/getSumQuarterReportProvData',
    method: 'post',
    data: data,
  })
}
//03查看汇总-省分 根据季度报告表主键、以流程状态分组、查询未删除非驳回的省分数据
export function queryProvList(data) {
  return request({
    url: '/quarterReport/queryProvList',
    method: 'post',
    data: data,
  })
}
//08根据季度报告主键查询省分人员数据quarterReportId 季度报告表主键status 流程状态
export function getReportUnitUserData(params,queryParams){
  return request({
    url: '/quarterReport/getReportUnitUserData',
    method: 'post',
    data: params,
    params: queryParams
  })
}

//进行中的进行催办
export function toUrgent(quarterReportProvId){
  return request({
    url: '/quarterReport/toUrgent/'+quarterReportProvId,
    method: 'post'
  })
}




