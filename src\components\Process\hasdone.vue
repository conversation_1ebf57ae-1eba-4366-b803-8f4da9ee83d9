<template>
  <div class="process">
    <div>
      <el-button type="primary" icon="el-icon-s-fold" v-if="withdrawFlag==1" @click="WithdrawProcess()" size="mini" plain>撤回</el-button>
      <el-button @click="closeEmit" icon="el-icon-close" size="mini">关闭</el-button>
    </div>
  </div>
</template>
<script>
  import { withdrawProcess } from "@/api/components/process";

  export default {
    inheritAttrs: false,
    components: {},
    props: {
      selectValue: {
        type: Object
      },
      centerVariable:{
        type: Object
      },
      // 1表示待办已办通用，2表示待办，3表示已办，4表示待阅，5表示已阅，6表示我的申请，默认为1
      tabFlag: {
        type: String
      },
      withdrawFlag:{
        type:String
      }
    },
    data() {
      return {
        processVisible:false,
        processTitle:'流程提交',
        processType:1,//1:下一步 2:退回 3:转派 4:中止
        formData: {
          nextLinkKey: undefined,
          nextLinkName:undefined,
          assignee:'',
          processComment: '',
          sendMsg: false,
          mailMsg:false,
        },
        rules: {
          nextLinkKey: [{
            required: true,
            message: '请选择下一环节名称',
            trigger: 'change'
          }],
          assignee: [{
            required: true,
            message: '请选择下环节处理人',
            trigger: 'change'
          }],
          processComment: [{
            required: true,
            message: '请输入处理意见',
            trigger: 'blur'
          }]
        },
        taskDefinitionKey:'',
        processDefinitionKey:'SupervisionDailyReport',
        nextLinkKey: [],
        refreshNextAssigneeList: [],
      }
    },
    computed: {},
    watch: {},
    created() {
    },
    mounted() {},
    methods: {
      onOpen() {},
      onClose() {
        this.processVisible=false;
      },
      close() {
        this.processVisible=false;
      },
      closeEmit() {
        this.$emit('close');
      },
      //撤回点击
      WithdrawProcess(){
        let dataForm = {flowKey:this.centerVariable.flowKey,processInstanceId:this.selectValue.processInstanceId,businessKey:this.centerVariable.busiKey,taskId:this.centerVariable.taskId,title:this.centerVariable.title,nextLinkKey:this.selectValue.linkKey};
        this.$modal.confirm('【撤回】确认后，当前流程将撤回本环节，是否确认？').then(function() {
          return withdrawProcess(dataForm)
        }).then((response) => {
          this.$modal.msgSuccess(response.msg);
          setTimeout(() => {
            this.close();
            this.closeEmit();
          },1500);
        }).catch(() => {
        })
      },
    }
  }

</script>
<style scoped lang="scss">
  .process{
    ::v-deep.el-dialog__body{
      padding-top: 16px !important;
      height: auto  !important;
      background: #fff !important;
      padding-bottom:0 !important;
    }
  }
</style>
