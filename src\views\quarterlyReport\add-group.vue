<!-- 季度报告--新增和编辑页面--集团 -->

<template>
  <div class="wai-container" style="background-color: #fff">
    <div class="layui-row width height">
      <div class="width height">
        <div class="common-wai-box" style="height: 100%">
          <div class="common-in-box" style="height: auto; min-height: 100%">
            <div class="common-in-box-header">
              <div class="common-in-box-header-line"></div>
              <div class="common-in-box-header-text">基本信息</div>
            </div>

            <div class="common-in-box-content">
              <div class="top-search">
                <el-col :span="8" class="height">
                  <div class="layui-form">
                    <div class="layui-form-left">
                      <span class="must-icon">*</span>上报年度
                    </div>

                    <el-date-picker
                      format="yyyy"
                      value-format="yyyy"
                      v-model="infoData.monthYear"
                      @change="monthYearChanage"
                      type="year"
                      placeholder="请选择"
                    >
                    </el-date-picker>
                  </div>
                </el-col>

                <el-col :span="8" class="height">
                  <div class="layui-form">
                    <div class="layui-form-left">
                      <span class="must-icon">*</span>上报季度
                    </div>

                    <el-select
                      @change="uploadQuarterChanage"
                      v-model="infoData.uploadQuarter"
                      placeholder="请选择"
                    >
                      <el-option
                        v-for="(item, index) in uploadQuarterList"
                        :key="index"
                        :label="item.name"
                        :value="item.value"
                      ></el-option>
                      <!-- <el-option v-for="(item, index) in dict.type.corresponding_adverse_effect" :key="index" :label="item.label" :value="item.value"></el-option> -->
                    </el-select>
                  </div>
                </el-col>

                <el-col :span="8" class="height">
                  <div class="layui-form">
                    <div class="layui-form-left">
                      <span class="must-icon">*</span>上报截止日期
                    </div>

                    <el-input
                      readonly
                      type="text"
                      placeholder="请选择"
                      v-model="infoData.uploadEndTime"
                    />
                  </div>
                </el-col>
              </div>

              <div class="top-search">
                <div class="layui-col-md12 layui-col-sm12 layui-col-lg12">
                  <div class="layui-form">
                    <div class="layui-form-left">
                      <span class="must-icon">*</span>上报标题
                    </div>

                    <el-input
                      readonly
                      type="text"
                      placeholder="请选择"
                      v-model="infoData.uploadTitle"
                    />
                  </div>
                </div>
              </div>

              <div class="top-search">
                <div class="layui-col-md12 layui-col-sm12 layui-col-lg12">
                  <div class="layui-form">
                    <div class="layui-form-left">上报要求</div>
                    <div class="layui-form-value" id="uploadAsk">
                      {{ infoData.uploadAsk }}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div class="common-in-box-header" style="margin-top: 10px">
              <div class="common-in-box-header-line"></div>
              <div class="common-in-box-header-text">附件列表</div>
              <div class="flex-1"></div>

              <el-upload
                ref="upload"
                class="upload-demo"
                :action="files.actionUrl"
                :headers="files.myHeaders"
                :on-success="handleFileSuccess"
                :data="{
                  busiTableId: files.busiTableId,
                  busiTableName: files.busiTableName,
                }"
                :show-file-list="false"
              >
                <el-button
                  size="small"
                  type="primary"
                  icon="el-icon-upload2"
                  class="el-button-common"
                  style="background: transparent"
                  >附件上传
                </el-button>
              </el-upload>
            </div>

            <div class="tables tables_1">
              <el-table
                :data="filesData"
                border
                v-loading="tableLoading"
                style="width: 100%"
              >
                <el-table-column
                  label="序号"
                  type="index"
                  min-width="5%"
                  align="center"
                />
                <el-table-column label="文件名" prop="fileName" min-width="50%">
                  <template slot-scope="scope">
                    <div
                      style="text-align: left"
                      class="overflowHidden-1"
                      :title="scope.row.fileName"
                    >
                      {{ scope.row.title || "" }}
                    </div>
                  </template>
                </el-table-column>
                <el-table-column
                  label="上传人"
                  align="center"
                  prop="uploaderUserName"
                  min-width="10%"
                />
                <el-table-column
                  label="上传时间"
                  align="center"
                  prop="createTime"
                  min-width="20%"
                />

                <el-table-column
                  label="操作"
                  fixed="right"
                  min-width="15%"
                  align="center"
                  class-name="small-padding fixed-width"
                >
                  <template slot-scope="scope">
                    <el-button
                      size="mini"
                      type="text"
                      title="下载"
                      icon="el-icon-bottom"
                      @click="fileDownload(scope.row)"
                    ></el-button>
                    <el-button
                      size="mini"
                      type="text"
                      title="删除"
                      icon="el-icon-delete"
                      @click="delFile(scope.row)"
                    ></el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>

            <div class="common-in-box-header" style="margin-top: 10px">
              <div class="common-in-box-header-line"></div>
              <div class="common-in-box-header-text">本单位汇总信息</div>
            </div>

            <div
              class="common-in-box-header"
              style="margin-top: 10px; border: 0px; padding-left: 10px"
            >
              <div class="common-in-box-header-text">工作部署情况</div>
            </div>

            <div class="common-in-box-content">
              <div class="top-search">
                <el-col :span="8" class="height">
                  <div class="layui-form">
                    <div class="layui-form-left width-label-2">
                      <span class="must-icon">*</span
                      >本季度召开领导小组会议（次）
                    </div>
                    <el-input
                      v-limit-input-number
                      v-model="formData.num1"
                      type="text"
                      placeholder="请输入"
                      class="num-input"
                    />
                  </div>
                </el-col>

                <el-col :span="8" class="height">
                  <div class="layui-form">
                    <div class="layui-form-left width-label-2">
                      <span class="must-icon">*</span
                      >本季度召开领导小组办公室会议（次）
                    </div>
                    <el-input
                      v-limit-input-number
                      v-model="formData.num2"
                      type="text"
                      id="num2"
                      placeholder="请输入"
                      class="num-input"
                    />
                  </div>
                </el-col>

                <el-col :span="8" class="height">
                  <div class="layui-form">
                    <div class="layui-form-left width-label-2">
                      <span class="must-icon">*</span>本季度召开专题会议 （次）
                    </div>
                    <el-input
                      v-limit-input-number
                      v-model="formData.num3"
                      type="text"
                      id="num3"
                      placeholder="请输入"
                      class="num-input"
                    />
                  </div>
                </el-col>
              </div>

              <div class="top-search">
                <el-col :span="8" class="height">
                  <div class="layui-form">
                    <div class="layui-form-left width-label-2">
                      <span class="must-icon">*</span
                      >当年累计召开领导小组会议（次）
                    </div>
                    <el-input
                      v-limit-input-number
                      v-model="formData.num4"
                      type="text"
                      id="num4"
                      placeholder="请输入"
                      class="num-input"
                    />
                  </div>
                </el-col>

                <el-col :span="8" class="height">
                  <div class="layui-form">
                    <div class="layui-form-left width-label-2">
                      <span class="must-icon">*</span
                      >当年累计召开领导小组办公室会议（次）
                    </div>
                    <el-input
                      type="text"
                      v-limit-input-number
                      v-model="formData.num5"
                      placeholder="请输入"
                      class="num-input"
                    />
                  </div>
                </el-col>

                <el-col :span="8" class="height">
                  <div class="layui-form">
                    <div class="layui-form-left width-label-2">
                      <span class="must-icon">*</span>当年累计召开专题会议（次）
                    </div>
                    <el-input
                      v-limit-input-number
                      v-model="formData.num6"
                      type="text"
                      placeholder="请输入"
                      class="num-input"
                    />
                  </div>
                </el-col>
              </div>
            </div>

            <div
              class="common-in-box-header"
              style="margin-top: 10px; border: 0px; padding-left: 10px"
            >
              <div class="common-in-box-header-text">体系建设情况</div>
            </div>

            <div class="common-in-box-content">
              <div class="top-search">
                <el-col :span="8" class="height">
                  <div class="layui-form">
                    <div class="layui-form-left width-label-2">
                      <span class="must-icon">*</span
                      >2019年至今累计印发责任追究相关制度数量（项）
                    </div>
                    <el-input
                      type="text"
                      v-limit-input-number
                      v-model="formData.num7"
                      placeholder="请输入"
                      class="num-input"
                    />
                  </div>
                </el-col>

                <el-col :span="8" class="height">
                  <div class="layui-form">
                    <div class="layui-form-left width-label-2">
                      <span class="must-icon">*</span>2019年至今累计专职人员数量 (人)
                    </div>
                    <el-input
                      type="text"
                      v-limit-input-number
                      v-model="formData.num8"
                      placeholder="请输入"
                      class="num-input"
                    />
                  </div>
                </el-col>
              </div>

              <div class="top-search">
                <el-col :span="8" class="height">
                  <div class="layui-form">
                    <div class="layui-form-left width-label-2">
                      <span class="must-icon">*</span>当年累计新增配套制度（项）
                    </div>
                    <el-input
                      type="text"
                      v-limit-input-number
                      v-model="formData.num9"
                      placeholder="请输入"
                      class="num-input"
                    />
                  </div>
                </el-col>

                <el-col :span="8" class="height">
                  <div class="layui-form">
                    <div class="layui-form-left width-label-2">
                      <span class="must-icon">*</span>当年累计新增工作机制（项）
                    </div>
                    <el-input
                      type="text"
                      v-limit-input-number
                      v-model="formData.num10"
                      placeholder="请输入"
                      class="num-input"
                    />
                  </div>
                </el-col>

                <el-col :span="8" class="height">
                  <div class="layui-form">
                    <div class="layui-form-left width-label-2">
                      <span class="must-icon">*</span
                      >当年累计新增专职人员数量（人）
                    </div>
                    <el-input
                      type="text"
                      v-limit-input-number
                      v-model="formData.num11"
                      placeholder="请输入"
                      class="num-input"
                    />
                  </div>
                </el-col>
              </div>

              <div class="top-search">
                <el-col :span="24" class="height">
                  <div class="layui-form">
                    <div class="layui-form-left width-label-2">
                      新增配套制度名称
                    </div>
                    <el-input
                      type="text"
                      v-model="formData.num12"
                      placeholder="请输入"
                    />
                  </div>
                </el-col>
              </div>

              <div class="top-search">
                <el-col :span="24" class="height">
                  <div class="layui-form">
                    <div class="layui-form-left width-label-2">
                      新增工作机制名称
                    </div>
                    <el-input
                      type="text"
                      v-model="formData.num13"
                      placeholder="请输入"
                    />
                  </div>
                </el-col>
              </div>

              <div class="top-search">
                <el-col :span="24" class="height">
                  <div class="layui-form">
                    <div class="layui-form-left width-label-2">
                      <span class="must-icon">*</span>集团主责部门
                    </div>
                    <el-input
                      type="text"
                      v-model="formData.num14"
                      placeholder="请输入"
                    />
                  </div>
                </el-col>
              </div>

              <div
                class="common-in-box-header"
                style="margin-top: 10px; border: 0px; padding-left: 10px"
              >
                <div class="common-in-box-header-text">
                  违规问题线索查办情况
                </div>
              </div>

              <div class="common-in-box-content">
                <div class="top-search">
                  <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left width-label-2">
                        <span class="must-icon">*</span
                        >本季度新受理问题线索数量（件）
                      </div>
                      <el-input
                        type="text"
                        v-limit-input-number
                        v-model="formData.num15"
                        placeholder="请输入"
                        class="num-input"
                      />
                    </div>
                  </el-col>
                  <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left width-label-2">
                        <span class="must-icon">*</span>本季度涉及资产损失（万元）
                      </div>
                      <el-input
                        type="text"
                        v-limit-input-money
                        v-model="formData.num16"
                        @blur="dottedClear"
                        placeholder="请输入"
                        class="money-input"
                      />
                    </div>
                  </el-col>

                  <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left width-label-2">
                        <span class="must-icon">*</span>本季度涉及资产损失风险（万元）
                      </div>
                      <el-input
                        type="text"
                        v-limit-input-money
                        v-model="formData.num17"
                        @blur="dottedClear"
                        placeholder="请输入"
                        class="money-input"
                        onkeyup="num(this)"
                      />
                    </div>
                  </el-col>
                </div>

                <div class="top-search">
                  <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left width-label-2">
                        <span class="must-icon">*</span
                        >当年累计受理问题线索数量（件）
                      </div>
                      <el-input
                        type="text"
                        v-limit-input-number
                        v-model="formData.num18"
                        placeholder="请输入"
                        class="num-input"
                      />
                    </div>
                  </el-col>

                  <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left width-label-2">
                        <span class="must-icon">*</span
                        >上年结转问题线索数量（件）
                      </div>
                      <el-input
                        type="text"
                        v-limit-input-number
                        v-model="formData.num19"
                        placeholder="请输入"
                        class="num-input"
                      />
                    </div>
                  </el-col>
                </div>

                <div class="top-search">
                  <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left width-label-2">
                        <span class="must-icon">*</span>其中:未启动核查（件）
                      </div>
                      <el-input
                        type="text"
                        v-limit-input-number
                        v-model="formData.num20"
                        placeholder="请输入"
                        class="num-input"
                      />
                    </div>
                  </el-col>

                  <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left width-label-2">
                        <span class="must-icon">*</span>其中: 正在核查(件)
                      </div>
                      <el-input
                        type="text"
                        v-limit-input-number
                        v-model="formData.num21"
                        placeholder="请输入"
                        class="num-input"
                      />
                    </div>
                  </el-col>

                  <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left width-label-2">
                        <span class="must-icon">*</span>其中: 完成核查(件)
                      </div>
                      <el-input
                        type="text"
                        v-limit-input-number
                        v-model="formData.num22"
                        placeholder="请输入"
                        @change="checkCompletedNumber()"
                        class="num-input"
                      />
                    </div>
                  </el-col>
                </div>
              </div>

              <div
                class="common-in-box-header"
                style="margin-top: 10px; border: 0px; padding-left: 10px"
              >
                <div class="common-in-box-header-text">追责整改工作成效</div>
              </div>

              <div class="common-in-box-content">
                <div class="top-search">
                  <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left width-label-2">
                        <span class="must-icon">*</span
                        >当年累计完成追责问题数量（件）
                      </div>
                      <el-input
                        type="text"
                        v-limit-input-number
                        @change="compareBfQuarter()"
                        v-model="formData.num23"
                        placeholder="请输入"
                        class="num-input"
                      />
                    </div>
                  </el-col>

                  <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left width-label-2">
                        <span class="must-icon">*</span>当年累计追责总人数（人）
                      </div>
                      <el-input
                        type="text"
                        v-limit-input-number
                        v-model="formData.num24"
                        placeholder="请输入"
                        class="num-input"
                      />
                    </div>
                  </el-col>

                  <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left width-label-2">
                        <span class="must-icon">*</span
                        >当年累计追责总人次（人次）
                      </div>
                      <el-input
                        type="text"
                        v-limit-input-number
                        v-model="formData.num25"
                        placeholder="请输入"
                        class="num-input"
                      />
                    </div>
                  </el-col>
                </div>

                <div class="top-search">
                  <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left width-label-2">
                        <span class="must-icon">*</span>其中：集团管理干部（人）
                      </div>
                      <el-input
                        type="text"
                        v-limit-input-number
                        v-model="formData.num26"
                        placeholder="请输入"
                        class="num-input"
                      />
                    </div>
                  </el-col>

                  <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left width-label-2">
                        <span class="must-icon">*</span>其中:
                        子企业管理干部（人）
                      </div>
                      <el-input
                        type="text"
                        v-limit-input-number
                        v-model="formData.num27"
                        placeholder="请输入"
                        class="num-input"
                      />
                    </div>
                  </el-col>

                  <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left width-label-2">
                        <span class="must-icon">*</span>其中:
                        中央企业负责人（人）
                      </div>
                      <el-input
                        type="text"
                        v-limit-input-number
                        v-model="formData.num28"
                        placeholder="请输入"
                        class="num-input"
                      />
                    </div>
                  </el-col>
                </div>

                <div class="top-search">
                  <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left width-label-2">
                        <span class="must-icon">*</span>其中: 组织处理 （人次）
                      </div>
                      <el-input
                        type="text"
                        v-limit-input-number
                        v-model="formData.num29"
                        placeholder="请输入"
                        class="num-input"
                      />
                    </div>
                  </el-col>

                  <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left width-label-2">
                        <span class="must-icon">*</span>其中: 扣减薪酬 （人次）
                      </div>
                      <el-input
                        type="text"
                        v-limit-input-number
                        v-model="formData.num30"
                        placeholder="请输入"
                        class="num-input"
                      />
                    </div>
                  </el-col>

                  <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left width-label-2">
                        <span class="must-icon">*</span>其中: 党纪处分（人次）
                      </div>
                      <el-input
                        type="text"
                        v-limit-input-number
                        v-model="formData.num31"
                        placeholder="请输入"
                        class="num-input"
                      />
                    </div>
                  </el-col>
                </div>

                <div class="top-search">
                  <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left width-label-2">
                        <span class="must-icon">*</span>其中: 政务处分（人次）
                      </div>
                      <el-input
                        type="text"
                        v-limit-input-number
                        v-model="formData.num32"
                        placeholder="请输入"
                        class="num-input"
                      />
                    </div>
                  </el-col>

                  <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left width-label-2">
                        <span class="must-icon">*</span>其中: 禁入限制（人次）
                      </div>
                      <el-input
                        type="text"
                        v-limit-input-number
                        v-model="formData.num33"
                        placeholder="请输入"
                        class="num-input"
                      />
                    </div>
                  </el-col>
                  <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left width-label-2">
                        <span class="must-icon">*</span
                        >其中:移送监察机关或司法机关（人次）
                      </div>
                      <el-input
                        type="text"
                        v-limit-input-number
                        v-model="formData.num34"
                        placeholder="请输入"
                        class="num-input"
                      />
                    </div>
                  </el-col>
                </div>
                <div class="top-search">
                  <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left width-label-2">
                        <span class="must-icon">*</span>其中: 其他 （人次）
                      </div>
                      <el-input
                        type="text"
                        v-limit-input-number
                        v-model="formData.num35"
                        placeholder="请输入"
                        class="num-input"
                      />
                    </div>
                  </el-col>

                  <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left width-label-2">
                        <span class="must-icon">*</span
                        >当年累计扣减薪酬金额（万元）
                      </div>
                      <el-input
                        type="text"
                        v-limit-input-money
                        v-model="formData.num36"
                        @blur="dottedClear"
                        placeholder="请输入"
                        class="money-input"
                        onkeyup="num(this)"
                      />
                    </div>
                  </el-col>
                </div>

                <div class="top-search">
                  <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left width-label-2">
                        <span class="must-icon">*</span
                        >责任约谈-当年累计责任约谈次数（次）
                      </div>
                      <el-input
                        type="text"
                        v-limit-input-number
                        v-model="formData.num37"
                        placeholder="请输入"
                        class="num-input"
                      />
                    </div>
                  </el-col>

                  <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left width-label-2">
                        <span class="must-icon">*</span
                        >责任约谈-当年累计责任约谈总人次（人次）
                      </div>
                      <el-input
                        type="text"
                        v-limit-input-number
                        v-model="formData.num38"
                        placeholder="请输入"
                        class="num-input"
                      />
                    </div>
                  </el-col>
                </div>

                <div class="top-search">
                  <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left width-label-2">
                        <span class="must-icon">*</span
                        >当年累计挽回资产损失（万元）
                      </div>
                      <el-input
                        type="text"
                        v-limit-input-money
                        v-model="formData.num39"
                        @blur="dottedClear"
                        placeholder="请输入"
                        class="money-input"
                        onkeyup="num(this)"
                      />
                    </div>
                  </el-col>

                  <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left width-label-2">
                        <span class="must-icon">*</span
                        >当年累计降低损失风险（万元）
                      </div>
                      <el-input
                        type="text"
                        v-limit-input-money
                        v-model="formData.num40"
                        @blur="dottedClear"
                        placeholder="请输入"
                        class="money-input"
                        onkeyup="num(this)"
                      />
                    </div>
                  </el-col>

                  <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left width-label-2">
                        <span class="must-icon">*</span
                        >当年累计制修订管理制度（项）
                      </div>
                      <el-input
                        type="text"
                        v-limit-input-number
                        v-model="formData.num41"
                        placeholder="请输入"
                        class="num-input"
                      />
                    </div>
                  </el-col>
                </div>

                <div class="top-search">
                  <el-col :span="24" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left width-label-2">
                        <span class="must-icon">*</span>
                        其他工作成效
                      </div>
                      <el-input
                        type="text"
                        v-model="formData.num42"
                        placeholder="请输入"
                      />
                    </div>
                  </el-col>
                </div>

                <!-- 其他 -->
                <div
                  class="common-in-box-header"
                  style="margin-top: 10px; border: 0px; padding-left: 10px"
                >
                  <div class="common-in-box-header-text">其他</div>
                </div>

                <div class="top-search">
                  <el-col :span="24" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left">备注</div>
                      <el-input
                        type="text"
                        v-model="formData.num43"
                        placeholder="请输入"
                      />
                    </div>
                  </el-col>
                </div>

                <div class="top-search">
                  <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left">
                        <span class="must-icon">*</span>追责部门填报人
                      </div>
                      <el-input
                        type="text"
                        v-model="formData.num44"
                        placeholder="请输入"
                      />
                    </div>
                  </el-col>

                  <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left">
                        <span class="must-icon">*</span>联系电话
                      </div>
                      <el-input
                        type="text"
                        v-model="formData.num45"
                        placeholder="请输入"
                      />
                    </div>
                  </el-col>
                </div>
              </div>
            </div>
          </div>
          <div class="bottom-btn">
            <div class="left-empty" />
            <el-button size="mini" type="primary" @click="saveForm" plain>保存</el-button>
            <el-button size="mini" type="primary" @click="submitForm">提交</el-button>
          </div>
        </div>
      </div>
    </div>

    <Process
      :key="flowInfo.busiKey"
      ref="process"
      :refresh-assignee-url="flowInfo.refreshAssigneeUrl"
      :save-btn-type="flowInfo.saveBtnType"
      :tab-flag="flowInfo.tabFlag"
      :select-value="{
        busiKey: flowInfo.busiKey,
        title: flowInfo.title,
      }"
      :center-variable="{}"
      @close="closeAdd"
    />
  </div>
</template>
<script>
// import {} from '@/api/views/quarterly-report'
import { getToken } from "@/utils/auth";
import Process from "@/components/process-common/index";
export default {
  name: "addGroup",
  components: { Process },
  props: {
    closeBtn: {
      type: Function,
      default: null,
    },
    // add 为新增 edit 为编辑
    editType: {
      type: String,
      default: "",
    },
    //编辑内容
    rowData: {
      type: Object,
      default: () => {},
    },
  },
  dicts: [],
  data() {
    return {
      infoData: {}, //基本信息
      uploadQuarterName: "", //第几季度名称
      uploadQuarterList: [
        {
          name: "第一季度",
          value: "1",
        },
        {
          name: "第二季度",
          value: "2",
        },
        {
          name: "第三季度",
          value: "3",
        },
        {
          name: "第四季度",
          value: "4",
        },
      ],
      tableLoading: false, //表格loading
      filesData: [{}, {}], //附件列表
      //附件上传
      files: {
        busiTableId: "",
        busiTableName: "",
        actionUrl: process.env.VUE_APP_BASE_API + "/sys/attachment/uploadFile", // 上传地址
        myHeaders: { Authorization: "Bearer " + getToken() }, // 上传header
      },
      //本单位汇总信息
      formData: {
        num22:'',
        num23:''
      },
      //流程信息

      flowInfo: {
        busiKey: "", // 业务中获取
        title: "", // 业务中获取
        saveBtnType: true, // 是否需要保存按钮
        tabFlag: true, // 表明是业务发起环节
        refreshAssigneeUrl: "/riskEventFormFlow", // 自定义业务url
      },
    };
  },
  created() {
    this.loadTips();
  },
  watch: {

  },
  methods: {
    //清楚输入.
    dottedClear(evt) {
      if (evt.target.value.indexOf(".") != -1) {
        var length = evt.target.value.toString().split(".")[1].length;
        if (length == "0") {
          evt.target.value = evt.target.value + "00";
        }
        if (length == "1") {
          evt.target.value = evt.target.value + "0";
        }
      } else {
        evt.target.value = evt.target.value + ".00";
      }
    },
    //编辑提示
    loadTips() {
      var text =
        "您即将填报本单位违规经营投资责任追究季度数据，页面有自动填充的标灰数据为系统自动带出，您须核对后根据实际情况重新手工填写，请务必保证所有数据的真实准确，并注意与前期报送数据的滚动关联！";
      this.$alert(text, {
        confirmButtonText: "确定",
        callback: (action) => {},
      });
    },

    //季度报告改变事件
    uploadQuarterChanage() {
      if (this.infoData.uploadQuarter == "1") {
        this.uploadQuarterName = "第一季度";
      }
      if (this.infoData.uploadQuarter == "2") {
        this.uploadQuarterName = "第二季度";
      }
      if (this.infoData.uploadQuarter == "3") {
        this.uploadQuarterName = "第三季度";
      }
      if (this.infoData.uploadQuarter == "4") {
        this.uploadQuarterName = "第四季度";
      }

      //如果年度已经选择则去请求 校验 是否存在数据
      if (this.infoData.monthYear) {
        this.testYear();
        this.loadUploadEndTime(this.infoData.uploadQuarter);
        this.loadUploadTitle();
      }
    },

    //年度改变事件
    monthYearChanage(val) {
      if (val && this.infoData.uploadQuarter) {
        this.testYear();
        this.loadUploadEndTime(this.infoData.uploadQuarter);
        this.loadUploadTitle();
      }
    },
    // 校验年度和季度是否存在数据
    testYear() {
      var text =
        this.infoData.monthYear +
        "年" +
        this.uploadQuarterName +
        "数据已存在，请在查询列表中编辑。";

      this.$alert(text, {
        confirmButtonText: "确定",
        callback: (action) => {
          this.infoData.uploadEndTime = "";
          this.infoData.uploadTitle = "";
          this.uploadQuarterName = "";
          this.infoData.uploadQuarter = "";
        },
      });
      // getunitList()
      //   .then((res) => {})
      //   .catch(() => {
      //     var text =
      //       this.infoData.monthYear +
      //       "年" +
      //       this.uploadQuarterName +
      //       "数据已存在，请在查询列表中编辑.";

      //     this.$alert(text, {
      //       confirmButtonText: "确定",
      //       callback: (action) => {
      //         this.infoData.uploadEndTime = "";
      //         this.infoData.uploadTitle = "";
      //         this.uploadQuarterName = "";
      //         this.infoData.uploadQuarter = "";
      //       },
      //     });
      //   });
    },

    // 默认截止日期

    loadUploadEndTime(value) {
      //默认上报截至日期
      if (value == "1") {
        this.infoData.uploadEndTime = this.infoData.monthYear + "-3-23";
      }
      if (value == "2") {
        this.infoData.uploadEndTime = this.infoData.monthYear + "-6-23";
      }
      if (value == "3") {
        this.infoData.uploadEndTime = this.infoData.monthYear + "-9-23";
      }
      if (value == "4") {
        this.infoData.uploadEndTime = this.infoData.monthYear + "-12-23";
      }
    },

    // 默认上报标题
    loadUploadTitle() {
      var text =
        "中国联通集团公司" +
        this.infoData.monthYear +
        "年度" +
        this.uploadQuarterName +
        "违规责任追究工作情况报告";
      this.infoData.uploadTitle = text;
    },

    // 附件上传成功
    handleFileSuccess(res, file, fileList) {
      if (res.code == 200) {
        // this.fileListData()
      } else {
        this.$message.error(res.msg);
      }
    },

    /** 附件删除操作 */
    delFile(row) {
      let title = "确认删除该附件吗？";
      this.$modal
        .confirm(title)
        .then(function () {
          //   return deleteViolFile(id);
        })
        .then(() => {
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },

    /**下载文件*/
    fileDownload(obj) {
      this.download(
        "/sys/attachment/downloadSysAttachment/" + obj.attachmentId,
        {},
        obj.fileName
      );
    },
    // 关闭
    cancel() {
      this.closeBtn();
    },
    //保存
    saveForm() {},
    //提交
    submitForm() {
      //this.$message.info('')
    },
    //流程提交
    closeAdd() {
      this.closeBtn();
    },
    startProcessBtn() {
      // if (!this.formData.fillMatterName) {
      //   this.$message.error('填报事项名称不能为空！')
      //   return false
      // } else if (!this.formData.fillDate) {
      //   this.$message.error('填报日期不能为空！')
      //   return false
      // }
      // this.title = this.formData.fillMatterName
      const loadProcessData = {
        // businessKey: this.id,
        // title: this.formData.fillMatterName,
        // flowKey: 'riskEventForm'
      };
      this.$confirm('确认数据内容填写准确进行提交?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$refs.process.handle(1, loadProcessData);
      }).catch(() => {
      });
      // this.$confirm('确认提交审批此条任务吗?', '提示', {
      //   confirmButtonText: '确定',
      //   cancelButtonText: '取消',
      //   type: 'warning'
      // }).then(() => {
      //   infoSave({
      //     ...this.formData,
      //     ...{ id: this.id }
      //   }).then(res => {
      //     if (res.code === 200) {
      //       verification({ id: this.id }).then(
      //         response => {
      //           // 业务操作完成后调用
      //           this.closeLoading()

      //         }
      //       ).catch(() => {
      //         this.closeLoading()
      //       })
      //     } else {
      //       this.closeLoading()
      //       this.$modal.alertError(res.msg)
      //     }
      //   })
      // }).catch(() => {
      //   this.closeLoading()
      // })
    },

    //当年累计完成追责问题数量（件）
    compareBfQuarter(){
        if(this.formData.num22!==''&&this.formData.num23>this.formData.num22){
          this.formData.num23 = ''
          this.$message.error('【当年累计完成追责问题数量（件）】需小于等于【其中: 完成核查（件）】');
          this.$forceUpdate();
        }
    },
    //校验  其中: 完成核查（件）
    checkCompletedNumber(){
      if(this.formData.num23!==''&&(this.formData.num22<this.formData.num23)){
        this.formData.num22 = ''
        this.$message.error('【其中: 完成核查（件）】需大于等于【当年累计完成追责问题数量（件）】');
        this.$forceUpdate();
      }
    }
  },
};
</script>
<style lang="scss" scoped>
@import "~@/assets/styles/quarterly-report/index.css";
</style>
