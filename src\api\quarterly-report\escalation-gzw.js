/*
 * @Author:liqing<PERSON><PERSON>
 * @Date: 2023-09-11 14:21:09
 * @FilePath: \jtaudithtmld:\新项目\trigram-ui\src\api\quarterly-report\escalation-gzw.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import request from '@/utils/request'

// 01根据上送条件获取季度报告上送国资委列表数据
export function getSasacQuarterList(data,param) {
  return request({
    url: '/sasac/quarter/getSasacQuarterList',
    method: 'post',
    data:data,
    params:param
  })
}

//02查询上报国资委信息
export function getSasacQuarterInfo(data) {
  return request({
    url: '/sasac/quarter/getSasacQuarterInfo',
    method: 'post',
    data:data
  })
}

//03新增查询数据
export function addQuerySasacQuarterInfo(data) {
  return request({
    url: '/sasac/quarter/addQuerySasacQuarterInfo',
    method: 'post',
    data:data
  })
}

//04删除-根据上报国资委主键删除上报国资委数据
export function deleteSasacQuarterInfo(data) {
  return request({
    url: '/sasac/quarter/deleteSasacQuarterInfo',
    method: 'post',
    data:data
  })
}

//05保存-保存上报国资委数据
export function saveSasacQuarterInfo(data) {
  return request({
    url: '/sasac/quarter/saveSasacQuarterInfo',
    method: 'post',
    data:data
  })
}
//06同步数据
export function syncSasacQuarterInfo(data) {
  return request({
    url: '/sasac/quarter/syncSasacQuarterInfo',
    method: 'post',
    data:data
  })
}

//获取基础信息
export function queryQuarterReportParam(data) {
  return request({
    url: '/quarterReport/queryQuarterReportParam',
    method: 'post',
    data:data
  })
}


