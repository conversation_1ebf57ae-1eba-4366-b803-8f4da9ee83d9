import request from '@/utils/request'

// 上报国资委--企业基本信息查询
export function getReportArea(query) {
  return request({
    url: '/colligate/baseInfo/report/getReportArea',
    method: 'post',
    data: query
  })
}

// 上报国资委--已上报：企业基本信息查询
export function getReportedArea(query) {
  return request({
    url: '/colligate/baseInfo/report/getReportedArea',
    method: 'post',
    data: query
  })
}

// 上报国资委--企业基本信息刷新
export function refreshReportArea(query) {
  return request({
    url: '/colligate/baseInfo/report/refreshReportArea',
    method: 'post',
    data: query
  })
}
// 上报国资委--企业联系人上报数据查询
export function getReportAreaPerson(query) {
  return request({
    url: '/colligate/baseInfo/report/getReportAreaPerson',
    method: 'post',
    data: query
  })
}

// 上报国资委--已上报：企业联系人上报数据查询
export function getReportedAreaPerson(query) {
  return request({
    url: '/colligate/baseInfo/report/getReportedAreaPerson',
    method: 'post',
    data: query
  })
}

// 上报国资委--查询所有已提交的最新版本企业联系人数据
export function getReportAreaPersonAll(query) {
  return request({
    url: '/colligate/baseInfo/report/getReportAreaPersonAll',
    method: 'post',
    data: query
  })
}
// 上报国资委--保存企业联系人上报信息
export function saveReportedAreaPerson(query) {
  return request({
    url: '/colligate/baseInfo/report/saveReportedAreaPerson',
    method: 'post',
    data: query
  })
}

// 上报国资委--保存规章制度上报信息
export function saveReportLaw(query) {
  return request({
    url: '/colligate/baseInfo/report/saveReportLaw',
    method: 'post',
    data: query
  })
}
// 上报国资委--规章制度上报信息查询
export function getReportLaw(query) {
  return request({
    url: '/colligate/baseInfo/report/getReportLaw',
    method: 'post',
    data: query
  })
}

// 上报国资委--已上报：规章制度上报信息查询
export function getReportedLaw(query) {
  return request({
    url: '/colligate/baseInfo/report/getReportedLaw',
    method: 'post',
    data: query
  })
}

// 上报国资委--查询所有已提交的最新版本规章制度
export function getReportLawAll(query) {
  return request({
    url: '/colligate/baseInfo/report/getReportLawAll',
    method: 'post',
    data: query
  })
}
// 上报国资委--查询要上报的禁入限制人员
export function getReportLimitPerson(query) {
  return request({
    url: '/colligate/baseInfo/report/getReportLimitPerson',
    method: 'post',
    data: query
  })
}

// 上报国资委--已上报：查询要上报的禁入限制人员
export function getReportedLimitPerson(query) {
  return request({
    url: '/colligate/baseInfo/report/getReportedLimitPerson',
    method: 'post',
    data: query
  })
}

// 上报国资委--查询所有已提交最新版本禁入限制人员
export function getReportLimitPersonAll(query) {
  return request({
    url: '/colligate/baseInfo/report/getReportLimitPersonAll',
    method: 'post',
    data: query
  })
}
// 上报国资委--保存要上报的禁入限制人员
export function saveReportLimitPerson(query) {
  return request({
    url: '/colligate/baseInfo/report/saveReportLimitPerson',
    method: 'post',
    data: query
  })
}

// 上报国资委--查询要上报的发件箱
export function getReportSendBox(query) {
  return request({
    url: '/colligate/baseInfo/report/getReportSendBox',
    method: 'post',
    data: query
  })
}

// 上报国资委--已上报：查询要上报的发件箱
export function getReportedSendBox(query) {
  return request({
    url: '/colligate/baseInfo/report/getReportedSendBox',
    method: 'post',
    data: query
  })
}


// 上报国资委--刷新要上报的发件箱
export function refreshReportSendBox(query) {
  return request({
    url: '/colligate/baseInfo/report/refreshReportSendBox',
    method: 'post',
    data: query
  })
}

export function actualReportData(reportSasacId) {
  return request({
    url: '/colligate/violSasacActualReport/actualReportData/' + reportSasacId,
    method: 'post'
  });
}

export function deleteActualReportRecord(realtimeReportId,reportSasacId,reportStageId) {
  return request({
    url: '/colligate/violSasacActualReport/deleteActualReportRecord',
    method: 'post',
    data:JSON.stringify({
      reportStageId:reportStageId,
      realtimeReportId:realtimeReportId,
      reportSasacId:reportSasacId
    })
  });
}

export function cancelConfirmedStatus(data) {
  return request({
    url: '/colligate/violSasacReportManagement/cancelConfirmedStatus',
    method: 'post',
    data: data
  });
}

export function regularReportData(reportSasacId) {
  return request({
    url: '/colligate/violSasacRegularReport/regularReportData/' + reportSasacId,
    method: 'post'
  });
}

export function waitEditRegularReportData(id) {
  return request({
    url: '/colligate/violSasacRegularReport/waitEditRegularReportData/' + id,
    method: 'post'
  });
}

export function switchReportInterval(data) {
  return request({
    url: '/colligate/violSasacRegularReport/switchReportInterval',
    method: 'post',
    data: data
  });
}

export function saveEditableCell(data) {
  return request({
    url: '/colligate/violSasacRegularReport/saveEditableCell',
    method: 'post',
    data: data
  });
}

export function saveEditedRegularReportData(data) {
  return request({
    url: '/colligate/violSasacRegularReport/saveEditedRegularReportData',
    method: 'post',
    data: data
  });
}

export function downloadFilledDataTemplate(data) {
  return request({
    url: '/colligate/violSasacRegularReport/downloadFilledDataTemplate',
    method: 'post',
    data: data
  });
}

export function deleteRegularPartUploadedFile(data) {
  return request({
    url: '/colligate/violSasacRegularReport/deleteRegularPartUploadedFile',
    method: 'post',
    data: data
  });
}
