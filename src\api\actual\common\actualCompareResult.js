import request from '@/utils/request';

/**
 * 保存违规追责实时报送与日常报送对比数据
 * @param data
 */
export function saveActualCompareWithDailyData(data) {
  return request({
    url: '/colligate/violActualCompareResult/saveActualCompareWithDailyData',
    method: 'post',
    data: data
  });
}

/**
 * 获取实时报送与日常报送对比结果数据
 * @param data
 */
export function actualAndDailyCompareData(data) {
  return request({
    url: '/colligate/violActualCompareResult/actualAndDailyCompareData',
    method: 'post',
    data: data
  });
}
