import request from '@/utils/request'

// 查询页面信息
export function queryDailyCheckInfo(data) {
  return request({
    url: '/colligate/violationDailyReform/findViolationReformRecord/' + data,
    method: 'post',
    data: data
  })
}

// 保存数据

export function saveViolationReformRecord(data) {
  return request({
    url: '/colligate/violationDailyReform/saveViolationReformRecord',
    method: 'post',
    data: data
  })
}

// 保存数据

export function temporarySaveViolationReformRecord(data) {
  return request({
    url: '/colligate/violationDailyReform/temporarySaveViolationReformRecord',
    method: 'post',
    data: data
  })
}

// 保存整改责任单位
export function saveReformResponsibleUnit(data) {
  return request({
    url: '/colligate/violationDailyReform/saveReformResponsibleUnit',
    method: 'post',
    data: data
  })
}

// 保存整改责任人
export function saveReformResponsiblePerson(data) {
  return request({
    url: '/colligate/violationDailyReform/saveReformResponsiblePerson',
    method: 'post',
    data: data
  })
}

// 删除单位
export function deleteReformResponsibleUnit(data) {
  return request({
    url: '/colligate/violationDailyReform/deleteReformResponsibleUnit/'+data,
    method: 'post',
    data: data
  })
}

// 删除责任人
export function deleteReformResponsiblePerson(data) {
  return request({
    url: '/colligate/violationDailyReform/deleteReformResponsiblePerson/'+data,
    method: 'post',
    data: data
  })
}
