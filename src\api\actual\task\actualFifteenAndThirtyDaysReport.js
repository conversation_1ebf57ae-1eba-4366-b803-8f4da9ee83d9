import request from '@/utils/request';

/**
 * 查询待处理的15日报告
 * @param actualProblemId
 */
export function waitHandleFifteenReport(actualProblemId) {
  return request({
    url: '/colligate/violActualFifteenReport/waitHandleFifteenReport/' + actualProblemId,
    method: 'post'
  });
}

/**
 * 保存十五日报告
 * @param data
 */
export function saveFifteenReport(data) {
  return request({
    url: '/colligate/violActualFifteenReport/saveFifteenReport',
    method: 'post',
    data: data
  });
}

/**
 * 十五日实时报告提交
 * @param data
 */
export function submitFifteenReport(data) {
  return request({
    url: '/colligate/violActualFifteenReport/submitFifteenReport',
    method: 'post',
    data: data
  });
}

/**
 * 违规追责实时报送十五日报告与日常报送问题比较
 * @param data
 */
export function fifteenReportCompareWithDailyProblem(data) {
  return request({
    url: '/colligate/violActualCompareResult/fifteenReportCompareWithDailyProblem',
    method: 'post',
    data: data
  });
}

/**
 * 查询待处理的30日报告
 * @param actualProblemId
 */
export function waitHandleThirtyReport(actualProblemId) {
  return request({
    url: '/colligate/violActualFifteenReport/waitHandleThirtyReport/' + actualProblemId,
    method: 'post'
  });
}

/**
 * 保存30日报告
 * @param data
 */
export function saveThirtyReport(data) {
  return request({
    url: '/colligate/violActualFifteenReport/saveThirtyReport',
    method: 'post',
    data: data
  });
}

/**
 * 30日实时报告提交
 * @param data
 */
export function submitThirtyReport(data) {
  return request({
    url: '/colligate/violActualFifteenReport/submitThirtyReport',
    method: 'post',
    data: data
  });
}

/**
 * 违规追责实时报送三十日报告与日常报送问题比较
 * @param data
 */
export function thirtyReportCompareWithDailyProblem(data) {
  return request({
    url: '/colligate/violActualCompareResult/thirtyReportCompareWithDailyProblem',
    method: 'post',
    data: data
  });
}
