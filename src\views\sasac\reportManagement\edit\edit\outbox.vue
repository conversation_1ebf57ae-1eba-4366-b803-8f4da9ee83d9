<template>
  <div>
    <el-container style="border: none;">
      <el-dialog  :visible.sync="visible" width="90%" append-to-body :modal-append-to-body="false" @close="close" title="发件箱">
      <el-main>
        <div style=" margin-bottom: 30px;">
          <span class="submit-s" v-show="dataDetails.commitFlag=='1'">【已提交】</span>
          <span class="submit-n" v-show="dataDetails.commitFlag=='0'">【待提交】</span>
          <span class="submit-w"><i class="el-icon-info submit-i"></i>温馨提示：状态为已提交时，其他人员才能查询到</span>
        </div>
        <el-form ref="dataDetails" :model="dataDetails" label-width="120px"  style="padding: 20px 300px; ">

          <el-form-item label="收件人">
            <el-input v-model="dataDetails.receivePerson"></el-input>
          </el-form-item>
          <el-form-item label="发件人">
            <el-input v-model="dataDetails.sendPerson"></el-input>
          </el-form-item>
          <el-form-item label="标题">
            <el-input v-model="dataDetails.title"></el-input>
          </el-form-item>
          <el-form-item label="发件内容">
            <el-input v-model="dataDetails.mailContent"></el-input>
          </el-form-item>
        </el-form>

        <div style="height: 36px; line-height: 36px; margin-bottom: 20px">
          <span class="file-title">附件列表</span>
          <div style="float: right; padding: 3px 0; white-space:nowrap" type="text" >
            <el-upload
              class="upload-demo"
              accept=".doc, .docx"
              :headers="headers"
              :data="fileParams"
              :action="url"
              :show-file-list="false"
              :on-preview="handlePreview"
              :on-success="handleFileSuccess"
            >
              <el-button size="mini"  type="primary">上传</el-button>
            </el-upload>
          </div>
        </div>
        <div>
          <el-table :data="dataDetails.files" max-height="250" style="width: 100%" border :show-header="false" :cell-class-name="rowClass">
            <el-table-column label="序号" type="index"  min-width="10%" align="center" />
            <el-table-column label="文档名称" prop="fileName" min-width="55%"/>
            <el-table-column label="上传人" prop="createLoginName"  min-width="13%"/>
            <el-table-column label="上传时间" prop="createTime" :formatter="dateFormat" min-width="13%"/>
            <el-table-column label="操作" fixed="right" min-width="9%" align="center" class-name="small-padding fixed-width">
              <template slot-scope="scope">
                <i class="el-icon-delete" style="color: red" @click="deleteFile(scope.row)" ></i>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-main>
        <div slot="footer">
          <el-button size="mini" @click="saveSendBoxBaseInfo(0)">保存</el-button>
          <el-button size="mini" type="primary" @click="saveSendBoxBaseInfo(1)">提交</el-button>
        </div>
    </el-dialog>
    </el-container>
  </div>
</template>

<script lang="ts">
  import {getSendBoxOne, saveSendBox, deleteViolFile, getSendBoxById, getBaseSendBoxNull } from "@/api/base/sendBox";
  import moment from "moment";
  import { getToken } from "@/utils/auth";

  export default {
    name: "sendBox",
    data() {
      return {
        visible:false,
        dataDetails: {},
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        url: process.env.VUE_APP_API_URL + '/colligate/baseInfo/uploadViolFile',
        fileParams: {
          busiId: '',
          busiTableName: 'T_COL_VIOL_BASE_SEND_BOX',
        },
        commitFlag: 0,
      };
    },
    created() {
    },
    methods: {
      /**查询企业基本信息详情*/
      onShow() {
        this.visible=true;
        getSendBoxOne().then(
          response => {
            this.dataDetails = response.data;
            this.fileParams = {
              ...this.fileParams,
              busiId: response.data.id,
            };
          }
        );
      },
      //关闭弹框
      close(){
        this.visible = false;
        this.$emit('editClose', this.commitFlag);
        this.commitFlag = 0;
      },
      /**保存或提交*/
      saveSendBoxBaseInfo(commitFlag){
        const params = {
          ...this.dataDetails,
          commitFlag
        };
        saveSendBox(params).then(
          response => {
            if(response.code === 200){
              this.$message({
                message: response.msg,
                type: 'success'
              });
              this.onShow();
              if(commitFlag){
                this.commitFlag = 1;
                this.close();
              }
            }else{
              this.$message.error(response.msg);
            }
          }
        );
      },
      /*日期处理*/
      dateFormat:function(row){
        if(row.createTime === undefined){
          return ''
        }
        return moment(row.createTime).format("YYYY-MM-DD")
      },
      /*删除附件*/
      deleteFile: function(row){
        this.$confirm('确认删除附件【' + row.fileName + '】吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          deleteViolFile({
            busiTableName:'T_COL_VIOL_BASE_SEND_BOX',
            busiId:this.dataDetails.id,
            id:row.id,
          }).then(response =>{
            if(response.code === 200){
              this.handleFileSuccess();
            }else{
              this.$message.error(response.msg);
            }
          })
        });
      },
      /*附件上传之前*/
      handlePreview: function(file){
        if(!this.dataDetails || !this.dataDetails.id){
          getBaseSendBoxNull().then(
            response => {
              this.dataDetails = response.data;
              this.fileParams = {
                ...this.fileParams,
                busiId: response.data.id,
              };
            }
          );
        }
      },
      /**附件上传成功*/
      handleFileSuccess(){
        if(this.dataDetails.version == -2){
          getSendBoxById({id: this.dataDetails.id}).then(response =>{
            this.dataDetails = response.data;
            this.fileParams = {
              ...this.fileParams,
              busiId: response.data.id,
            };
          })
        }else{
          this.onShow();
        }
      },
      /** 修改附件表样式 */
      rowClass ({ row, column, rowIndex, columnIndex }) {
        if (columnIndex === 1 || columnIndex === 2 || columnIndex === 3) {
          return 'no-right-border'
        }else if(columnIndex === 0){
          return 'cell-color'
        }
      },
    }
  };
</script>

<style scoped>
  @import "../../../../base/common/common.css";

</style>
