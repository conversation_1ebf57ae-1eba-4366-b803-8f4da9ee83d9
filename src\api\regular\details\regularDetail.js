import request from '@/utils/request'

//查询全部数量
export function listLength0(regularReportId) {
  return request({
    url: '/colligate/violRegular/report/selectReportDetailList/'+regularReportId+"/0",
    method: 'post'
  })
}
//查询进行中数量
export function listLength1(regularReportId) {
  return request({
    url: '/colligate/violRegular/report/selectReportDetailList/'+regularReportId+"/1",
    method: 'post'
  })
}
//查询已完成数量
export function listLength2(regularReportId) {
  return request({
    url: '/colligate/violRegular/report/selectReportDetailList/'+regularReportId+"/2",
    method: 'post'
  })
}

//查询上报单位列表
export function selectReportDetailList(regularReportId,type){
  return request({
    url: '/colligate/violRegular/report/selectReportDetailList/' + regularReportId + '/' + type,
    method: 'post'
  })
}

//单条催办
export function regularReportUrge(regularReportId,reportUnitId){
  return request({
    url: '/colligate/violRegular/report/regularReportUrge/' + regularReportId + '/' + reportUnitId,
    method: 'post'
  })
}

//批量催办
export function regularReportUrgeAll(regularReportId,idList){
  return request({
    url: '/colligate/violRegular/report/regularReportUrgeAll/' + regularReportId ,
    method: 'post',
    data:JSON.stringify(idList)
  })
}

//查询已保存的经办人填报数据
export function savedHandlerFillData(reportUnitId){
  return request({
    url: '/colligate/violRegularHandlerFill/savedHandlerFillData/' + reportUnitId ,
    method: 'post'
  })
}
