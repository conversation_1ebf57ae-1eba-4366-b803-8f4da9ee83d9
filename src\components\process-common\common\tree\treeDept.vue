<template>
  <div>
    <el-tree ref="tree"
             :data="data"
             lazy
             node-key="id"
             @check-change="checkChange"
             :load="loadnode"
             :props="defaultProps"
             @node-click="nodeclick">
    </el-tree>
  </div>
</template>

<script>
  import {userTree,treeUrl} from "@/api/common/index";

  export default {
    components: {
    },
    props: {
    },
    data() {
      return {
        selectTree:[],
        provCode:'',
        url:'/system/userTree/departmentUserTreeInProcess',
        data:[],
        defaultTree:[],
        query:{
          name:'',
          areaCode:'',
          isParent:'',
          provCode:this.provCode,
          checked:'',
          id:'',
          pId:'',
          isAll:false,
          open:false,
          nocheck:'',
          userId:'',
          selectName:'',
        },
        defaultProps: {//树对象属性对应关系
          children: 'children',
          label: 'name',
          isLeaf:function(data, node){
            return !data.isParent
          },
          disabled:function(data, node){
            return data.isParent
          }
        }
      }
    },
    methods: {
      //查询人员姓名
      treeQuery(){
        treeUrl(this.url,this.query).then(
          response => {
            this.data = response.data
          }
        );
      },
      loadnode(node,resolve){
        //如果展开第一级节点，从后台加载一级节点列表
        if(node.level==0)
        {
          this.loadfirstnode(resolve);
        }
        //如果展开其他级节点，动态从后台加载下一级节点列表
        if(node.level>=1)
        {
          this.loadchildnode(node,resolve);
        }
      },
      //加载第一级节点
      loadfirstnode(resolve){
        treeUrl(this.url,this.query).then(
          response => {
            resolve(response.data);
          }
        );
      },
      //加载节点的子节点集合
      loadchildnode(node,resolve){
        node.data.provCode = this.provCode;
        treeUrl(this.url,node.data).then(
          response => {
            resolve(response.data);
          }
        );
      },
      //点击节点上触发的事件，传递三个参数，数据对象使用第一个参数
      nodeclick(data,dataObj,self)
      {
        if(!data.isParent){//人员
          this.selectTree=[data];
          this.save();
        }
      },
      //修改状态
      checkChange(node,type){

      },
      //返回数据
      save(){
        this.$emit('accept',this.selectTree)
      }
    }
  }
</script>

<style scoped>

</style>
