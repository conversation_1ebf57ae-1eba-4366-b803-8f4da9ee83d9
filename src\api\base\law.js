import request from '@/utils/request'

// 基础数据查询--规章制度列表查询
export function getBaseLawPage(query) {
  return request({
    url: '/colligate/baseInfo/getBaseLawPage',
    method: 'post',
    data: query
  })
}


// 基础数据查询--规章制度详情查询
export function getBaseLaw(query) {
  return request({
    url: '/colligate/baseInfo/getBaseLaw',
    method: 'post',
    data: query
  })
}


// 基础数据维护--规章制度列表查询
export function getBaseLawList(query) {
  return request({
    url: '/colligate/baseInfo/getBaseLawList',
    method: 'post',
    data: query
  })
}

// 基础数据维护--规章制度保存或提交
export function saveBaseLaw(query) {
  return request({
    url: '/colligate/baseInfo/saveBaseLaw',
    method: 'post',
    data: query
  })
}

// 基础数据维护--规章制度保存或提交
export function getBaseLawNull() {
  return request({
    url: '/colligate/baseInfo/getBaseLawNull',
    method: 'post',
  })
}

// 基础数据维护--规章制度根据唯一标识获取最新版本
export function getBaseLawNewVerson(query) {
  return request({
    url: '/colligate/baseInfo/getBaseLawNewVerson',
    method: 'post',
    data: query
  })
}

// 基础数据维护--规章制度根据唯一标识获取最新版本
export function delBaseLaw(query) {
  return request({
    url: '/colligate/baseInfo/delBaseLaw',
    method: 'post',
    data: query
  })
}

// 基础数据维护--规章制度根据ID物理删除数据
export function delBaseLawById(query) {
  return request({
    url: '/colligate/baseInfo/delBaseLawById',
    method: 'post',
    data: query
  })
}


