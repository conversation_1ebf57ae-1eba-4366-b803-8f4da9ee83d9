{"version": 3, "sources": ["webpack:///src/views/system/user/authRole.vue", "webpack:///./src/views/system/user/authRole.vue?e41a", "webpack:///./src/views/system/user/authRole.vue", "webpack:///./src/views/system/user/authRole.vue?80eb", "webpack:///./src/views/system/user/authRole.vue?cb15"], "names": ["name", "data", "loading", "total", "pageNum", "pageSize", "roleIds", "roles", "form", "created", "_this", "userId", "$route", "params", "getAuthRole", "then", "response", "user", "length", "$nextTick", "for<PERSON>ach", "row", "flag", "$refs", "table", "toggleRowSelection", "methods", "clickRow", "handleSelectionChange", "selection", "map", "item", "roleId", "getRowKey", "submitForm", "_this2", "join", "updateAuthRole", "$modal", "msgSuccess", "close", "obj", "path", "$tab", "closeOpenPage"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgDA;AAEe;EACfA,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,KAAA;MACAC,OAAA;MACAC,QAAA;MACA;MACAC,OAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IACA,IAAAC,MAAA,QAAAC,MAAA,CAAAC,MAAA,SAAAD,MAAA,CAAAC,MAAA,CAAAF,MAAA;IACA,IAAAA,MAAA;MACA,KAAAT,OAAA;MACAY,oEAAA,CAAAH,MAAA,EAAAI,IAAA,WAAAC,QAAA;QACAN,KAAA,CAAAF,IAAA,GAAAQ,QAAA,CAAAC,IAAA;QACAP,KAAA,CAAAH,KAAA,GAAAS,QAAA,CAAAT,KAAA;QACAG,KAAA,CAAAP,KAAA,GAAAO,KAAA,CAAAH,KAAA,CAAAW,MAAA;QACAR,KAAA,CAAAS,SAAA;UACAT,KAAA,CAAAH,KAAA,CAAAa,OAAA,WAAAC,GAAA;YACA,IAAAA,GAAA,CAAAC,IAAA;cACAZ,KAAA,CAAAa,KAAA,CAAAC,KAAA,CAAAC,kBAAA,CAAAJ,GAAA;YACA;UACA;QACA;QACAX,KAAA,CAAAR,OAAA;MACA;IACA;EACA;EACAwB,OAAA;IACA,cACAC,QAAA,WAAAA,SAAAN,GAAA;MACA,KAAAE,KAAA,CAAAC,KAAA,CAAAC,kBAAA,CAAAJ,GAAA;IACA;IACA;IACAO,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAAvB,OAAA,GAAAuB,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAC,MAAA;MAAA;IACA;IACA;IACAC,SAAA,WAAAA,UAAAZ,GAAA;MACA,OAAAA,GAAA,CAAAW,MAAA;IACA;IACA,WACAE,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,IAAAxB,MAAA,QAAAH,IAAA,CAAAG,MAAA;MACA,IAAAL,OAAA,QAAAA,OAAA,CAAA8B,IAAA;MACAC,uEAAA;QAAA1B,MAAA,EAAAA,MAAA;QAAAL,OAAA,EAAAA;MAAA,GAAAS,IAAA,WAAAC,QAAA;QACAmB,MAAA,CAAAG,MAAA,CAAAC,UAAA;QACAJ,MAAA,CAAAK,KAAA;MACA;IACA;IACA,WACAA,KAAA,WAAAA,MAAA;MACA,IAAAC,GAAA;QAAAC,IAAA;MAAA;MACA,KAAAC,IAAA,CAAAC,aAAA,CAAAH,GAAA;IACA;EACA;AACA,CAAC,E;;;;;;;;;;;;ACnHD;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK,+BAA+B;AACpC;AACA,gBAAgB,gCAAgC;AAChD;AACA;AACA,SAAS,sBAAsB,yCAAyC,EAAE;AAC1E;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB,SAAS,qBAAqB,EAAE;AACjD;AACA;AACA;AACA,qBAAqB,SAAS,kCAAkC,EAAE;AAClE;AACA;AACA,gCAAgC,eAAe;AAC/C;AACA;AACA;AACA;AACA,2BAA2B;AAC3B;AACA,yBAAyB;AACzB,uBAAuB;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB,SAAS,qBAAqB,EAAE;AACjD;AACA;AACA;AACA,qBAAqB,SAAS,qCAAqC,EAAE;AACrE;AACA;AACA,gCAAgC,eAAe;AAC/C;AACA;AACA;AACA;AACA,2BAA2B;AAC3B;AACA,yBAAyB;AACzB,uBAAuB;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB,gCAAgC;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA,WAAW;AACX,SAAS;AACT;AACA;AACA,oBAAoB,8CAA8C;AAClE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB,eAAe;AACf;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA,aAAa;AACb,WAAW;AACX;AACA,oBAAoB,iDAAiD;AACrE,WAAW;AACX;AACA,oBAAoB,mDAAmD;AACvE,WAAW;AACX;AACA,oBAAoB,kDAAkD;AACtE,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB,eAAe;AACf;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA,gBAAgB,2DAA2D;AAC3E;AACA;AACA;AACA,WAAW;AACX;AACA;AACA,WAAW;AACX,SAAS;AACT,OAAO;AACP;AACA;AACA,SAAS,SAAS,yBAAyB,EAAE;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf,aAAa;AACb;AACA;AACA;AACA;AACA,0BAA0B,kBAAkB;AAC5C;AACA;AACA;AACA,qBAAqB;AACrB,mBAAmB;AACnB,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB,mBAAmB;AACnB,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC7NA;AAAA;AAAA;AAAA;AAAuF;AAC3B;AACL;;;AAGvD;AACgG;AAChG,gBAAgB,2GAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,mFAAM;AACR,EAAE,4FAAe;AACjB;AACA;AACA;AACA;;AAEA;;AAEA;AACA,IAAI,KAAU,EAAE,YAiBf;AACD;AACe,gF;;;;;;;;;;;;ACtCf;AAAA;AAAA,wCAA6S,CAAgB,yUAAG,EAAC,C;;;;;;;;;;;;ACAjU;AAAA;AAAA;AAAA;AAAA;AAAA", "file": "js/21.1693388085916.js", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <h4 class=\"form-header h4\">基本信息</h4>\r\n    <el-form ref=\"form\" :model=\"form\" label-width=\"80px\">\r\n      <el-row>\r\n        <el-col :span=\"8\" :offset=\"2\">\r\n          <el-form-item label=\"用户昵称\" prop=\"nickName\">\r\n            <el-input v-model=\"form.nickName\" disabled />\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"8\" :offset=\"2\">\r\n          <el-form-item label=\"登录账号\" prop=\"phonenumber\">\r\n            <el-input  v-model=\"form.userName\" disabled />\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n    </el-form>\r\n\r\n    <h4 class=\"form-header h4\">角色信息</h4>\r\n    <el-table v-loading=\"loading\" :row-key=\"getRowKey\" @row-click=\"clickRow\" ref=\"table\" @selection-change=\"handleSelectionChange\" :data=\"roles.slice((pageNum-1)*pageSize,pageNum*pageSize)\">\r\n      <el-table-column label=\"序号\" type=\"index\" align=\"center\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{(pageNum - 1) * pageSize + scope.$index + 1}}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column type=\"selection\" :reserve-selection=\"true\" width=\"55\"></el-table-column>\r\n      <el-table-column label=\"角色编号\" align=\"center\" prop=\"roleId\" />\r\n      <el-table-column label=\"角色名称\" align=\"center\" prop=\"roleName\" />\r\n      <el-table-column label=\"权限字符\" align=\"center\" prop=\"roleKey\" />\r\n      <el-table-column label=\"创建时间\" align=\"center\" prop=\"createTime\" width=\"180\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.createTime) }}</span>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n    \r\n    <pagination v-show=\"total>0\" :total=\"total\" :page.sync=\"pageNum\" :limit.sync=\"pageSize\" />\r\n\r\n    <el-form label-width=\"100px\">\r\n      <el-form-item style=\"text-align: center;margin-left:-120px;margin-top:30px;\">\r\n        <el-button type=\"primary\" @click=\"submitForm()\">提交</el-button>\r\n        <el-button @click=\"close()\">返回</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getAuthRole, updateAuthRole } from \"@/api/system/user\";\r\n\r\nexport default {\r\n  name: \"AuthRole\",\r\n  data() {\r\n    return {\r\n       // 遮罩层\r\n      loading: true,\r\n      // 分页信息\r\n      total: 0,\r\n      pageNum: 1,\r\n      pageSize: 10,\r\n      // 选中角色编号\r\n      roleIds:[],\r\n      // 角色信息\r\n      roles: [],\r\n      // 用户信息\r\n      form: {}\r\n    };\r\n  },\r\n  created() {\r\n    const userId = this.$route.params && this.$route.params.userId;\r\n    if (userId) {\r\n      this.loading = true;\r\n      getAuthRole(userId).then((response) => {\r\n        this.form = response.user;\r\n        this.roles = response.roles;\r\n        this.total = this.roles.length;\r\n        this.$nextTick(() => {\r\n          this.roles.forEach((row) => {\r\n            if (row.flag) {\r\n              this.$refs.table.toggleRowSelection(row);\r\n            }\r\n          });\r\n        });\r\n        this.loading = false;\r\n      });\r\n    }\r\n  },\r\n  methods: {\r\n    /** 单击选中行数据 */\r\n    clickRow(row) {\r\n      this.$refs.table.toggleRowSelection(row);\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.roleIds = selection.map((item) => item.roleId);\r\n    },\r\n    // 保存选中的数据编号\r\n    getRowKey(row) {\r\n      return row.roleId;\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      const userId = this.form.userId;\r\n      const roleIds = this.roleIds.join(\",\");\r\n      updateAuthRole({ userId: userId, roleIds: roleIds }).then((response) => {\r\n        this.$modal.msgSuccess(\"授权成功\");\r\n        this.close();\r\n      });\r\n    },\r\n    /** 关闭按钮 */\r\n    close() {\r\n      const obj = { path: \"/system/user\" };\r\n      this.$tab.closeOpenPage(obj);\r\n    },\r\n  },\r\n};\r\n</script>", "var render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return _c(\n    \"div\",\n    { staticClass: \"app-container\" },\n    [\n      _c(\"h4\", { staticClass: \"form-header h4\" }, [_vm._v(\"基本信息\")]),\n      _c(\n        \"el-form\",\n        { ref: \"form\", attrs: { model: _vm.form, \"label-width\": \"80px\" } },\n        [\n          _c(\n            \"el-row\",\n            [\n              _c(\n                \"el-col\",\n                { attrs: { span: 8, offset: 2 } },\n                [\n                  _c(\n                    \"el-form-item\",\n                    { attrs: { label: \"用户昵称\", prop: \"nickName\" } },\n                    [\n                      _c(\"el-input\", {\n                        attrs: { disabled: \"\" },\n                        model: {\n                          value: _vm.form.nickName,\n                          callback: function ($$v) {\n                            _vm.$set(_vm.form, \"nickName\", $$v)\n                          },\n                          expression: \"form.nickName\",\n                        },\n                      }),\n                    ],\n                    1\n                  ),\n                ],\n                1\n              ),\n              _c(\n                \"el-col\",\n                { attrs: { span: 8, offset: 2 } },\n                [\n                  _c(\n                    \"el-form-item\",\n                    { attrs: { label: \"登录账号\", prop: \"phonenumber\" } },\n                    [\n                      _c(\"el-input\", {\n                        attrs: { disabled: \"\" },\n                        model: {\n                          value: _vm.form.userName,\n                          callback: function ($$v) {\n                            _vm.$set(_vm.form, \"userName\", $$v)\n                          },\n                          expression: \"form.userName\",\n                        },\n                      }),\n                    ],\n                    1\n                  ),\n                ],\n                1\n              ),\n            ],\n            1\n          ),\n        ],\n        1\n      ),\n      _c(\"h4\", { staticClass: \"form-header h4\" }, [_vm._v(\"角色信息\")]),\n      _c(\n        \"el-table\",\n        {\n          directives: [\n            {\n              name: \"loading\",\n              rawName: \"v-loading\",\n              value: _vm.loading,\n              expression: \"loading\",\n            },\n          ],\n          ref: \"table\",\n          attrs: {\n            \"row-key\": _vm.getRowKey,\n            data: _vm.roles.slice(\n              (_vm.pageNum - 1) * _vm.pageSize,\n              _vm.pageNum * _vm.pageSize\n            ),\n          },\n          on: {\n            \"row-click\": _vm.clickRow,\n            \"selection-change\": _vm.handleSelectionChange,\n          },\n        },\n        [\n          _c(\"el-table-column\", {\n            attrs: { label: \"序号\", type: \"index\", align: \"center\" },\n            scopedSlots: _vm._u([\n              {\n                key: \"default\",\n                fn: function (scope) {\n                  return [\n                    _c(\"span\", [\n                      _vm._v(\n                        _vm._s(\n                          (_vm.pageNum - 1) * _vm.pageSize + scope.$index + 1\n                        )\n                      ),\n                    ]),\n                  ]\n                },\n              },\n            ]),\n          }),\n          _c(\"el-table-column\", {\n            attrs: {\n              type: \"selection\",\n              \"reserve-selection\": true,\n              width: \"55\",\n            },\n          }),\n          _c(\"el-table-column\", {\n            attrs: { label: \"角色编号\", align: \"center\", prop: \"roleId\" },\n          }),\n          _c(\"el-table-column\", {\n            attrs: { label: \"角色名称\", align: \"center\", prop: \"roleName\" },\n          }),\n          _c(\"el-table-column\", {\n            attrs: { label: \"权限字符\", align: \"center\", prop: \"roleKey\" },\n          }),\n          _c(\"el-table-column\", {\n            attrs: {\n              label: \"创建时间\",\n              align: \"center\",\n              prop: \"createTime\",\n              width: \"180\",\n            },\n            scopedSlots: _vm._u([\n              {\n                key: \"default\",\n                fn: function (scope) {\n                  return [\n                    _c(\"span\", [\n                      _vm._v(_vm._s(_vm.parseTime(scope.row.createTime))),\n                    ]),\n                  ]\n                },\n              },\n            ]),\n          }),\n        ],\n        1\n      ),\n      _c(\"pagination\", {\n        directives: [\n          {\n            name: \"show\",\n            rawName: \"v-show\",\n            value: _vm.total > 0,\n            expression: \"total>0\",\n          },\n        ],\n        attrs: { total: _vm.total, page: _vm.pageNum, limit: _vm.pageSize },\n        on: {\n          \"update:page\": function ($event) {\n            _vm.pageNum = $event\n          },\n          \"update:limit\": function ($event) {\n            _vm.pageSize = $event\n          },\n        },\n      }),\n      _c(\n        \"el-form\",\n        { attrs: { \"label-width\": \"100px\" } },\n        [\n          _c(\n            \"el-form-item\",\n            {\n              staticStyle: {\n                \"text-align\": \"center\",\n                \"margin-left\": \"-120px\",\n                \"margin-top\": \"30px\",\n              },\n            },\n            [\n              _c(\n                \"el-button\",\n                {\n                  attrs: { type: \"primary\" },\n                  on: {\n                    click: function ($event) {\n                      return _vm.submitForm()\n                    },\n                  },\n                },\n                [_vm._v(\"提交\")]\n              ),\n              _c(\n                \"el-button\",\n                {\n                  on: {\n                    click: function ($event) {\n                      return _vm.close()\n                    },\n                  },\n                },\n                [_vm._v(\"返回\")]\n              ),\n            ],\n            1\n          ),\n        ],\n        1\n      ),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }", "import { render, staticRenderFns } from \"./authRole.vue?vue&type=template&id=2a58ea3c&\"\nimport script from \"./authRole.vue?vue&type=script&lang=js&\"\nexport * from \"./authRole.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\n/* hot reload */\nif (module.hot) {\n  var api = require(\"E:\\\\workspace\\\\info\\\\trigram-ui\\\\node_modules\\\\vue-hot-reload-api\\\\dist\\\\index.js\")\n  api.install(require('vue'))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!api.isRecorded('2a58ea3c')) {\n      api.createRecord('2a58ea3c', component.options)\n    } else {\n      api.reload('2a58ea3c', component.options)\n    }\n    module.hot.accept(\"./authRole.vue?vue&type=template&id=2a58ea3c&\", function () {\n      api.rerender('2a58ea3c', {\n        render: render,\n        staticRenderFns: staticRenderFns\n      })\n    })\n  }\n}\ncomponent.options.__file = \"src/views/system/user/authRole.vue\"\nexport default component.exports", "import mod from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./authRole.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./authRole.vue?vue&type=script&lang=js&\"", "export * from \"-!../../../../node_modules/cache-loader/dist/cjs.js?{\\\"cacheDirectory\\\":\\\"node_modules/.cache/vue-loader\\\",\\\"cacheIdentifier\\\":\\\"f1aaaf00-vue-loader-template\\\"}!../../../../node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./authRole.vue?vue&type=template&id=2a58ea3c&\""], "sourceRoot": ""}