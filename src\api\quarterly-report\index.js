import request from '@/utils/request'

export function queryDefaultInfo(){
  return request({
    url: '/quarter/edit/queryDefaultInfo',
    method: 'post'
  })
}

export function baseInfoValidate(params){
  return request({
    url: '/quarter/validate/baseInfoValidate',
    method: 'post',
    data:params
  })
}
//查询附件列表
export function queryQuarterReportFileList(reportProvId){
  return request({
    url: '/quarter/file/queryQuarterReportFileList/'+reportProvId,
    method: 'post'
  })
}
//删除附件
export function delQuarterReportFile(fileId){
  return request({
    url: '/quarter/file/delQuarterReportFile/'+fileId,
    method: 'post'
  })
}
//新增、编辑查询上报内容
export function queryQuarterReportInfo(params){
  return request({
    url: '/quarter/edit/queryQuarterReportInfo',
    method: 'post',
    data:params
  })
}
//保存上报内容
export function saveQuarterReportInfo(params) {
  return request({
    url: '/quarter/edit/saveQuarterReportInfo',
    method: 'post',
    data:params
  })
}
//查询流程参数
export function flowParams() {
  return request({
    url: '/quarter/report/flow/flowParams',
    method: 'post'
  })
}

//提交校验
export function submitValidateProvInfo(reportProvInfo) {
  return request({
    url: '/quarter/validate/submitValidateProvInfo',
    method: 'post',
    data:reportProvInfo,
    tostType:true
  })
}

//驳回
export function rejectQuarterReportInfo(params){
  return request({
    url: '/quarter/edit/rejectQuarterReportInfo',
    method: 'post',
    data: params,
  })
}

export function saveProblemFlowEndInfo(params) {
  return request({
    url: '/quarter/edit/saveProblemFlowEndInfo',
    method: 'post',
    data:params
  })
}


//下级单位上报情况-更多
export function restoreInitData(reportProvId){
  return request({
    url: '/quarter/edit/restoreInitData/'+reportProvId,
    method: 'post'
  })
}
