<!--规章制度-->
<template>
  <div class="padding_b10 app-lawList">
    <SearchList :searchList="searchList" @on-search="handleQuery" @on-reset="resetQuery"></SearchList>
    <el-table border :data="tableList" :header-cell-style="{background:'#F4F8FC',color:'#606266'}">
      <el-table-column label="序号" type="index" min-width="4%" align="center">
        <template slot-scope="scope">
          <table-index
          :index="scope.$index"
          :pageNum="queryParams.pageNum"
          :pageSize="queryParams.pageSize"
          />
        </template>
      </el-table-column>
      <el-table-column label="单位名称" prop="involProvName" min-width="8%"  show-overflow-tooltip  align="center"/>
      <el-table-column label="标题" prop="title"  min-width="25%" show-overflow-tooltip  align="left"/>
      <el-table-column label="文号" prop="issueCode"  min-width="15%" show-overflow-tooltip align="center"/>
      <el-table-column label="印发日期" prop="publishDate" :formatter="dateFormat" min-width="10%" align="center"/>
      <el-table-column label="施行日期" prop="implementDate" :formatter="dateFormat" min-width="10%" align="center"/>
      <el-table-column label="制度类型" prop="classifyText" min-width="8%" align="center"/>
      <el-table-column label="类别" prop="categoryText" min-width="8%" align="center"/>
      <el-table-column label="制度文件" prop="lawNum" min-width="5%" align="center"/>
      <el-table-column label="操作" fixed="right" width="150" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            v-if="scope.row.dispatchStatus != 0"
            size="mini"
            type="text"
            icon="el-icon-search"
            title="查看"
            @click="handleDetails(scope.row)"
          ></el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="lawBaseInfo"
    />
    <lawDetail v-if="dialogVisible" v-on:closeModal="closeModal" :id="id"></lawDetail>
  </div>
</template>

<script>
  import {getBaseLawPage} from "@/api/base/law";
  import lawDetail from "./lawDetail";
  import SearchList from "../../common/SearchList";
  import moment from "moment"

  export default {
    name: "lawList",
    components: { lawDetail, SearchList  },
    data() {
      return {
        // 总条数
        total: 0,
        // 表格数据
        tableList: [],
        // 是否显示弹出层
        dialogVisible: false,
        id:'',
        //新增主键
        //日常问题查询 参数
        queryParams: {
          pageNum: 1,
          pageSize: 10,
          involProvName:'',
          title:'',
        },
        searchList: [
          {
            label: "单位名称",
            name: "involProvName",
            value: null,
            type: "Input"
          },
          {
            label: "标题",
            name: "title",
            value: null,
            type: "Input"
          }
        ]
      };
    },
    created() {
      this.lawBaseInfo();
    },
    methods: {
      /**查询企業基本信息列表*/
      lawBaseInfo() {
       //this.loading = true;
        getBaseLawPage(this.queryParams).then(
          response => {
            this.tableList = response.rows;
            this.total = response.total;
            //this.loading = false;
          }
        );
      },
      /** 搜索按钮操作*/
      handleQuery(params) {
        this.queryParams={
          ...this.queryParams,
          ...params,
          pageNum: 1,
        }
        this.lawBaseInfo();
      },
      /**重置按钮操作*/
      resetQuery() {
        this.queryParams = {
          pageNum: 1,
          pageSize: 10,
        };
        this.lawBaseInfo();
      },
      /**查看按钮操作*/
      handleDetails(row) {
        this.dialogVisible = !this.dialogVisible;
        this.id = row.id;
      },
      /**关闭模态框*/
      closeModal(){
        this.dialogVisible = !this.dialogVisible;
      },
      /*日期处理*/
      dateFormat:function(row,column){
        var date = row[column.property];
        if(date === undefined){
          return ''
        }
        return moment(date).format("YYYY-MM-DD")
      },
    }
  };
</script>
<style rel="stylesheet/scss" lang="scss">
</style>







