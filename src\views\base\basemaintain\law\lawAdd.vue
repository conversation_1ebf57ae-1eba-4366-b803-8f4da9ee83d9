<template>
    <el-dialog
        :title="title"
        :visible.sync="dialogVisible"
        width="50%"
        :before-close="handleClose">

        <div style=" margin-bottom: 30px;">
          <span class="submit-s" v-show="dataDetails.commitFlag=='1'">【已提交】</span>
          <span class="submit-n" v-show="dataDetails.commitFlag=='0'">【待提交】</span>
          <span class="submit-w"><i class="el-icon-info submit-i"></i>温馨提示：状态为已提交时，其他人员才能查询到</span>
        </div>

        <div >
          <el-form ref="dataDetails" :model="dataDetails" label-width="120px"  style="margin-top: 20px" :rules="rules">
            <el-form-item label="标题" prop="title">
              <el-input v-model="dataDetails.title"></el-input>
            </el-form-item>
            <el-form-item label="文号" prop="issueCode">
              <el-input v-model="dataDetails.issueCode"></el-input>
            </el-form-item>
            <el-form-item label="印发日期" prop="publishDate">
              <el-date-picker v-model="dataDetails.publishDate" type="date" placeholder="选择印发日期" style="width: 100%;"></el-date-picker>
            </el-form-item>
            <el-form-item label="施行日期" prop="implementDate">
              <el-date-picker v-model="dataDetails.implementDate" type="date" placeholder="选择施行日期" style="width: 100%;"></el-date-picker>
            </el-form-item>
            <el-form-item label="制度类型" prop="classify">
              <el-radio-group v-model="dataDetails.classify">
                <el-radio label="special_law">专门制度</el-radio>
                <el-radio label="supporting_law">配套制度</el-radio>
                <el-radio label="work_law">工作规范</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="类别" prop="category">
              <el-radio-group v-model="dataDetails.category">
                <el-radio label="new_system">新制度</el-radio>
                <el-radio label="revise">修订</el-radio>
              </el-radio-group>
            </el-form-item>
            <div style="height: 36px; line-height: 36px; margin-bottom: 20px">
              <span class="file-title">附件列表</span>
              <div style="float: right; padding: 3px 0; white-space:nowrap" type="text" >
                <el-upload
                  class="upload-demo"
                  accept=".doc, .docx"
                  :headers="headers"
                  :data="fileParams"
                  :action="url"
                  :show-file-list="false"
                  style="display: inline"
                  :before-upload="handlePreview"
                  :on-success="handleFileSuccess"
                  >
                  <el-button size="small" type="primary">制度文件.DOC</el-button>
                </el-upload>
                <el-upload
                  class="upload-demo"
                  accept=".pdf"
                  :headers="headers"
                  :data="fileParams"
                  :action="url"
                  :show-file-list="false"
                  style="display: inline; margin-left: 8px;"
                  :before-upload="handlePreview"
                  :on-success="handleFileSuccess"
                  >
                  <el-button size="small" type="primary">制度文件.PDF</el-button>
                </el-upload>
              </div>
            </div>
            <div>
              <el-table :data="dataDetails.files" max-height="250" style="width: 100%" border :show-header="false":cell-class-name="rowClass">
                <el-table-column label="序号" type="index"  min-width="10%" align="center" />
                <el-table-column label="文档名称" prop="fileName" min-width="55%"/>
                <el-table-column label="上传人" prop="createLoginName"  min-width="12%"/>
                <el-table-column label="上传时间" prop="createTime" :formatter="dateFormat1" min-width="13%"/>
                <el-table-column label="操作" fixed="right" min-width="10%" align="center" class-name="small-padding fixed-width">
                  <template slot-scope="scope">
                    <i class="el-icon-delete" style="color: red" @click="deleteFile(scope.row)" ></i>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-form>

        </div>

        <span slot="footer" class="dialog-footer">
              <el-button size="mini" @click="saveLawBaseInfo(0)" :loading="buttonLoading=='save'">保存</el-button>
              <el-button size="mini" type="primary" @click="saveLawBaseInfo(1)" :loading="buttonLoading=='sub'">提交</el-button>
        </span>
    </el-dialog>
</template>

<script lang="ts">
  import {getBaseLaw, saveBaseLaw, getBaseLawNull, getBaseLawNewVerson, delBaseLawById} from "@/api/base/law";
  import {deleteViolFile} from "@/api/base/sendBox";
  import moment from "moment";
  import { getToken } from "@/utils/auth";

  export default {
    name: "lawAdd",
    props: {
      dialogVisible: {
        type: Boolean,
        default: true
      },
      id: {
        type: String,
        default: null
      },
    },
    data() {
      return {
        dataDetails: {
          classify:null,
          category:null,
        },
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        url: process.env.VUE_APP_BASE_API + '/colligate/baseInfo/uploadViolFile',
        fileParams: {
          busiId: '',
          busiTableName: 'T_COL_VIOL_BASE_LAW',
        },
        title: "企业规章制度新增",
        buttonLoading: null,
        rules: {
          title: [
            { required: true, message: '请输入标题', trigger: 'blur' },
            { min: 1, max: 128, message: '长度在 1 到 128 个字符', trigger: 'blur' }
          ],
          issueCode: [
            { required: true, message: '请输入文号', trigger: 'blur' },
            { min: 1, max: 32, message: '长度在 1 到 32 个字符', trigger: 'blur' }
          ],
          publishDate: [
            { required: true, message: '请选择印发日期', trigger: 'blur' }
          ],
          implementDate: [
            { required: true, message: '请选择实施日期', trigger: 'blur' }
          ],
          classify: [
            { required: true, message: '请选择制度类型', trigger: 'change' }
          ],
          category: [
            { required: true, message: '请选择类别', trigger: 'change' }
          ]
        },
      };
    },
    created() {
      this.lawInfo();
    },
    methods: {
      /**查询企业基本信息详情*/
      lawInfo() {
        if(this.id){
          getBaseLaw({id: this.id,}).then(
            response => {
              this.dataDetails = response.data;
              this.fileParams = {
                ...this.fileParams,
                busiId: response.data.id,
              };
            }
          );
          this.title = "企业规章制度编辑";
        }else{
          getBaseLawNull().then(
            response => {
              this.dataDetails = {
                ...this.dataDetails,
                id: response.data.id,
                uniqueCode: response.data.uniqueCode,
                version: response.data.version,
                commitFlag: "0",
              };
              this.fileParams = {
                ...this.fileParams,
                busiId: response.data.id,
              };
            }
          );
        }
      },
      /* 保存或提交 */
      saveLawBaseInfo(commitFlag){
        if(commitFlag==1){
          this.buttonLoading = 'sub';
        }else{
          this.buttonLoading = 'save';
        }
        this.$refs["dataDetails"].validate((valid) => {
          if (!valid) {
            this.buttonLoading = null;
            return false;
          }else{
            const params = {
              ...this.dataDetails,
              commitFlag
            };
            saveBaseLaw(params).then(
              response => {
                if(response.code == 200){
                  this.$message({
                    message: response.msg,
                    type: 'success'
                  });
                  if(params.commitFlag){
                    this.handleClose();
                  }else{
                    this.handleFileSuccess();
                  }
                }
              }
            ).catch(err=>{
              this.buttonLoading = null;
            });
          }
        });
      },
      /**关闭模态框*/
      handleClose() {
        if(!this.dataDetails || this.dataDetails.version == -2){
          delBaseLawById({id: this.dataDetails.id});
        }
        this.$emit("closeModal");
        this.buttonLoading = null;
      },
      /*日期处理*/
      dateFormat:function(date){
        if(date === undefined){
          return ''
        }
        return moment(date).format("YYYY-MM-DD")
      },
      dateFormat1:function(data){
        if(data.time === undefined){
          return ''
        }
        return moment(data.time).format("YYYY-MM-DD")
      },
      /*附件上传之前*/
      handlePreview: function(file){
        if(file.size / 1024 / 1024 > 100){
          this.$message.error('附件大小不能超过 100MB!');
          return false;
        }
      },
      /**附件上传成功*/
      handleFileSuccess(){
        getBaseLawNewVerson({uniqueCode: this.dataDetails.uniqueCode}).then(
          response =>{
            this.dataDetails = {
              ...this.dataDetails,
              id: response.data.id,
              files: response.data.files,
              version: response.data.version,
              commitFlag: response.data.version==-2 ? "0" : response.data.commitFlag,
            };
            this.fileParams = {
              ...this.fileParams,
              busiId: response.data.id,
            };
            this.buttonLoading = null;
          }
        )
      },
      /*删除附件*/
      deleteFile: function(row){
        this.$confirm('确认删除附件【' + row.fileName + '】吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          deleteViolFile({
            busiTableName:'T_COL_VIOL_BASE_LAW',
            busiId:this.dataDetails.id,
            id:row.id,
          }).then(response =>{
              if(response.code === 200){
                this.handleFileSuccess();
              }else{
                this.$message.error(response.msg);
              }
          })
        });
      },
      /** 修改附件表样式 */
      rowClass ({ row, column, rowIndex, columnIndex }) {
        if (columnIndex === 1 || columnIndex === 2 || columnIndex === 3) {
          return 'no-right-border'
        }else if(columnIndex === 0){
          return 'cell-color'
        }
      },
    }
  };
</script>
<style>
  @import "../../common/common.css";
</style>
