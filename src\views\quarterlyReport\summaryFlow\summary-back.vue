<template xmlns="http://www.w3.org/1999/html">

  <div>
    <el-form   style="height: calc(100vh - 340px)">
      <div class="bottom-btn">
        <div class="left-empty" />
        <el-button  type="primary" size="mini" @click="syncData()">数据同步</el-button>
        <el-button  type="primary" size="mini" @click="detailedExport()">明细导出</el-button>
      </div>
      <el-table ref="table" border v-loading="loading" :data="tableList" style="width: 100%">
        <el-table-column label="序号" type="index" width="100" align="center" >
          <template slot-scope="scope">
            <table-index
              :index="scope.$index"
              :page-num="queryParams.pageNum"
              :page-size="queryParams.pageSize"
            />
          </template>
        </el-table-column>
        <el-table-column label="基本信息"  show-overflow-tooltip>
          <el-table-column label="公司名称" prop="reportUnitFullName"  width="200" show-overflow-tooltip/>
          <el-table-column label="所属行业" prop="industryType"  width="100" show-overflow-tooltip/>
          <el-table-column label="填报季度" prop="reportYearQuarter" align="center"  width="100" show-overflow-tooltip>
          <template slot-scope="scope">
            {{ scope.row.reportYear }}年{{scope.row.reportQuarterName}}
          </template>
          </el-table-column>
        </el-table-column>

        <el-table-column label="工作部署情况"  show-overflow-tooltip>
          <el-table-column label="季度数据"  show-overflow-tooltip>
            <el-table-column label="本季度召开领导小组会议（次）" align="center" prop="quarterTeamMeetingTime"  width="100" show-overflow-tooltip/>
            <el-table-column label="本季度召开领导小组办公室会议（次）" align="center" prop="quarterTeamOfficeMeetingTime"  width="100" show-overflow-tooltip/>
            <el-table-column label="本季度召开专题会议（次）" align="center" prop="quarterSpecialMeetingTime"  width="100" show-overflow-tooltip/>
          </el-table-column>
          <el-table-column label="当年累计数据"  show-overflow-tooltip>
            <el-table-column label="当年累计召开领导小组会议（次）" align="center" prop="totalLeaderTeamMeetingTime"  width="100" show-overflow-tooltip/>
            <el-table-column label="当年累计召开领导小组办公室会议（次）" align="center" prop="totalTeamOfficeMeetingTime"  width="100" show-overflow-tooltip/>
            <el-table-column label="当年累计召开专题会议（次）" align="center" prop="totalSpecialMeetingTime"  width="100" show-overflow-tooltip/>
          </el-table-column>
        </el-table-column>
        <el-table-column label="累计印发责任追究相关制度数量（项）" align="center" prop="totalAccountabilitySystemNumber"  width="100" show-overflow-tooltip/>
        <el-table-column label="体系建设情况"  show-overflow-tooltip>
          <el-table-column label="当年累计新增配套制度（项）" align="center" prop="totalNewSupportingSystem"  width="100" show-overflow-tooltip/>
          <el-table-column label="新增配套制度名称" prop="newSupportingName"  width="200" show-overflow-tooltip/>
          <el-table-column label="当年累计新增工作机制（项）" align="center" prop="totalNewWorkSystem"  width="100" show-overflow-tooltip/>
          <el-table-column label="新增工作机制名称" prop="newWorkName"  width="200" show-overflow-tooltip/>
          <el-table-column label="累计专职人员数量（人）" align="center" prop="totalProfessionalNumber"  width="100" show-overflow-tooltip/>
          <el-table-column label="当年累计新增专职人员数量（人）" align="center" prop="totalNewSpecialPersonNumber"  width="100" show-overflow-tooltip/>
          <el-table-column label="主责部门" prop="groupMainDept"  width="150" show-overflow-tooltip/>
        </el-table-column>

        <el-table-column label="违规问题线索查办情况"  show-overflow-tooltip>
          <el-table-column label="全级次企业"  show-overflow-tooltip>
            <el-table-column label="新受理问题线索情况（季度数据）"  show-overflow-tooltip>
              <el-table-column label="本季度新受理问题线索数量（件）" prop="quarterNewProblemNumber" align="center"  width="100" show-overflow-tooltip/>
              <el-table-column label="本季度涉及资产损失（万元）" prop="lossAmount" align="right"  width="100" show-overflow-tooltip>
                <template slot-scope="scope">
                  {{ scope.row.lossAmount | filterNum }}
                </template>
                </el-table-column >
              <el-table-column label="本季度涉及资产损失风险（万元）" prop="lossRisk" align="right"  width="100" show-overflow-tooltip>
                <template slot-scope="scope">
                  {{ scope.row.lossRisk | filterNum }}
                </template>
              </el-table-column >
            </el-table-column>
            <el-table-column label="受理问题线索办理进展情况（当年累计数据）"  show-overflow-tooltip>
              <el-table-column label="当年累计受理问题线索数量（件）" align="center" prop="totalProblemSourceNumber"  width="100" show-overflow-tooltip/>
              <el-table-column label="上年结转问题线索数量（件）" align="center" prop="lastYearProblemSourceNumber"  width="100" show-overflow-tooltip/>
              <el-table-column label="其中"  show-overflow-tooltip>
                <el-table-column label="未启动核查（件）" align="center" prop="checkNoStartedNumber"  width="100" show-overflow-tooltip/>
                <el-table-column label="正在核查（件）" align="center" prop="checkInProcessNumber"  width="100" show-overflow-tooltip/>
                <el-table-column label="完成核查（件）" align="center" prop="checkCompletedNumber"  width="100" show-overflow-tooltip/>
              </el-table-column>
            </el-table-column>
          </el-table-column>
        </el-table-column>

        <el-table-column label="追责整改工作成效"  show-overflow-tooltip>
          <el-table-column label="当年累计完成追责问题数量（件）" align="center" prop="totalCompletedProblemNumber"  width="100" show-overflow-tooltip/>
          <el-table-column label="当年累计追责总人数（人）" align="center" prop="totalAccountabilityPersonNumber"  width="100" show-overflow-tooltip/>
          <el-table-column label="其中"  show-overflow-tooltip>
            <el-table-column label="中央企业负责人（人）" align="center" prop="enterpriseManagementNumber"  width="100" show-overflow-tooltip/>
            <el-table-column label="集团管理干部（人）" align="center" prop="groupManagementNumber"  width="100" show-overflow-tooltip/>
            <el-table-column label="子企业管理干部（人）" align="center" prop="subManagementNumber"  width="100" show-overflow-tooltip/>
          </el-table-column>
          <el-table-column label="当年累计追责总人次（人次）" align="center" prop="totalAccountabilityPersonTime"  width="100" show-overflow-tooltip/>
          <el-table-column label="其中"  show-overflow-tooltip>
            <el-table-column label="组织处理（人次）" align="center" prop="orgHandleTime"  width="100" show-overflow-tooltip/>
            <el-table-column label="扣减薪酬（人次）" align="center" prop="deductionSalaryTime"  width="100" show-overflow-tooltip/>
            <el-table-column label="党纪处分（人次）" align="center" prop="partyPunishmentTime"  width="100" show-overflow-tooltip/>
            <el-table-column label="政务处分（人次）" align="center" prop="governmentPunishmentTime"  width="100" show-overflow-tooltip/>
            <el-table-column label="禁入限制（人次）" align="center" prop="prohibitTime"  width="100" show-overflow-tooltip/>
            <el-table-column label="移送监察机关或司法机关（人次）" align="center" prop="transferAuthorityTime"  width="100" show-overflow-tooltip/>
            <el-table-column label="其他（人次）" align="center" prop="accountabilityOtherTime"  width="100" show-overflow-tooltip/>
          </el-table-column>
          <el-table-column label="当年累计扣减薪酬金额（万元）" prop="totalDeductionSalary" align="right"  width="100" show-overflow-tooltip>
            <template slot-scope="scope">
              {{ scope.row.totalDeductionSalary | filterNum }}
            </template>
          </el-table-column >
          <el-table-column label="责任约谈"  show-overflow-tooltip>
            <el-table-column label="当年累计责任约谈次数（次）" align="center" prop="dutyInterviewNumber"  width="100" show-overflow-tooltip/>
            <el-table-column label="当年累计责任约谈总人次（人次）" align="center" prop="dutyInterviewPersonTime"  width="100" show-overflow-tooltip/>
          </el-table-column>
          <el-table-column label="当年累计挽回资产损失（万元）" prop="totalRetrieveLossAmount" align="right"  width="100" show-overflow-tooltip>
            <template slot-scope="scope">
              {{ scope.row.totalRetrieveLossAmount | filterNum }}
            </template>
          </el-table-column >
          <el-table-column label="当年累计降低损失风险（万元）" prop="totalReduceLossRisk" align="right"  width="100" show-overflow-tooltip>
            <template slot-scope="scope">
              {{ scope.row.totalReduceLossRisk | filterNum }}
            </template>
          </el-table-column >
          <el-table-column label="当年累计制修订管理制度（项）" align="center" prop="totalPerfectSystemNumber"  width="100" show-overflow-tooltip/>
          <el-table-column label="其他工作成效" align="center" width="300" prop="otherAchievement">
            <!-- <template slot-scope="scope">
              <el-input
                v-model="scope.row.otherAchievement"
                type="textarea"
                :rows="2"
                placeholder="请输入其他工作成效"
                @blur="updateOtherAchievement(scope.row)"
              />
            </template> -->
          </el-table-column>
        </el-table-column>


        <el-table-column label="备注" align="left" prop="remark"  width="200" show-overflow-tooltip/>
        <el-table-column label="追责部门填报人" align="center" prop="informantName"  width="100" show-overflow-tooltip/>
        <el-table-column label="联系电话" align="center" prop="informantPhone"  width="150" show-overflow-tooltip/>

      </el-table>
    </el-form>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="list"
    />
    <Process
      :key="processIndex"
      ref="process"
      :refresh-assignee-url="flowInfo.refreshAssigneeUrl"
      :save-btn-type="flowInfo.saveBtnType"
      :tab-flag="flowInfo.tabFlag"
      :select-value="{
        busiKey:flowInfo.busiKey,
        title:flowInfo.title
      }"
      :center-variable="{}"
      @close="closeAdd"
    />
  </div>

</template>

<script>

import BlockCard from '@/components/BlockCard'
import Process from "@/components/process-common/index";
import {
  flowParams,
  queryQuarterReportProvInfo,
  queryQuarterReportProvSummary,
  saveQuarterReportProvSummary
} from "@/api/quarterly-report/summary";

export default {
  name: "summary",
  components: {
    BlockCard,
    Process
  },
  dicts: [],
  props: {
    closeBtn: {
      type: Function,
      default: null,
    },
    //编辑内容
    rowData: {
      type: Object,
      default: () => {},
    },
    //流程参数
    centerVariable: {
      type: Object
    },
  },
  data() {
    return {
      loading:true,
      //参数
      queryParams: {
        pageNum: 1,
        pageSize: 10
      },
      queryData:{},
      tableList:[],
      total:0,
      //流程信息
      flowInfo: {
          processIndex:0,
          busiKey: '', // 业务中获取
          title: '', // 业务中获取
          saveBtnType: false, // 是否需要保存按钮
          tabFlag: true, // 表明是业务发起环节
          refreshAssigneeUrl: '/quarter/summary/flow' // 下环节自定义业务url
      },
      saveParams:{},//保存参数
      flowKey:'',//流程key
      quarterReportId: '',
      title: '',
      reportYear:'',
      reportQuarterName:''
    };
  },
  created() {
    this.$emit('collocation',{
      refreshAssigneeUrl:'/quarter/summary/flow',//业务url
      saveBtn:false,//保存按钮
    })
    this.quarterReportId = this.centerVariable.busiKey
    this.list();
    this.queryFlowParams()
  },
  filters: {
  },
  methods: {
    //获取流程key
    queryFlowParams(){
      flowParams().then((res)=>{
        this.flowKey = res.data.processDefinitionKey;
      })
    },
    list() {
      //接口请求
      queryQuarterReportProvSummary({quarterReportId:this.quarterReportId},this.queryParams).then(res => {
        this.tableList = res.rows;
        this.loading = false;
        this.total = res.total;
        this.reportYear = res.rows[0].reportYear
        this.reportQuarterName = res.rows[0].reportQuarterName
        this.flowInfo.title =this.reportYear +"年"+this.reportQuarterName+"违规责任追究工作情况汇总报告";
      })
    },
    submitSummary(){
      this.$confirm('确定要发起汇总吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.flowInfo.busiKey = this.quarterReportId;
        const loadProcessData = {
          businessKey: this.quarterReportId,
          // title: this.formData.fillMatterName,
          flowKey: this.flowKey
        };
        saveQuarterReportProvSummary({quarterReportId:this.quarterReportId}).then(res => {
          if (res.code == 200){
            this.$refs.process.handle(1, loadProcessData);
          }
        })
      });

    },
    syncData(){
      //接口请求
      queryQuarterReportProvInfo({quarterReportId:this.quarterReportId},this.queryParams).then(res => {
        this.tableList = res.rows;
        this.loading = false;
        this.total = res.total;
        this.reportYear = res.rows[0].reportYear
        this.reportQuarterName = res.rows[0].reportQuarterName
        this.flowInfo.title =this.reportYear +"年"+this.reportQuarterName+"违规责任追究工作情况汇总报告";
        saveQuarterReportProvSummary({quarterReportId:this.quarterReportId}).then(res => {
          if (res.code == 200){
            this.$message.success("同步成功")
          }else {
            this.$message.error(res.msg);
          }
        })
      })
    },
    detailedExport() {
      this.download(
        "/quarterReport/getSummaryDetailExport",
        { quarterReportId: this.quarterReportId },
        this.getFileName()
      );
    },
    getFileName(){
      const date =  this.reportYear +"年"+this.reportQuarterName
      return date + '违规责任追究工作情况汇总报告表.xlsx';
    },
    //流程 方法
    openLoading(){//打开加载...
      this.$emit('openLoading');
    },
    closeLoading(){//关闭加载...
      this.$emit('closeLoading');
    },
    //额外参数
    loadProcessData(){
      return {}
    },
    //校验
    passValidate(){
      return true;
    },
    // 关闭
    cancel() {
      this.closeBtn();
    },
    closeAdd(){
      this.closeBtn();
    },
    // 更新其他工作成效
    updateOtherAchievement(row) {
      // 这里可以添加保存逻辑，比如调用API更新数据
      console.log('更新其他工作成效:', row.otherAchievement);
      // 如果需要实时保存，可以调用相应的API
      // this.saveOtherAchievement(row);
    },
  }
};
</script>

<style scoped>


.el-table {
  overflow-x: auto !important;
  overflow-y: auto !important;
}

.el-table__body-wrapper {
  overflow-x: auto !important;
  overflow-y: auto !important;
}

.el-table__header-wrapper {
  overflow-x: auto !important;
}

/* 确保滚动条始终可见 */
.el-table__body-wrapper::-webkit-scrollbar,
.el-table__header-wrapper::-webkit-scrollbar {
  height: 12px;
  width: 12px;
}

.el-table__body-wrapper::-webkit-scrollbar-thumb,
.el-table__header-wrapper::-webkit-scrollbar-thumb {
  background-color: #c1c1c1;
  border-radius: 6px;
}

.el-table__body-wrapper::-webkit-scrollbar-track,
.el-table__header-wrapper::-webkit-scrollbar-track {
  background-color: #f1f1f1;
  border-radius: 6px;
}
</style>
