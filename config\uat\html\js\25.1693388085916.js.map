{"version": 3, "sources": ["webpack:///src/views/error/cancellation.vue", "webpack:///./src/views/error/cancellation.vue?98ec", "webpack:///./src/views/error/cancellation.vue?7cc9", "webpack:///./src/views/error/cancellation.vue?31ab", "webpack:///./src/assets/images/background.png", "webpack:///./src/assets/images/login/logo.png", "webpack:///./src/assets/images/vector.png", "webpack:///./src/views/error/cancellation.vue", "webpack:///./src/views/error/cancellation.vue?2ea9", "webpack:///./src/views/error/cancellation.vue?50f7", "webpack:///./src/views/error/cancellation.vue?1dce"], "names": ["name", "computed", "message", "data", "times", "created", "timeNum", "methods", "_this", "timer", "setInterval", "jump", "clearInterval", "close", "window", "location", "href"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyBA;EACAA,IAAA;EACAC,QAAA;IACAC,OAAA,WAAAA,QAAA;MACA;IACA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,KAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACAD,OAAA,WAAAA,QAAA;MAAA,IAAAE,KAAA;MACA,KAAAC,KAAA,GAAAC,WAAA;QACAF,KAAA,CAAAJ,KAAA;QACA,IAAAI,KAAA,CAAAJ,KAAA;UACAI,KAAA,CAAAG,IAAA;UACAC,aAAA,CAAAJ,KAAA,CAAAC,KAAA;QACA;MACA;IACA;IACA,WACAI,KAAA,WAAAA,MAAA;MACAC,MAAA,CAAAC,QAAA,CAAAC,IAAA;MACAF,MAAA,CAAAD,KAAA;IACA;IACA,WACAF,IAAA,WAAAA,KAAA;MACAI,QAAA,CAAAC,IAAA;IACA;EACA;AACA,G;;;;;;;;;;;;AC5DA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA,eAAe,qCAAqC;AACpD;AACA,iBAAiB,qCAAqC;AACtD,mBAAmB,0CAA0C;AAC7D;AACA;AACA,oBAAoB,MAAM,mBAAO,CAAC,sEAAgC,GAAG;AACrE,WAAW;AACX,qBAAqB,wCAAwC;AAC7D,sBAAsB,sCAAsC;AAC5D;AACA;AACA,qBAAqB,qCAAqC;AAC1D;AACA;AACA,qBAAqB,qCAAqC;AAC1D;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB,SAAS,iBAAiB,OAAO,kBAAkB,EAAE;AACxE;AACA;AACA;AACA;AACA,mBAAmB,SAAS,YAAY,OAAO,mBAAmB,EAAE;AACpE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsB,8BAA8B;AACpD;AACA;AACA,gBAAgB,MAAM,mBAAO,CAAC,8EAAoC,GAAG;AACrE,OAAO;AACP;AACA,GAAG;AACH;AACA;;;;;;;;;;;;;AC1DA;AACA,kCAAkC,mBAAO,CAAC,2GAAsD;AAChG,sCAAsC,mBAAO,CAAC,iHAAyD;AACvG,oCAAoC,mBAAO,CAAC,8EAAoC;AAChF;AACA;AACA;AACA,cAAc,QAAS,0CAA0C,kBAAkB,GAAG,8DAA8D,kBAAkB,kFAAkF,+BAA+B,GAAG,mEAAmE,0BAA0B,uBAAuB,cAAc,aAAa,6CAA6C,qCAAqC,GAAG,0FAA0F,0BAA0B,iBAAiB,kBAAkB,wBAAwB,2BAA2B,GAAG,2FAA2F,0BAA0B,qBAAqB,GAAG,iHAAiH,oBAAoB,mBAAmB,qBAAqB,wBAAwB,wBAAwB,GAAG,gHAAgH,oBAAoB,mBAAmB,wBAAwB,GAAG;AAC1uC;AACA;;;;;;;;;;;;ACTA;;AAEA;AACA,cAAc,mBAAO,CAAC,oyBAA8c;AACpe;AACA,4CAA4C,QAAS;AACrD;AACA;AACA,UAAU,mBAAO,CAAC,6HAAgE;AAClF,8CAA8C,qCAAqC;AACnF;AACA,GAAG,KAAU,EAAE,E;;;;;;;;;;;ACXf,iBAAiB,qBAAuB,wC;;;;;;;;;;;ACAxC,iBAAiB,qBAAuB,kC;;;;;;;;;;;ACAxC,iBAAiB,qBAAuB,oC;;;;;;;;;;;;ACAxC;AAAA;AAAA;AAAA;AAAA;AAAuG;AACvC;AACL;AACsC;;;AAGjG;AAC6F;AAC7F,gBAAgB,2GAAU;AAC1B,EAAE,kFAAM;AACR,EAAE,mGAAM;AACR,EAAE,4GAAe;AACjB;AACA;AACA;AACA;;AAEA;;AAEA;AACA,IAAI,KAAU,EAAE,YAiBf;AACD;AACe,gF;;;;;;;;;;;;ACvCf;AAAA;AAAA,wCAAqS,CAAgB,6UAAG,EAAC,C;;;;;;;;;;;;ACAzT;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA", "file": "js/25.1693388085916.js", "sourcesContent": ["<template>\r\n <div>\r\n   <div class=\"cancellation-header\">\r\n     <div class=\"padding10_20\">\r\n       <img class=\"login-log\" src=\"../../assets/images/login/logo.png\">\r\n     </div>\r\n     <div class=\"cancellation-center\">\r\n       <div class=\"cancellation-center-view\">\r\n       <img class=\"cancellation-view-img\" src=\"../../assets/images/vector.png\">\r\n       <div class=\"cancellation-view-text\">\r\n         <h1 class=\"cancellation-text-h1\">登录状态已注销</h1>\r\n         <p class=\"cancellation-text-p\">由于您长时间未操作，现需要重新登录</p>\r\n         <p class=\"cancellation-text-p\">{{times}}秒后将自动跳转至登录页</p>\r\n         <div>\r\n           <el-button type=\"danger\" @click=\"jump\">手动跳转</el-button>\r\n           <el-button plain @click=\"close\">关闭</el-button>\r\n         </div>\r\n       </div>\r\n       </div>\r\n     </div>\r\n   </div>\r\n </div>\r\n</template>\r\n\r\n<script>\r\n  export default {\r\n    name: 'cancellation',\r\n    computed: {\r\n      message() {\r\n        return '门户跳转！'\r\n      }\r\n    },\r\n    data() {\r\n      return {\r\n        times: 5\r\n      }\r\n    },\r\n    created() {\r\n       this.timeNum();\r\n    },\r\n    methods: {\r\n      timeNum(){\r\n        this.timer = setInterval(()=>{\r\n          this.times--;\r\n          if(this.times===0){\r\n            this.jump();\r\n            clearInterval(this.timer)\r\n          }\r\n        },1000)\r\n      },\r\n      /** 关闭页面 */\r\n      close() {\r\n        window.location.href=\"about:blank\";\r\n        window.close();\r\n      },\r\n      /** 跳转页面 */\r\n      jump(){\r\n        location.href = 'http://aiportal.unicom.local';\r\n      },\r\n    }\r\n  }\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n    .cancellation-header{\r\n      height: 100vh;\r\n      .cancellation-center{\r\n        height: 450px;\r\n        background: url(\"../../assets/images/background.png\") 50% 50% no-repeat;\r\n        background-size: 100% 100%;\r\n      }\r\n      .cancellation-center-view {\r\n        display: inline-block;\r\n        position: relative;\r\n        left: 50%;\r\n        top: 50%;\r\n        -webkit-transform: translate(-50%, -50%);\r\n        -ms-transform: translate(-50%, -50%);\r\n        transform: translate(-50%, -50%);\r\n      .cancellation-view-img{\r\n        display: inline-block;\r\n        width: 174px;\r\n        height: 240px;\r\n        margin-right: 110px;\r\n        vertical-align: bottom;\r\n      }\r\n        .cancellation-view-text{\r\n          display: inline-block;\r\n          text-align: left;\r\n          .cancellation-text-h1{\r\n            font-size: 36px;\r\n            color: #313131;\r\n            font-weight: 600;\r\n            letter-spacing: 2px;\r\n            margin-bottom: 24px;\r\n          }\r\n          .cancellation-text-p {\r\n            font-size: 20px;\r\n            color: #313131;\r\n            margin-bottom: 24px;\r\n          }\r\n        }\r\n      }\r\n    }\r\n</style>\r\n", "var render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return _c(\"div\", [\n    _c(\"div\", { staticClass: \"cancellation-header\" }, [\n      _vm._m(0),\n      _c(\"div\", { staticClass: \"cancellation-center\" }, [\n        _c(\"div\", { staticClass: \"cancellation-center-view\" }, [\n          _c(\"img\", {\n            staticClass: \"cancellation-view-img\",\n            attrs: { src: require(\"../../assets/images/vector.png\") },\n          }),\n          _c(\"div\", { staticClass: \"cancellation-view-text\" }, [\n            _c(\"h1\", { staticClass: \"cancellation-text-h1\" }, [\n              _vm._v(\"登录状态已注销\"),\n            ]),\n            _c(\"p\", { staticClass: \"cancellation-text-p\" }, [\n              _vm._v(\"由于您长时间未操作，现需要重新登录\"),\n            ]),\n            _c(\"p\", { staticClass: \"cancellation-text-p\" }, [\n              _vm._v(_vm._s(_vm.times) + \"秒后将自动跳转至登录页\"),\n            ]),\n            _c(\n              \"div\",\n              [\n                _c(\n                  \"el-button\",\n                  { attrs: { type: \"danger\" }, on: { click: _vm.jump } },\n                  [_vm._v(\"手动跳转\")]\n                ),\n                _c(\n                  \"el-button\",\n                  { attrs: { plain: \"\" }, on: { click: _vm.close } },\n                  [_vm._v(\"关闭\")]\n                ),\n              ],\n              1\n            ),\n          ]),\n        ]),\n      ]),\n    ]),\n  ])\n}\nvar staticRenderFns = [\n  function () {\n    var _vm = this\n    var _h = _vm.$createElement\n    var _c = _vm._self._c || _h\n    return _c(\"div\", { staticClass: \"padding10_20\" }, [\n      _c(\"img\", {\n        staticClass: \"login-log\",\n        attrs: { src: require(\"../../assets/images/login/logo.png\") },\n      }),\n    ])\n  },\n]\nrender._withStripped = true\n\nexport { render, staticRenderFns }", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../../../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_GET_URL_IMPORT___ = require(\"../../../node_modules/css-loader/dist/runtime/getUrl.js\");\nvar ___CSS_LOADER_URL_IMPORT_0___ = require(\"../../assets/images/background.png\");\nexports = ___CSS_LOADER_API_IMPORT___(false);\nvar ___CSS_LOADER_URL_REPLACEMENT_0___ = ___CSS_LOADER_GET_URL_IMPORT___(___CSS_LOADER_URL_IMPORT_0___);\n// Module\nexports.push([module.id, \".cancellation-header[data-v-1766d444] {\\n  height: 100vh;\\n}\\n.cancellation-header .cancellation-center[data-v-1766d444] {\\n  height: 450px;\\n  background: url(\" + ___CSS_LOADER_URL_REPLACEMENT_0___ + \") 50% 50% no-repeat;\\n  background-size: 100% 100%;\\n}\\n.cancellation-header .cancellation-center-view[data-v-1766d444] {\\n  display: inline-block;\\n  position: relative;\\n  left: 50%;\\n  top: 50%;\\n  -webkit-transform: translate(-50%, -50%);\\n  transform: translate(-50%, -50%);\\n}\\n.cancellation-header .cancellation-center-view .cancellation-view-img[data-v-1766d444] {\\n  display: inline-block;\\n  width: 174px;\\n  height: 240px;\\n  margin-right: 110px;\\n  vertical-align: bottom;\\n}\\n.cancellation-header .cancellation-center-view .cancellation-view-text[data-v-1766d444] {\\n  display: inline-block;\\n  text-align: left;\\n}\\n.cancellation-header .cancellation-center-view .cancellation-view-text .cancellation-text-h1[data-v-1766d444] {\\n  font-size: 36px;\\n  color: #313131;\\n  font-weight: 600;\\n  letter-spacing: 2px;\\n  margin-bottom: 24px;\\n}\\n.cancellation-header .cancellation-center-view .cancellation-view-text .cancellation-text-p[data-v-1766d444] {\\n  font-size: 20px;\\n  color: #313131;\\n  margin-bottom: 24px;\\n}\", \"\"]);\n// Exports\nmodule.exports = exports;\n", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../../node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-2!../../../node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./cancellation.vue?vue&type=style&index=0&id=1766d444&lang=scss&scoped=true&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add the styles to the DOM\nvar add = require(\"!../../../node_modules/vue-style-loader/lib/addStylesClient.js\").default\nvar update = add(\"d664c310\", content, false, {\"sourceMap\":false,\"shadowMode\":false});\n// Hot Module Replacement\nif(module.hot) {\n // When the styles change, update the <style> tags\n if(!content.locals) {\n   module.hot.accept(\"!!../../../node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-2!../../../node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./cancellation.vue?vue&type=style&index=0&id=1766d444&lang=scss&scoped=true&\", function() {\n     var newContent = require(\"!!../../../node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-2!../../../node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./cancellation.vue?vue&type=style&index=0&id=1766d444&lang=scss&scoped=true&\");\n     if(newContent.__esModule) newContent = newContent.default;\n     if(typeof newContent === 'string') newContent = [[module.id, newContent, '']];\n     update(newContent);\n   });\n }\n // When the module is disposed, remove the <style> tags\n module.hot.dispose(function() { update(); });\n}", "module.exports = __webpack_public_path__ + \"static/img/background.70beb5f3.png\";", "module.exports = __webpack_public_path__ + \"static/img/logo.c6b4613d.png\";", "module.exports = __webpack_public_path__ + \"static/img/vector.fb9fc78e.png\";", "import { render, staticRenderFns } from \"./cancellation.vue?vue&type=template&id=1766d444&scoped=true&\"\nimport script from \"./cancellation.vue?vue&type=script&lang=js&\"\nexport * from \"./cancellation.vue?vue&type=script&lang=js&\"\nimport style0 from \"./cancellation.vue?vue&type=style&index=0&id=1766d444&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"1766d444\",\n  null\n  \n)\n\n/* hot reload */\nif (module.hot) {\n  var api = require(\"E:\\\\workspace\\\\info\\\\trigram-ui\\\\node_modules\\\\vue-hot-reload-api\\\\dist\\\\index.js\")\n  api.install(require('vue'))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!api.isRecorded('1766d444')) {\n      api.createRecord('1766d444', component.options)\n    } else {\n      api.reload('1766d444', component.options)\n    }\n    module.hot.accept(\"./cancellation.vue?vue&type=template&id=1766d444&scoped=true&\", function () {\n      api.rerender('1766d444', {\n        render: render,\n        staticRenderFns: staticRenderFns\n      })\n    })\n  }\n}\ncomponent.options.__file = \"src/views/error/cancellation.vue\"\nexport default component.exports", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./cancellation.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./cancellation.vue?vue&type=script&lang=js&\"", "export * from \"-!../../../node_modules/vue-style-loader/index.js??ref--8-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-2!../../../node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./cancellation.vue?vue&type=style&index=0&id=1766d444&lang=scss&scoped=true&\"", "export * from \"-!../../../node_modules/cache-loader/dist/cjs.js?{\\\"cacheDirectory\\\":\\\"node_modules/.cache/vue-loader\\\",\\\"cacheIdentifier\\\":\\\"f1aaaf00-vue-loader-template\\\"}!../../../node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./cancellation.vue?vue&type=template&id=1766d444&scoped=true&\""], "sourceRoot": ""}