<template>
  <div class="app-container" style="padding:0;">
    <div class="common-box-one">
<!--      <el-form ref="form" :model="formData" label-width="50px">-->
<!--        <el-row>-->
<!--          <el-col :span="24">-->
<!--            <el-form-item class="width">-->
<!--              <el-button-->
<!--                icon="el-icon-circle-plus-outline"-->
<!--                size="mini"-->
<!--                class="el-button-common"-->
<!--                @click="changePeoples"-->
<!--              >选择</el-button-->
<!--              >-->
<!--            </el-form-item>-->
<!--          </el-col>-->
<!--        </el-row>-->
<!--      </el-form>-->
      <el-table
        v-loading="loading"
        :data="dataList"
        border
        height = "500"
        ref="tb"
        @selection-change="handleSelectionChange"
        :header-cell-style="{
          background: 'rgba(244, 248, 252, 0.39)',
          color: '#222222',
          fontWeight: 'bold',
          borderColor: '#E2E7F1',
        }"
      >
        <el-table-column type="selection" width="50" align="center" />
        <el-table-column
          prop="nick_name"
          label="姓名"
          min-width="20%"
          align="center"
        >
          <template slot-scope="scope">
            <div>{{ scope.row.nick_name || "-" }}</div>
          </template>
        </el-table-column>
        <el-table-column
          prop="phonenumber"
          label="电话"
          min-width="20%"
          align="center"
        >
          <template slot-scope="scope">
            <div>{{ scope.row.phonenumber || "-" }}</div>
          </template>
        </el-table-column>
        <el-table-column
          prop="dept_name"
          label="部门"
          min-width="20%"
          align="center"
        >
          <template slot-scope="scope">
            <div>{{ scope.row.dept_name || "-" }}</div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div slot="footer" class="dialog-footer text-right" style="margin-top:10px;">
      <el-button size="mini" type="primary" @click="changePeoples">保存</el-button>
    </div>

  </div>
</template>

<script>
  import {userList} from '@/api/regular/add/changePeoples';
  export default {
    name: "changePeoples",
    props: {
      closeBtn: {
        type: Function,
        default: null,
      },
      rowData: {
        type: Object,
        default: () => {},
      },
    },

    data() {
      return {
        // 表单参数
        formData: {


        },
        // 表格数据
        dataList: [
        ],
        // 表格请求loading
        loading: false,
        // 表格页码
        // 表格总数
        total: 0,
        selection: [], //选择信息
      };
    },

    created() {
      //alert(this.rowData.reportUnitCode)
      this.getList(this.rowData.reportUnitCode);
    },
    methods: {
      table_index(index) {
        return (this.formData.pageNum - 1) * this.formData.pageSize + index + 1;
      },
      // 选择
      handleSelectionChange(selection) {
        this.selection = [];
        if (selection.length > 1) {
          this.$refs.tb.clearSelection();
          this.$refs.tb.toggleRowSelection(selection.pop());
        } else {
          this.selection = selection;
        }
      },
      // 确定选择
      changePeoples() {
        if (this.selection.length === 0) {
          this.$message.warning("请选择人员");
          return false;
        }
        this.loading = true;
        this.closeBtn(this.selection);
      },
      //列表请求
      getList(reportUnitCode) {
        this.loading = true;
        userList(reportUnitCode).then(response => {
          this.dataList = response.data;
          this.loading = false
        })
      },
      // 查询
      handleQuery() {
        this.loading = true;
        this.getList(this.rowData.reportUnitCode);
      },
      // 重置
      resetQuery() {
        this.formData = {};
        this.handleQuery();
      },
    },
  };
</script>

<style scoped lang="scss">
  .width {
    ::v-deep .el-form-item__content {
      margin-left: 0px !important;
      float: right;
    }
  }

  ::v-deep .el-select {
    width: 100%;
  }
</style>
