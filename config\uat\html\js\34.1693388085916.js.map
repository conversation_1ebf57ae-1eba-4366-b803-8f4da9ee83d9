{"version": 3, "sources": ["webpack:///./src/api/system/user.js"], "names": ["listUser", "query", "request", "url", "method", "params", "getUser", "userId", "praseStrEmpty", "addUser", "data", "updateUser", "<PERSON><PERSON><PERSON>", "resetUserPwd", "password", "changeUserStatus", "status", "getUserProfile", "updateUserProfile", "updateUserPwd", "oldPassword", "newPassword", "uploadAvatar", "getAuthRole", "updateAuthRole"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAqC;AACS;;AAE9C;AACO,SAASA,QAAQA,CAACC,KAAK,EAAE;EAC9B,OAAOC,8DAAO,CAAC;IACbC,GAAG,EAAE,mBAAmB;IACxBC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,OAAOA,CAACC,MAAM,EAAE;EAC9B,OAAOL,8DAAO,CAAC;IACbC,GAAG,EAAE,eAAe,GAAGK,kEAAa,CAACD,MAAM,CAAC;IAC5CH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,OAAOA,CAACC,IAAI,EAAE;EAC5B,OAAOR,8DAAO,CAAC;IACbC,GAAG,EAAE,cAAc;IACnBC,MAAM,EAAE,MAAM;IACdM,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,UAAUA,CAACD,IAAI,EAAE;EAC/B,OAAOR,8DAAO,CAAC;IACbC,GAAG,EAAE,cAAc;IACnBC,MAAM,EAAE,KAAK;IACbM,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,OAAOA,CAACL,MAAM,EAAE;EAC9B,OAAOL,8DAAO,CAAC;IACbC,GAAG,EAAE,eAAe,GAAGI,MAAM;IAC7BH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASS,YAAYA,CAACN,MAAM,EAAEO,QAAQ,EAAE;EAC7C,IAAMJ,IAAI,GAAG;IACXH,MAAM,EAANA,MAAM;IACNO,QAAQ,EAARA;EACF,CAAC;EACD,OAAOZ,8DAAO,CAAC;IACbC,GAAG,EAAE,uBAAuB;IAC5BC,MAAM,EAAE,KAAK;IACbM,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,gBAAgBA,CAACR,MAAM,EAAES,MAAM,EAAE;EAC/C,IAAMN,IAAI,GAAG;IACXH,MAAM,EAANA,MAAM;IACNS,MAAM,EAANA;EACF,CAAC;EACD,OAAOd,8DAAO,CAAC;IACbC,GAAG,EAAE,2BAA2B;IAChCC,MAAM,EAAE,KAAK;IACbM,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASO,cAAcA,CAAA,EAAG;EAC/B,OAAOf,8DAAO,CAAC;IACbC,GAAG,EAAE,sBAAsB;IAC3BC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASc,iBAAiBA,CAACR,IAAI,EAAE;EACtC,OAAOR,8DAAO,CAAC;IACbC,GAAG,EAAE,sBAAsB;IAC3BC,MAAM,EAAE,KAAK;IACbM,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASS,aAAaA,CAACC,WAAW,EAAEC,WAAW,EAAE;EACtD,IAAMX,IAAI,GAAG;IACXU,WAAW,EAAXA,WAAW;IACXC,WAAW,EAAXA;EACF,CAAC;EACD,OAAOnB,8DAAO,CAAC;IACbC,GAAG,EAAE,gCAAgC;IACrCC,MAAM,EAAE,MAAM;IACdC,MAAM,EAAEK;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASY,YAAYA,CAACZ,IAAI,EAAE;EACjC,OAAOR,8DAAO,CAAC;IACbC,GAAG,EAAE,6BAA6B;IAClCC,MAAM,EAAE,MAAM;IACdM,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASa,WAAWA,CAAChB,MAAM,EAAE;EAClC,OAAOL,8DAAO,CAAC;IACbC,GAAG,EAAE,wBAAwB,GAAGI,MAAM;IACtCH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASoB,cAAcA,CAACd,IAAI,EAAE;EACnC,OAAOR,8DAAO,CAAC;IACbC,GAAG,EAAE,uBAAuB;IAC5BC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEK;EACV,CAAC,CAAC;AACJ,C", "file": "js/34.1693388085916.js", "sourcesContent": ["import request from '@/utils/request'\r\nimport { praseStrEmpty } from \"@/utils/ruoyi\";\r\n\r\n// 查询用户列表\r\nexport function listUser(query) {\r\n  return request({\r\n    url: '/system/user/list',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询用户详细\r\nexport function getUser(userId) {\r\n  return request({\r\n    url: '/system/user/' + praseStrEmpty(userId),\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 新增用户\r\nexport function addUser(data) {\r\n  return request({\r\n    url: '/system/user',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 修改用户\r\nexport function updateUser(data) {\r\n  return request({\r\n    url: '/system/user',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 删除用户\r\nexport function delUser(userId) {\r\n  return request({\r\n    url: '/system/user/' + userId,\r\n    method: 'delete'\r\n  })\r\n}\r\n\r\n// 用户密码重置\r\nexport function resetUserPwd(userId, password) {\r\n  const data = {\r\n    userId,\r\n    password\r\n  }\r\n  return request({\r\n    url: '/system/user/resetPwd',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 用户状态修改\r\nexport function changeUserStatus(userId, status) {\r\n  const data = {\r\n    userId,\r\n    status\r\n  }\r\n  return request({\r\n    url: '/system/user/changeStatus',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 查询用户个人信息\r\nexport function getUserProfile() {\r\n  return request({\r\n    url: '/system/user/profile',\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 修改用户个人信息\r\nexport function updateUserProfile(data) {\r\n  return request({\r\n    url: '/system/user/profile',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 用户密码重置\r\nexport function updateUserPwd(oldPassword, newPassword) {\r\n  const data = {\r\n    oldPassword,\r\n    newPassword\r\n  }\r\n  return request({\r\n    url: '/system/user/profile/updatePwd',\r\n    method: 'post',\r\n    params: data\r\n  })\r\n}\r\n\r\n// 用户头像上传\r\nexport function uploadAvatar(data) {\r\n  return request({\r\n    url: '/system/user/profile/avatar',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 查询授权角色\r\nexport function getAuthRole(userId) {\r\n  return request({\r\n    url: '/system/user/authRole/' + userId,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 保存授权角色\r\nexport function updateAuthRole(data) {\r\n  return request({\r\n    url: '/system/user/authRole',\r\n    method: 'put',\r\n    params: data\r\n  })\r\n}\r\n"], "sourceRoot": ""}