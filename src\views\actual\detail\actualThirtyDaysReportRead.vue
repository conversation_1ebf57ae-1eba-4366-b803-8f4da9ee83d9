<!-- 监督追责实时报送-30个工作日实时报告快报 -->
<template>
  <div class=" app-report">
    <ModifyrecordBtn
      :key="detailInfo"
      :businessData="detailInfo"
    ></ModifyrecordBtn>
    <opinion
      :processInstanceId="procInsId"
      :isShow="isShow"
    />
      <Jscrollbar :height="detail?'100%':'68vh'">
        <el-row class="el-dialog-div">
          <el-col :span="24">
            <BlockCard
              title="基本信息"
            >
              <el-form ref="elForm" :model="detailInfo" :rules="rules" size="medium" label-width="150px">
                <el-row>
                  <el-col :span="8">
                    <el-form-item label="系统编号">
                      <span> {{detailInfo.auditCode}}</span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="问题编号">
                      <span> {{detailInfo.problemCode}}</span>
                    </el-form-item>
                  </el-col>

                  <el-col :span="24">
                    <el-form-item label="违规事项 ">
                      <span class="cursor text-red" @click="dailyDetail"> {{detailInfo.problemTitle}}</span>
                    </el-form-item>
                  </el-col>

                  <el-col :span="8">
                    <el-form-item label="发生时间" prop="findTime">
                      <span> {{detailInfo.findTime}}</span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="损失金额（万元）" prop="lossAmount">
                      <span> {{(detailInfo.lossAmount).toFixed(2)}}</span>
                    </el-form-item>
                  </el-col>

                  <el-col :span="8">
                    <el-form-item label="损失风险（万元）" prop="lossRisk">
                      <span> {{(detailInfo.lossRisk).toFixed(2)}}</span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="涉及企业级次" prop="involveUnitGrade">
                      <span>{{detailInfo.involveUnitGrade}}</span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="24">
                    <el-form-item  label="涉及企业名称">
                      <div class="select-list">
                        <div v-for="(item,index) of unitData" :key="index" class="list-li">
                          <span>{{ item.involveUnitName }}</span>
                        </div>
                      </div>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
            </BlockCard>
          </el-col>
          <el-col :span="24">
            <BlockCard
              title="30个工作日实时报告快报"
            >
              <el-form size="medium" label-width="150px">
                <el-row>
                  <el-col :span="24">
                    <el-form-item label="工作开展情况" prop="workDevelopment">
                      <span> {{detailInfo.workDevelopment}}</span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="24">
                    <el-form-item label="资产损失及其他严重不良后果" prop="consequences">
                      <span> {{detailInfo.consequences}}</span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="24">
                    <el-form-item label="存在主要问题" prop="importProblem">
                      <span> {{detailInfo.importProblem}}</span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="24">
                    <el-form-item label="初步核实违规违纪情况" prop="importReason">
                      <span> {{detailInfo.violationsInfo}}</span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="24">
                    <el-form-item label="初步核实是否属于责任追究范围" prop="isLiabilityRange">
                      <span> {{detailInfo.isLiabilityRange=='1'?'是':'否'}}</span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="24">
                    <el-form-item label="有关方面处置建议和要求" prop="measuresTaken">
                      <span>{{detailInfo.measuresTaken}}</span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="24">
                    <el-form-item label="已开展的应对处置、成效" prop="developDisposal">
                      <span>{{detailInfo.developDisposal}}</span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="24">
                    <el-form-item label="提出处置意见后期工作安排" prop="nextWork">
                      <span>{{detailInfo.nextWork}}</span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="24">
                    <el-form-item label="备注" prop="remark">
                      <span>{{detailInfo.remark}}</span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="联系人" prop="companyContacts">
                      <span>{{detailInfo.companyContacts}}</span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="联系电话" prop="contactsTel">
                      <span>{{detailInfo.contactsTel}}</span>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
            </BlockCard>
          </el-col>
          <el-col :span="24">
            <BlockCard
              title="报告附件"
            >
              <FileUpload
                :edit='edit'
                :problemId="field"
                :relevantTableId="relevantTableId"
                :relevantTableName="relevantTableName"
                flowType="VIOL_ACTUAL"
                problemStatus="3"
                linkKey="a001"
                ref="file"
                flowKey = "SupervisionDailyReport"
              ></FileUpload>
            </BlockCard>
          </el-col>
        </el-row>
        <el-dialog :visible.sync="dailyVisible" width="90%" :title="'日常问题-'+detailInfo.problemTitle" append-to-body>
          <Details
            :key="detailInfo"
            :selectValue="detailInfo"
            activeName="0"
          >
          </Details>
          <div slot="footer" class="dialog-footer">
            <el-button size="mini" type="primary"  @click="dailyClose" >确定</el-button>
          </div>
        </el-dialog>
      </Jscrollbar>
  </div>
</template>
<script>
  import {waitHandleThirtyReport, saveThirtyReport, submitThirtyReport, thirtyReportCompareWithDailyProblem} from "@/api/actual/task/actualFifteenAndThirtyDaysReport";
  import {queryActualInvolveUnit, deleteActualInvolveUnit, saveActualInvolveUnitData} from '@/api/actual/common/actualInvolveUnit';
  import BlockCard from "@/components/BlockCard";
  import FileUpload from '../../components/fileUpload/index';//附件
  import Recipient from '../common/recipient';// recipient
  import ModifyrecordBtn from '../common/modifyRecordBtn';
  import opinion from '../../daily/modifyRecord/opinion';
  import Details from '@/views/daily/actualDetail';

  export default {
    components: {BlockCard,FileUpload,Recipient,ModifyrecordBtn,opinion,Details},
    props: {
      isShow:{
        type: String,
        default: '0'
      },
      procInsId:{
        type: String
      },
      field:{
        type: String
      },
      detail:{
        type: Boolean,
        default:false
      }
    },
    data() {
      return {
        dailyVisible:false,
        flowParamsUrl:'',
        selectTree:[],
        VisibleCheckTree:false,
        url:'colligate/violActualInvolveUnit/actualInvolveUnitTreeData',
        actualProblemId: "1",
        relevantTableId: undefined,
        relevantTableName: undefined,
        edit: false,
        flag:false,
        visible:false,
        visibleTree:false,
        detailInfo:'',
        findTime: null,
        acceptTime: null,
        problemSource:null,
        problemTitle: null,
        problemDescribe: undefined,
        contactsTel: undefined,
        lossAmount: 0,
        lossRisk: 0,
        groupReceivers: undefined,
        provinceReceivers: undefined,
        seriousAdverseEffectsFlag: 1,
        otherSeriousAdverseEffects: undefined,
        illegalActivities: undefined,
        companyContacts: undefined,
        involveUnitGrade:'',
        specList: [],
        problemSourceList:[],
        unitData:[],
        groupData:{},//待阅接收人
        receiverGrade:'G'
      }
    },
    computed: {},
    watch: {},
    created() {
    },
    mounted() {
    },
    methods: {
      cancel(){
        this.visible=false;
      },
      /**初始化数据*/
      show(){
        this.visible=true;
        waitHandleThirtyReport(this.field).then(
          response => {
            const { code, data } = response
            if (code === 200) {
              this.detailInfo = Object.assign({}, data);
              this.actualProblemId = this.detailInfo.actualProblemId;
              this.relevantTableId = this.detailInfo.id;
              this.relevantTableName = this.detailInfo.businessTable;
              this.detailInfo.businessTable = this.relevantTableName;
              this.$nextTick(()=>{
                this.$refs.file.ViolationFileItems();
              });
              this.QueryFiveReportInvolveUnit();
            }
          }
        );
      },
      nextStep(){
        this.$emit('handle',1);
      },
      //企业数据
      QueryFiveReportInvolveUnit(){
        queryActualInvolveUnit({actualProblemId: this.detailInfo.actualProblemId, relevantTableId: this.detailInfo.id}).then(
          response => {
            this.selectTree = [];
            this.detailInfo.involveUnitGrade = response.involveUnitGrade;
            this.unitData = response.data;
            for(let i=0;i<this.unitData.length;i++){
              this.selectTree.push({id:this.unitData[i].compareId,name:this.unitData[i].involveUnitName})
            }
          }
        );
      },
      dailyDetail(){
        this.dailyVisible=true;
      },
      dailyClose(){
        this.dailyVisible=false;
      }
    }
  }

</script>
<style lang="scss" scoped>
  .dialog-body {
    height: 70vh;
  }
  .depart_li {
    min-width: 84px;
    height: auto;
    position: relative;
    background-color: #e6f7ff;
    color: #40a9ff;
    line-height: 30px;
    margin: 0 6px 0;
    display: inline-block;
    padding: 0 30px 0 12px;
    border-radius: 2px;
    box-sizing: border-box;
    .icon {
      float: right;
      cursor: pointer;
      position: absolute;
      right: 8px;
      top: 6px;
      font-size: 16px;
    }
  }
</style>
