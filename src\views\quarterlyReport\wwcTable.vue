
<!-- 季度报告--进行中数据 -->

<template>
    <div class="wai-container" style="background-color: #fff">
      <div class="layui-row width height">
        <div class="width height">
          <div class="common-wai-box" style="height: 100%">
            <div class="common-in-box" style="height: auto; min-height: 100%">
              <div class="tables tables_1">
                <el-table
                  :data="tablesData"
                  border
                  height="600px"
                  v-loading="tableLoading"
                  style="width: 100%"
                >
                <el-table-column
          type="index"
          label="序号"
          align="center"
          sortable
          min-width="5%"
          :index="table_index"
        />


                  <el-table-column
                    label="上报单位"
                    prop="reportUnitName"
                    min-width="10%"
                    align="center"
                  >
                    <template slot-scope="scope">
                      <div
                        style="text-align: center"
                        class="overflowHidden-1"
                        :title="scope.row.reportUnitName"
                      >
                        {{ scope.row.reportUnitName || "" }}
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column
                    label="接口人"
                    align="center"
                    prop="nickName"
                    min-width="10%"
                  />
                  <el-table-column
                    label="邮箱"
                    align="center"
                    prop="email"
                    min-width="20%"
                  />
                  <el-table-column
                    label="联系电话"
                    align="center"
                    prop="phoneNumber"
                    min-width="15%"
                  />
                </el-table>
                <pagination
                  v-show="total > 0"
                  :total="total"
                  :page.sync="queryParams.pageNum"
                  :limit.sync="queryParams.pageSize"
                  @pagination="queryWwcList"
                />
              </div>
            </div>
          </div>
        </div>
      </div>


    </div>
  </template>
      <script>
  import {
    getReportUnitUserData
  } from '@/api/quarterly-report/view';
  export default {
    components: {},
    props: {
      //编辑内容
      rowData: {
        type: Object,
        default: () => {},
      },
    },
    dicts: [],
    data() {
      return {
        tableLoading: false, //表格loading
        tablesData: [], //列表数据
        //表格页码
        total: 0,
        queryParams: {
          pageNum: 1,
          pageSize: 10,
        },
      };
    },
    created() {
      this.queryWwcList();
    },
    methods: {
      table_index(index) {
      return (this.queryParams.pageNum - 1) * this.queryParams.pageSize + index + 1
    },
      //查询列表
      queryWwcList(){
        this.tableLoading = true
        var params = {
          quarterReportId : this.rowData.quarterReportId,
          status:this.rowData.procStatus
        }
        getReportUnitUserData(params,this.queryParams).then((res)=>{
          this.tablesData = res.rows;
          this.total = res.total
          this.tableLoading = false
        })
      },
    },
  };
  </script>
  <style lang="scss" scoped>
      @import "~@/assets/styles/quarterly-report/index.css";
  </style>
