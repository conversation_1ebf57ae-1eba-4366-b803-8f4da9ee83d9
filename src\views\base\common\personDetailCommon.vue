<template>
  <div>
    <el-row :gutter="15">
      <el-col :span="12">
        <BlockCard  title="责任追究工作领导小组">
          <div>
            <div class="person-header-style">
              <span>成员单位</span>
              <el-input style="width: 30%; margin-left: 5px; margin-right: 5px;" placeholder="请选择成员单位" readonly v-model="dataDetails.memberOrgName"></el-input>
              <el-button v-if="type=='edit'" type="primary" plain size="small" @click="openOrgTree('成员单位', dataDetails.memberOrgId, dataDetails.memberOrgName)">修改成员单位</el-button>
              <el-button v-if="type=='edit'" type="primary" plain icon="el-icon-plus" size="small" class="float-right"  @click="openNewOrgStaffTree('invol_leader_group','invol_person_type','责任追究工作领导小组', dataDetails.groupList)">添加人员</el-button>
            </div>
            <el-table border stripe :data="dataDetails.groupList" class="table-new-height" height="200px" style="width: 100%; margin-top: 8px;height:200px!important;" :header-cell-style="{background:'#F4F8FC',color:'#606266','text-align':'center'}" :cell-style="{'text-align':'center'}">
              <el-table-column prop="personType" label="类别" width="100">
                <template slot-scope="scope">
                  {{scope.row.personType | fromatComonInvol(invol_leader_group)}}
                </template>
              </el-table-column>
              <el-table-column prop="personName" label="姓名" width="100"></el-table-column>
              <el-table-column prop="personDuty" label="职务" show-overflow-tooltip></el-table-column>
              <el-table-column label="操作" v-if="type=='edit'" width="90">
                <template slot-scope="scope">
                  <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-edit"
                    title="编辑"
                    @click="openOrgStaffTreeOneNew(scope.row, scope.$index)"
                  ></el-button>
<!--                  <el-button-->
<!--                    v-if="scope.$index > 0 && dataDetails.groupList.length>2"-->
<!--                    size="mini"-->
<!--                    type="text"-->
<!--                    icon="el-icon-delete"-->
<!--                    title="删除"-->
<!--                    @click="delPerson(scope.row, dataDetails.groupList)"-->
<!--                  ></el-button>-->
                  <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-delete"
                    title="删除"
                    @click="delPerson(scope.row, dataDetails.groupList)"
                  ></el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </BlockCard>
      </el-col>
      <el-col :span="12">
        <BlockCard  title="责任追究工作领导小组办公室">
        <div>
          <div class="person-header-style">
            <span>所在部门</span>
            <el-input style="width: 30%; margin-left: 8px; margin-right: 8px;" placeholder="请选择所在部门" readonly v-model="dataDetails.officeDeptName"></el-input>
            <el-button v-if="type=='edit'" type="primary" plain size="small" @click="openOrgTree('所在部门', dataDetails.officeDeptId, dataDetails.officeDeptName)">修改所在部门</el-button>
            <el-button v-if="type=='edit'" type="primary" plain icon="el-icon-plus" size="small" class="float-right" @click="openNewOrgStaffTree('invol_office','invol_person_type','责任追究工作领导小组办公室', dataDetails.officeDeptList)">添加人员</el-button>
          </div>
          <el-table border height="200px"  class="table-new-height" stripe :data="dataDetails.officeDeptList" style="width: 100%; margin-top: 8px" :header-cell-style="{background:'#F4F8FC',color:'#606266','text-align':'center'}" :cell-style="{'text-align':'center'}">
            <el-table-column prop="personTypeText" label="类别" width="100">
              <template slot-scope="scope">
                {{scope.row.personType | fromatComonInvol(invol_office)}}
              </template>
            </el-table-column>
            <el-table-column prop="personName" label="姓名" width="100"></el-table-column>
            <el-table-column prop="personDuty" label="职务" show-overflow-tooltip></el-table-column>
            <el-table-column label="操作" v-if="type=='edit'" width="90">
              <template slot-scope="scope">
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-edit"
                  title="编辑"
                  @click="openOrgStaffTreeOneNew(scope.row, scope.$index)"
                ></el-button>
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-delete"
                  title="删除"
                  @click="delPerson(scope.row, dataDetails.officeDeptList)"
                ></el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        </BlockCard>
      </el-col>
      <el-col :span="24">
        <BlockCard  title="职能部门">
          <div>
            <div class="person-header-style">
              <span>职能部门</span>
              <el-input style="width: 30%; margin-left: 8px; margin-right: 8px;" placeholder="请选择职能部门"  readonly v-model="dataDetails.funDeptName"></el-input>
              <el-button v-if="type=='edit'" type="primary" plain size="small"  @click="openOrgTree('职能部门', dataDetails.funDeptId, dataDetails.funDeptName)">修改职能部门</el-button>
            </div>
            <el-table stripe border :data="dataDetails.funDeptList"  style="width: 100%; margin-top: 8px;height: 200px!important;" :header-cell-style="{background:'#F4F8FC',color:'#606266','text-align':'center'}" :cell-style="{'text-align':'center'}">
              <el-table-column prop="personTypeText" label="类别" width="185"></el-table-column>
              <el-table-column prop="personName" label="负责人" width="100"></el-table-column>
              <el-table-column prop="personDuty" label="职务" show-overflow-tooltip></el-table-column>
              <el-table-column prop="personPhone" label="办公电话" width="150"></el-table-column>
              <el-table-column prop="personMobilePhone" label="手机"></el-table-column>
              <el-table-column label="操作" v-if="type=='edit'">
                <template slot-scope="scope">
                  <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-edit"
                    title="编辑"
                    @click="openOrgStaffTreeOne(scope.row, scope.$index)"
                  ></el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </BlockCard>
      </el-col>
      <el-col :span="24">
        <BlockCard  title="职能处室">
          <div>
            <div class="person-header-style">
              <span>职能处室</span>
              <el-input style="width: 30%; margin-left: 8px; margin-right: 8px;" placeholder="请选择职能处室" readonly v-model="dataDetails.funOfficeName"></el-input>
              <el-button v-if="type=='edit'" type="primary" plain size="small" @click="openOrgTree('职能处室', dataDetails.funOfficeId, dataDetails.funOfficeName)">修改职能处室</el-button>
              <el-button v-if="type=='edit'" type="primary" plain icon="el-icon-plus" size="small" style="float: right; margin-right: 8px;" @click="openOrgStaffTree('工作人员', dataDetails.funOfficeList)">添加工作人员</el-button>
            </div>
            <el-table stripe border :data="dataDetails.funOfficeList" style="width: 100%; margin-top: 8px" :header-cell-style="{background:'#F4F8FC',color:'#606266','text-align':'center'}" :cell-style="{'text-align':'center'}">
              <el-table-column prop="personTypeText" label="类别" width="180"></el-table-column>
              <el-table-column prop="personName" label="负责人" width="180"></el-table-column>
              <el-table-column prop="personDuty" label="职务" show-overflow-tooltip></el-table-column>
              <el-table-column prop="personPhone" label="办公电话" width="180"></el-table-column>
              <el-table-column prop="personMobilePhone" label="手机"></el-table-column>
              <el-table-column label="操作" v-if="type=='edit'">
                <template slot-scope="scope">
                  <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-edit"
                    title="编辑"
                    @click="openOrgStaffTreeOne(scope.row, scope.$index)"
                  ></el-button>
                  <el-button
                    v-if="scope.$index > 0 && dataDetails.funOfficeList.length>2"
                    size="mini"
                    type="text"
                    icon="el-icon-delete"
                    title="删除"
                    @click="delPerson(scope.row, dataDetails.funOfficeList)"
                  ></el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </BlockCard>
      </el-col>
      <el-col :span="24">
        <BlockCard  title="联系人">
          <div>
            <div  class="person-header-style">
              <el-button v-if="type=='edit'"  type="primary" plain icon="el-icon-plus" size="small" style="float: right; margin-right: 8px;"  @click="openOrgStaffTree('联系人', dataDetails.contactsList)">添加联系人</el-button>
            </div>
            <el-table border stripe :data="dataDetails.contactsList" style="width: 100%; margin-top: 8px" :header-cell-style="{background:'#F4F8FC',color:'#606266','text-align':'center'}" :cell-style="{'text-align':'center'}">
              <el-table-column prop="personTypeText" label="类别" width="150"></el-table-column>
              <el-table-column prop="personName" label="姓名" width="150"></el-table-column>
              <el-table-column prop="personDuty" label="职务" show-overflow-tooltip></el-table-column>
              <el-table-column prop="personEmail" label="电子邮箱" width="220" show-overflow-tooltip></el-table-column>
              <el-table-column prop="personAdress" label="地址" show-overflow-tooltip></el-table-column>
              <el-table-column prop="personPostalCode" label="邮编" width="150"></el-table-column>
              <el-table-column label="操作" v-if="type=='edit'">
                <template slot-scope="scope">
                  <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-edit"
                    title="编辑"
                    @click="openOrgStaffTreeOne(scope.row, scope.$index)"
                  ></el-button>
                  <el-button
                    v-if="dataDetails.contactsList.length>1"
                    size="mini"
                    type="text"
                    icon="el-icon-delete"
                    title="删除"
                    @click="delPerson(scope.row, dataDetails.contactsList)"
                  ></el-button>
                </template>
              </el-table-column>
            </el-table>
            <orgTreeModal v-if="dialogVisible=='orgTree'" :nodeData="nodeData" :modalTitle="modalTitle" v-on:closeModal="closeModal" v-on:getTreeData="getTreeData" v-on:getOrg="getOrg" :treeList="treeList"></orgTreeModal>
            <orgStaffTreeModal v-if="dialogVisible=='orgStaffTree'"
              :modalTitle="modalTitle" v-on:closeModal="closeModal" v-on:getOrg="getOrg"
              v-on:getStaffTreeData="getStaffTreeData" :treeList="treeList" :selectList="selectList"
              :personInfo="personInfo" :selectType="selectType">
            </orgStaffTreeModal>
            <orgStaffTreeModalNew v-if="dialogVisible=='orgStaffTreeNew'"
                                  :personInfoList="personInfoList"
                               :modalTitle="modalTitle" v-on:closeModal="closeModal" v-on:getOrg="getOrg"
                               v-on:getStaffTreeData="getStaffTreeData" :treeList="treeList" :selectList="selectList"
                               :personInfo="personInfo" :selectType="selectType">
            </orgStaffTreeModalNew>
          </div>
        </BlockCard>
      </el-col>
      <el-col :span="24">
        <BlockCard  title="信息系统管理员">
          <div>
            <el-table border stripe :data="dataDetails.infoAdminList" style="width: 100%; margin-top: 8px" :header-cell-style="{background:'#F4F8FC',color:'#606266','text-align':'center'}" :cell-style="{'text-align':'center'}">
              <el-table-column prop="personTypeText" label="类别" width="180"></el-table-column>
              <el-table-column prop="personName" label="姓名" width="180"></el-table-column>
              <el-table-column prop="personEmail" label="电子邮箱" show-overflow-tooltip></el-table-column>
              <el-table-column prop="personFax" label="传真" width="180"></el-table-column>
              <el-table-column label="操作" v-if="type=='edit'">
                <template slot-scope="scope">
                  <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-edit"
                    title="编辑"
                    @click="openOrgStaffTreeOne(scope.row, scope.$index)"
                  ></el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </BlockCard>
      </el-col>
    </el-row>
  </div>
</template>

<script lang="ts">
  import {getAreaPersonBaseInfoById, getOrgTreeWithPerson, getOrgTree, getAreaPersonBaseInfo
  ,queryPerSonType} from "@/api/base/person";
  import orgTreeModal from "./orgTreeModal";
  import orgStaffTreeModal from "./orgStaffTreeModal";
  import orgStaffTreeModalNew from "./orgStaffTreeModalNew";
  import BlockCard from '@/components/BlockCard';
  import moment from "moment";

  export default {
    name: "personDetailCommon",
    components: { orgTreeModal, orgStaffTreeModal,orgStaffTreeModalNew, BlockCard },
    props: {
      id: {
        type: String,
        default: ''
      },
      type: {
        type: String,
        default: 'edit'
      },
      dataDetailsProps: {
        type: Object,
        default: ()=>{return {}}
      },
    },
    data() {
      return {
        invol_leader_group:[],//责任追究工作领导小组 字典
        invol_office:[],//责任追究工作领导小组 字典
        dataDetails: {},
        // 是否显示弹出层
        dialogVisible: '',
        treeList: [],
        modalTitle: '',//模态框title
        nodeData:{},//组织树选中的数据
        selectList:[],//人员组织树 选中的人员数据
        personInfo:{
          personType: "",
          personTypeText: "",
        },
        personInfoList:[],
        selectType: '',
        listIndex: 0,//编辑的内容下标
      };
    },
    filters: {
      fromatComonInvol (value, list) {
        let lastLabel = '-'
        if (value && list.length > 0) {
          list.forEach(element => {
            if (element.personType == value) {
              lastLabel = element.personTypeText
            }
          })
        }
        return lastLabel
      },
    },
    created() {
      this.getDetail();
      this.involLeaderGroup();
      this.involOffice();
    },
    watch: {
      dataDetailsProps: 'getDetail'
    },
    methods: {

      //查询责任追究工作领导小组类型
      involLeaderGroup(){
        queryPerSonType({
          parentCode:'invol_leader_group'
          ,type:'invol_person_type'
        }).then( response => {
          this.invol_leader_group = response.data;
        })
      },
      //查询责任追究工作领导小组办公室类型
      involOffice(){
        queryPerSonType({
          parentCode:'invol_office'
          ,type:'invol_person_type'
        }).then( response => {
          this.invol_office = response.data;
        })
      },
      /**获取企业基本信息详情*/
      getDetail(){
        this.dataDetails = this.dataDetailsProps;
      },
      /**关闭模态框*/
      closeModal(){
        this.dialogVisible = '';
      },
      // 打开组织树选择框
      openOrgTree(title, id, name){
        // var staffOrgList = getOrgTreeWithPerson({});
        this.getTreeData(null);
        this.dialogVisible = 'orgTree';
        this.modalTitle = title;
        this.nodeData = {
          id,
          name,
        }
      },
      // 查询组织树数据
      getTreeData(name){
        getOrgTree({name,}).then(
            response => {
              this.treeList = this.arrayToTree(response.data, false);
            }
        );
      },
      /* 转树结构，
      type: true: 父节点不可选， */
      arrayToTree(array, type) {
        let data = JSON.parse(JSON.stringify(array));
        let result = [];
        let hash = {};
        data.forEach((item) => {
          hash[item['id']] = item;
        });
        data.forEach(item => {
          let hashVP = hash[item['pId']];
          if (hashVP) {
            !hashVP['children'] && (hashVP['children'] = []);
            if (item['id']) {
              item.id = item['id'];
              item.disabled = !!item.isParent;
            }
            if (item['name']) {
              item.label = item['name'];
            }
            hashVP['children'].push(item);
          } else {// 根节点直接放入结果集
            if (item['id']) {
              item.id = item['id'];
              item.disabled = !!item.isParent;
            }
            if (item['name']) {
              item.label = item['name'];
            }
            result.push(item);
          }
        });
        return result;
      },
      // 回显选择信息
      getOrg(nodeData){
        if(this.modalTitle == "成员单位"){
          this.dataDetails.memberOrgId = nodeData.id;
          this.dataDetails.memberOrgName = nodeData.name;
        }else if(this.modalTitle == "所在部门"){
          this.dataDetails.officeDeptId = nodeData.id;
          this.dataDetails.officeDeptName = nodeData.name;
        }else if(this.modalTitle == "职能部门"){
          this.dataDetails.funDeptId = nodeData.id;
          this.dataDetails.funDeptName = nodeData.name;
        }else if(this.modalTitle == "职能处室"){
          this.dataDetails.funOfficeId = nodeData.id;
          this.dataDetails.funOfficeName = nodeData.name;
        }else if(this.modalTitle == "副组长" || this.modalTitle == "组长"){
          if(this.selectType == 'one'){
            this.dataDetails.groupList[this.listIndex] = nodeData[0];
          }else{
            this.dataDetails.groupList = nodeData;
          }
        }else if(this.modalTitle == "成员"){
          if(this.selectType == 'one'){
            if(nodeData[0].personType=='leading_numbers'){//责任追究工作领导小组办公室
              this.dataDetails.officeDeptList[this.listIndex] = nodeData[0];
            }else{//责任追究工作领导小组
              this.dataDetails.groupList[this.listIndex] = nodeData[0];
            }
          }
        }else if(this.modalTitle == "主任" || this.modalTitle == "副主任"){
          if(this.selectType == 'one'){
            this.dataDetails.officeDeptList[this.listIndex] = nodeData[0];
          }else{
            this.dataDetails.officeDeptList = nodeData;
          }
        }else if(this.modalTitle == "工作人员" || this.modalTitle == "职能处室负责人"){
          if(this.selectType == 'one'){
            this.dataDetails.funOfficeList[this.listIndex] = nodeData[0];
          }else{
          this.dataDetails.funOfficeList = nodeData;
          }
        }else if(this.modalTitle == "联系人"){
          this.dataDetails.contactsList = nodeData;
        }else if(this.modalTitle == "负责人"){
          this.dataDetails.officeDeptList[this.listIndex] = nodeData[0];
        }else if(this.modalTitle == "部门主要负责人" || this.modalTitle == "分管监督追责工作负责人"){
          this.dataDetails.funDeptList[this.listIndex] = nodeData[0];
        }else if(this.modalTitle == "信息系统管理员"){
          this.dataDetails.infoAdminList[this.listIndex] = nodeData[0];
        }else if(this.modalTitle == "责任追究工作领导小组-添加人员"){
          this.dataDetails.groupList = nodeData;
        }else if(this.modalTitle == "责任追究工作领导小组办公室-添加人员"){
          this.dataDetails.officeDeptList = nodeData;
        }
        const dataDetailsTemp = JSON.parse(JSON.stringify(this.dataDetails));
        this.$emit("onDetailsChange", dataDetailsTemp);
      },

      // 查询人员组织树数据
      getStaffTreeData(name){
        getOrgTreeWithPerson({name,}).then(
            response => {
              this.treeList = this.arrayToTree(response.data, true);
            }
        );
      },
      //设置模态框人员信息
      setPersonInfo(title,row){
        if(title == "工作人员"){
          this.personInfo = {
            orgType: "invol_fun_office",
            personType: "fun_office_worker",
            personTypeText: "工作人员",
            minLength: 2,
          };
        }else if(title == "联系人"){
          this.personInfo = {
            orgType: "invol_contacts",
            personType: "contacts",
            personTypeText: "联系人",
            minLength: 1,
          };
        }else if(title == "负责人"){
          this.personInfo = {
            orgType: "invol_office",
            personType: "leading",
            personTypeText: "负责人",
            minLength: 1,
          };
        }else if(title == "部门主要负责人"){
          this.personInfo = {
            orgType: "invol_fun_dept",
            personType: "fun_dept_leader",
            personTypeText: "部门主要负责人",
            minLength: 1,
          };
        }else if(title == "分管监督追责工作负责人"){
          this.personInfo = {
            orgType: "invol_fun_dept",
            personType: "fun_dept_monitor_leader",
            personTypeText: "分管监督追责工作负责人",
            minLength: 2,
          };
        }else if(title == "信息系统管理员"){
          this.personInfo = {
            orgType: "invol_info_admin",
            personType: "info_admin",
            personTypeText: "信息系统管理员",
            minLength: 1,
          };
        }else if(title == "职能处室负责人"){
          this.personInfo = {
            orgType: "invol_fun_office",
            personType: "fun_office_leader",
            personTypeText: "职能处室负责人",
            minLength: 1,
          };
        }else if(title == "责任追究工作领导小组-添加人员"){
          this.personInfo = {
            orgType: "invol_leader_group",
            // personType:"deputy_grouper",
            // personTypeText:"副组长"
          };
        }else if(title == "责任追究工作领导小组办公室-添加人员"){
          this.personInfo = {
            orgType: "invol_office",
            // personType:"deputy_leading",
            // personTypeText:"副主任"
          };
        }else{
          console.log(row)
          this.personInfo = {
              orgType: row.orgType,
              personType: row.personType,
              personTypeText: row.personTypeText
          };
        }
      },
      // 打开人员组织树模态框
      openOrgStaffTree(title, list){
        this.getStaffTreeData(null);
        this.dialogVisible = 'orgStaffTree';
        this.modalTitle = title;
        this.selectList = list;
        this.setPersonInfo(title);
        this.selectType = "more";
      },
      // 打开人员组织树模态框--单个人选择
      openOrgStaffTreeOne(row, index){
        this.getStaffTreeData(null);
        this.dialogVisible = 'orgStaffTree';
        this.modalTitle = row.personTypeText;
        this.selectList = [row];
        this.setPersonInfo(row.personTypeText);
        this.selectType = "one";
        this.listIndex = index;
      },

      //责任追究工作领导小组\责任追究工作领导小组办公室--添加人员
      openNewOrgStaffTree(parentCode,type,title, list){
        this.getStaffTreeData(null);
        this.dialogVisible = 'orgStaffTreeNew';
        this.modalTitle = title+"-添加人员";
        this.selectList = list;
        this.setPersonInfo(this.modalTitle);
        this.queryPerSonType(parentCode,type);
        this.selectType = "more";
      },

      // 打开人员组织树模态框--单个人选择
      openOrgStaffTreeOneNew(row, index){
        this.getStaffTreeData(null);
        this.dialogVisible = 'orgStaffTreeNew';
        this.modalTitle = row.personTypeText;
        this.selectList = [row];
        this.setPersonInfo(row.personTypeText,row);
        this.selectType = "one";
        this.listIndex = index;
      },

      //查询人员类型
      queryPerSonType(parentCode,type){
        queryPerSonType({
          parentCode:parentCode
          ,type:type
        }).then( response => {
          this.personInfoList = response.data;
        })
      },


      // 删除操作
      delPerson(row, list){
        this.$confirm('确定删除该条数据？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          let listCopy = JSON.parse(JSON.stringify(list));
          listCopy.map((item, index)=>{
            if(item.personType == row.personType && item.personId == row.personId){
              list.splice(index,1);
              return;
            }
          })
        })
      },
    }
  };
</script>

<style scoped>

  .text {
    font-size: 14px;
  }

  .item {
    margin-bottom: 18px;
  }

  .clearfix:before,
  .clearfix:after {
    display: table;
    content: "";
  }
  .clearfix:after {
    clear: both
  }

  .file-title{
    text-indent: 10px;
    line-height: 41px;
    border-left: 5px solid red;
    padding-left: 26px;
  }
  .el-card__header{
    border-bottom: none !important;
    padding-bottom: 0px !important;
    background-color:transparent;
  }
  .el-divider--horizontal{
    margin: 0 !important;
  }
  .el-button--small{
    padding: 9px 10px !important;
}
  .public-box{
    box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
    margin-bottom:10px;
  }
  .person-header-style{
    height: 52px;
    padding: 8px 10px;
    white-space: nowrap;
    background: rgb(246, 246, 246);
  }
  .table-new-height{
    height: 200px!important;
  }
</style>
