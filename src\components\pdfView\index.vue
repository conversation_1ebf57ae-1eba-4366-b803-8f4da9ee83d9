<template>
  <el-dialog
    :title="elDialogHeaderName"
    class="my-el-dialog"
    :visible.sync="open"
    width="85%"
    append-to-body
    @close="closeDialog"
  >
    <div slot="title" class="el-header-title">
      <svg-icon icon-class="viewfile" />
      <span class="el-dialog-header-name">{{ elDialogHeaderName }}</span>
    </div>
    <div class="big-box" style="height:720px">
      <el-scrollbar style="height:100%">
        <pdf ref="wrapper" :src="src" :page="currentPage" @progress="loadedRatio = $event" @num-pages="pageCount=$event" @page-loaded="currentPage=$event" @loaded="loadPdfHandler" />
      </el-scrollbar>
    </div>

    <div class="dialog-footer">
      <el-button
        type="primary"
        :class="{select:idx==3}"
        @touchstart="idx=3"
        @touchend="idx=-1"
        @click="changePdfPage(1)"
      >下一页</el-button>
      <el-button
        type="primary"
        :class="{select:idx==2}"
        @touchstart="idx=2"
        @touchend="idx=-1"
        @click="changePdfPage(0)"
      >上一页</el-button>

    </div>
  </el-dialog>

</template>
<script>
import axios from 'axios'
import pdf from 'vue-pdf'
import { Loading } from 'element-ui'
import { getToken } from '@/utils/auth'
export default {
  components: {
    pdf
  },
  props: ['data'],
  data() {
    return {
      elDialogHeaderName: '预览',
      loading: false,
      src: '',
      open: true,
      currentPage: 0, // pdf文件页码
      pageCount: 0, // pdf文件总页数
      scale: 100, // 放大系数
      idx: -1,
      clauseTitle: '',
      loadedRatio: 0,

    }
  },
  mounted() {
    this.getPdfCode()
  },
  methods: {

    closeDialog(){
      this.$emit('close')
    },
    // loading设置
    start() {
      this.loading = Loading.service({
        lock: true,
        text: '正在加载中...',
        background: 'rgba(255,255,255,0.1)'
      })
    },
    end() {
      this.loading.close()
    },
    getPdfCode() {
      this.start()
      axios({
        method: 'get',
        url:
          process.env.VUE_APP_BASE_API +
          `/sys/preview/downLoadShow?fileId=${this.data.id}&fileName=${encodeURI(this.data.fileName)}&waterMark=${encodeURI(this.data.waterMark)}`,
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          Authorization: 'Bearer ' + getToken()
        },
        responseType: 'blob' // 设置响应的数据类型为一个包含二进制数据的 Blob 对象，必须设置！！！
      }).then((res) => {
        this.fileStream = res
        this.src = this.getObjectURL(res.data) // 将返回的数据流转换为url
        this.previewContract = true
        this.end()
      })
    },
    // 将返回的流数据转换为url
    getObjectURL(file) {
      let url = null
      if (window.createObjectURL != undefined) {
        // basic
        url = window.createObjectURL(file)
      } else if (window.webkitURL != undefined) {
        // webkit or chrome
        try {
          url = window.webkitURL.createObjectURL(file)
        } catch (error) {}
      } else if (window.URL != undefined) {
        // mozilla(firefox)
        try {
          url = window.URL.createObjectURL(file)
        } catch (error) {}
      }
      return url
    },
    // 改变PDF页码,val传过来区分上一页下一页的值,0上一页,1下一页
    changePdfPage(val) {
      if (val === 0 && this.currentPage > 1) {
        this.currentPage--
      }
      if (val === 1 && this.currentPage < this.pageCount) {
        this.currentPage++
      }
    },
    goBack() {
      this.$router.go(-1)
    },
    // pdf加载时
    loadPdfHandler(e) {
      this.currentPage = 1 // 加载的时候先加载第一页
    },
    // 放大
    scaleD() {
      this.scale += 5
      // this.$refs.wrapper.$el.style.transform = "scale(" + this.scale + ")";
      this.$refs.wrapper.$el.style.width = parseInt(this.scale) + '%'
    },

    // 缩小
    scaleX() {
      if (this.scale == 100) {
        return
      }
      this.scale += -5
      this.$refs.wrapper.$el.style.width = parseInt(this.scale) + '%'
      // this.$refs.wrapper.$el.style.transform = "scale(" + this.scale + ")";
    }

  }
}

</script>

<style scoped>
::v-deep .el-scrollbar__wrap{
  overflow-x: hidden;
}
.dialog-footer{
  height: 50px;
  margin-top: 15px;
}
.el-button--medium{
  float: right;
  margin-right: 10px;
}
</style>
