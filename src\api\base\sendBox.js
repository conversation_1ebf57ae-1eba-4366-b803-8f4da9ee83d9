import request from '@/utils/request'

// 基础数据查询--发件箱列表查询
export function getSendBoxList(query) {
  return request({
    url: '/colligate/baseInfo/getSendBoxList',
    method: 'post',
    data: query
  })
}


// 基础数据查询--发件箱详情查询
export function getSendBoxById(query) {
  return request({
    url: '/colligate/baseInfo/getSendBoxById',
    method: 'post',
    data: query
  })
}

// 基础数据维护--发件箱查询（最新版本）
export function getSendBoxOne(query) {
  return request({
    url: '/colligate/baseInfo/getSendBoxOne',
    method: 'post',
    data: query
  })
}
// 基础数据维护--保存或提交
export function saveSendBox(query) {
  return request({
    url: '/colligate/baseInfo/saveSendBox',
    method: 'post',
    data: query
  })
}

// 基础数据维护--删除附件
export function deleteViolFile(query) {
  return request({
    url: '/colligate/baseInfo/deleteViolFile/' + query.busiTableName+'/' + query.busiId + '/' + query.id,
    method: 'post',
    data: query
  })
}

// 基础数据维护--发件箱查询空数据
export function getBaseSendBoxNull() {
  return request({
    url: '/colligate/baseInfo/getBaseSendBoxNull',
    method: 'post',
  })
}

// 基础数据维护--查询发件箱最新版本数据
export function getBaseSendBoxNewVerson(query) {
  return request({
    url: '/colligate/baseInfo/getBaseSendBoxNewVerson',
    method: 'post',
    data: query
  })
}