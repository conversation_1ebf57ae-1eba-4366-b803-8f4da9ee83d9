<!--日常问题录入-->
<template>
  <div class="app-container app-report">
    <el-form :model="queryParams" ref="queryForm" id="queryParams" v-show="showSearch" :inline="true" label-width="120px">
      <el-form-item label="问题涉及单位">
        <el-input v-model="queryParams.involCompanyName" placeholder="问题涉及单位"  :style="{width: '100%'}">
        </el-input>
      </el-form-item>
      <el-form-item label="受理日期" prop="queryParams.acceptTime">
        <el-date-picker v-model="queryParams.acceptTime" format="yyyy-MM-dd" value-format="yyyy-MM-dd" placeholder="受理日期" clearable></el-date-picker>
      </el-form-item>
      <el-form-item label="违规事项">
        <el-input v-model="queryParams.problemTitle" placeholder="违规事项"  :style="{width: '100%'}">
        </el-input>
      </el-form-item>
      <div class="float-right">
        <el-form-item>
          <el-button
            type="primary"

            icon="el-icon-plus"
            size="mini"
            @click="handleAdd"
          >新增</el-button>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
      </div>

    </el-form>
    <el-form   style="height: calc(100vh - 300px)">
      <el-table ref="table" border v-loading="loading" :data="tableList"  height="100%">
        <el-table-column label="序号" type="index" min-width="5%" align="center" >
          <template slot-scope="scope">
            <table-index
              :index="scope.$index"
              :page-num="queryParams.pageNum"
              :page-size="queryParams.pageSize"
            />
          </template>
        </el-table-column>
        <el-table-column label="违规事项" prop="problemTitle" min-width="30%" show-overflow-tooltip/>
        <el-table-column label="问题涉及单位" prop="involCompanyNames"  min-width="30%" show-overflow-tooltip/>
        <el-table-column label="问题线索来源" prop="problemSourceName"  min-width="20%" show-overflow-tooltip/>
        <el-table-column label="关联专项台账" prop="problemAudit"  min-width="15%">
          <template slot-scope="scope">
            <a @click="relationLedger(scope.row.relationId)"class="table-btn"style='color: #c20000;'>
              {{ scope.row.problemAudit }}
            </a>
          </template>
        </el-table-column>
        <el-table-column label="发现时间" prop="findTime" align="center" min-width="15%" show-overflow-tooltip/>
<!--        <el-table-column label="受理日期" prop="acceptTime" align="center" min-width="15%" show-overflow-tooltip/>-->
        <!--<el-table-column label="经办人" prop="createUserName" align="center" min-width="15%" show-overflow-tooltip/>-->
        <el-table-column label="操作"  min-width="20%" align="center" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              title="编辑"
              @click="handleDetail(scope.row,1)"
            >
            </el-button>
            <el-button
              size="mini"
              type="text"
              title="查看"
              icon="el-icon-search"
              @click="handleDetail(scope.row,0)"
            ></el-button>
            <el-button
              size="mini"
              type="text"
              title="删除"
              icon="el-icon-delete"
              @click="handleDel(scope.row)"
            ></el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-form>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="ViolDailyDraftList"
    />
    <!--新增-->
    <Add
      ref="add"
      :key="keyAdd"
      v-on:closeConfirm="closeConfirm"
    ></Add>
    <!--填报-->
      <taskTodoViewAccept
        :key="problemId"
        ref="accept"
        :edit='edit'
        :problemId="problemId"
        :relevantTableId = "relevantTableId"
        :relevantTableName = "relevantTableName"
        @close="ViolDailyDraftList"
      >
      </taskTodoViewAccept>
  <!--台账攥取-->
    <RelationUnSpLedger
      ref="relation"
      :key="index"
      :relationId="relationId"
      :queryType="queryType"
    ></RelationUnSpLedger>
  </div>
</template>

<script>
  import {violDailyDraftList,violDailyDraftDelete} from "@/api/daily/enter";
  import Add from './add';
  import taskTodoViewAccept from './process/taskTodoViewAccept';
  import RelationUnSpLedger from '@/views/daily/spledger/relationUnSpLedger';//攥取台账页面

  export default {
    name: "Daily/enter",
    components: {
      Add,
      taskTodoViewAccept
      ,RelationUnSpLedger
    },
    dicts: [],
    data() {
      return {
        keyAdd:0,
        loading:false,
        //填报遮罩层
        visible:false,
        //选中的数据
        problemId:'',
        relevantTableId:'',
        relevantTableName:'',
        //查看与编辑
        edit:true,
        // 显示搜索条件
        showSearch: true,
        // 总条数
        total: 0,
        // 表格数据
        tableList: [],
        //新增主键
        //日常问题查询 参数
        queryParams: {
          pageNum: 1,
          pageSize: 10,
          involCompanyName:'',
          acceptTime:'',
          problemTitle:''
        },
        index:0,
        queryType:'relation',
        relationId:'',
      };
    },
    created() {
      this.ViolDailyDraftList();
    },
    activated() {
      this.$nextTick(() => {
        this.$refs.table.doLayout(); //解决表格错位
      });
    },
    filters: {
    },
    methods: {
      /**查询日常问题列表*/
      ViolDailyDraftList() {
        this.loading = true;
        violDailyDraftList(this.queryParams).then(
          response => {
            this.tableList = response.rows;
            this.total = response.total;
            this.loading = false;
          }
        );
      },
      /** 新增按钮操作*/
      handleAdd() {
        this.keyAdd++;
        this.$nextTick(()=>{
          this.$refs.add.show();
        })
      },
      /** 新增关闭触发操作*/
      closeAdd() {
        console.log('子组件关闭了弹出层');
      },
      /** 搜索按钮操作*/
      handleQuery() {
        this.queryParams.pageNum = 1;
        this.ViolDailyDraftList();
      },
      /**重置按钮操作*/
      resetQuery() {
        this.queryParams = {
          pageNum: 1,
          pageSize: 10,
          involCompanyName:'',
          acceptTime:'',
          problemTitle:''
        };
        this.ViolDailyDraftList();
      },

      /** 删除按钮操作 */
      handleDel(row) {
        this.$modal.confirm('是否确定删除该数据？').then(function() {
          return violDailyDraftDelete({id:row.id});
        }).then(() => {
          this.$modal.msgSuccess("删除成功");
          this.ViolDailyDraftList();
        }).catch(() => {});
      },
      /** 编辑与查看操作 */
      handleDetail(row,type){
        this.problemId = row.id;
        this.relevantTableId=row.relevantTableId;
        this.relevantTableName=row.relevantTableName;
        this.edit = !!type;
        this.$nextTick(() => {
          this.$refs.accept.show();
        });
      },
      /** 新增成功 */
      closeConfirm(formData){
        this.problemId = formData.problemId;
        this.relevantTableId=formData.relevantTableId;
        this.relevantTableName=formData.relevantTableName;
        this.edit = true;
        this.$nextTick(() => {
          this.$refs.accept.show();
        });
      },
    /**填报关闭*/
      onClose(){
        console.log('关闭填报');
      },
      /**填报提交*/
      submitForm(){
        console.log('调用子页面方法提交');
      },
      //专项台账攥取
      relationLedger(relationId){
        this.relationId = relationId
        this.index++;
        this.$nextTick(()=> {
          this.$refs.relation.show();
        });
      }
    }
  };
</script>
<style rel="stylesheet/scss" lang="scss">
</style>







