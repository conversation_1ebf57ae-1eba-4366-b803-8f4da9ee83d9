<!--新增台账-->
<template>
  <div  class="scope">
    <el-dialog  :visible.sync="visible"   :modal-append-to-body="false" append-to-body  :title="title" width="90%">
      <el-form ref="elForm" :model="formData" :rules="rules" size="medium" label-width="138px">

        <el-col :span="12">
          <el-form-item label="问题编号" prop="problemNum">
            <el-input v-model="formData.problemNum" placeholder="问题编号" clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="发现问题业务类型" prop="problemTypeEnumId">
            <el-select
              v-model="formData.problemTypeEnumId"
              placeholder="请选择发现问题业务类型"
              clearable
              :style="{width: '100%'}"
              value="formData.problemTypeEnumId"
            >
              <el-option
                v-for="(item, index) in dict.type.business_type"
                :key="index"
                :label="item.label"
                :value="item.value"
                :disabled="item.disabled"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="审计发现问题" prop="reportRequire">
            <el-input v-model="formData.problemAudit" type="textarea" placeholder="审计发现问题"
                      :autosize="{minRows: 4, maxRows: 4}"></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item label="具体问题描述" prop="reportRequire">
            <el-input v-model="formData.problemDescription" type="textarea" placeholder="具体问题描述"
                      :autosize="{minRows: 4, maxRows: 4}"></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-form-item label="是否上报告" prop="reportFlag">
            <el-radio-group v-model="formData.reportFlag">
              <el-radio label="1">是</el-radio>
              <el-radio label="0">否</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>

        <el-col :span="8" v-if="projectUpdateMethod === '0'">
          <el-form-item label="是否追责" prop="ifDuty">
            <el-radio-group v-model="formData.ifDuty">
              <el-radio label="1">是</el-radio>
              <el-radio label="0">否</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-form-item label="是否移交纪检" prop="transferFlag">
            <el-radio-group v-model="formData.transferFlag">
              <el-radio label="1">是</el-radio>
              <el-radio label="0">否</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-form>
      <div slot="footer">
        <el-button size="mini" type="primary" @click="toSaveData" plain>保存</el-button>
        <el-button size="mini" @click="close">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  saveLedgersInfo
  ,queryLedgersInfoById
} from '@/api/special-report'
export default {
  name: "addLedger"
  ,props: {
    projectId:{
      type:String,
      default:''
    },
    ledgerId:{
      type:String,
      default:''
    }
    ,ledgerType:{
      type:String,
      default: ''
    }
    ,title:{
      type:String,
      default: '新增台账'
    },
    projectUpdateMethod:{
      type:String,
      default: ''
    },
  },
  dicts: ['business_type'],
  data() {
    return {
      visible:false,//弹框
      formData:{
        problemNum:undefined//问题编号
        ,problemTypeEnumName:undefined
        ,problemAudit:undefined
        ,problemDescription:undefined
        ,reportFlag:undefined
        ,ifDuty:undefined
        ,transferFlag:undefined
      }
    }
  }
  ,created(){
    if(this.ledgerType == 'edit'){
      this.title = "编辑台账";
      //调用查询方法
      this.queryLedgersInfoById();
    }
  }
  ,methods:{
    // 显示弹框
    show() {
      this.visible = true;
    },
    //关闭弹窗
    close(){
      this.visible = false;
      this.$emit("close");
    },

    //编辑台账，根据主键查询台账信息
    queryLedgersInfoById(){
      queryLedgersInfoById(this.ledgerId).then((res)=>{
        this.formData = res.data;
        this.$forceUpdate();
      })
    },

    //校验  保存
    toSaveData(){
      if (!this.formData.problemTypeEnumId) {
        this.$modal.msgError("【发现问题业务类型】不能为空！");
        return false;
      } else if (!this.formData.problemAudit) {
        this.$modal.msgError("【审计发现问题】不能为空！");
        return false;
      } else if (!this.formData.problemDescription) {
        this.$modal.msgError("【具体问题描述】不能为空！");
        return false;
      } else if (!this.formData.reportFlag) {
        this.$modal.msgError("请选择【是否上报告】！");
        return false;
      } else if (this.projectUpdateMethod === '0' && !this.formData.ifDuty) {
        this.$modal.msgError("请选择【是否追责】！");
        return false;
      } else if (!this.formData.transferFlag) {
        this.$modal.msgError("请选择【是否移交纪检】！");
        return false;
      }
      //后端校验
      saveLedgersInfo({...this.formData,...{projectId:this.projectId}}).then(
        response => {
          if (response.code == 200) {
            this.$modal.msgSuccess("保存成功");
            this.$emit('close');
            this.close();
          }
        }
      )
    },
  }
}
</script>

<style lang="scss">
.regular{
  .el-dialog__body{
    padding-top:20px  !important;
    background: #fff !important;
  }
}
</style>
