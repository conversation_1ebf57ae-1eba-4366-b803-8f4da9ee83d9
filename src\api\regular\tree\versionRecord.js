import request from '@/utils/request'


//查询默认版本的上报单位
export function getDefaultProvVersion(){
  return request({
    url:'/colligate/violRegular/version/getDefaultProvVersion',
    method: 'post'
  })
}

//保存上报单位版本
export function saveProvVersion(data){
  return request({
    url:'/colligate/violRegular/version/saveProvVersion',
    method: 'post',
    data: data
  })
}

//查询上报单位版本记录
export function getProvVersionList(data){
  return request({
    url:'/colligate/violRegular/version/getProvVersionList',
    method: 'post',
    data: data
  })
}

//根据版本主表ID修改最后使用时间
export function updateLastUseTime(data){
  return request({
    url:'/colligate/violRegular/version/updateLastUseTime',
    method: 'post',
    data: data
  })
}

//设置默认版本
export function setDefaultProvVersion(data){
  return request({
    url:'/colligate/violRegular/version/setDefaultProvVersion',
    method: 'post',
    data: data
  })
}

//查询当前使用的版本code
export function getRegularProvVersionId(data){
  return request({
    url:'/colligate/violRegular/report/getRegularProvVersionId',
    method: 'post',
    data: data
  })
}

//保存使用的哪个版本
export function updateRegularProvVersionId(data){
  return request({
    url:'/colligate/violRegular/report/updateRegularProvVersionId',
    method: 'post',
    data: data
  })
}

//删除版本
export function deleteProvVersionById(data){
  return request({
    url:'/colligate/violRegular/version/deleteProvVersionById',
    method: 'post',
    data: data
  })
}
