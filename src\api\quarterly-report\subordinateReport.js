import request from '@/utils/request'



//初始化上报单位 查询
export function queryReportConfirmList(data){
  return request({
    url: '/quarter/area/queryReportConfirmList',
    method: 'post',
    data:data
  })
}

//初始化上报单位 发起上报--发起前校验
export function reportLowerUnitValidateInfo(data) {
  return request({
    url: '/quarter/validate/reportLowerUnitValidateInfo',
    method: 'post',
    data:data
  })
}

//发起上报
export function saveAndStartProcess(data) {
  return request({
    url: '/quarter/report/area/flow/saveAndStartProcess',
    method: 'post',
    data:data
  })
}








// 刷新上报数据
export function refreshReportData(reportProvId) {
  return request({
    url: '/quarter/area/refreshLowerUnitInfo/'+reportProvId,
    method: 'post'
  })
}

// 下级单位上报情况  地市未选择列表   进行中列表   已完成列表
export function areaSelectList(data) {
  return request({
    url: '/areaSelectList',
    method: 'post',
    data:data
  })
}

// 下级单位上报情况  上报地市确认
export function areaSelectSave(data) {
  return request({
    url: '/areaSelectSave',
    method: 'post',
    data:data
  })
}



//下级单位上报情况（进行中） 列表查询
export function selectReportDetailList(data){
  return request({
    url: '/colligate/violRegular/report/selectReportDetailList',
    method: 'post',
    data:data
  })
}



//下级单位上报情况（进行中） 催办
export function toUrgeProcess(data){
  return request({
    url: '/quarter/area/toUrgeProcess',
    method: 'post',
    data:data
  })
}

//下级单位上报情况（已完成） 列表查询
export function queryQuarterReportProvSummary(data,param){
  return request({
    url: '/quarterReport/queryQuarterReportProvSummary',
    method: 'post'
    ,data
    ,params:param
  })
}

//保存上报单位
export function saveQuarterAreaUnitInfo(data){
  return request({
    url: '/quarter/area/saveQuarterAreaUnitInfo',
    method: 'post'
    ,data:data
  })
}

//删除上报单位信息
export function delReportUnitById(reportUnitId){
  return request({
    url: '/quarter/area/delReportUnitById/'+reportUnitId,
    method: 'post'
  })
}


//省分查看下级单位上报情况
export function queryProvList(data){
  return request({
    url: '/quarterReport/queryProvList',
    method: 'post',
    data:data
  })
}


//下级单位上报情况-更多
export function getReportUnitUserData(data){
  return request({
    url: '/quarterReport/getReportUnitUserData',
    method: 'post',
    data:data
  })
}

