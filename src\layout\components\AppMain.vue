
<template>
  <section class="app-main">


    <transition name="fade-transform" mode="out-in">
      <keep-alive :include="cachedViews">
        <router-view v-if="!$route.meta.noCache"></router-view>
      </keep-alive>
    </transition>

    <transition name="fade-transform" mode="out-in">
    <router-view v-if="$route.meta.noCache"></router-view>
  </transition>
    <div class="bottom">
      <span>热线：010-67882255转3转0</span>
    </div>
  </section>
</template>

<script>
export default {
  name: 'AppMain',
  computed: {
    cachedViews() {
      console.log(this.$store.state.tagsView.cachedViews)
      return this.$store.state.tagsView.cachedViews
    },
    key() {
      return this.$route.path
    }
  }
}
</script>

<style lang="scss" scoped>
.app-main {
  /* 50= navbar  50  */
  min-height: calc(100vh - 50px);
  width: 100%;
  position: relative;
  overflow: hidden;
  padding-bottom:30px;
  .bottom{
    position: fixed;
    bottom:-0;
    z-index: 999;
    background: #e3e3e3;
    height: 30px;
    width: 100%;
    line-height: 32px;
    color: #555555;
    text-align: center;
    font-size: 13px;
  }
}

.fixed-header+.app-main {
  padding-top: 68px;
}

.hasTagsView {
  .app-main {
    /* 84 = navbar + tags-view = 50 + 34 */
    min-height: calc(100vh - 30px);
  }

  .fixed-header+.app-main {
    padding-top: 99px;
  }
}
</style>

<style lang="scss">
// fix css style bug in open el-dialog
.el-popup-parent--hidden {
  .fixed-header {
    padding-right: 17px;
  }
}
</style>
