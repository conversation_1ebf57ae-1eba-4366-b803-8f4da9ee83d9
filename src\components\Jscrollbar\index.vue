<template>
      <div class="j-scrollbar" :style="{height:height?height:'70vh'}">
        <el-scrollbar style="height:100%">
        <slot></slot>
        </el-scrollbar>
      </div>
</template>

<script>
  export default {
    name: "Jscrollbar",
    props: {
      height: {
        type: String,
        default: 0
      },
    }
  }
</script>

<style rel="stylesheet/scss" lang="scss">
  .j-scrollbar{
    .el-scrollbar__wrap {
      overflow-x: hidden !important;
    }
    .el-scrollbar__view{
      height: 90%;
    }
  }
</style>
