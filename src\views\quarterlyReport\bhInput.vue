
<!-- 季度报告--驳回原因 -->

<template>
    <div class="wai-container" style="background-color: #fff">
      <div class="layui-row width height">
        <div class="width height">
          <div class="common-wai-box" style="height: 100%">
            <div class="common-in-box" style="height: 180px; min-height: 100%">

              <div class="common-in-box-content">

                <div class="top-search">
                  <div class="layui-col-md12 layui-col-sm12 layui-col-lg12">
                    <div class="layui-form" style="align-items: baseline;">
                      <div class="layui-form-left">
                        <span class="must-icon">*</span>驳回原因
                      </div>

                      <el-input
                      rows="5"
                      type="textarea"
                        placeholder="请选择"
                        v-model="formData.rejectReason"
                      />
                    </div>
                  </div>
                </div>


              </div>
            </div>
            <div class="bottom-btn">
              <div class="left-empty" />
              <el-button size="mini" @click="cancel()">取消</el-button>
              <el-button size="mini" type="primary" @click="submitForm">提交</el-button>
            </div>
          </div>
        </div>
      </div>


    </div>
  </template>
  <script>
  import {
    rejectQuarterReportInfo
  } from '@/api/quarterly-report/index';
  export default {
    name: "addGroup",
    components: {  },
    props: {
      closeBtn: {
        type: Function,
        default: null,
      },
      // 编辑内容
      rowData:{
        type: Object,
        default:()=>{}
      }
    },
    dicts: [],
    data() {
      return {
        formData: {
            rejectReason:""
        },
      };
    },
    created() {
      this.loadTips();
    },
    methods: {
      //提交
      submitForm() {
        if(!this.formData.rejectReason){
            this.$message.info('请输入驳回原因！')
            return  false
        }
        this.rowData.rejectReason=this.formData.rejectReason;
        rejectQuarterReportInfo(this.rowData).then((res)=>{
            if(res.code != 200){
              this.$message.warning(res.msg);
            }else{
              this.$message.success('操作成功')
              this.cancel()
            }
        })
      },
      //取消
      cancel(){
        this.closeBtn()
      }

    },
  };
  </script>
  </script>
  <style lang="scss" scoped>
  @import "~@/assets/styles/quarterly-report/index.css";
  </style>
