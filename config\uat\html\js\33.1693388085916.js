(window["webpackJsonp"] = window["webpackJsonp"] || []).push([[33],{

/***/ "./src/api/system/dict/type.js":
/*!*************************************!*\
  !*** ./src/api/system/dict/type.js ***!
  \*************************************/
/*! exports provided: listType, getType, addType, updateType, delType, refreshCache, optionselect */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "listType", function() { return listType; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "getType", function() { return getType; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "addType", function() { return addType; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "updateType", function() { return updateType; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "delType", function() { return delType; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "refreshCache", function() { return refreshCache; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "optionselect", function() { return optionselect; });
/* harmony import */ var _utils_request__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/utils/request */ "./src/utils/request.js");


// 查询字典类型列表
function listType(query) {
  return Object(_utils_request__WEBPACK_IMPORTED_MODULE_0__["default"])({
    url: '/system/dict/type/list',
    method: 'get',
    params: query
  });
}

// 查询字典类型详细
function getType(dictId) {
  return Object(_utils_request__WEBPACK_IMPORTED_MODULE_0__["default"])({
    url: '/system/dict/type/' + dictId,
    method: 'get'
  });
}

// 新增字典类型
function addType(data) {
  return Object(_utils_request__WEBPACK_IMPORTED_MODULE_0__["default"])({
    url: '/system/dict/type',
    method: 'post',
    data: data
  });
}

// 修改字典类型
function updateType(data) {
  return Object(_utils_request__WEBPACK_IMPORTED_MODULE_0__["default"])({
    url: '/system/dict/type',
    method: 'put',
    data: data
  });
}

// 删除字典类型
function delType(dictId) {
  return Object(_utils_request__WEBPACK_IMPORTED_MODULE_0__["default"])({
    url: '/system/dict/type/' + dictId,
    method: 'delete'
  });
}

// 刷新字典缓存
function refreshCache() {
  return Object(_utils_request__WEBPACK_IMPORTED_MODULE_0__["default"])({
    url: '/system/dict/type/refreshCache',
    method: 'delete'
  });
}

// 获取字典选择框列表
function optionselect() {
  return Object(_utils_request__WEBPACK_IMPORTED_MODULE_0__["default"])({
    url: '/system/dict/type/optionselect',
    method: 'get'
  });
}

/***/ })

}]);
//# sourceMappingURL=33.1693388085916.js.map