import request from '@/utils/request'

// 接口
export function getFileType(data) {
  return request({
    url: '/sys/preview/getFileType',
    method: 'post',
    data: data
  })
}

// 附件预览
export function openFileView(fileId, fileName) {
  let str = fileName;
  getFileType({name:str}).then(res => {
    if(res.data){
      var index = str.lastIndexOf(".");
      str = str.substring(index + 1, str.length);
      const url = `/system/webOffice?fileUrl=${fileId}&fileName=${encodeURIComponent(fileName)}&fileType=${str}`; // 根据实际情况设置fileType
      window.open(url, '_blank');
    }else{

    }
  }).catch(error => {

  });

}



