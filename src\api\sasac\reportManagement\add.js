import request from '@/utils/request'


export function violDailyList(query) {
  let pageParams = {pageNum: query.pageNum, pageSize: query.pageSize};
  return request({
    url: '/colligate/violQuery/violDailyList',
    method: 'post',
    data: query,
    params: pageParams
  })
}

export function saveSasacReportRecord(data) {
  return request({
    url: '/colligate/violSasacReportManagement/saveSasacReportRecord',
    method: 'post',
    data: data
  });
}






