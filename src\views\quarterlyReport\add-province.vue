<!-- 季度报告--新增和编辑页面--省分 -->

<template>
  <div class="wai-container" style="background-color: #fff">
    <div class="layui-row width height">
      <div class="width height">
        <div class="common-wai-box" style="height: 100%">
          <div class="common-in-box" style="height: auto; min-height: 100%">
            <div class="common-in-box-header">
              <div class="common-in-box-header-line"></div>
              <div class="common-in-box-header-text">基本信息</div>
              <div class="common-in-box-header-right"  v-show="editType=='edit'">
                <el-button class="" plain type="primary" v-show="showRestroeBtn" icon="el-icon-refresh-right" size="mini" @click="restoreInitDataClick">还原默认数据</el-button>
              </div>
            </div>

            <div class="common-in-box-content">
              <div class="top-search">
                <el-col :span="8" class="height">
                  <div class="layui-form">
                    <div class="layui-form-left">
                      <span class="must-icon">*</span>上报年度
                    </div>
                    <el-date-picker
                      format="yyyy"
                      value-format="yyyy"
                      :clearable="clearable"
                      v-model="infoData.reportYear"
                      @change="reportYearChange"
                      type="year"
                      placeholder="请选择"
                      v-if="editType=='add'"
                    >
                    </el-date-picker>
                    <div class="layui-form-value" v-if="editType=='edit'">
                      {{ infoData.reportYear }}
                    </div>

                  </div>
                </el-col>

                <el-col :span="8" class="height">
                  <div class="layui-form">
                    <div class="layui-form-left">
                      <span class="must-icon">*</span>上报季度
                    </div>

                    <el-select v-if="editType=='add'"
                      @change="reportQuarterChange"
                      v-model="infoData.reportQuarter"
                      placeholder="请选择"
                    >
                      <el-option
                        v-for="(item, index) in reportQuarterList"
                        :key="index"
                        :label="item.dictLabel"
                        :value="item.dictValue"
                      ></el-option>
                      <!-- <el-option v-for="(item, index) in dict.type.corresponding_adverse_effect" :key="index" :label="item.label" :value="item.value"></el-option> -->
                    </el-select>
                    <div class="layui-form-value" v-if="editType=='edit'">
                      {{ infoData.reportQuarter | fromatComonDict(reportQuarterList) }}
                    </div>
                  </div>
                </el-col>

                <el-col :span="8" class="height">
                  <div class="layui-form">
                    <div class="layui-form-left">
                      <span class="must-icon">*</span>上报截止日期
                    </div>
                    <el-date-picker
                      format="yyyy-MM-dd"
                      value-format="yyyy-MM-dd"
                      v-model="infoData.reportCloseTime"
                      @change="reportYearChange"
                      type="datetime"
                      placeholder=""
                      readonly
                    >
                    </el-date-picker>
                  </div>
                </el-col>
              </div>

              <div class="top-search">
                <div class="layui-col-md12 layui-col-sm12 layui-col-lg12">
                  <div class="layui-form">
                    <div class="layui-form-left">
                      <span class="must-icon">*</span>上报标题
                    </div>

                    <el-input
                      readonly
                      type="text"
                      placeholder=""
                      v-model="infoData.reportTitle"
                    />
                  </div>
                </div>
              </div>

              <div class="top-search">
                <div class="layui-col-md12 layui-col-sm12 layui-col-lg12">
                  <div class="layui-form">
                    <div class="layui-form-left">上报要求</div>
                    <div class="layui-form-value" id="reportRequire">
                      {{ infoData.reportRequire }}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div class="common-in-box-header" style="margin-top: 10px">
              <div class="common-in-box-header-line"></div>
              <div class="common-in-box-header-text">附件列表</div>
              <div class="flex-1"></div>

              <el-upload
                ref="upload"
                class="upload-demo"
                :action="files.actionUrl"
                :headers="files.myHeaders"
                :on-success="handleFileSuccess"
                :data="{
                  busiTableId: files.busiTableId,
                  busiTableName: files.busiTableName,
                }"
                :show-file-list="false"
              >
                <el-button
                  size="small"
                  type="primary"
                  icon="el-icon-upload2"
                  v-show="infoData.reportYear && infoData.reportQuarter"
                  >附件上传
                </el-button>
              </el-upload>
            </div>

            <div class="tables tables_1">
              <el-table
                :data="filesData"
                border
                v-loading="tableLoading"
                style="width: 100%"
              >
                <el-table-column
                  label="序号"
                  type="index"
                  min-width="5%"
                  align="center"
                />
                <el-table-column label="文件名" prop="fileName" min-width="50%">
                  <template slot-scope="scope">
                    <div
                      style="text-align: left"
                      class="overflowHidden-1"
                      :title="scope.row.fileName"
                    >
                      {{ scope.row.fileName || "" }}
                    </div>
                  </template>
                </el-table-column>
                <el-table-column
                  label="上传人"
                  prop="createUserName"
                  min-width="10%"
                  align="center"
                />
                <el-table-column
                  label="上传时间"
                  prop="createTime"
                  min-width="20%"
                  align="center"
                />

                <el-table-column
                  label="操作"
                  fixed="right"
                  min-width="15%"
                  align="center"
                  class-name="small-padding fixed-width"
                >
                  <template slot-scope="scope">
                    <el-button
                      size="mini"
                      type="text"
                      title="下载"
                      icon="el-icon-bottom"
                      @click="fileDownload(scope.row)"
                    ></el-button>
                    <el-button
                      size="mini"
                      type="text"
                      title="删除"
                      icon="el-icon-delete"
                      @click="delFile(scope.row)"
                    ></el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>

            <div class="common-in-box-header" style="margin-top: 10px;">
                <div class="common-in-box-header-line"></div>
                <div class="common-in-box-header-text">本单位汇总信息</div>
              <div class="common-in-box-header-right" v-if="editUnitType=='province'">
                  <el-button plain type="primary" v-show="infoData.reportYear && infoData.reportQuarter" icon="el-icon-refresh-left" size="mini" @click="refreshReport">刷新上报数据</el-button>
                  <el-button plain type="primary" v-show="infoData.reportYear && infoData.reportQuarter" icon="el-icon-edit" size="mini" @click="subordinateReport">下级单位数据上报</el-button>
                </div>
            </div>

            <div class="common-in-box-header" style="margin-top: 10px;border: 0px;padding-left: 10px;">
                <div class="common-in-box-header-text">工作部署情况</div>
            </div>
            <div class="common-in-box-content">
              <div class="top-search">
                <el-col :span="8" class="height">
                      <div class="layui-form">
                          <div class="layui-form-left  width-label-2"> <span
                                  class="must-icon">*</span>本季度召开领导小组会议（次）</div>
                        <el-input type="text" @change="quarterNumber('quarterTeamMeetingTime')"  v-limit-input-number v-model="infoData.quarterTeamMeetingTime" placeholder="请输入" class="num-input" />
                      </div>
                    </el-col>
                    <el-col :span="8" class="height">
                      <div class="layui-form">
                          <div class="layui-form-left  width-label-2"> <span
                                  class="must-icon">*</span>本季度召开领导小组办公室会议（次）</div>
                          <el-input type="text"  @change="quarterNumber('quarterTeamOfficeMeetingTime')"  v-limit-input-number v-model="infoData.quarterTeamOfficeMeetingTime" placeholder="请输入" class="num-input" />
                      </div>
                    </el-col>
                    <el-col :span="8" class="height">
                      <div class="layui-form">
                          <div class="layui-form-left width-label-2"> <span
                                  class="must-icon">*</span>本季度召开专题会议（次）</div>
                          <el-input type="text"  @change="quarterNumber('quarterSpecialMeetingTime')" v-limit-input-number v-model="infoData.quarterSpecialMeetingTime" placeholder="请输入" class="num-input" />
                      </div>
                    </el-col>
              </div>
              <div class="top-search">
                <el-col :span="8" class="height">
                      <div class="layui-form">
                          <div class="layui-form-left  width-label-2"> <span
                                  class="must-icon">*</span>当年累计召开领导小组会议（次）</div>
                          <el-input type="text"  v-limit-input-number v-model="infoData.totalLeaderTeamMeetingTime" placeholder="请输入" class="num-input"
                                    @change="compareBfQuarter('totalLeaderTeamMeetingTime','当年累计召开领导小组会议（次）')" />
                      </div>
                  </el-col>
                  <el-col :span="8" class="height">
                      <div class="layui-form">
                          <div class="layui-form-left  width-label-2"> <span
                                  class="must-icon">*</span>当年累计召开领导小组办公室会议（次）</div>
                          <el-input type="text"   v-limit-input-number v-model="infoData.totalTeamOfficeMeetingTime" placeholder="请输入" class="num-input"
                                    @change="compareBfQuarter('totalTeamOfficeMeetingTime','当年累计召开领导小组办公室会议（次）')"/>
                      </div>
                  </el-col>
                  <el-col :span="8" class="height">
                      <div class="layui-form">
                          <div class="layui-form-left  width-label-2"> <span
                                  class="must-icon">*</span>当年累计召开专题会议（次）</div>
                          <el-input type="text"   v-limit-input-number v-model="infoData.totalSpecialMeetingTime" placeholder="请输入" class="num-input"
                                    @change="compareBfQuarter('totalSpecialMeetingTime','当年累计召开专题会议（次）')"/>
                      </div>
                  </el-col>
              </div>
            </div>
            <div class="common-in-box-header" style="margin-top:10px;border: 0px;padding-left: 10px;">
                <div class="common-in-box-header-text">体系建设情况</div>
            </div>
            <div class="common-in-box-content">
                <div class="top-search">
                  <el-col :span="8" class="height">
                    <div class="layui-form">
                        <div class="layui-form-left  width-label-2"> <span
                                class="must-icon">*</span>2019年至今累计印发责任追究相关制度数量（项）</div>
                        <el-input type="text"  v-limit-input-number v-model="infoData.totalAccountabilitySystemNumber" placeholder="请输入" class="num-input"
                                  @change="compareBfQuarter('totalAccountabilitySystemNumber','2019年至今累计印发责任追究相关制度数量（项）')"/>
                    </div>
                  </el-col>
                  <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left  width-label-2"> <span
                              class="must-icon">*</span>2019年至今累计专职人员数量 （人）</div>
                      <el-input type="text"  v-limit-input-number v-model="infoData.totalProfessionalNumber" placeholder="请输入" class="num-input"
                                @change="compareBfQuarter('totalProfessionalNumber','2019年至今累计专职人员数量 （人）')"/>
                    </div>
                  </el-col>
                </div>
                <div class="top-search">
                  <el-col :span="8" class="height">
                     <div class="layui-form">
                       <div class="layui-form-left  width-label-2"> <span
                              class="must-icon">*</span>当年累计新增配套制度（项）</div>
                       <el-input type="text"  v-limit-input-number v-model="infoData.totalNewSupportingSystem" placeholder="请输入" class="num-input"
                                 @change="compareBfQuarter('totalNewSupportingSystem','当年累计新增配套制度（项）')"/>
                     </div>
                  </el-col>
                  <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left  width-label-2"> <span
                            class="must-icon">*</span>当年累计新增工作机制（项）</div>
                      <el-input type="text"   v-limit-input-number v-model="infoData.totalNewWorkSystem" placeholder="请输入" class="num-input"
                                @change="compareBfQuarter('totalNewWorkSystem','当年累计新增工作机制（项）')"/>
                    </div>
                  </el-col>
                  <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left  width-label-2"> <span
                              class="must-icon">*</span>当年累计新增专职人员数量（人）</div>
                      <el-input type="text"   v-limit-input-number v-model="infoData.totalNewSpecialPersonNumber" placeholder="请输入" class="num-input"
                                @change="compareBfQuarter('totalNewSpecialPersonNumber','当年累计新增专职人员数量（人）')"/>
                    </div>
                  </el-col>
                </div>
                <div class="top-search">
                  <el-col :span="24" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left  width-label-2"> 新增配套制度名称</div>
                      <el-input type="text" :disabled="infoData.totalNewSupportingSystem==='0'||infoData.totalNewSupportingSystem===0" v-model="infoData.newSupportingName" placeholder="请输入" />
                    </div>
                  </el-col>
                </div>
                <div class="top-search">
                  <el-col :span="24" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left  width-label-2"> 新增工作机制名称</div>
                      <el-input type="text" :disabled="infoData.totalNewWorkSystem==='0'||infoData.totalNewWorkSystem===0" v-model="infoData.newWorkName" placeholder="请输入" />
                    </div>
                  </el-col>
                </div>
                <div class="top-search">
                  <el-col :span="12" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left  width-label-2">
                        <span class="must-icon">*</span>主要部门</div>
                      <el-input type="text" v-model="infoData.groupMainDept" placeholder="请输入" />
                    </div>
                  </el-col>
                </div>
                <div class="common-in-box-header" style="margin-top: 10px;border: 0px;padding-left: 10px;">
                    <div class="common-in-box-header-text">违规问题线索查办情况</div>
                </div>
                  <div class="common-in-box-content">
                    <div class="top-search">
                      <el-col :span="8" class="height">
                        <div class="layui-form">
                          <div class="layui-form-left  width-label-2">
                            <span class="must-icon">*</span>本季度新受理问题线索数量（件）</div>
                          <el-input type="text" @change="quarterNumber('quarterNewProblemNumber')"  v-limit-input-number v-model="infoData.quarterNewProblemNumber" placeholder="请输入" class="num-input" />
                        </div>
                      </el-col>
                      <el-col :span="8" class="height">
                        <div class="layui-form">
                          <div class="layui-form-left  width-label-2"> <span
                                  class="must-icon">*</span>本季度涉及资产损失（万元）</div>
                          <el-input type="text"    v-limit-input-money v-model="infoData.lossAmount" @blur="dottedClear" placeholder="请输入" />
                        </div>
                      </el-col>
                      <el-col :span="8" class="height">
                        <div class="layui-form">
                          <div class="layui-form-left  width-label-2"> <span
                                  class="must-icon">*</span>本季度涉及资产损失风险（万元）</div>
                          <el-input type="text"  v-limit-input-money v-model="infoData.lossRisk" @blur="dottedClear" placeholder="请输入" />
                        </div>
                      </el-col>
                    </div>
                    <div class="top-search">
                      <el-col :span="8" class="height">
                        <div class="layui-form">
                          <div class="layui-form-left  width-label-2"> <span
                                  class="must-icon">*</span>当年累计受理问题线索数量（件）</div>
                          <el-input type="text"   v-limit-input-number v-model="infoData.totalProblemSourceNumber" placeholder="请输入" class="num-input"
                                    @change="compareBfQuarter('totalProblemSourceNumber','当年累计受理问题线索数量（件）')"/>
                        </div>
                      </el-col>
                      <el-col :span="8" class="height">
                        <div class="layui-form">
                          <div class="layui-form-left  width-label-2"> <span
                                  class="must-icon">*</span>上年结转问题线索数量（件）</div>
                          <el-input type="text"   v-limit-input-number v-model="infoData.lastYearProblemSourceNumber" placeholder="请输入" class="num-input" />
                        </div>
                       </el-col>
                    </div>
                    <div class="top-search">
                      <el-col :span="8" class="height">
                        <div class="layui-form">
                          <div class="layui-form-left  width-label-2"> <span
                                  class="must-icon">*</span>其中:未启动核查（件）</div>
                          <el-input type="text"   v-limit-input-number v-model="infoData.checkNoStartedNumber" placeholder="请输入" class="num-input" />
                        </div>
                      </el-col>
                      <el-col :span="8" class="height">
                        <div class="layui-form">
                          <div class="layui-form-left  width-label-2"> <span
                                  class="must-icon">*</span>其中: 正在核查（件）</div>
                          <el-input type="text"   v-limit-input-number v-model="infoData.checkInProcessNumber" placeholder="请输入" class="num-input" />
                        </div>
                      </el-col>
                      <el-col :span="8" class="height">
                        <div class="layui-form">
                          <div class="layui-form-left  width-label-2"> <span
                                  class="must-icon">*</span>其中: 完成核查（件）</div>
                          <el-input type="text"   v-limit-input-number @change="checkCompletedNumber()" v-model="infoData.checkCompletedNumber" placeholder="请输入" class="num-input" />
                        </div>
                      </el-col>
                    </div>
                  </div>
                  <div class="common-in-box-header" style="margin-top: 10px;border: 0px;padding-left: 10px;">
                      <div class="common-in-box-header-text">追责整改工作成效</div>
                  </div>
                  <div class="common-in-box-content">
                    <div class="top-search new-change-height-bottom">
                      <el-col :span="8" class="height">
                        <div class="layui-form">
                            <div class="layui-form-left  width-label-2"> <span
                                    class="must-icon">*</span>当年累计完成追责问题数量（件）</div>
                            <el-input type="text"   v-limit-input-number v-model="infoData.totalCompletedProblemNumber" placeholder="请输入" class="num-input"
                                      @change="compareBfQuarter('totalCompletedProblemNumber','当年累计完成追责问题数量（件）')"/>
                        </div>
                      </el-col>


                    </div>
                    <div class="top-search">
                      <el-col :span="8" class="height">
                        <div class="layui-form">
                          <div class="layui-form-left  width-label-2"> <span
                                  class="must-icon">*</span>当年累计追责总人数（人）</div>
                          <el-input type="text"   v-limit-input-number v-model="infoData.totalAccountabilityPersonNumber" placeholder="请输入" class="num-input"
                                    @change="compareBfQuarter('totalAccountabilityPersonNumber','当年累计追责总人数（人）')"/>
                        </div>
                      </el-col>
                      <el-col :span="8" class="height" v-show="infoData.orgGrade=='G'">
                        <div class="layui-form">
                          <div class="layui-form-left  width-label-2"> <span
                            class="must-icon">*</span>其中: 中央企业负责人（人）</div>
                          <el-input type="text"   v-limit-input-number v-model="infoData.enterpriseManagementNumber" placeholder="请输入" class="num-input" />
                        </div>
                      </el-col>

                      <el-col :span="8" class="height" v-show="infoData.orgGrade=='G'">
                        <div class="layui-form">
                          <div class="layui-form-left  width-label-2"> <span
                            class="must-icon">*</span>其中: 集团管理干部（人）</div>
                          <el-input type="text"   v-limit-input-number v-model="infoData.groupManagementNumber" placeholder="请输入" class="num-input" />
                        </div>
                      </el-col>


                    </div>
                    <div class="top-search new-change-height-bottom" >
                      <el-col :span="8" class="height">
                        <div class="layui-form">
                          <div class="layui-form-left  width-label-2"> <span
                            class="must-icon">*</span>其中: 子企业管理干部（人）</div>
                          <el-input type="text"   v-limit-input-number v-model="infoData.subManagementNumber" placeholder="请输入" class="num-input" />
                        </div>
                      </el-col>

                    </div>
                      <div class="top-search" >

                      <el-col :span="8" class="height">
                        <div class="layui-form">
                          <div class="layui-form-left  width-label-2"> <span
                            class="must-icon">*</span>当年累计追责总人次（人次）</div>
                          <el-input type="text"  v-limit-input-number v-model="infoData.totalAccountabilityPersonTime" placeholder="请输入" class="num-input"
                                    @change="compareBfQuarter('totalAccountabilityPersonTime','当年累计追责总人次（人次）')"/>
                        </div>
                      </el-col>

                      <el-col :span="8" class="height">
                        <div class="layui-form">
                          <div class="layui-form-left  width-label-2"> <span
                                  class="must-icon">*</span>其中: 组织处理 （人次）</div>
                          <el-input type="text"   v-limit-input-number v-model="infoData.orgHandleTime"  placeholder="请输入" class="num-input" />
                        </div>
                      </el-col>
                      <el-col :span="8" class="height">
                        <div class="layui-form">
                          <div class="layui-form-left  width-label-2"> <span
                                  class="must-icon">*</span>其中: 扣减薪酬 （人次）</div>
                          <el-input type="text"   v-limit-input-number v-model="infoData.deductionSalaryTime" placeholder="请输入" class="num-input" />
                        </div>
                       </el-col>

                    </div>

                    <div class="top-search">

                      <el-col :span="8" class="height">
                        <div class="layui-form">
                          <div class="layui-form-left  width-label-2"> <span
                            class="must-icon">*</span>其中: 党纪处分（人次）</div>
                          <el-input type="text"   v-limit-input-number v-model="infoData.partyPunishmentTime" placeholder="请输入" class="num-input" />
                        </div>
                      </el-col>

                      <el-col :span="8" class="height" v-show="infoData.orgGrade=='G'">
                        <div class="layui-form">
                          <div class="layui-form-left  width-label-2"> <span
                            class="must-icon">*</span>其中：政务处分 （人次）</div>
                          <el-input type="text"   v-limit-input-number v-model="infoData.governmentPunishmentTime" placeholder="请输入" class="num-input" />
                        </div>
                      </el-col>
                      <el-col :span="8" class="height" v-show="infoData.orgGrade=='G'">
                        <div class="layui-form">
                          <div class="layui-form-left  width-label-2"> <span
                            class="must-icon">*</span>其中：禁入限制（人次）</div>
                          <el-input type="text"   v-limit-input-number v-model="infoData.prohibitTime" placeholder="请输入" class="num-input" />
                        </div>
                      </el-col>
                    </div>

                    <div class="top-search new-change-height-bottom">
                      <el-col :span="8" class="height">
                        <div class="layui-form">
                          <div class="layui-form-left  width-label-2"> <span
                            class="must-icon">*</span>其中:移送监察机关或司法机关 （人次）</div>
                          <el-input type="text"   v-limit-input-number v-model="infoData.transferAuthorityTime" placeholder="请输入" class="num-input" />
                        </div>
                      </el-col>
                      <el-col :span="8" class="height">
                        <div class="layui-form">
                          <div class="layui-form-left  width-label-2"> <span
                            class="must-icon">*</span>其中: 其他 （人次）</div>
                          <el-input type="text"  v-limit-input-number v-model="infoData.processingOtherItem" placeholder="请输入" class="num-input" />
                        </div>
                      </el-col>

                    </div>

                    <div class="top-search new-change-height-bottom" >

                      <el-col :span="8" class="height">
                        <div class="layui-form">
                          <div class="layui-form-left  width-label-2"> <span
                                  class="must-icon">*</span>当年累计扣减薪酬金额（万元）</div>
                          <el-input type="text"  v-limit-input-money v-model="infoData.totalDeductionSalary"  @blur="dottedClear" placeholder="请输入"
                                    @change="compareBfQuarter('totalDeductionSalary','当年累计扣减薪酬金额（万元）')"/>
                        </div>
                      </el-col>
                    </div>
                    <div class="top-search new-change-height-bottom">
                        <el-col :span="8" class="height">
                          <div class="layui-form">
                            <div class="layui-form-left  width-label-2"> <span
                                    class="must-icon">*</span>责任约谈-当年累计责任约谈次数（次）</div>
                            <el-input type="text"  v-limit-input-number v-model="infoData.dutyInterviewNumber" placeholder="请输入" class="num-input"
                                      @change="compareBfQuarter('dutyInterviewNumber','责任约谈-当年累计责任约谈次数（次）')"/>
                          </div>
                        </el-col>
                        <el-col :span="8" class="height">
                          <div class="layui-form">
                            <div class="layui-form-left  width-label-2"> <span
                                    class="must-icon">*</span>责任约谈-当年累计责任约谈总人次（人次）</div>
                            <el-input type="text"  v-limit-input-number v-model="infoData.dutyInterviewPersonTime" placeholder="请输入" class="num-input"
                                      @change="compareBfQuarter('dutyInterviewPersonTime','责任约谈-当年累计责任约谈总人次（人次）')"/>
                          </div>
                        </el-col>
                    </div>
                    <div class="top-search">
                      <el-col :span="8" class="height">
                        <div class="layui-form">
                          <div class="layui-form-left  width-label-2"> <span
                                  class="must-icon">*</span>当年累计挽回资产损失（万元）</div>
                          <el-input type="text"  v-limit-input-money v-model="infoData.totalRetrieveLossAmount" @blur="dottedClear" placeholder="请输入"
                                    @change="compareBfQuarter('totalRetrieveLossAmount','当年累计挽回资产损失（万元）')"/>
                        </div>
                      </el-col>
                      <el-col :span="8" class="height">
                        <div class="layui-form">
                          <div class="layui-form-left  width-label-2"> <span
                                  class="must-icon">*</span>当年累计降低损失风险（万元）</div>
                          <el-input type="text" v-limit-input-money v-model="infoData.totalReduceLossRisk" @blur="dottedClear" placeholder="请输入"
                                    @change="compareBfQuarter('totalReduceLossRisk','当年累计降低损失风险（万元）')"/>
                        </div>
                      </el-col>
                      <el-col :span="8" class="height">
                        <div class="layui-form">
                          <div class="layui-form-left  width-label-2"> <span
                                  class="must-icon">*</span>当年累计制修订管理制度（项）</div>
                          <el-input type="text" v-limit-input-number v-model="infoData.totalPerfectSystemNumber" placeholder="请输入" class="num-input"
                                    @change="compareBfQuarter('totalPerfectSystemNumber','当年累计制修订管理制度（项）')"/>
                        </div>
                      </el-col>
                    </div>
                    <div class="top-search">
                      <el-col :span="24" class="height">
                        <div class="layui-form">
                          <div class="layui-form-left  width-label-2"><span class="must-icon">*</span> 其他工作成效</div>
                          <el-input type="text" v-model="infoData.otherAchievement"  placeholder="请输入" />
                        </div>
                      </el-col>
                    </div>

                    <div class="common-in-box-header" style="margin-top: 10px;border: 0px;padding-left: 10px;">
                        <div class="common-in-box-header-text">其他</div>
                    </div>
                    <div class="top-search">
                      <el-col :span="24" class="height">
                        <div class="layui-form">
                          <div class="layui-form-left">备注</div>
                          <el-input type="text" v-model="infoData.remark" placeholder="请输入" />
                        </div>
                      </el-col>
                    </div>
                    <div class="top-search">
                      <el-col :span="8" class="height">
                        <div class="layui-form">
                          <div class="layui-form-left"> <span class="must-icon">*</span>追责部门填报人</div>
                          <el-input type="text" v-model="infoData.informantName" placeholder="请输入" />
                        </div>
                      </el-col>
                      <el-col :span="8" class="height">
                        <div class="layui-form">
                          <div class="layui-form-left"> <span class="must-icon">*</span>联系电话</div>
                          <el-input type="text" v-model="infoData.informantPhone"  placeholder="请输入" @change="validatePhone" />
                        </div>
                      </el-col>
                    </div>
                  </div>
              </div>
          </div>
          <div class="bottom-btn">
            <div class="left-empty" />
            <el-button size="mini" type="primary" @click="saveForm" plain>保存</el-button>
            <el-button size="mini" type="primary" @click="submitForm">提交</el-button>
          </div>
        </div>
      </div>
    </div>

    <Process
    :key="processIndex"
      ref="process"
      :refresh-assignee-url="flowInfo.refreshAssigneeUrl"
      :save-btn-type="flowInfo.saveBtnType"
      :tab-flag="flowInfo.tabFlag"
      :select-value="{
        busiKey:flowInfo.busiKey,
        title:flowInfo.title
      }"
      :center-variable="{}"
      @close="closeAdd"
    />
    <!--未发起上报-->
    <subordinateReportEditNo type="1" v-if="infoData.reportLowerFlag!='1'" :key="infoData.reportLowerFlag"  ref="subordinateReport" @close="refreshQuarterInfo"/>
    <!--已发起上报-->
    <subordinateReportEditYes v-else-if="infoData.reportLowerFlag=='1'" :key="infoData.reportLowerFlag"  ref="subordinateReport" @close="refreshQuarterInfo"/>

    <!-- 数据不一致弹窗 -->
    <DataInconsistencyDialog
      v-model="showInconsistencyDialog"
      :inconsistent-data="inconsistentData"
      @close="handleInconsistencyDialogClose"
    />
    </div>
</template>
<script>
import {
  queryDefaultInfo
  , baseInfoValidate
  , queryQuarterReportFileList
  , delQuarterReportFile
  , queryQuarterReportInfo
  , saveQuarterReportInfo
  , flowParams
  , submitValidateProvInfo, saveProblemFlowEndInfo,
  restoreInitData
} from '@/api/quarterly-report'
import {areaSelectList,refreshReportData} from '@/api/quarterly-report/subordinateReport'
import {queryQuarterReportProv} from '@/api/quarterly-report/view'
import { getToken } from "@/utils/auth";
import Process from "@/components/process-common/index";
import subordinateReportEdit from "@/views/quarterlyReport/subordinateReport/edit";
import subordinateReportEditYes from "@/views/quarterlyReport/subordinateReport/editInitiateYes";
import subordinateReportEditNo from "@/views/quarterlyReport/subordinateReport/editInitiateNo";
import DataInconsistencyDialog from "@/components/DataInconsistencyDialog.vue";
export default {
  name: "addGroup",
  components: { Process,subordinateReportEdit,subordinateReportEditYes,subordinateReportEditNo,DataInconsistencyDialog },
  props: {
    closeBtn: {
      type: Function,
      default: null,
    },
    // add 为新增 edit 为编辑
    editType: {
      type: String,
      default: "",
    },
    //编辑内容
    rowData: {
      type: Object,
      default: () => {},
    },
    //省分与集团 province 和 group
    editUnitType:{
      type:String,
      default:'province'
    },
  },
  dicts: [],
  data() {
    return {
      clearable:false,
      showRestroeBtn:false,//是否显示恢复按钮
      infoData: {}, //基本信息
      defaultReportProvInfo:{},//系统默认统计的数值
      reportProvInfoBf:{},//上一季度数据
      reportQuarterList: [],
      tableLoading: false, //表格loading
      filesData: [], //附件列表
      //附件上传
      files: {
        busiTableId: "",
        busiTableName: "t_col_viol_quarter_report_prov",
        actionUrl: process.env.VUE_APP_BASE_API + "/quarter/file/uploadFiledQuarterReportFile", // 上传地址
        myHeaders: { Authorization: "Bearer " + getToken() }, // 上传header
      },
      //流程信息
      flowInfo: {
        processIndex:0,
        busiKey: '', // 业务中获取
        title: '', // 业务中获取
        saveBtnType: true, // 是否需要保存按钮
        tabFlag: true, // 表明是业务发起环节
        refreshAssigneeUrl: '/quarter/report/flow' // 下环节自定义业务url
      },
      saveParams:{},//保存参数
      flowKey:'',//流程key
      loading:{},
      tipsShowFlag:true,
      // 数据不一致弹窗相关
      showInconsistencyDialog: true,
      inconsistentData: [],
    };
  },
  created() {
    this.queryDefaultInfo();
    if(this.editType == 'edit'){
      this.id = this.rowData.quarterReportProvId;
      this.queryQuarterReportInfo();
    }else{
      this.tipsShowFlag = false;
    }
    this.queryFlowParams();

  },
  methods: {
    //清楚输入.
    dottedClear(evt){
      if(evt.target.value.indexOf(".") != -1){
        var length = evt.target.value.toString().split('.')[1].length
      if(length == '0'){
        evt.target.value = evt.target.value + '00'
      }
      if(length == '1'){
        evt.target.value = evt.target.value + '0'
      }
      }else{
        evt.target.value = evt.target.value + '.00'
      }

    },
    //编辑提示
    loadTips() {
      var text =
        "您即将填报本单位违规经营投资责任追究季度数据，页面有自动填充的标灰数据为系统自动带出，您须核对后根据实际情况重新手工填写，请务必保证所有数据的真实准确，并注意与前期报送数据的滚动关联！" ;
      if(this.editType == 'add'){
        text += this.infoData.reportRequireTime;
      }else{
        let quarterStartTime = this.infoData.statisticsStartTime.split(' ')[0];
        let quarterEndTime = this.infoData.statisticsEndTime.split(' ')[0];
        text += "（本季度需上报受理时间为" + quarterStartTime + "至" + quarterEndTime + "的日常报送数据）";
      }
      this.$alert(text, {
        confirmButtonText: "确定",
        callback: (action) => {
          this.tipsShowFlag = false;
        },
      });
    },
    //查询字典、新增查询上报标题、上报要求、上报表主键
    queryDefaultInfo(){
      queryDefaultInfo().then((res)=>{
          if(res.code == 200){
            this.showRestroeBtn = res.data.showRestroeBtn;
            this.reportQuarterList = res.data.reportQuarterList;
            this.infoData.orgGrade = this.$store.getters.orgGrade;
          }
      })
    },
    //季度报告改变事件
    reportQuarterChange() {
      //如果年度已经选择则去请求 校验 是否存在数据
      if (this.infoData.reportYear) {
        this.baseInfoValidate();
      }
    },
    //年度改变事件
    reportYearChange(val) {
      if (val && this.infoData.reportQuarter) {
        this.baseInfoValidate();
      }
    },
    // 校验年度和季度是否存在数据，如果存在且上报标题不为空则返回提示信息，不存在或者上报标题为空则返回生成的上报标题、上报截止日期
    baseInfoValidate() {
      var params = {
        reportYear:this.infoData.reportYear
        ,reportQuarter : this.infoData.reportQuarter
        ,operationType:this.editType
      }
      baseInfoValidate(params).then((res)=>{
          if(res.code == 200){
            this.infoData.reportCloseTime = res.data.reportCloseTime;
            this.infoData.reportTitle = res.data.reportTitle;
            this.infoData.reportRequire = res.data.reportRequire;
            this.infoData.reportRequireTime = res.data.reportRequireTime;
            if(this.editType == 'add'){
              this.infoData.id = res.data.reportProvId;
              this.$forceUpdate();
              //查询系统默认统计数值
              this.queryQuarterReportInfo();
            }
            // this.loadTips();
          }
      }).catch((err)=>{
        var reportYear = this.infoData.reportYear
        this.infoData = {reportYear:reportYear};
      })
    },
    //获取流程key
    queryFlowParams(){
      flowParams().then((res)=>{
        this.flowKey = res.data.processDefinitionKey;
      })
    },
    //下级单位上报页面关闭，刷新本页面
    refreshQuarterInfo(type){
      this.editType=type;
      this.queryQuarterReportInfo();
    },
    //新增、编辑查询默认上报内容、上一季度累计、保存的本季度上报内容
    queryQuarterReportInfo(){
      var params = {}
      if(this.editType == 'add'){
        params = {
          reportYear:this.infoData.reportYear,
          reportQuarter:this.infoData.reportQuarter,
          operationType:this.editType
        }
      }else{
        params = {
          reportProvId:this.id,
          operationType:this.editType
        }
      }
      queryQuarterReportInfo(params).then((res)=>{
        if(this.editType == 'add'){
          var reportCloseTime = this.infoData.reportCloseTime;
          var reportTitle = this.infoData.reportTitle;
          var reportRequire = this.infoData.reportRequire;
          var reportProvId = this.infoData.id;
          this.infoData = res.data.reportProvInfo;
          this.infoData.reportCloseTime = reportCloseTime;
          this.infoData.reportTitle = reportTitle;
          this.infoData.reportRequire = reportRequire;
          this.infoData.id = reportProvId;
        }else{
          this.infoData = res.data.reportProvInfo;
        }
        this.defaultReportProvInfo = res.data.defaultReportProvInfo;
        this.reportProvInfoBf = res.data.reportProvInfoBf;
        this.files.busiTableId = this.infoData.id;
        if(this.infoData.totalNewSupportingSystem=='0'){//初始化判断（防止老数据两个都有值）
          this.infoData.newSupportingName = ''
        }
        if(this.infoData.totalNewWorkSystem=='0'){//初始化判断（防止老数据两个都有值）
          this.infoData.newWorkName = ''
        }
        this.$forceUpdate();
        //查询附件列表
        this.queryFileList();
        //编辑提示
        if(this.tipsShowFlag){
          // this.loadTips()
        }
      })
    },
    //查询附件列表
    queryFileList(){
      this.tableLoading = true
      queryQuarterReportFileList(this.infoData.id).then((res)=>{
        this.filesData = res.data;
        this.tableLoading = false
      })
    },
    // 附件上传成功
    handleFileSuccess(res, file, fileList) {
      const loading = this.$loading({
            lock: true,//lock的修改符--默认是false
            text: '正在上传中',//显示在加载图标下方的加载文案
            background: 'transparent',
            spinner: 'el-icon-loading',//自定义加载图标类名
            target: document.querySelector('#table')//loadin覆盖的dom元素节点
    });
      if (res.code == 200) {
        this.queryFileList()
        loading.close()
      } else {
        this.$message.error(res.msg);
        loading.close()
      }
    },
    /** 附件删除操作 */
    delFile(row) {
      this.$modal.confirm("确认删除该附件吗？").then( ()=> {
          return delQuarterReportFile(row.id);
       }).then(()=>{
        this.queryFileList();
        this.$modal.msgSuccess("删除成功");
      }) .catch(() => {});
    },

    /**下载文件*/
    fileDownload(obj) {
      this.download(
        "/sys/attachment/downloadSysAttachment/" + obj.attachmentId,
        {},
        obj.fileName
      );
    },
    // 关闭
    cancel() {
      this.closeBtn();
    },
    refreshParams(){
      if(this.infoData.orgGrade!='G'){
        //非集团的季度报告，集团管理干部（人）、政务处分（人次）、禁入限制（人次）默认为空
        this.infoData.groupManagementNumber = '';
        this.infoData.governmentPunishmentTime = '';
        this.infoData.prohibitTime = '';
      }
      this.saveParams={
        defaultReportProvInfo:this.defaultReportProvInfo,
        reportProvInfo:this.infoData,
        reportCloseTime:this.infoData.reportCloseTime,
        reportRequire:this.infoData.reportRequire,
        operationType:this.editType
      }
    },
    getLoading(){
      this.loading = this.$loading({
        lock: true,//lock的修改符--默认是false
        text: '保存中',//显示在加载图标下方的加载文案
        spinner: 'el-icon-loading',//自定义加载图标类名
        background: 'rgba(0, 0, 0, 0.7)',//遮罩层颜色
        target: document.querySelector('#table')//loadin覆盖的dom元素节点
      });
    },
    //保存
    saveForm() {
      this.getLoading();
      this.refreshParams();
      saveQuarterReportInfo(this.saveParams).then((res)=>{
        this.loading.close();
          if(res.code == 200){
            this.editType = 'edit';
            this.infoData.quarterReportId = res.msg;
            this.$modal.msgSuccess('保存成功！');
          }
      })
      .catch((err)=>{
        this.loading.close();
      })
    },
    //提交
    submitForm() {
      const that = this;
      this.loading = this.$loading({
            lock: true,//lock的修改符--默认是false
            text: '正在提交中',//显示在加载图标下方的加载文案
            background: 'transparent',
            spinner: 'el-icon-loading',//自定义加载图标类名
    });
     //先调用保存方法
      this.refreshParams();
      saveQuarterReportInfo(this.saveParams).then((res)=>{
        this.loading.close();
        if(res.code == 200){
          this.editType = 'edit';
          this.infoData.quarterReportId = res.msg;
          //调用提交校验方法
          submitValidateProvInfo({...this.infoData,...{editUnitType:this.province}}).then((response)=>{
            if(response.code == 200){
               this.saveProblemFlowEndInfoFun()
            }else if(response.code == 300 && response.data && response.data.length > 0){
              // 数据不一致，显示弹窗
              this.inconsistentData = this.formatInconsistentData(response.data);
              this.showInconsistencyDialog = true;
              // 高亮不一致字段
              this.checkAndHighlightInconsistentFields(response.data);
              this.loading.close();
            }else if(res.lowerMsg){
              this.$modal.confirm(res.msg+",是否确认继续提交？").then(function () {
                this.saveProblemFlowEndInfoFun()
              }).then(function () {
              }).catch(function () {});;
              loading.close()
            }else{
              this.$modal.msgError(response.meg);
              this.loading.close()
            }
          }).catch(()=>{
            this.loading.close()
      })
        }else{
          this.$modal.msgError('保存失败，请联系管理员');
          this.loading.close()
        }
      }).catch(()=>{
        this.loading.close()
      })
    },
    //提交方法
    saveProblemFlowEndInfoFun(){
      const that = this;
      this.flowInfo.processIndex++
      this.flowInfo.busiKey = this.infoData.id;
      this.flowInfo.title = this.infoData.reportTitle;
      //调用流程发起
      const loadProcessData = {
        businessKey: this.infoData.id,
        title: this.infoData.reportTitle,
        flowKey:this.flowKey
      }
      this.loading.close()
      this.$confirm('确认数据内容填写准确进行提交?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        if(this.infoData.orgGrade!='G') {
          that.$refs.process.handle(1, loadProcessData)
        }else{
          saveProblemFlowEndInfo({quarterReportProvId:that.infoData.id}).then((res)=> {
            if(res.code == 200){
              this.$modal.msgSuccess('提交成功')
              this.closeAdd()
              this.loading.close()
            }else{
              this.$modal.msgError(res.meg);
            }
          })
        }
      }).catch(() => {
      });
    },
    //流程提交
    closeAdd(){
      this.closeBtn();
    },
    //校验手机号
    validatePhone(){
      if(!/^(0\d{2,3}-[1-9]\d{6,7})|(0\d{2,3}[1-9]\d{6,7})|(1[3456789]\d{9})$/.test(this.infoData.informantPhone)){
        // this.$modal.msgWarning("请输入正确格式的联系电话");
        this.$message.error("请输入正确格式的联系电话");
        this.infoData.informantPhone="";
      }
    },
    //输入的当年累计与上一季度对比
    compareBfQuarter(filed,message){
      if(this.reportProvInfoBf && this.reportProvInfoBf[filed] && this.infoData[filed] < this.reportProvInfoBf[filed]){
        // this.$modal.msgWarning(message+"不能小于上一季度数据："+this.reportProvInfoBf[filed]);
        this.$message.error(message+"不能小于上一季度数据："+this.reportProvInfoBf[filed]);
        this.infoData[filed]=this.defaultReportProvInfo[filed]
        if(filed=='totalCompletedProblemNumber'){//当年累计完成追责问题数量（件）
          if(this.infoData.checkCompletedNumber!==''&&this.infoData[filed]>this.infoData.checkCompletedNumber){
            this.$message.error('【当年累计完成追责问题数量（件）】需小于等于【其中: 完成核查（件）】');
            this.infoData[filed] = ''
            this.$forceUpdate();
          }
        }
        if(filed=='totalLeaderTeamMeetingTime'&&(this.infoData.totalLeaderTeamMeetingTime&&this.infoData.quarterTeamMeetingTime)){
          if(this.infoData.totalLeaderTeamMeetingTime<this.infoData.quarterTeamMeetingTime){
            this.$message.error('【当年累计召开领导小组会议（次）】需大于等于【本季度召开领导小组会议（次）】');
            this.infoData[filed] = ''
          }
        }
        if(filed=='totalTeamOfficeMeetingTime'&&(this.infoData.totalTeamOfficeMeetingTime&&this.infoData.quarterTeamOfficeMeetingTime)){
          if(this.infoData.totalTeamOfficeMeetingTime<this.infoData.quarterTeamOfficeMeetingTime){
            this.$message.error('【当年累计召开领导小组办公室会议（次）】需大于等于【本季度召开领导小组办公室会议（次）】');
            this.infoData[filed] = ''
          }
        }
        if(filed=='totalSpecialMeetingTime'&&(this.infoData.totalSpecialMeetingTime&&this.infoData.quarterSpecialMeetingTime)){
          if(this.infoData.totalSpecialMeetingTime<this.infoData.quarterSpecialMeetingTime){
            this.$message.error('【当年累计召开专题会议（次）】需大于等于【本季度召开专题会议（次）】');
            this.infoData[filed] = ''
          }
        }
        if(filed=='totalProblemSourceNumber'&&(this.infoData.totalProblemSourceNumber&&this.infoData.quarterNewProblemNumber)){
          if(this.infoData.totalProblemSourceNumber<this.infoData.quarterNewProblemNumber){
            this.$message.error('【当年累计受理问题线索数量（件）】需大于等于【本季度新受理问题线索数量（件）】');
            this.infoData[filed] = ''
          }
        }
        this.$forceUpdate();
      }else if(filed=='totalCompletedProblemNumber'){//当年累计完成追责问题数量（件）
        if(this.infoData.checkCompletedNumber!==''&&this.infoData[filed]>this.infoData.checkCompletedNumber){
          this.$message.error('【当年累计完成追责问题数量（件）】需小于等于【其中: 完成核查（件）】');
          this.infoData[filed] = ''
          this.$forceUpdate();
        }
      }else if(filed=='totalNewSupportingSystem'){//当年累计新增配套制度（项）
        if(this.infoData.newSupportingName!=''&&this.infoData.totalNewSupportingSystem=='0'){
          this.$message.error('【新增配套制度名称】有值，【当年累计新增配套制度（项）】不能为0');
          this.infoData.totalNewSupportingSystem = ''
        }

      }else if(filed=='totalNewWorkSystem'){//当年累计新增工作机制（项）
        if(this.infoData.newWorkName!=''&&this.infoData.totalNewWorkSystem=='0'){
          this.$message.error('【新增工作机制名称】有值，【当年累计新增工作机制（项）】不能为0');
          this.infoData.totalNewWorkSystem = ''
        }
      }else if(filed=='totalLeaderTeamMeetingTime'&&(this.infoData.totalLeaderTeamMeetingTime&&this.infoData.quarterTeamMeetingTime)){//当年累计召开领导小组会议（次）
        if(this.infoData.totalLeaderTeamMeetingTime<this.infoData.quarterTeamMeetingTime){
          this.$message.error('【当年累计召开领导小组会议（次）】需大于等于【本季度召开领导小组会议（次）】');
          this.infoData[filed] = ''
          this.$forceUpdate();
        }
      }else if(filed=='totalTeamOfficeMeetingTime'&&(this.infoData.totalTeamOfficeMeetingTime&&this.infoData.quarterTeamOfficeMeetingTime)){//当年累计召开领导小组办公室会议

        if(this.infoData.totalTeamOfficeMeetingTime<this.infoData.quarterTeamOfficeMeetingTime){
          this.$message.error('【当年累计召开领导小组办公室会议（次）】需大于等于【本季度召开领导小组办公室会议（次）】');
          this.infoData[filed] = ''
          this.$forceUpdate();
        }
      }else  if(filed=='totalSpecialMeetingTime'&&(this.infoData.totalSpecialMeetingTime&&this.infoData.quarterSpecialMeetingTime)){//当年累计召开专题会议
        if(this.infoData.totalSpecialMeetingTime<this.infoData.quarterSpecialMeetingTime){
          this.$message.error('【当年累计召开专题会议（次）】需大于等于【本季度召开专题会议（次）】');
          this.infoData[filed] = ''
          this.$forceUpdate();
        }
      }else if(filed=='totalProblemSourceNumber'&&(this.infoData.totalProblemSourceNumber&&this.infoData.quarterNewProblemNumber)){//当年累计受理问题线索数量
        if(this.infoData.totalProblemSourceNumber<this.infoData.quarterNewProblemNumber){
          this.$message.error('【当年累计受理问题线索数量（件）】需大于等于【本季度新受理问题线索数量（件）】');
          this.infoData[filed] = ''
          this.$forceUpdate();
        }
      }
    },
    //校验 大小
    quarterNumber(filed){
      if(filed=='quarterTeamMeetingTime'&&(this.infoData.quarterTeamMeetingTime&&this.infoData.totalLeaderTeamMeetingTime)) {//本季度召开领导小组会议
        if (this.infoData.totalLeaderTeamMeetingTime < this.infoData.quarterTeamMeetingTime) {
          this.$message.error('【本季度召开领导小组会议（次）】需小于等于【当年累计召开领导小组会议（次）】');
          this.infoData[filed] = ''
          this.$forceUpdate();
        }
      }else  if(filed=='quarterTeamOfficeMeetingTime'&&(this.infoData.quarterTeamOfficeMeetingTime&&this.infoData.totalTeamOfficeMeetingTime)) {//本季度召开领导小组办公室会议
        if (this.infoData.totalTeamOfficeMeetingTime < this.infoData.quarterTeamOfficeMeetingTime) {
          this.$message.error('【本季度召开领导小组办公室会议（次）】需小于等于【当年累计召开领导小组办公室会议（次）】');
          this.infoData[filed] = ''
          this.$forceUpdate();
        }
      }else  if(filed=='quarterSpecialMeetingTime'&&(this.infoData.totalSpecialMeetingTime&&this.infoData.quarterSpecialMeetingTime)) {//本季度召开专题会议
        if(this.infoData.totalSpecialMeetingTime<this.infoData.quarterSpecialMeetingTime){
          this.$message.error('【本季度召开专题会议（次）】需小于等于【当年累计召开专题会议（次）】');
          this.infoData[filed] = ''
          this.$forceUpdate();
        }
      }else  if(filed=='quarterNewProblemNumber'&&(this.infoData.totalProblemSourceNumber&&this.infoData.quarterNewProblemNumber)) {//本季度新受理问题线索数量
        if(this.infoData.totalProblemSourceNumber<this.infoData.quarterNewProblemNumber){
          this.$message.error('【本季度新受理问题线索数量（件）】需小于等于【当年累计受理问题线索数量（件）】');
          this.infoData[filed] = ''
          this.$forceUpdate();
        }
      }
    },
    //校验  其中: 完成核查（件）
    checkCompletedNumber(){
      if(this.infoData.checkCompletedNumber!==''&&this.infoData.totalCompletedProblemNumber!==''&&(this.infoData.checkCompletedNumber<this.infoData.totalCompletedProblemNumber)){
        this.infoData.checkCompletedNumber = ''
        this.$message.error('【其中: 完成核查（件）】需大于等于【当年累计完成追责问题数量（件）】');
        this.$forceUpdate();
      }
    },
    //下级单位数据上报
    subordinateReport() {
      this.refreshParams();
      if(this.infoData.id){
        saveQuarterReportInfo(this.saveParams).then((res)=>{
          if(res.code == 200){
            this.$refs.subordinateReport.open(this.infoData.id);
            //主键赋值
            this.id = this.infoData.id;
          }
        }).catch((err)=>{})
      }else{
        if(!this.infoData.reportYear){
          this.$modal.msgError("【上报年度】不能为空！");
        }else if(!this.infoData.reportQuarter){
          this.$modal.msgError("【上报季度】不能为空！");
        }
      }
    },
    //刷新上报数据
    refreshReport(){
        if(!this.infoData.reportYear){
          this.$modal.msgError("【上报年度】不能为空！");
        }else if(!this.infoData.reportQuarter){
          this.$modal.msgError("【上报季度】不能为空！");
        }else{
          this.getLoading();
          this.refreshParams();
          saveQuarterReportInfo(this.saveParams).then((res)=>{
            this.loading.close();
            if(res.code == 200){
              this.editType = 'edit';
              this.infoData.quarterReportId = res.msg;
              //主键赋值
              this.id = this.infoData.id;
              if(this.infoData.reportLowerFlag=='0'){//未发起过上报
                this.$modal.msgError("未发起下级单位上报！");
              }else if(this.infoData.reportLowerFlag=='1'&&this.infoData.completedLowerFlag =='0'){//已发起 但 未完成
                this.$confirm('存在未完成的下级单位上报，是否确认刷新数据，并将填写的【本单位汇总信息】覆盖？', '提示', {
                  confirmButtonText: '确定',
                  cancelButtonText: '取消',
                  type: 'warning'
                }).then(()=>{
                  this.refreshReportDataFun()
                }).catch(()=>{});
              }else{
                this.$confirm('是否确认刷新数据，填写的【本单位汇总信息】将会覆盖？', '提示', {
                  confirmButtonText: '确定',
                  cancelButtonText: '取消',
                  type: 'warning'
                }).then(()=>{
                  this.refreshReportDataFun()
                }).catch(()=>{});

              }
            }
          }).catch((err)=>{
              this.loading.close();
          })
        }
      },
    //调用刷新接口
    refreshReportDataFun(){
      refreshReportData(this.id).then((response)=>{
        if (response.code == 200) {
          this.queryQuarterReportInfo();
          this.$message({message: '操作成功',type: 'success'});
        }
      }).catch(()=>{
      })
    },
    //还原默认数据
    restoreInitDataClick(){
      this.$confirm('确认还原数据？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(()=>{
        this.restoreInitform()
      }).catch(()=>{});
    },
    //调用还原接口
    restoreInitform(){
      restoreInitData(this.infoData.id).then((response)=>{
        if (response.code == 200) {
          this.queryQuarterReportInfo();
          this.$message({message: '操作成功',type: 'success'});
        }
      }).catch(()=>{
      })
    },
    // 格式化数据不一致数据
    formatInconsistentData(data) {
      // 根据后端返回的数据格式进行处理
      // 这里假设后端返回的数据格式为数组，每个元素包含字段信息
      const sections = [];

      // 基础模块数据不一致
      const baseModuleData = data.filter(item => item.type === 'base');
      if (baseModuleData.length > 0) {
        sections.push({
          title: '',
          data: baseModuleData.map(item => ({
            fieldName: item.fieldName,
            quarterValue: item.quarterValue,
            baseValue: item.baseValue
          })),
          description: baseModuleData[0]?.description || ''
        });
      }

      // 日常报送模块数据不一致
      const dailyReportData = data.filter(item => item.type === 'daily');
      if (dailyReportData.length > 0) {
        sections.push({
          title: '',
          data: dailyReportData.map(item => ({
            fieldName: item.fieldName,
            quarterValue: item.quarterValue,
            baseValue: item.baseValue
          })),
          description: dailyReportData[0]?.description || ''
        });
      }

      return sections;
    },
    // 处理数据不一致弹窗关闭
    handleInconsistencyDialogClose() {
      this.showInconsistencyDialog = false;
      this.inconsistentData = [];
    },
    // 检查并高亮不一致字段
    checkAndHighlightInconsistentFields(inconsistentFields) {
      // 清除之前的高亮
      this.clearInconsistentHighlight();

      if (!inconsistentFields || inconsistentFields.length === 0) {
        return;
      }

      // 为不一致字段添加高亮样式
      this.$nextTick(() => {
        inconsistentFields.forEach(field => {
          const fieldElement = document.querySelector(`[data-field="${field.fieldCode}"]`);
          if (fieldElement) {
            fieldElement.classList.add('inconsistent-field');

            // 为对应的标签添加样式
            const labelElement = fieldElement.closest('.form-item')?.querySelector('label');
            if (labelElement) {
              labelElement.classList.add('inconsistent-label');
            }
          }
        });
      });
    },
    // 清除不一致字段高亮
    clearInconsistentHighlight() {
      const inconsistentFields = document.querySelectorAll('.inconsistent-field');
      const inconsistentLabels = document.querySelectorAll('.inconsistent-label');

      inconsistentFields.forEach(el => el.classList.remove('inconsistent-field'));
      inconsistentLabels.forEach(el => el.classList.remove('inconsistent-label'));
    },
  },
};
</script>
<style lang="scss" scoped>
@import "~@/assets/styles/quarterly-report/index.css";

/* 数据不一致字段高亮样式 */
.inconsistent-field {
  border: 2px solid #f56c6c !important;
  background-color: #fef0f0 !important;
  animation: inconsistent-blink 2s infinite;
}

.inconsistent-field:focus {
  border-color: #f56c6c !important;
  box-shadow: 0 0 0 2px rgba(245, 108, 108, 0.2) !important;
}

@keyframes inconsistent-blink {
  0%, 50% {
    border-color: #f56c6c;
  }
  25%, 75% {
    border-color: #ff8080;
  }
}

/* 不一致字段标签样式 */
.inconsistent-label {
  color: #f56c6c !important;
  font-weight: bold !important;
}

.inconsistent-label::after {
  content: " ⚠️";
  color: #e6a23c;
}
</style>
