<template>
  <div class="site-home">
    <!--顶部内容-->
    <div class="uni-header uni-header-bordered inner-home">

      <div class="uni-header-logo uni-header-item xiao-logo">
        <img src="@/assets/images/login/logo.png"/>
      </div>
    </div>
    <div class="cancellation-center-bg">
      <div class="cancellation-center-cont">

      </div>
    </div>
    <div class="cancellation-center">
      <div class="cancellation-center-view">
        <img alt="" class="cancellation-view-img" src="@/assets/images/index/vector1.png">
        <div class="cancellation-view-text">
          <h1 class="cancellation-text-h1">登录状态已注销</h1>
          <p class="cancellation-text-p">抱歉，由于您长时间没有操作，现需要重新登录</p>
          <p class="cancellation-text-p"><span>{{times}}</span><span>秒</span>后将跳转至登录页</p>
          <div>
            <span class="cancellation-button cancellation-btn1" @click="jump()">手动跳转</span>
            <span class="cancellation-button cancellation-btn2" @click="close()">关闭</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'cancellation',
  computed: {
    message() {
      return '门户跳转！'
    }
  },
  data() {
    return {
      times: 5
    }
  },
  created() {
    this.timeNum();
  },
  methods: {
    timeNum() {
      this.timer = setInterval(() => {
        this.times--;
        if (this.times === 0) {
          this.jump();
          clearInterval(this.timer)
        }
      }, 1000)
    },
    /** 关闭页面 */
    close() {
      window.location.href = "about:blank";
      window.close();
    },

    /** 跳转页面 */
    jump() {
      // location.href = 'http://sso.portal.unicom.local/eip_sso/aiportalLogin.html?appid=na186&success=http://aiportal.unicom.local/zuul/ssoclient/ssologin%3Faction%3Dlogin&error=http://sso.portal.unicom.local/eip_sso/aiportalLogin.html&return=http://sso.portal.unicom.local/eip_sso/aiportalLogin.html&oawx_t=A0004';
      location.href = 'http://**************:8181/eip_sso/aiportalLogin.html?appid=na186&success=https://uat-aiportal.chinaunicom.cn/zuul/ssoclient/ssologin%3Faction%3Dlogin&error=http://**************:8181/eip_sso/aiportalLogin.html&return=http://**************:8181/eip_sso/aiportalLogin.html&oawx_t=A0004';
      },
  }
}
</script>

<style lang="scss" scoped>
@import '../../assets/styles/js-pro.css';

.site-home{
  background-color: #FFF; overflow: hidden; height: 100%
}
/*登录注销页样式开始*/
.cancellation-header {
  height: 100px;
  width: 100%;
  padding: 0 60px;
  box-sizing: border-box;
}

.cancellation-header .china-logo2 {
  width: 174px;
  height: 100px;
  vertical-align: top;
}

.cancellation-center {
  width: 100%;
  height: 100%;
  background-size: 100% 100%;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.cancellation-center-view {
  display: inline-block;
  position: relative;
  left: 50%;
  top: 50%;
  -webkit-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}

.cancellation-center-view .cancellation-view-img {
  display: inline-block;
  width: 360px;
  height: 266px;
  margin-right: 72px;
  vertical-align: bottom;
}

.cancellation-view-text {
  font-family: "-apple-system", "BlinkMacSystemFont", "Segoe UI", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", "Helvetica", "Arial", sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol";
  float:right;
  display: inline-block;
  text-align: left;
  padding-top: 28px;
}

.cancellation-text-h1 {
  font-size: 26px;
  line-height: 36px;
  color: #282828;
  font-weight: 600;
  letter-spacing: 0px;
  margin-bottom: 20px;
}

.cancellation-text-p {
  font-size: 18px;
  letter-spacing: 0px;
  line-height: 26px;
  font-weight: 400;
  color: #595959;
  margin-bottom: 8px;
}
.cancellation-text-p span{
  color:#c20000;
}
.cancellation-center-bg{
  width: 100%;
  height: calc(100% - 58px);
  position: fixed;
  top: 58px;
  background: #f3f3f3;
  padding:6px 8px;
}
.cancellation-center-cont{
  border-radius: 4px;
  width: 100%;
  height: 100%;
  background: #fff;
}

.cancellation-button {
  margin-top:24px;
  border: 1px solid #ACAFB9;
  margin-right:8px;
  outline: none;
  user-select: none;
  font-weight: 400;
  border-radius: 3px;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  text-decoration: none;
  justify-content: center;
  height: 32px;
  padding: 0 20px;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.65);
  background-color: #fff;
  border-color: #ACAFB9;
}

.cancellation-btn1 {
  color: #fff;
  background-color: #c20000;
  border-color: #c20000;
}
.cancellation-btn1:hover {
  background-color: #c20000;
  border-color: #c20000;
}
.cancellation-btn2 {
  color: rgba(0, 0, 0, 0.65);
  background: transparent;
}
.cancellation-btn2:hover {
  color: #c20000;
  border-color: #c20000;
}

.sj-login {
  text-align: center;
  display: inline-block;
  margin: 20px 0 0 12px;
}

.sj-login .head-title {
  font-size: 16px;
  color: #313131;
  margin: 10px auto 0;
  font-weight: bold;
}

/*登录注销页样式结束*/
</style>
