import request from '@/utils/request'

// 查询页面信息
export function queryViolateInfo(data) {
  return request({
    url: '/colligate/violateWorkDaily/queryViolateInfo',
    method: 'post',
    data:data
  })
}

// 保存
export function saveViolateInfo(data) {
  return request({
    url: '/colligate/violateWorkDaily/saveViolateInfo',
    method: 'post',
    data:data
  })
}

// 提交前的校验
export function checkAndSaveViolateInfo(data) {
  return request({
    url: '/colligate/violateWorkDaily/checkAndSaveViolateInfo',
    method: 'post',
    data:data
  })
}

//判断是否发起实时，返回主要单位
export function checkActualMainDept(data){
  return request({
    url: '/colligate/violDaily/checkActualMainDept',
    method: 'post',
    data:data
  })
}


