<template>
    <div>
      <el-dialog  title="待阅接收人" class="app-report" :visible.sync="visible" width="600px" append-to-body>
        <Jscrollbar height="200px">
          <el-form size="medium" label-width="150px">
          <el-row>
            <el-col :span="24" v-for="(item,index) in formData">
              <el-form-item :label="item.label">
                <el-select v-model="item.selectedValue" placeholder="请选择" value="item.selectedValue">
                  <el-option
                    v-for="row in item.assignees"
                    :key="row.value"
                    :label="row.label"
                    :value="row.value">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          </el-form>
        </Jscrollbar>
        <div slot="footer" class="dialog-footer">
          <el-button size="mini" type="primary" @click="save">保存</el-button>
        </div>
      </el-dialog>
    </div>
</template>

<script>
  import {actualReadReceiverCandidate, saveActualReadReceiver} from '@/api/actual/common/actualReadReceiver';
  export default {
      name: "recipient",
        data(){
          return{
            visible:false,
            formData:[]
          }
      },
      props: {
        actualProblemId: {type: String},
        relevantTableId: {type: String},
        relevantTableName: {type: String},
        receiverGrade: {type: String}
      },
      created(){

      },
      mounted(){

      },
      methods:{
        //弹出
        show(){
          this.visible=true;
          this.ActualReadReceiverCandidate();
        },
        //获取数据
        ActualReadReceiverCandidate(){
          actualReadReceiverCandidate({
            actualProblemId: this.actualProblemId,
            relevantTableId: this.relevantTableId,
            receiverGrade: this.receiverGrade
          }).then(response => {
            this.formData = response.data;
          });
        },
        //保存
        save(){
          let dataArray = [];
          let selectArray = this.formData;
          let saveEnable = true;
          let size = selectArray.length;
          for (let i = 0; i < size; i++) {
            let item = selectArray[i];
            if (!item.selectedValue) {
              saveEnable = false;
              this.$message.error('【' + item.label + '】不能为空！');
              break;
            } else {
              let arrayData = {};
              item.assignees.forEach((assignee) => {
                if (item.selectedValue === assignee.value) {
                  arrayData.actualProblemId = this.actualProblemId;
                  arrayData.relevantTableName = this.relevantTableName;
                  arrayData.relevantTableId = this.relevantTableId;
                  arrayData.receiverGrade = this.receiverGrade;
                  arrayData.receiverPlayRole = item.selectedKey;
                  arrayData.receiverPostId = assignee.value;
                  dataArray.push(arrayData);
                }
              });
            }
          }

          if (saveEnable) {
            saveActualReadReceiver({
              actualProblemId: this.actualProblemId,
              relevantTableId: this.relevantTableId,
              receiverGrade: this.receiverGrade,
              selectedReadReceivers: dataArray
            }).then(response => {
              if (200 === response.code) {
                this.$modal.msgSuccess('保存成功');
                this.visible=false;
                this.$emit('save');
              } else {
                this.$modal.alertError(response.msg);
              }
            });
          }
        }
      }
    }
</script>

<style scoped>

</style>
