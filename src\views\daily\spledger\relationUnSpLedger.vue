<!--日常问题关联专项报告台账列表-->
<template>
  <div class="scope">
    <el-dialog v-bind="$attrs" :visible.sync="visible" width="80%" v-on="$listeners" @open="onOpen" append-to-body :title="title">
      <!--<Jscrollbar height="68vh">-->

      <el-form :model="queryParams" ref="queryForm" id="queryParams"  :inline="true" label-width="125px">
        <el-row>
           <el-col :span="8">
              <el-form-item label="项目名称" prop="projectName">
                <el-input
                  v-model="queryParams.projectName"
                  :style="{width: '100%'}"
                  placeholder="项目名称"
                  clearable
                />
              </el-form-item>
           </el-col>
           <el-col :span="8">
             <el-form-item label="项目编号" prop="projectNum">
               <el-input
                 v-model="queryParams.projectNum"
                 :style="{width: '100%'}"
                 placeholder="项目编号"
                 clearable
               />
             </el-form-item>
           </el-col>
           <el-col :span="8">
             <el-form-item label="发现问题业务类型" prop="status">
               <el-select
                 v-model="queryParams.problemTypeEnumId"
                 placeholder="请选择发现问题业务类型"
                 clearable
                 :style="{width: '100%'}"
                 value="formData.problemTypeEnumId"
               >
                 <el-option
                   v-for="(item, index) in dict.type.business_type"
                   :key="index"
                   :label="item.label"
                   :value="item.value"
                   :disabled="item.disabled"
                 />
               </el-select>
             </el-form-item>
           </el-col>
          </el-row>
           <el-row>
           <el-col :span="8">
             <el-form-item label="审计发现问题" prop="problemAudit">
               <el-input
                 v-model="queryParams.problemAudit"
                 :style="{width: '100%'}"
                 placeholder="审计发现问题"
                 clearable
               />
             </el-form-item>
           </el-col>
        <el-form-item style="float: right">
          <el-button type="primary" icon="el-icon-search" size="mini" @click="queryTableList">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-row>
      </el-form>
      <div style="height: calc(70vh - 180px)">
      <el-table v-loading="loading" :data="tableList" ref="table"  height="calc(100% - 70px)">
        <el-table-column label="序号" type="index" width="80" align="center" >
          <template slot-scope="scope">
            <table-index
              :index="scope.$index"
              :page-num="queryParams.pageNum"
              :page-size="queryParams.pageSize"
            />
          </template>
        </el-table-column>
        <el-table-column label="项目编号"  width="260" align="center">
          <template slot-scope="scope">
              {{ scope.row.projectNum }}
          </template>
        </el-table-column>
        <el-table-column label="项目名称" prop="projectName" show-overflow-tooltip  width="260" align="center">
          <template slot-scope="scope">
            <div
              style="text-align: left"
              class="overflowHidden-1"
            >
              <a @click="showProjectInfo(scope.row.projectId)" class="table-btn" style='color: #c20000;'>
                {{ scope.row.projectName || "" }}
              </a>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="审计对象" prop="projectOrgName"  width="200" show-overflow-tooltip align="center"/>
        <el-table-column label="问题编号" prop="problemNum"  width="200" show-overflow-tooltip align="center"/>
        <el-table-column label="发现问题业务类型" prop="problemTypeEnumName"  width="260" show-overflow-tooltip   align="center">
        </el-table-column>
        <el-table-column label="审计发现问题" prop="problemAudit"  width="260" show-overflow-tooltip  align="left">
        </el-table-column>
        <el-table-column label="具体问题描述" prop="problemDescription"  show-overflow-tooltip width="300"  align="left">
        </el-table-column>
        <el-table-column label="是否上报告" prop="reportFlag" width="100" align="center">
          <template slot-scope="scope" class="text-center">
            {{ scope.row.reportFlag==1?'是':scope.row.reportFlag==0?'否':'' }}
          </template>
        </el-table-column>
        <el-table-column label="是否移交纪检" prop="transferFlag" width="100" align="center">
          <template slot-scope="scope" class="text-center">
            {{ scope.row.transferFlag==1?'是':scope.row.transferFlag==0?'否':'' }}
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          width="80"
          align="center"
          fixed="right"
          class-name="small-padding fixed-width"
          v-if="!relationId"
        >
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-connection"
              title="关联"
              @click="dailyRelationLedger(scope.row.pbInId,scope.row.projectId)"
            >
            </el-button>
          </template>
        </el-table-column>
      </el-table>
        <pagination
          v-show="total>0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="queryUnRelationLedgerList"
        />
    </div>
      <!--</Jscrollbar>-->
  </el-dialog>
    <!--查看-->
    <SpecialReportDetail
      ref="specialReportDetail"
      :key="showProjectKey"
    >
    </SpecialReportDetail>
  </div>
</template>

<script>
  import {queryUnRelationLedgerList
    ,dailyRelationLedger
    ,queryRelationLedger} from "@/api/daily/spledger/index";
  import SpecialReportDetail from '@/views/specialreport/add/operation/specialReportDetail';//查看专项报告

  export default {
    name: "report",
    components: {SpecialReportDetail},

    props: {
      problemId:'',
      relationId:'',
      queryType:''
    },
    dicts: ['business_type'],
    data() {
      return {
        loading:false,
        title:'关联专项报告台账',
        visible:false,//弹框
        status:'',
        // 显示搜索条件
        showSearch: true,
        // 总条数
        total: 0,
        // 表格数据
        tableList: [],
        //已选中id
        hasSelectList:[],
        // 查看专项报告
        showProjectKey:0,
        //新增主键
        //专项报告台账查询 参数
        queryParams: {
          pageNum: 1,
          pageSize: 10,
          problemId:this.problemId,
          projectName:'',
          projectNum:'',
          problemAudit:'',
          problemTypeEnumId:'',
          relationId:this.relationId,
        },
      };
    },
    created() {
      // this.queryUnRelationLedgerList();
    },
    filters: {
    },
    methods: {
      // 显示弹框
      show() {
        this.visible = true;
        if(this.queryType != 'un'){
          //查询日常问题已关联的台账
          this.queryRelationLedger();
        }else{
          this.queryUnRelationLedgerList();
        }
      },
      //搜索
      queryTableList(){
        if(this.queryType != 'un'){
          //查询日常问题已关联的台账
          this.queryRelationLedger();
        }else{
          this.queryUnRelationLedgerList();
        }
      },
      /**查询未关联台账列表*/
      queryUnRelationLedgerList() {
        this.loading = true;
        queryUnRelationLedgerList(this.queryParams).then(
          response => {
            this.tableList = response.rows;
            this.total = response.total;
            this.loading = false;
          }
        );
      },
      queryRelationLedger(){
        this.loading = true;
        queryRelationLedger(this.queryParams).then(
          response => {
            this.tableList = response.rows;
            this.total = response.total;
            this.loading = false;
          }
        );
      },
      //攥取项目编号，显示专项报告查看页面
      showProjectInfo(id){
        this.showProjectKey++;
        this.$nextTick(()=>{
          this.$refs.specialReportDetail.show(id);
        })
      },
      /**重置按钮操作*/
      resetQuery() {
        this.queryParams = {
          pageNum: 1,
          pageSize: 10,
          problemId:this.problemId,
          projectName:'',
          projectNum:'',
          problemAudit:'',
          problemTypeEnumId:'',
          relationId:this.relationId,
        }
        if(this.queryType != 'un'){
          //查询日常问题已关联的台账
          this.queryRelationLedger();
        }else{
          this.queryUnRelationLedgerList();
        }
      },
      //关联
      dailyRelationLedger(pbInId,projectId){
        var relationParams = {
          projectId:projectId,
          pbInId:pbInId,
          problemId:this.problemId
        }
        dailyRelationLedger(relationParams).then(response=>{
          if(response.code == 200){
            this.$modal.msgSuccess("关联成功");
            this.$emit('queryReLedgerInfoByProblemId');
            this.close();
          }else if (response.code == 400) {
            this.$modal.msgWarning(response.msg);
          }else{
            this.$modal.msgError(response.msg);
          }
        })
      },
      close() {
        this.visible = false;
      },
      handelConfirm() {
       let paramsData = JSON.parse(JSON.stringify(this.queryParams));
        paramsData.rangeEntityList = this.hasSelectList;
        saveAspectSituate(paramsData).then(
          response => {
            this.$modal.msgSuccess("保存成功");
            this.$emit('queryRangeList');
            this.close();
          })
      },
      //选中的值
      handleSelectionChange(val){
        let selectList = [];
        val.forEach(function (item) {
          selectList.push(item)
        });
        this.hasSelectList = selectList;
      },
      //关闭
      onOpen(){},
    }
  };
</script>

<style lang="scss">
  .height{
    height: 100%;
  }
</style>




