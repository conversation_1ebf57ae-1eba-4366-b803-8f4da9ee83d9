import request from '@/utils/request'

/**
 * 初始化归档阶段数据
 * @param problemId
 */
export function initDailyFiledData(problemId) {
  return request({
    url: "/colligate/violDailyFiled/initDailyFiledData/" + problemId,
    method: "post"
  })
}

/**
 * 日常报送归档阶段附件
 * @param data
 * @param params
 */
export function dailyFiledAttachments(data, params) {
  return request({
    url: "/colligate/violDailyFiled/dailyFiledAttachments",
    method: "post",
    data: data,
    params: params
  })
}
