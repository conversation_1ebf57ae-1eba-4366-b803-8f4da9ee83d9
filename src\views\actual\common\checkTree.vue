<template>
  <div>
    <el-row>
      <el-col :span="16" class="tree-box">
        <el-tree :data="data" :props="defaultProps"
                 style="margin-top: 20px"
                 :expand-on-click-node="false"
                 node-key="id"
                 lazy
                 :load="loadnode"
                 :default-expanded-keys="defaultTree"

        >
   <span slot-scope="{ node, data }">
     <el-radio  v-if="!data.nocheck" v-model="defaultTree[0]" :label="data.id" @change="change(data)">{{data.name}}</el-radio>
     <span v-else>{{data.name}}</span>
    </span>
        </el-tree>
      </el-col>
      <el-col :span="8" class="tree-box">
        <TreeSelect
          :key="index"
          :isDelete="isDelete"
          :selectTree="selectTree"
          @noCheck="noCheck"
        >
        </TreeSelect>
      </el-col>
    </el-row>
  </div>

</template>

<script>
  import {treeUrl} from "@/api/components/index";
  import TreeSelect from '@/components/TreeSelect/checked';

  export default {
    name:'checkTree',
    components: {
      TreeSelect
    },
    props: {
      selectTree:[],
      url:'',
      params:{}
    },
    data() {
      return {
        radio:1,
        isDelete:false,
        data:[],
        defaultTree:[],
        index:0,
        query:{
          name:'',
          areaCode:'',
          isParent:'',
          provCode:'',
          checked:'',
          id:'',
          pId:'',
          isAll:false,
          open:false,
          nocheck:'',
          userId:'',
          selectName:'',
        },
        defaultProps: {//树对象属性对应关系
          children: 'children',
          label: 'name',
          isLeaf:function(data, node){
            return !data.isParent
          },
          disabled:function(data, node){
            return data.nocheck
          }
        }
      }
    },
    created(){
    },
    methods: {
      //获取数据返回
      list(){
        this.$emit('list',this.selectTree);
      },
      //查询人员姓名
      treeQuery(){
        treeUrl(this.url,{...this.query,...this.params}).then(
          response => {
            this.data=response
          }
        );
      },
      change(data){
        let selectTree  = JSON.parse(JSON.stringify(this.selectTree));
        if(this.selectTree.length){
          this.$modal.confirm('涉及单位只能选择一个，是否将原涉及单位删除？').then(() => {
            this.selectTree=[data];
            this.defaultTree=[data.id];
          }).catch(() => {
            this.selectTree=[selectTree[0]];
            this.defaultTree=[selectTree[0].id];
          });
        }else{
          this.selectTree=[data];
          this.defaultTree=[data.id];
        }
      },
      loadnode(node,resolve){
        //如果展开第一级节点，从后台加载一级节点列表
        if(node.level==0)
        {
          this.index++;
          this.loadfirstnode(resolve);
        }
        //如果展开其他级节点，动态从后台加载下一级节点列表
        if(node.level>=1)
        {
          this.loadchildnode(node,resolve);
        }
      },
      //加载第一级节点
      loadfirstnode(resolve){
        treeUrl(this.url,{...this.params}).then(
          response => {
            for(let i = 0;i<this.selectTree.length;i++){
              this.defaultTree[i] = this.selectTree[i].id;
            }
            resolve(response.data);
          }
        );
      },
      //加载节点的子节点集合
      loadchildnode(node,resolve){
        treeUrl(this.url,{...node.data,...this.params}).then(
          response => {
            resolve(response.data);
          }
        );
      },
      //点击节点上触发的事件，传递三个参数，数据对象使用第一个参数
      nodeclick(data,dataObj,self)
      {},
      //修改状态
      checkChange(node,type){
        console.log(node);
        // if(type){//选中
        //   if(this.selectTree.length){
        //     // this.$modal.confirm('是否确定删除该报告？').then(() => {
        //     //   this.DeleteActualReportRecord(realtimeReportId, index);
        //     // }).catch(() => {
        //     // });
        //     this.$modal.confirm('涉及单位只能选择一个，是否将原涉及单位删除？').then(() => {
        //       for (let i = 0;  i < this.selectTree.length; i++) {
        //         if(this.selectTree[i].id!=node.id){
        //             this.noCheck(this.dataList[i]);
        //         }
        //       }
        //       this.SaveViolateInfo(node);
        //     }).catch(() => {
        //       this.$refs.tree.setChecked(node.id,false);
        //     });
        //   }else{
        //     this.SaveViolateInfo(node);
        //   }
        // }else{
        //   const index = this.selectTree.findIndex(item => item.id === node.id)-1;
        //   this.selectTree.splice(index, 1);
        //   this.defaultTree.splice(index, 1);
        // }
      },
      //保存某个节点
      SaveViolateInfo(node){
        this.selectTree.push(node);
        this.defaultTree.push(node.id);
        console.log('新增的：');
        console.log(this.selectTree);
      },
      //删除某节点
      noCheck(obj){
        if(this.$refs.tree.getNode(obj.id)){
          this.$refs.tree.setChecked(obj.id,false);
        }else{
          const index = this.selectTree.findIndex(item => item.id === obj.id)-1;
          this.selectTree.splice(index, 1);
          this.defaultTree.splice(index, 1);
        }
      }
    }
  }
</script>
<style  rel="stylesheet/scss" scoped lang="scss">
  .is-disabled{
    display: none !important;
  }
  .tree-box{
    height: calc(100vh - 400px);
    overflow: auto;

  }
  .depart_li {
    min-width: 84px;
    height: auto;
    position: relative;
    background-color: #e6f7ff;
    color: #40a9ff;
    line-height: 30px;
    margin: 0 6px 12px;
    display: inline-block;
    padding: 0 30px 0 12px;
    border-radius: 2px;
    box-sizing: border-box;
    .icon {
      float: right;
      cursor: pointer;
      position: absolute;
      right: 8px;
      top: 6px;
      font-size: 16px;
    }
  }
</style>
