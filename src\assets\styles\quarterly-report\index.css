.wai-container {
    background-color: #F4F4F4;
    width: 100%;
    height: 100%;
}

.common-wai-box {
    width: 100%;
    box-sizing: border-box;
    padding: 8px;
}

.common-in-box {
    background-color: #fff;
    height: 100%;
    padding: 10px;
    box-sizing: border-box;
}

.width {
    width: 100%;
}

.height {
    height: 100%;
}

.common-in-box-header {
    display: flex;
    position: relative;
    align-items: center;
    padding-bottom: 10px;
    border-bottom: 1px solid #E8EAEB;
}

.common-in-box-header-right{
  position: absolute;
  right: 0;
}

.common-in-box-header-line {
    width: 5px;
    height: 14px;
    background: #F5222D;
    margin-right:8px;
}

.common-in-box-header-text {
    font-size: 16px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 700;
    color: #333333;
}

.flex-1 {
    flex: 1
}


.num-info {
    background: #FFFFFF;
    box-shadow: 0px 0px 7px 0px rgba(0, 0, 0, 0.09);
    border-radius: 2px;
    margin-top: 10px;
    padding: 10px 20px;
    box-sizing: border-box;
    display: flex;
    align-items: center;
}

.num-info-left .num-info-left-top {
    color: #F5222D;
    font-size: 18px;
    margin-bottom:10px;
    text-align: center;
}

.num-info-left .num-info-left-bottom {
    color: #333;
    font-size:14px;
    text-align: center;
}

.num-info-line {
    height: 50px;
    width: 1px;
    background-color: #E8EAEB;
    margin: 0px 30px;
}

.num-info-right-row .num-info-right-tr {
    display: flex;
    align-items: center;
}

.num-info-right-tr-text-1 {
    color: #333;
    font-size: 14px;
    padding-right:8px;
}

.num-info-right-tr-text-2 {
    color: #F5222D;
    font-size: 16px;
    width: 25px;
}

.layui-progress {
   flex:1;
    margin-left: 10px;
}

.layui-progress-bar-num {
    padding-left: 10px;
    position: absolute;
    right: 40px;
    top: 50%;
    transform: translate(0, -50%);
    font-size: 15px;
}

.layui-progress-bar {
    background-color: #F5222D;
}

.report-list {
    padding: 10px 0px;
}

.report-list .report-list-one {
    margin-bottom: 15px;
    border-bottom: 1px solid #eee;
    padding-bottom: 8px;
}

.report-list .report-list-one .report-list-one-title {
    display: flex;
    align-items: center;
}

.report-list .report-list-one .report-list-one-title .el-icon-document {
    color: #F5222D;
    margin-right: 5px;
    font-size: 16px;
}

.report-list-one-title-txt {
    font-size: 14px;
    color: #333;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    flex: 1;
}

.report-list-one-info {
    margin-top: 10px;
    display: flex;
    align-items: center;
}

.report-list-one-info-1 {
    display: flex;
    align-items: center;
}

.report-list-one-info-1-text {
    color: #333;
    font-size:14px;
    margin-right: 10px;
}

.report-list-one-info-2-text {
    color: #F5222D;
    font-size: 16px;
}

.top-search {
    padding: 10px 0px;
    min-height: 45px;
}

.top-search .layui-form {
    display: flex;
    align-items: center;
}

.layui-form-left {
    color:#606266;
    font-size:14px;
    margin-right: 10px;
    padding-left: 10px;
    position: relative;
    flex-shrink: 0;
}
.must-icon{
    color:#F5222D;
}

.top-search .layui-form input {
    height: 32px;
    background: #FFFFFF;
    border-radius:2px;
    border: 1px solid #DCDEE0;
    padding: 0px 10px;
    flex: 1;
    box-sizing: border-box;
}

.top-search .layui-form-select {
    flex: 1;
}

.model-btn {
    cursor: pointer;
    height: 32px;
    line-height:32px;
    display: inline-block;
    color: #fff;
    font-size: 14px;
    border-radius:2px;
    text-align: center;
    box-sizing: border-box;
}

.model-btn.model-btn-submit {
    min-width: 88px;
    padding: 0 12px;
    box-sizing: border-box;
    background-color: #ff4d4e;
    border: 1px solid #ff4d4e;
    border-radius: 2px;
    margin-right: 8px;
    margin-left: 8px;
}

.model-btn.model-btn-add .iconfont {
    color: #F5222D;
}

.model-btn.model-btn-add span {
    color: #F5222D;
}

.model-btn.model-btn-add {
    min-width: 88px;
    padding: 0 12px;
    box-sizing: border-box;
    background-color: #fff;
    border: 1px solid #ff4d4e;
    border-radius: 2px;

}
.model-btn.model-btn-reset {
    width: 88px;
    background-color: #e6f7ff;
    color: #1890ff;
    border: 1px solid #1890ff;
    margin-right: 8px;
}

.layui-table th,
.layui-table td,
.layui-table[lay-skin="line"],
.layui-table[lay-skin="row"],
.layui-table-view,
.layui-table-header,
.layui-table-tool,
.layui-table-page,
.layui-table-fixed-r,
.layui-table-tips-main {
border-width: 0px;
}
 .el-table th.el-table__cell.is-leaf{
    border: 0px;
    height: 48px;
    background: #F4F8FC;
}
.el-table--border, .el-table--group{
    border: 0px;
}
.common-in-box.el-table::before{
    height: 0px;
}
.layui-table-header {
height: 48px;
background: #F4F8FC;
}

.layui-table-view .layui-table th {
height: 48px;
background: #F4F8FC;
}

.el-table td.el-table__cell {
border-color: #fff;
height:48px;
border-bottom: 1px solid #E6EAEE;
}

.layui-table[lay-even] tr:nth-child(even) {
background-color: #fff;
}

.layui-table thead tr {
border-bottom: 1px solid #E6EAEE;
}

.width-label-2{
    width: 270px;
    text-align: right;
    flex-shrink: 0;
}

.overflowHidden-1{
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}
.layui-form-value{
    color: #333;
    font-size:14px;
    line-height: 1;
}
.layui-table-cell{
    font-size: 13px;
}



.all-province{
    padding: 10px 0px;
}
.one-row-province{
    margin-bottom: 12px;
}
.one-row-province .province-left-status .province-left-status-text-1{
    color: #F5222D;
    font-size: 15px;
}
.province-mid-num{
    color: #F5222D;
    font-size: 15px;
}

.one-row-province .province-lists{
    display: flex;
    flex-wrap: wrap;
    margin-top: 10px;
}
.one-row-province .province-lists .one-province{
    height: 30px;
    padding: 0px 20px;
    color: #F5222D;
    font-size: 14px;
    border: 1px solid #F5222D;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    margin-bottom: 8px;
    width: 120px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    -o-text-overflow: ellipsis;
    line-height: 30px;
    text-align: center;
    margin-right: 8px;
}
.one-row-province .province-lists .one-province:last-child{
    border-right: 1px solid #F5222D;
}
.one-province-more{
    height:30px;
    padding: 0px 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    font-size: 14px;
    background-color: #F5222D;
    border-right: 0px;
    box-sizing: border-box;
    cursor: pointer;
}
.num-info-right{
    flex:1
}

.top-search .el-select{
    flex:1
}
.top-search .el-input--medium .el-input__icon{
    line-height: 32px;
}
.table-btn{
    color: #F5222D;
    font-size: 13px;
    padding: 0px 5px;
}
.el-table__fixed-right::before, .el-table__fixed::before,.el-table--border::after, .el-table--group::after{
    height: 0px;
}

input:focus-visible {
    outline: none;
}

.el-date-editor.el-input, .el-date-editor.el-input__inner{
    flex:1
}

.el-date-editor.el-input input{
    padding-left: 30px;
}

.el-table--border, .el-table--group{
    border: 0px!important;
}
.flex{
    display: flex;
}
a, a:focus, a:hover{
    color: #F5222D;
}
.empty-box{
    text-align: center;
}
.commons_popup .el-dialog__body{
    height: auto;
}


.new-change-height-bottom{
    padding-bottom: 45px;
    border-bottom: 1px solid rgb(238, 238, 238);
}
.new-change-height-bottom-view{
    padding-bottom: 35px;
    border-bottom: 1px solid #eee;
}
.new-change-height-top{
    padding-top: 45px;
    border-top: 1px solid rgb(238, 238, 238);
}
@media only screen and (min-width: 1600px) and (max-width: 1800px) {
    .model-btn.model-btn-submit{
        width: 75px;
        margin-left: 5px;
        margin-right: 5px;
        min-width:75px;
    }
    .model-btn.model-btn-reset{
        width:75px;
        margin-left: 5px;
        margin-right: 5px;
        min-width:75px;
    }
    .model-btn.model-btn-add{
        width:75px;
        margin-left: 5px;
        margin-right: 5px;
        min-width:75px;
    }
    .model-btn.model-btn-add.model-btn-add-new{
        width: auto;
        margin-left: 5px;
        margin-right: 5px;
        min-width:auto;
    }
    .el-date-editor.el-input, .el-date-editor.el-input__inner{
        flex:0.9;
    }
    .top-search .el-select{
        flex:0.9;
    }
}
@media only screen and (min-width: 1440px) and (max-width: 1600px) {
    .model-btn.model-btn-submit{
        width: 70px;
        margin-left: 4px;
        margin-right:4px;
        min-width:70px;
    }
    .model-btn.model-btn-reset{
        width:70px;
        margin-left: 4px;
        margin-right:4px;
        min-width:70px;
    }
    .model-btn.model-btn-add{
        width: 70px;
        margin-left: 4px;
        margin-right: 4px;
        min-width:70px;
    }
    .model-btn.model-btn-add.model-btn-add-new{
        width: auto;
        margin-left: 5px;
        margin-right: 5px;
        min-width:auto;
    }
    .el-date-editor.el-input, .el-date-editor.el-input__inner{
        flex:0.9;
    }
    .top-search .el-select{
        flex:0.9;
    }
    .layui-form-left{
        font-size: 13px;

    }
    .layui-form-value{
        font-size: 13px;
    }
    .el-table .cell{
        font-size: 13px;
    }
    .table-btn{
        font-size: 13px;
    }
    .num-info-line{
        margin: 0px 15px;
    }
    .num-info-right-tr-text-1{
        color: #333;
        font-size: 12px;
        padding-right: 8px;
    }
    .num-info{
        padding: 8px 8px;
    }
    .report-list-one-info-1-text{
        font-size: 12px;
        margin-right: 5px;
    }
    .top-search{
        min-height: 40px;
    }
    .num-info-left .num-info-left-bottom{
        font-size: 13px;
    }
    .report-list-one-title-txt{
        font-size: 12px;
    }
    .common-in-box-header-text{
        font-size: 13px;
    }
    .common-in-box-header-line {
        width: 4px;
    }
    .report-list .report-list-one .report-list-one-title .el-icon-document{
        font-size: 13px;
    }
    .el-radio__label{
        font-size: 12px!important;
    }
}

@media only screen and  (max-width: 1440px) {


    .model-btn.model-btn-submit{
        width: 70px;
        margin-left: 4px;
        margin-right:4px;
        min-width:70px;
    }
    .model-btn.model-btn-reset{
        width:70px;
        margin-left: 4px;
        margin-right:4px;
        min-width:70px;
    }
    .model-btn.model-btn-add{
        width: 70px;
        margin-left: 4px;
        margin-right: 4px;
        min-width:70px;
    }
    .model-btn.model-btn-add.model-btn-add-new{
        width: auto;
        margin-left: 5px;
        margin-right: 5px;
        min-width:auto;
    }
    .el-date-editor.el-input, .el-date-editor.el-input__inner{
        flex:0.9;
    }
    .top-search .el-select{
        flex:0.9;
    }
    .layui-form-left{
        font-size: 12px;

    }
    .layui-form-value{
        font-size: 12px;
    }
    .el-table .cell{
        font-size: 12px;
    }
    .table-btn{
        font-size: 12px;
    }
    .num-info-line{
        margin: 0px 10px;
    }
    .num-info-right-tr-text-1{
        color: #333;
        font-size: 12px;
        padding-right: 5px;
    }
    .num-info{
        padding: 8px 8px;
    }
    .report-list-one-info-1-text{
        font-size: 12px;
        margin-right: 5px;
    }
    .top-search{
        min-height: 40px;
    }
    .num-info-left .num-info-left-bottom{
        font-size: 12px;
    }
    .report-list-one-title-txt{
        font-size: 12px;
    }
    .common-in-box-header-text{
        font-size: 12px;
    }
    .common-in-box-header-line {
        width: 4px;
    }
    .report-list .report-list-one .report-list-one-title .el-icon-document{
        font-size: 12px;
    }
    .el-radio__label{
        font-size: 12px!important;
    }
}
