<!--2：初步核实-经办-->
<template>
  <div>
    <div>
      <el-form ref="elForm" :model="formData" :rules="rules" size="medium" label-width="160px">
        <BlockCard
          title="基本信息"
        >
          <el-row>
            <el-col :span="6">
              <el-form-item label="系统编号" prop="findTime">
                <el-input
                  v-model="formData.auditCode"
                  readonly
                  :style="{width: '100%'}"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="问题编号" prop="problemCode">
                <el-input
                  v-model="formData.problemCode"
                  readonly
                  :style="{width: '100%'}"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="违规事项" prop="problemTitle">
                <el-input
                  v-model="formData.problemTitle"
                  readonly
                  placeholder="请输入违规事项"
                  clearable
                  :style="{width: '100%'}"
                />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="问题线索描述" prop="problemDescribe">
                <el-input
                  v-model="formData.problemDescribe"
                  :readonly="!edit"
                  type="textarea"
                  placeholder="请输入问题线索描述"
                  :autosize="{minRows: 4, maxRows: 4}"
                  :style="{width: '100%'}"
                />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="涉及专业线" prop="specLists">
                <el-checkbox-group :key="formData.specLists" v-model="formData.specLists" size="medium">
                  <el-checkbox
                    v-for="(item, index) in specList"
                    :key="item.dictValue"
                    border
                    :label="item.dictValue"
                  >{{ item.dictLabel }}
                  </el-checkbox>
                </el-checkbox-group>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="涉及单位/部门/人员" prop="field107">
                <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="treeOpen">添加部门人员</el-button>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item>
                <PersList
                  ref="pers"
                  :edit="edit"
                  :problem-id="problemId"
                  :relevant-table-id="relevantTableId"
                  :relevant-table-name="relevantTableName"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </BlockCard>
        <Remind
          :key="actualFlag"
          :actual-flag="actualFlag"
        />
        <BlockCard
          title="初核信息"
        >
          <el-row>
            <el-col :span="8">
              <el-form-item label="初核时间" prop="verifyDate">
                <el-date-picker
                  v-model="formData.verifyDate"
                  type="daterange"
                  unlink-panels
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  value-format="yyyy-MM-dd"
                  :style="{width: '100%'}"
                  placeholder="初核时间"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="移交材料是否齐全" prop="isHandoverDataComplete">
                <el-radio-group v-model="formData.isHandoverDataComplete" size="medium">
                  <el-radio
                    v-for="(item, index) in YesOrNo"
                    :key="index"
                    :label="item.value"
                  >{{ item.label }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>

            <el-col :span="8">
              <el-form-item label="是否产生资产损失" prop="isCauseLoss">
                <el-radio-group v-model="formData.isCauseLoss" size="medium" @change="isCauseLossChange">
                  <el-radio
                    v-for="(item, index) in YesOrNo"
                    :key="index"
                    :label="item.value"
                  >{{ item.label }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="24" />
            <el-col :span="8">
              <el-form-item label="损失关联关系" prop="seriousAdverseEffectsDesc">
                <el-select
                  v-model="formData.lossType"
                  placeholder="请选择损失关联关系"
                  clearable
                  :style="{width: '100%'}"
                  value="formData.lossType"
                >
               <el-option
                    v-for="(item, index) in lossTypeOptions"
                    :key="index"
                    :label="item.dictLabel"
                    :value="item.dictValue"
                  >{{ item.dictLabel }}</el-option>

                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="预估损失金额（万元）" prop="lossAmount">
                <el-input-number
                  v-model="formData.lossAmount"
                  :min="0"
                  :precision="2"
                  placeholder="预估损失金额（万元）"
                  :disabled='formData.isCauseLoss!="1"'
                  controls-position="right"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="预估损失风险（万元）" prop="lossRisk">
                <el-input-number
                  v-model="formData.lossRisk"
                  :min="0"
                  :precision="2"
                  placeholder="预估损失风险（万元）"
                  controls-position="right"
                />
              </el-form-item>
            </el-col>

             <el-col :span="24" />
             <el-col :span="8">
              <el-form-item label="是否产生不良影响" prop="isAdverseEffect">
                 <el-radio-group v-model="formData.isAdverseEffect" size="medium" @change="isAdverseEffectChange">
                    <el-radio v-for="(item, index) in whetherEffectOptions" :key="index" :label="item.value" @change="radioEffectChanged"
                              :disabled="item.disabled">{{item.label}}</el-radio>
                  </el-radio-group>
              </el-form-item>
            </el-col>
             <el-col :span="16">
                <el-form-item label="对应不良影响" prop="correspondingAdverseEffects" v-if="formData.isAdverseEffect">
                  <el-select v-model="formData.correspondingAdverseEffects" :style="{width: '100%'}" clearable="clearable" multiple="multiple" value="">
                    <el-option v-for="(item, index) in dict.type.corresponding_adverse_effect" :key="index" :label="item.label" :value="item.value"></el-option>
                  </el-select>
                </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="损失形成主要原因" prop="lossReason">
                <el-input
                  v-model="formData.lossReason"
                  :style="{width: '100%'}"
                  :autosize="{minRows: 4, maxRows: 4}"
                  type="textarea"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="造成的不良影响" prop="adverseEffects" v-if="formData.isAdverseEffect">
                <el-input
                  v-model="formData.adverseEffects"
                  :style="{width: '100%'}"
                    :autosize="{minRows: 4, maxRows: 4}"
                    type="textarea"
                  clearable
                />
              </el-form-item>
            </el-col>
             <el-col :span="8">
              <el-form-item label="是否产生严重不良影响" prop="seriousAdverseEffectsFlag" v-show="formData.isAdverseEffect">
                <el-radio-group v-model="formData.seriousAdverseEffectsFlag" size="medium" @change="seriousAdverseEffectsFlagChange">
                  <el-radio
                    v-for="(item, index) in YesOrNo"
                    :key="index"
                    :label="item.value"
                    :disabled="item.disabled"
                  >{{ item.label }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col v-show="formData.seriousAdverseEffectsFlag && formData.isAdverseEffect" :span="16">
              <el-form-item label="严重不良影响描述" prop="seriousAdverseEffectsDesc">
                <el-select
                  v-model="formData.seriousAdverseEffectsDesc"
                  placeholder="请选择严重不良影响描述"
                  clearable
                  :style="{width: '100%'}"
                  value="formData.seriousAdverseEffectsDesc"
                >
                  <el-option
                    v-for="(item, index) in dict.type.VIOLD_ADVER_EFFECT_DES"
                    :key="index"
                    :label="item.label"
                    :value="item.value"
                    :disabled="item.disabled"
                  />
                </el-select>
              </el-form-item>
            </el-col>
             <el-col :span="24" />
            <el-col :span="8">
              <el-form-item label="涉嫌违规" prop="isViolation">
                <el-radio-group v-model="formData.isViolation" size="medium">
                  <el-radio
                    v-for="(item, index) in YesOrNo"
                    :key="index"
                    :label="item.value"
                    :disabled="item.disabled"
                  >{{ item.label }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="涉嫌违纪违法" prop="isIllegal">
                <el-radio-group v-model="formData.isIllegal" size="medium">
                  <el-radio
                    v-for="(item, index) in YesOrNo"
                    :key="index"
                    :label="item.value"
                    :disabled="item.disabled"
                  >{{ item.label }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>

            <el-col :span="24">
              <ScopeSituation
                v-if="scopeSituation"
                :key="problemId||relevantTableId||relevantTableName"
                ref="scope"
                :edit="edit"
                :problem-id="problemId"
                :relevant-table-id="relevantTableId"
                :relevant-table-name="relevantTableName"
              />
            </el-col>
            <el-col :span="24">
              <el-form-item label="其他说明事项" prop="otherExplainMatter">
                <el-input
                  v-model="formData.otherExplainMatter"
                  type="textarea"
                  placeholder="请输入其他说明事项"
                  :autosize="{minRows: 4, maxRows: 4}"
                  :style="{width: '100%'}"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </BlockCard>
      </el-form>
      <BlockCard
        title="附件列表"
      >
        <FileUpload
          v-if="relevantTableId!=''&&relevantTableName!=''"
          :key="problemId||relevantTableId||relevantTableName"
          ref="file"
          :edit="edit"
          :problem-id="problemId"
          :relevant-table-id="relevantTableId"
          :relevant-table-name="relevantTableName"
          flow-type="VIOL_DAILY"
          problem-status="2"
          flow-key="SupervisionDailyReport"
        />
        <el-dialog class="tree-body-dialog" :visible.sync="visibleTree" width="90%" :before-close="saveY" append-to-body title="人员选择">
          <Tree
            v-if="visibleTree"
            ref="persTree"
            :key="problemId||relevantTableId||relevantTableName"
            :problem-id="problemId"
            :relevant-table-id="relevantTableId"
            :relevant-table-name="relevantTableName"
            :is-edit="true"
            @save="saveY"
          />
          <div slot="footer" class="dialog-footer">
            <el-button size="mini" type="primary" @click="closeTree">保存</el-button>
          </div>
        </el-dialog>
      </BlockCard>
      <!--修改记录-->
      <el-dialog :visible.sync="visibleModify" width="80%" append-to-body title="修改记录">
        <modifyRecord
          v-if="visibleModify"
          ref="modify"
          :key="problemId||relevantTableId||relevantTableName"
          edit="true"
          :problem-id="problemId"
          :relevant-table-id="relevantTableId"
          :relevant-table-name="relevantTableName"
          :problem-status="2"
          @modifySave="modifySave"
        />
        <div slot="footer" class="dialog-footer">
          <el-button size="mini" type="primary" @click="modifyClose">保存</el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>
<script>
import { submitHisData } from '@/api/daily/historyQuestionEnter/index'
import { queryVerifyRecord, saveViewVerrify, saveViolationVerifyRecord } from '@/api/daily/process/taskTodoViewVerify'
import BlockCard from '@/components/BlockCard'
import ScopeSituation from '@/views/daily/scopeSituation/scopeSituationData'// 范围情形展示
import modifyRecord from '@/views/daily/modifyRecord'// 修改记录
import { checkInvolve } from '@/api/components/index'
import FileUpload from '@/views/components/fileUpload'// 附件
import PersList from '@/views/daily/tree/persList'// tree
import Tree from '@/views/daily/tree'// tree
import Remind from '@/views/components/remind'
import TaskTodoViewAccept from '@/views/daily/process/taskTodoViewAccept'// tree
import Process from '@/components/Process/daily'
import {
  // 生成情形范围修改记录
  generateSituationRangeModifyRecord
  // 生成业务数据修改记录
  , generateBusinessModifyRecord } from '@/api/daily/modifyRecord/modifyRecord'// 修改记录
import { generateInvolveItemModifyRecord } from '@/api/components/index'

export default {
  components: { BlockCard, ScopeSituation, FileUpload, Tree, PersList, Process, TaskTodoViewAccept, Remind, modifyRecord },
  dicts: ['VIOLD_DAILY_SPEC', 'VIOLD_ADVER_EFFECT_DES','corresponding_adverse_effect'],
  props: {
    problemId: {
      type: String
    }

  },
  data() {
    return {
      showAdverseEffectFlag:false,
      relevantTableId: '',
      relevantTableName: '',
      visibleModify: false,
      actualFlag: 1,
      edit: true,
      scopeSituation: false,
      flag: false,
      visible: false,
      visibleTree: false,
      formData: {
        isHandoverDataComplete: undefined,
        isCauseLoss: undefined,
        isViolation: undefined,
        lossType: undefined,
        isIllegal: undefined,
        verifyDate: undefined,
        problemTitle: null,
        problemDescribe: undefined,
        field107: undefined,
        lossAmount: undefined,
        lossRisk: undefined,
        lossReason: undefined,
        adverseEffects: undefined,
        seriousAdverseEffectsFlag: 1,
        seriousAdverseEffectsDesc: undefined,
        otherExplainMatter: undefined,
        specLists: []
      },
      specList: [],
      rules: {

      },
      YesOrNo: [{
        'label': '是',
        'value': 1
      }, {
        'label': '否',
        'value': 0
      }],
        whetherEffectOptions: [{
        "label": "是",
        "value": 1
      }, {
        "label": "否",
        "value": 0
      }],
      problemSourceList: [],
      lossTypeOptions: [],
      CopyLossTypeOptions:[]
    }
  },
  computed: {},
  watch: {},
  created() {
  },
  mounted() {
    this.QueryVerifyRecord()
  },
  methods: {
    isAdverseEffectChange(){
      if (this.formData.isCauseLoss !== undefined && this.formData.isAdverseEffect !== undefined) {
        if (!this.formData.isCauseLoss && !this.formData.isAdverseEffect) {
          this.$message.error("【是否产生资产损失】与【是否产生不良影响】全部选择“否”不符合填报规则！");
          this.formData.isAdverseEffect = 1;
        }
      }
      this.formData.seriousAdverseEffectsFlag = ''
    },
    seriousAdverseEffectsFlagChange(){
      if(this.formData.isAdverseEffect == '0'){
        if(this.formData.seriousAdverseEffectsFlag == '1'){
          this.$message.warning('是否产生不良影响在选择否的情况下，是否产生严重不良影响只能选择否！')
          this.formData.seriousAdverseEffectsFlag = 0
        }
      }
    },
    isCauseLossChange(){
      if (this.formData.isCauseLoss !== undefined && this.formData.isAdverseEffect !== undefined) {
        if (!this.formData.isCauseLoss && !this.formData.isAdverseEffect) {
          this.$message.error("【是否产生资产损失】与【是否产生不良影响】全部选择“否”不符合填报规则！");
          this.formData.isCauseLoss = 1;
        }
      }
      this.formData.lossType = ''
      if(this.formData.isCauseLoss== '1'){
        // this.formData.lossType 去掉dictValue为3的值
         this.lossTypeOptions =  this.CopyLossTypeOptions.filter(item=>{
          return item.dictValue != '3'
        })



      }

       if(this.formData.isCauseLoss== '0'){
        // this.formData.lossType 去掉dictValue为1的值
         this.lossTypeOptions =  this.CopyLossTypeOptions.filter(item=>{
          return item.dictValue != '1'
        })



      }
    },
    radioEffectChanged() {
      this.showAdverseEffectFlag = this.formData.isAdverseEffect;
    },
    // 关闭
    close() {
      this.visible = false
      this.$emit('close')
    },
    /** 初始化数据*/
    QueryVerifyRecord() {
      const array = []
      queryVerifyRecord(this.problemId).then(
        response => {
          this.actualFlag = response.data.actualFlag
          const specSelectedList = response.data.involveProfessionalLines
          this.formData = { ...this.formData, ...response.data }
          for (let i = 0, len = specSelectedList.length; i < len; i++) {
            if (specSelectedList[i].specCode) { array.push(specSelectedList[i].specCode) }
          }
          this.lossTypeOptions = response.data.lossTypeOptions;
          this.CopyLossTypeOptions = JSON.parse(JSON.stringify(this.lossTypeOptions));
          if (response.data.verifyStartDate) {
            this.formData.verifyDate = [
              response.data.verifyStartDate,
              response.data.verifyEndDate
            ]
          }

          if (this.formData.isCauseLoss) {
            this.lossTypeOptions =  this.CopyLossTypeOptions.filter(item=>{
              return item.dictValue != '3'
            })
          } else {
            this.lossTypeOptions =  this.CopyLossTypeOptions.filter(item=>{
              return item.dictValue != '1'
            })
          }

          this.formData.specLists = array
          this.specList = response.data.professionalLineOptions
          this.relevantTableId = response.data.id
          this.problemSourceList = response.data.problemSourceList
          this.relevantTableName = response.data.relevantTableName
          this.$nextTick(() => {
            this.scopeSituation = true
            this.$refs.pers.DueryDepartmentSelectInfo()
            this.$refs.file.ViolationFileItems()
          })
          this.$emit('closeLoading')
          console.log(this.formData)
        }
      )
    },
    // 校验单位、部门、人员
    CheckInvolve() {
      let final = true
      if (!this.formData.isCauseLoss && !this.formData.isAdverseEffect) {
        this.$message.error("【是否产生资产损失】与【是否造成不良影响】全部选择“否”不符合填报规则，请重新选择！");
        return false;
      }
      checkInvolve(this.problemId).then(
        response => {
          if (response.code == 200) {
            let companyString = ''; let // 单位列表
              deptString = ''// 部门列表
            if (response.data.resultCode == 'false') {
              this.$message.error('请选择单位！')
              final = false
            } else if (response.data.resultCode == 'company') {
              companyString = ''
              for (let i = 0; i < response.data.listCompany.length; i++) {
                companyString += '【' + response.data.listCompany[i].INVOL_COMPANY_NAME + '】'
                if (i + 1 != response.data.listCompany.length) {
                  companyString += '、'
                }
              }
              companyString = '' + companyString + '下未选择涉及部门！'
              this.$message.error(companyString)
              final = false
            } else if (response.data.resultCode == 'double') {
              companyString = ''
              deptString = ''
              for (let i = 0; i < response.data.listCompany.length; i++) {
                companyString += '【' + response.data.listCompany[i].INVOL_COMPANY_NAME + '】'
                if (i + 1 != response.data.listCompany.length) {
                  companyString += '、'
                }
              }
              for (let i = 0; i < response.data.listDept.length; i++) {
                deptString += '【' + response.data.listDept[i].INVOL_ORG_NAME + '】'
                if (i + 1 != response.data.listDept.length) {
                  deptString += '、'
                }
              }
              companyString = '1、' + companyString + '下未选择涉及部门！<br/>'
              deptString = '2、' + deptString + '下未选择涉及人员！'
              this.$message.error(companyString + deptString)
              final = false
            } else if (response.data.resultCode == 'dept') {
              deptString = ''
              for (let i = 0; i < response.data.listDept.length; i++) {
                deptString += '【' + response.data.listDept[i].INVOL_ORG_NAME + '】'
                if (i + 1 != response.data.listDept.length) {
                  deptString += '、'
                }
              }
              deptString = deptString + '下未选择涉及人员！'
              this.$message.error(deptString)
              final = false
            } else {
              generateInvolveItemModifyRecord({ problemId: this.problemId, businessId: this.relevantTableId }).then(response => {})
              final = true
            }
          }
        }
      )
      return final
    },
    // 调用修改记录保存
    modifyClose() {
      this.$refs.modify.save()
    },
    // 修改记录保存后
    modifySave(type) {
      if (type) {
        this.visibleModify = false
        this.submitHisDataFun()
      } else {
        this.visibleModify = false
      }
    },
    /** 提交数据*/
    nextStep() {
      const volve = this.CheckInvolve()
      if (!volve) {
        return false
      }
      this.$refs['elForm'].validate(valid => {
        if (!valid) return
        // TODO 提交表单
        const array = []
        const specSelectedList = this.formData.specLists
        const specList = this.specList
        for (let i = 0, len = specSelectedList.length; i < len; i++) {
          for (let j = 0, leng = specList.length; j < leng; j++) {
            if (specList[j].dictValue == specSelectedList[i]) {
              specList[j].specCode = specList[j].dictValue
              specList[j].specName = specList[j].dictLabel
              array.push(specList[j])
            }
          }
        }
        if (this.formData.verifyDate) {
          this.formData.verifyStartDate = this.formData.verifyDate[0]
          this.formData.verifyEndDate = this.formData.verifyDate[1]
        }
        this.formData.involveProfessionalLines = array;
        if (!this.formData.isAdverseEffect) { this.formData.seriousAdverseEffectsFlag = 0; }
        saveViolationVerifyRecord(this.formData).then(
          response => {
            this.$modal.msgSuccess('保存成功')
            this.Modify()
          }
        )
      })
    },
    // 修改记录
    Modify() {
      // 生成情形范围修改记录
      generateSituationRangeModifyRecord(this.problemId, this.relevantTableId)
      // 生成业务数据修改记录
      generateBusinessModifyRecord(this.problemId, this.relevantTableId, this.relevantTableName).then(
        response => {
          const isExistDifferenceField = response.data.isExistDifferenceField
          if (isExistDifferenceField) {
            this.visibleModify = true
          } else {
            this.submitHisDataFun()
          }
        }
      )
    },
    /** 保存数据*/
    publicSave() {
      // this.formData.specList=[];
      const array = []
      const specSelectedList = this.formData.specLists
      const specList = this.specList
      for (let i = 0, len = specSelectedList.length; i < len; i++) {
        for (let j = 0, leng = specList.length; j < leng; j++) {
          if (specList[j].dictValue == specSelectedList[i]) {
            specList[j].specCode = specList[j].dictValue
            specList[j].specName = specList[j].dictLabel
            array.push(specList[j])
          }
        }
      }
      if (this.formData.verifyDate) {
        this.formData.verifyStartDate = this.formData.verifyDate[0]
        this.formData.verifyEndDate = this.formData.verifyDate[1]
      }
      this.formData.involveProfessionalLines = array
      saveViewVerrify(this.formData).then(
        response => {
          this.$modal.msgSuccess('保存成功')
        }
      )
    },
    resetForm() {
      this.$refs['elForm'].resetFields()
    },
    // 打开弹窗
    show() {
      this.visible = true
    },
    // 关闭弹窗
    closeTree() {
      this.$refs.persTree.CheckInvolve()// 校验
    },
    saveY() {
      this.visibleTree = false
      this.$refs.pers.DueryDepartmentSelectInfo()
    },
    // 选择人员
    treeOpen() {
      this.flag = !this.flag
      this.visibleTree = true
    },
    //最终提交
    submitHisDataFun(){
      this.$confirm('请确认填写内容，提交后不能再进行修改', '提示', {
        confirmButtonText: '继续提交',
        cancelButtonText: '返回确认',
        type: 'warning'
      }).then(() => {
        const loading = this.$loading({
          spinner: 'el-icon-loading', // 自定义加载图标类名
          text: '正在加载...', // 显示在加载图标下方的加载文案
          lock: false, // lock的修改符--默认是false
        });
        submitHisData(this.problemId).then(
          response => {
            loading.close();
            this.$modal.msgSuccess("提交成功");
            this.closeEmit();
          }
        ).catch(err => {
          loading.close();
        })
      })
    },
    //流程提交后推进到下一个环节
    closeEmit(){
      this.$emit('hisNext')
    }
  }
}

</script>
<style scoped>
.tree-body-dialog ::v-deep.el-dialog__body{
  padding:0 20px;
}
</style>
