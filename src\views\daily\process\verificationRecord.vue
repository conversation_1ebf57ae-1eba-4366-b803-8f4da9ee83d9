<!--4：核查记录-经办-->
<template>
  <div v-if="formData">
    <div>
      <BlockCard title="基本信息">
        <el-row>
          <el-form
            ref="elForm"
            :model="formData"
            :rules="rules"
            size="medium"
            label-width="150px"
          >
            <el-col :span="12">
              <el-form-item label="系统编号" prop="auditCode">
                <span>{{ formData.auditCode }}</span>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="问题编号" prop="problemCode">
                <span>{{ formData.problemCode }}</span>
              </el-form-item>
            </el-col>

            <el-col :span="24">
              <el-form-item label="违规事项" prop="problemTitle">
                <span>{{ formData.problemTitle }}</span>
              </el-form-item>
            </el-col>

            <el-col :span="24">
              <el-form-item label="问题线索描述" prop="problemDescribe">
                <el-input
                  v-model="formData.problemDescribe"
                  type="textarea"
                  placeholder="请输入问题线索描述"
                  :autosize="{ minRows: 4, maxRows: 4 }"
                  :style="{ width: '100%' }"
                />
              </el-form-item>
            </el-col>

            <el-col :span="24">
              <el-form-item label="涉及专业线" prop="specLists">
                <el-checkbox-group
                  :key="formData.specLists"
                  v-model="formData.specLists"
                  size="medium"
                >
                  <el-checkbox
                    v-for="item in specList"
                    :key="item.dictValue"
                    border
                    :label="item.dictValue"
                  >{{ item.dictLabel }}
                  </el-checkbox>
                </el-checkbox-group>
              </el-form-item>
            </el-col>
            <el-col v-if="formData.id" :span="24">
              <ScopeSituation
                :key="problemId"
                ref="scope"
                :edit="edit"
                :problem-id="problemId"
                :relevant-table-id="formData.id"
                :relevant-table-name="formData.relevantTableName"
              />
            </el-col>
            <el-col :span="24">
              <el-form-item
                label="使用责任追究办法说明"
                prop="applicableInvestigateExplain"
              >
                <el-input
                  v-model="formData.applicableInvestigateExplain"
                  type="textarea"
                  placeholder="请输入使用责任追究办法说明"
                  :autosize="{ minRows: 4, maxRows: 4 }"
                  :style="{ width: '100%' }"
                />
              </el-form-item>
            </el-col>

            <el-col :span="24">
              <el-form-item
                label="移送纪检部门"
                prop="isHandoverInspectionDept"
              >
                <el-radio-group
                  v-model="formData.isHandoverInspectionDept"
                  size="medium"
                  @change="changeDel"
                >
                  <el-radio
                    v-for="(item, index) in YesOrNo"
                    :key="index"
                    :label="item.value"
                  >{{ item.label }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="24" v-if="formData.isHandoverInspectionDept">
              <el-form-item>
                <div class="layui-form-item layui-form-item-sm">
                  <el-col class="layui-input-block">
                    <el-col class="transfer-box">
                      <el-row
                        v-for="(
                          item, index
                        ) in formData.inspectionHandoverRecords"
                        class="transfer-li ry-row"
                        :key="index"
                      >
                        <div
                          v-if="
                            formData.isHandoverInspectionDept ||
                            !item.editEnable
                          "
                        >
                          <el-col :span="8" class="transfer-li-info">
                            <el-row>
                              <el-col :span="16" class="ovflowHidden text-left"
                              ><span>{{ item.fromUnitName }}</span></el-col
                              >
                              <el-col :span="8" class="text-right"
                              ><span>{{ item.fromUserName }}</span></el-col
                              >
                            </el-row>
                          </el-col>
                          <el-col :span="2" class="transfer-li-img">——</el-col>
                          <el-col
                            :span="8"
                            class="transfer-li-info ry-row cursor editSecondary"
                          >
                            <el-row>
                              <el-col :span="16" class="ovflowHidden text-left">
                                <span
                                  class="toUnitName"
                                  @click="
                                    item.editEnable == 1 &&
                                      BstaffOrgTree(2, item.toUserName)
                                  "
                                >{{
                                    item.toDeptName ||
                                    "请选择被移送纪检部门及人员信息"
                                  }}</span
                                >
                              </el-col>
                              <el-col :span="8" class="text-right">
                                <span
                                  class="toUserName"
                                  @click="
                                    item.editEnable == 1 &&
                                      BstaffOrgTree(2, item.toUserName)
                                  "
                                >{{ item.toUserName }}</span
                                >
                              </el-col>
                            </el-row>
                          </el-col>
                          <el-col :span="6" class="transfer-li-edit text-right">
                            <span
                              v-show="item.downloadLink"
                              style="padding-left: 20px"
                              class="table-btn tip-edit float-right text-red ovflowHidden cursor"
                              :title="item.fileName"
                              @click="
                                downloadLink(item.downloadLink, item.fileName)
                              "
                            >{{ item.fileName }}</span
                            >
                          </el-col>
                        </div>
                      </el-row>

                      <el-row
                        v-if="
                          formData.initialInspectionHandoverObj &&
                          formData.isHandoverInspectionDept == '1'
                        "
                        class="transfer-li ry-row"
                      >
                        <el-col :span="8" class="transfer-li-info">
                          <el-row>
                            <el-col :span="16" class="ovflowHidden text-left"
                            ><span>{{
                                formData.initialInspectionHandoverObj
                                  .fromUnitName
                              }}</span></el-col
                            >
                            <el-col :span="8" class="text-right"
                            ><span>{{
                                formData.initialInspectionHandoverObj
                                  .fromUserName
                              }}</span></el-col
                            >
                          </el-row>
                        </el-col>
                        <el-col :span="2" class="transfer-li-img">——</el-col>
                        <el-col
                          :span="8"
                          class="transfer-li-info ry-row cursor editSecondary"
                        >
                          <el-row>
                            <el-col :span="16" class="ovflowHidden text-left">
                              <span
                                class="toUnitName"
                                @click="
                                  BstaffOrgTree(
                                    1,
                                    formData.initialInspectionHandoverObj
                                      .toUserName
                                  )
                                "
                              >{{
                                  formData.initialInspectionHandoverObj
                                    .toUnitName || "请选择被移送单位及人员信息"
                                }}</span
                              >
                            </el-col>
                            <el-col :span="8" class="text-right">
                              <span
                                class="toUserName"
                                @click="
                                  BstaffOrgTree(
                                    1,
                                    formData.initialInspectionHandoverObj
                                      .toUserName
                                  )
                                "
                              >{{
                                  formData.initialInspectionHandoverObj
                                    .toUserName
                                }}</span
                              >
                            </el-col>
                          </el-row>
                        </el-col>
                      </el-row>
                    </el-col>
                  </el-col>
                </div>
              </el-form-item>
            </el-col>
          </el-form>
        </el-row>
      </BlockCard>
      <Remind
        :key="formData.actualFlag"
        :actualFlag="formData.actualFlag"
      ></Remind>
      <BlockCard title="核查方案">
        <el-row>
          <el-form
            ref="elForm"
            :model="formData"
            :rules="rules"
            size="medium"
            label-width="150px"
          >

            <el-col :span="12">
              <el-form-item
                label="核查组组长"
                prop="checkGroupLeaderList"
                class="input-btn"
              >
                <div class="list1">
                  <div
                    v-for="(item, index) of leaderList"
                    :key="index"
                    class="list1-one"
                    v-show="item.userName"
                  >
                    <span>{{ item.userName }}</span>
                    <span class="close"
                    ><i
                      class="el-icon-close icon iconfont"
                      @click="deleteCheckGroupLeaderList(item, 1)"
                    ></i
                    ></span>
                  </div>
                </div>
                <el-button
                  style="height: 28px"
                  type="primary"
                  plain
                  icon="el-icon-plus"
                  size="mini"
                  @click="addCheckGroupLeaderList(1)"
                >添加
                </el-button>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item
                label="核查组成员"
                prop="checkGroupMemberList"
                class="input-btn"
              >
                <div class="list1">
                  <div
                    v-for="(item, index) of memberList"
                    :key="index"
                    class="list1-one"
                  >
                    <span>{{ item.userName }}</span>
                    <span class="close"
                    ><i
                      class="el-icon-close icon iconfont"
                      @click="deleteCheckGroupLeaderList(item, 0)"
                    ></i
                    ></span>
                  </div>
                </div>
                <el-button
                  style="height: 28px"
                  type="primary"
                  plain
                  icon="el-icon-plus"
                  size="mini"
                  @click="addCheckGroupLeaderList(2)"
                >添加
                </el-button>
              </el-form-item>
            </el-col>
            <el-col :span="24"></el-col>
            <el-col :span="8">
              <el-form-item label="核查时间" prop="checkTime">
                <el-date-picker
                  v-model="formData.checkTime"
                  format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd"
                  :style="{ width: '100%' }"
                  placeholder="请选择核查时间"
                  :picker-options="pickerOptions"
                  clearable 
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="核查单位" prop="" class="input-btn">
                <div class="list1">
                  <div
                    v-for="(item, index) of subjectOrgDiv"
                    :key="index"
                    class="list1-one"
                  >
                    <span>{{ item.checkOrgName }}</span>
                  </div>
                </div>
                <el-button
                  type="primary"
                  style="height: 28px"
                  plain
                  icon="el-icon-plus"
                  size="mini"
                  @click="addSubjectOrgDiv"
                >添加
                </el-button>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="核查主体" prop="checkSubject">
                <el-input
                  v-model="formData.checkSubject"
                  readonly="true"
                  placeholder="核查主体"
                  clearable
                  :style="{ width: '100%' }"
                />
              </el-form-item>
            </el-col>

            <el-col :span="8">
              <el-form-item
                label="需要进一步核查"
                prop="furtherVerificationFlag"
              >
                <el-radio-group
                  v-model="formData.furtherVerificationFlag"
                  size="medium"
                >
                  <el-radio
                    v-for="(item, index) in seriousAdverseEffectsFlagOptions"
                    :key="index"
                    :label="item.value"
                  >{{ item.label }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="需要外聘专业机构" prop="employMechanismFlag">
                <el-radio-group
                  v-model="formData.employMechanismFlag"
                  size="medium"
                >
                  <el-radio
                    v-for="(item, index) in seriousAdverseEffectsFlagOptions"
                    :key="index"
                    :label="item.value"
                  >{{ item.label }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>

            <el-col :span="8">
              <el-form-item
                label="需要专业部门参与"
                prop="professionalDepartFlag"
              >
                <el-radio-group
                  v-model="formData.professionalDepartFlag"
                  size="medium"
                >
                  <el-radio
                    v-for="(item, index) in seriousAdverseEffectsFlagOptions"
                    :key="index"
                    :label="item.value"
                  >{{ item.label }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>

            <el-col v-if="formData.furtherVerificationFlag == '1'" :span="24">
              <el-form-item
                label="审计部组织或委托机构驻派审计部核查"
                prop="auditCheckSituation"
              >
                <el-input
                  type="textarea"
                  v-model="formData.auditCheckSituation"
                  placeholder="请填写审计部组织或委托机构驻派审计部核查情况"
                  clearable
                  :style="{ width: '100%' }"
                />
              </el-form-item>
            </el-col>

            <el-col v-if="formData.employMechanismFlag == '1'" :span="24">
              <el-form-item
                label="外部机构参与情况"
                prop="employMechanismSituation"
              >
                <el-input
                  type="textarea"
                  v-model="formData.employMechanismSituation"
                  placeholder="请填写外部机构参与情况"
                  clearable
                  :style="{ width: '100%' }"
                />
              </el-form-item>
            </el-col>

            <el-col v-if="formData.professionalDepartFlag == '1'" :span="24">
              <el-form-item
                label="参与核查的专业部门及人员情况"
                prop="professionalDepartSituation"
              >
                <el-input
                  type="textarea"
                  v-model="formData.professionalDepartSituation"
                  placeholder="请填写参与核查的专业部门及人员情况"
                  clearable
                  :style="{ width: '100%' }"
                />
              </el-form-item>
            </el-col>

            <el-col :span="24">
              <el-form-item label="核查结果" prop="checkResult">
                <el-input
                  type="textarea"
                  v-model="formData.checkResult"
                  placeholder="请填写核查结果"
                  clearable
                  :style="{ width: '100%' }"
                />
              </el-form-item>
            </el-col>
          </el-form>
        </el-row>
      </BlockCard>

      <BlockCard title="损失及影响">
        <el-row>
          <el-form
            ref="elForm"
            :model="formData"
            :rules="rules"
            size="medium"
            label-width="160px"
          >
            <el-col :span="8">
              <el-form-item label="是否产生资产损失" prop="lossStateAssetsFlag">
                <el-radio-group
                  v-model="formData.lossStateAssetsFlag"
                  size="medium"
                  @change="lossStateAssetsChanged"
                >
                  <el-radio
                    v-for="(item, index) in YesOrNo"
                    :key="index"
                    :label="item.value"
                  >{{ item.label }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>

            <el-col :span="8">
              <el-form-item label="预估损失金额（万元）" prop="lossAmount">
                <el-input-number
                  v-model="formData.lossAmount"
                  :min="0"
                  :precision="2"
                  placeholder="预估损失金额（万元）"
                  controls-position="right"
                  :disabled="!formData.lossStateAssetsFlag"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="预估损失风险（万元）" prop="lossRisk">
                <el-input-number
                  v-model="formData.lossRisk"
                  :min="0"
                  :precision="2"
                  placeholder="预估损失风险（万元）"
                  controls-position="right"
                />
              </el-form-item>
            </el-col>

            <el-col :span="8">
              <el-form-item label="损失关联关系" prop="lossCategory">
                <el-select
                  v-model="formData.lossCategory"
                  placeholder="请选择损失关联关系"
                  clearable
                  :style="{ width: '100%' }"
                >
                  <el-option
                    v-for="(item, index) in formData.lossCategoryList"
                    :key="index"
                    :label="item.dictLabel"
                    :value="item.dictValue"
                  />
                </el-select>
              </el-form-item>
            </el-col>

            <el-col :span="24"/>
            <el-col :span="8">
              <el-form-item label="是否产生不良影响" prop="isAdverseEffect">
                <el-radio-group
                  v-model="formData.isAdverseEffect"
                  size="medium"
                  @change="isAdverseEffectChange"
                >
                  <el-radio
                    v-for="(item, index) in whetherEffectOptions"
                    :key="index"
                    :label="item.value"
                    :disabled="item.disabled"
                  >{{ item.label }}
                  </el-radio
                  >
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="16">
              <el-form-item
                label="对应不良影响"
                prop="correspondingAdverseEffects"
                v-if="formData.isAdverseEffect"
              >
                <el-select
                  v-model="formData.correspondingAdverseEffects"
                  :style="{ width: '100%' }"
                  clearable="clearable"
                  multiple="multiple"
                  value=""
                >
                  <el-option
                    v-for="(item, index) in dict.type
                      .corresponding_adverse_effect"
                    :key="index"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item
                label="造成的不良影响"
                prop="adverseEffects"
                v-if="formData.isAdverseEffect"
              >
                <el-input
                  v-model="formData.adverseEffects"
                  :style="{ width: '100%' }"
                  :autosize="{ minRows: 4, maxRows: 4 }"
                  type="textarea"
                  clearable
                />
              </el-form-item>
            </el-col>

            <el-col :span="8">
              <el-form-item
                label="是否产生严重不良影响"
                prop="seriousAdverseEffectsFlag"
              >
                <el-radio-group
                  v-model="formData.seriousAdverseEffectsFlag"
                  size="medium"
                  @change="seriousAdverseEffectsFlagChange"
                >
                  <el-radio
                    v-for="(item, index) in YesOrNo"
                    :key="index"
                    :label="item.value"
                    :disabled="item.disabled"
                  >{{ item.label }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col v-show="formData.seriousAdverseEffectsFlag" :span="16">
              <el-form-item
                label="严重不良影响描述"
                prop="seriousAdverseEffectsDesc"
              >
                <el-select
                  v-model="formData.seriousAdverseEffectsDesc"
                  placeholder="请选择严重不良影响描述"
                  clearable
                  :style="{ width: '100%' }"
                  value="formData.seriousAdverseEffectsDesc"
                >
                  <el-option
                    v-for="(item, index) in dict.type.VIOLD_ADVER_EFFECT_DES"
                    :key="index"
                    :label="item.label"
                    :value="item.value"
                    :disabled="item.disabled"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="24"/>
          </el-form>
        </el-row>
      </BlockCard>

      <BlockCard title="责任认定">
        <el-row>
          <el-form
            ref="elForm"
            :model="formData"
            :rules="rules"
            size="medium"
            label-width="110px"
          >
            <el-col :span="24">
              <el-form-item
                label="是否追究上一级有关人员责任"
                prop="investigateUpperlevelFlag"
              >
                <el-radio-group
                  v-model="formData.investigateUpperlevelFlag"
                  size="medium"
                >
                  <el-radio
                    v-for="(item, index) in seriousAdverseEffectsFlagOptions"
                    :key="index"
                    :label="item.value"
                    :disabled="item.disabled"
                  >{{ item.label }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>

            <el-col :span="24" v-if="formData.investigateUpperlevelFlag">
              <el-form-item
                label="追究上一级有关人员责任原因"
                prop="upperlevelResponsibility"
              >
                <el-input
                  v-model="formData.upperlevelResponsibility"
                  type="textarea"
                  placeholder="请填写追究上一级有关人员责任原因"
                  :autosize="{ minRows: 4, maxRows: 4 }"
                  :style="{ width: '100%' }"
                />
              </el-form-item>
            </el-col>

            <!-- <el-col :span="24">
              <el-form-item
                label="涉及单位及人员的认定信息"
                prop="problemDescribe"
              >
                <el-button
                  type="primary"
                  class="float-right"
                  @click="department"
                >修改涉及/单位人员</el-button>
              </el-form-item>
            </el-col> -->

            <!--<el-col :span="24">-->
            <!--<el-form-item label="涉及单位/部门/人员" prop="field107">-->
            <!--<el-button type="primary" plain icon="el-icon-plus" size="mini" @click="treeOpen">添加部门人员</el-button>-->
            <!--</el-form-item>-->
            <!--</el-col>-->
            <!--<el-col :span="24">-->
            <!--<el-form-item>-->
            <!--<PersList-->
            <!--ref="pers"-->
            <!--edit="true"-->
            <!--:problem-id="problemId"-->
            <!--:relevant-table-id="formData.id"-->
            <!--:relevant-table-name="formData.relevantTableName"-->
            <!--/>-->
            <!--</el-form-item>-->
            <!--</el-col>-->

            <el-col :span="24">
              <el-form-item label="涉及单位及人员的认定信息">
                <el-button
                  type="primary"
                  plain
                  icon="el-icon-plus"
                  size="mini"
                  style="margin: 20px 0 10px"
                  @click="treeOpen"
                >添加部门人员
                </el-button>
                <el-collapse v-model="activeNames" accordion>
                  <el-collapse-item
                    v-for="(item, index) of list"
                    :key="item"
                    :title="item.involCompanyName"
                    :name="index"
                    style="position: relative"
                  >

                    <el-checkbox size="mini"
                                 v-model="item.mainFlag==='1'?true:false"
                                 class="right-btn"
                                 style="margin: 20px 0 10px"
                                 @change="saveMainCompany(item)"
                    >设为主责单位
                    </el-checkbox>
                    <div class="invol-company-content el-row">
                      <el-col :span="12" class="invol-company-content-1">
                        <el-form-item label="单位责任类型" prop="dutyType">
                          <el-radio-group v-model="item.dutyType" size="medium">
                            <el-radio
                              v-for="(
                                item2, index2
                              ) in item.involCompanyDutyList"
                              :key="index2"
                              :label="item2.dictValue"
                              @change="
                                involCompanyDutyListFun(item, item2.dictValue)
                              "
                            >{{ item2.dictLabel }}
                            </el-radio>
                          </el-radio-group>
                        </el-form-item>
                      </el-col>
                      <el-col :span="12" class="invol-company-content-2">


                        <el-button
                          class="float-right"
                          type="primary"
                          plain
                          icon="el-icon-plus"
                          size="mini"
                          @click="
                            departmentNew(
                              item.involCompany,
                              item.involProvCode,
                              item.involAreaCode
                            )
                          "
                        >修改涉及部门/人员
                        </el-button>

                        <el-button
                          class="float-right"
                          type="primary"
                          style="margin-right: 8px"
                          plain
                          icon="el-icon-plus"
                          size="mini"
                          @click="addNoWork(item)"
                        >添加不在职人员
                        </el-button>
                      </el-col>
                      <el-col :span="24" class="invol-company-content-3">
                        <div class="invol-company-content-3-ul">
                          <div
                            class="invol-company-content-3-li"
                            v-for="(obj, i) in item.involPersonList"
                          >
                            <div class="invol-company-content-li-header">
                              <span class="invol-company-header-left">{{
                                  obj.userName
                                }}</span>
                              <span
                                class="invol-company-header-right float-right"
                              >
                                <el-button
                                  size="mini"
                                  type="text"
                                  icon="el-icon-delete"
                                  @click="handleDel(item, obj)"
                                ></el-button>
                              </span>
                            </div>
                            <div class="invol-company-li-content el-row">
                              <div class="el-row invol-company-li-content-li">
                                <el-col :span="12">
                                  <div
                                    class="invol-company-content-label vio-file-border"
                                  >
                                    组织
                                  </div>
                                  <div
                                    class="invol-company-content-value vio-file-border"
                                  >
                                    {{ obj.involOrgName }}
                                  </div>
                                </el-col>
                                <el-col :span="12">
                                  <div
                                    class="invol-company-content-label vio-file-border"
                                  >
                                    职务
                                  </div>
                                  <div
                                    class="invol-company-content-value duan_text_style"
                                    style="display: flex;font-size: 12px;      padding: 0px 8px;  align-items: center;"
                                  >
                                    <el-input
                                      style="width: 80px"
                                      :ref="
                                        'enddateinput' +
                                        index +
                                        'tag1' +
                                        '&' +
                                        i
                                      "
                                      v-model="obj.postName"
                                      placeholder="请填写职务"
                                      size="mini"
                                      @blur="
                                        switchShow(
                                          i,
                                          'tag1',
                                          item.involPersonList,
                                          index
                                        )
                                      "
                                      @keyup.enter.native="$event.target.blur"
                                    />

                                    <el-select
                                      v-model="obj.cadreType"
                                      placeholder="请选择干部类型"
                                      style="width: 120px; margin-left: 8px"
                                      class="duan_select"
                                      @change="(val) => switchShow2(val, obj)"
                                    >
                                      <el-option
                                        v-for="(item, index) in dict.type
                                          .viold_cadre_type"
                                        :key="index"
                                        :label="item.label"
                                        :value="item.value"
                                      ></el-option>
                                    </el-select>

                                    <div style="padding-left: 5px;padding-right:5px;display:flex;align-items: center;">
                                      <span
                                        style="padding-right:5px;"
                                      >是否二级单位领导</span
                                      >
                                      <el-radio-group
                                        v-model="obj.isSecondUnitLeader"
                                        size="mini"
                                        @change="
                                          (val) =>
                                            saveSecondUnitLeaderOption(val, obj)
                                        "
                                      >
                                        <el-radio
                                          v-for="(item, index) in YesOrNo"
                                          :key="index"
                                          :label="item.value"
                                        >{{ item.label }}
                                        </el-radio>
                                      </el-radio-group>
                                    </div>
                                  </div>
                                </el-col>
                              </div>
                              <div class="el-row invol-company-li-content-li">
                                <el-col :span="12">
                                  <div
                                    class="invol-company-content-label vio-file-border"
                                  >
                                    责任类型
                                  </div>
                                  <div
                                    class="invol-company-content-value vio-file-border"
                                  >
                                    <el-radio-group
                                      :key="obj.dutyType"
                                      v-model="obj.dutyType[0]"
                                      size="medium"
                                    >
                                      <el-radio
                                        v-for="item4 in item.involvePersonDutyList"
                                        :key="item4.dictValue"
                                        :label="item4.dictValue"
                                        @change="checkbox(obj, item4.dictValue)"
                                      >{{ item4.dictLabel }}
                                      </el-radio>
                                    </el-radio-group>
                                  </div>
                                </el-col>
                                <el-col :span="12">
                                  <div
                                    class="invol-company-content-label vio-file-border"
                                  >
                                    责任认定原因标准
                                  </div>
                                  <div class="invol-company-content-value">
                                    <el-input
                                      :ref="
                                        'enddateinput' +
                                        index +
                                        'tag2' +
                                        '&' +
                                        i
                                      "
                                      v-model="obj.dutyReasonStandard"
                                      size="mini"
                                      placeholder="请填写原因"
                                      :style="{ width: '100%' }"
                                      @blur="
                                        switchShow(
                                          i,
                                          'tag2',
                                          item.involPersonList,
                                          index
                                        )
                                      "
                                      @keyup.enter.native="$event.target.blur"
                                    />
                                  </div>
                                </el-col>
                              </div>
                              <div class="el-row invol-company-li-content-li">
                                <el-col :span="12">
                                  <div
                                    class="invol-company-content-label vio-file-border"
                                  >
                                    是否适用从重处罚
                                  </div>
                                  <div
                                    class="invol-company-content-value vio-file-border"
                                  >
                                    <div
                                      style="display: flex; align-items: center"
                                    >
                                      <div style="padding: 0px 12px 0 0">
                                        <el-radio-group
                                          v-model="obj.heavierFlag"
                                          size="medium"
                                        >
                                          <el-radio
                                            v-for="(item5, index5) in radio2"
                                            :key="index5"
                                            :label="item5.value"
                                            @change="heavierFlagFun(obj)"
                                          >{{ item5.label }}
                                          </el-radio>
                                        </el-radio-group>
                                      </div>

                                      <div style="flex: 1">
                                        <el-input
                                          :ref="
                                            'enddateinput' +
                                            index +
                                            'tag3' +
                                            '&' +
                                            i
                                          "
                                          v-model="obj.heavierPunishReason"
                                          size="mini"
                                          placeholder="请填写原因"
                                          :style="{ width: '100%' }"
                                          @blur="
                                            switchShow(
                                              i,
                                              'tag3',
                                              item.involPersonList,
                                              index
                                            )
                                          "
                                          @keyup.enter.native="
                                            $event.target.blur
                                          "
                                        />
                                      </div>
                                    </div>
                                  </div>
                                </el-col>
                                <el-col :span="12">
                                  <div
                                    class="invol-company-content-label vio-file-border"
                                  >
                                    是否适用从轻处罚或免除处罚
                                  </div>
                                  <div class="invol-company-content-value">
                                    <div
                                      style="display: flex; align-items: center"
                                    >
                                      <div style="padding: 0px 12px">
                                        <el-radio-group
                                          v-model="obj.lighterImpunityFlag"
                                          size="medium"
                                        >
                                          <el-radio
                                            v-for="(item6, index6) in radio2"
                                            :key="index6"
                                            :label="item6.value"
                                            @change="
                                              lighterImpunityFlagFun(obj)
                                            "
                                          >{{ item6.label }}
                                          </el-radio>
                                        </el-radio-group>
                                      </div>
                                      <div
                                        style="flex: 1"
                                        @dblclick="
                                          changeEnddate(
                                            i,
                                            'tag4',
                                            item.involPersonList,
                                            index
                                          )
                                        "
                                      >
                                        <el-input
                                          :ref="
                                            'enddateinput' +
                                            index +
                                            'tag4' +
                                            '&' +
                                            i
                                          "
                                          v-model="obj.lighterImpunityReason"
                                          size="mini"
                                          placeholder="请填写原因"
                                          :style="{ width: '100%' }"
                                          @blur="
                                            switchShow(
                                              i,
                                              'tag4',
                                              item.involPersonList,
                                              index
                                            )
                                          "
                                          @keyup.enter.native="
                                            $event.target.blur
                                          "
                                        />
                                      </div>
                                    </div>
                                  </div>
                                </el-col>
                              </div>
                            </div>
                          </div>
                          <div>
                            <div class="vio-file-box">
                              <div class="vio-file-div el-row">
                                <div
                                  class="vio-file-type flex vio-file-border el-col el-col-4"
                                >
                                  <span>上传被处罚单位及人员意见</span>
                                </div>
                                <div class="vio-file-download flex vio-file-border el-col " style="width: 130px">
                                  <FileUpload
                                    v-for="(
                              item3, index3
                            ) of formData.expandFileTypeOptions"
                                    :key="index3"
                                    :isShowTip="showTip"
                                    fileUrl="/colligate/violFile/uploadViolFile"
                                    btnTitle="上传附件"
                                    :param="{
                              linkKey: 'a007',
                              fileType: item3.dictValue,
                              problemId: problemId,
                              busiTableId: formData.id,
                              busiTable: formData.relevantTableName,
                              flowKey: 'SupervisionDailyReport',
                              involCompany: item.involCompany,
                            }"
                                    @handleUploadSuccess="queryInvolveCompanyAndPerson"
                                  >
                                  上传附件
                                  </FileUpload>
                                </div>
                                <div class="vio-file-content el-col el-col-16">
                                  <ul class="vio-file-list">
                                    <div
                                      class="vio-file-li ry-row flex el-row"
                                      v-for="(obj, i) in item.fileList"
                                    >
                                      <div
                                        class="vio-file-name el-col el-col-13"
                                      >
                                        <i class="el-icon-tickets"></i
                                        ><span>{{ obj.fileName }}</span>
                                      </div>
                                      <div
                                        class="vio-file-user icon-grey el-col el-col-2"
                                      >
                                        <span>{{ obj.uploaderName }}</span>
                                      </div>
                                      <div
                                        class="vio-file-time layui-col-md3 layui-col-sm3 icon-grey el-col el-col-5"
                                      >
                                        {{ obj.createTime }}
                                      </div>
                                      <div
                                        class="vio-file-del layui-col-md2 layui-col-sm2 text-center el-col el-col-4"
                                      >
                                        <a
                                          href="javascript:void(0);"
                                          title="下载"
                                          class="table-btn tip-edit"
                                          @click="fileDownload(obj)"
                                        >
                                          <i class="el-icon-bottom"></i>
                                        </a>
                                        <a
                                          href="javascript:void(0);"
                                          title="删除"
                                          class="table-btn tip-edit"
                                          @click="DelFile(item, obj)"
                                        >
                                          <i class="el-icon-delete"></i>
                                        </a>
                                      </div>
                                    </div>
                                  </ul>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </el-col>
                    </div>
                  </el-collapse-item>
                </el-collapse>
              </el-form-item>
            </el-col>
          </el-form>
        </el-row>
      </BlockCard>

      <BlockCard title="附件列表">
        <File
          v-if="formData.id != '' && formData.relevantTableName != ''"
          :key="problemId || formData.id || formData.relevantTableName"
          ref="file"
          edit="true"
          :problem-id="problemId"
          :relevant-table-id="formData.id"
          :relevant-table-name="formData.relevantTableName"
          flow-type="VIOL_DAILY"
          problem-status="4"
          flowKey="SupervisionDailyReport"
        />
      </BlockCard>

      <!--修改记录-->
      <el-dialog
        :visible.sync="visibleModify"
        width="80%"
        append-to-body
        title="修改记录"
      >
        <modifyRecord
          v-if="visibleModify"
          ref="modify"
          edit="true"
          :key="problemId || relevantTableId || relevantTableName"
          :problemId="problemId"
          :relevantTableId="relevantTableId"
          :relevantTableName="relevantTableName"
          :problemStatus="4"
          @modifySave="modifySave"
          @modifySaveMainDept="modifySaveMainDept"
        ></modifyRecord>
        <div slot="footer" class="dialog-footer">
          <el-button size="mini" type="primary" @click="modifyClose">保存</el-button>
        </div>
      </el-dialog>
    </div>
    <el-dialog
      :visible.sync="visibleTree"
      width="90%"
      class="tree-body-dialog"
      :before-close="saveY"
      append-to-body
      title="人员选择"
    >
      <Tree
        v-if="visibleTree"
        ref="persTree"
        @save="saveY"
        :key="problemId || formData.id || formData.relevantTableName"
        :problemId="problemId"
        :relevantTableId="formData.id"
        :relevantTableName="formData.relevantTableName"
        :is-edit="true"
      ></Tree>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="closeTree">保存</el-button>
      </div>
    </el-dialog>
    <el-dialog
      :visible.sync="fourTreeVisible"
      :before-close="queryInvolveCompanyAndPerson"
      width="90%"
      class="tree-body-dialog"
      append-to-body
      title="人员选择"
    >
      <FourTree
        ref="fourTreeFun"
        @save="queryInvolveCompanyAndPerson"
        :key="problemId || formData.id || formData.relevantTableName"
        :problemId="problemId"
        :relevantTableId="formData.id"
        :relevantTableName="formData.relevantTableName"
      ></FourTree>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="closeTreeFour">保存</el-button>
      </div>
    </el-dialog>
    <el-dialog
      :visible.sync="VisibleCheckTree"
      width="60%"
      append-to-body
      :title="title"
    >
      <CheckTree
        v-if="VisibleCheckTree"
        :key="selectTree"
        ref="checkTree"
        :url="url"
        :selectTree="selectTree"
        :params="params"
        @list="persList"
      />
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="savePers">保存</el-button>
      </div>
    </el-dialog>
    <el-dialog
      :visible.sync="VisibleRadioTree"
      width="60%"
      append-to-body
      title="核查单位"
    >
      <RadioTree
        v-if="VisibleRadioTree"
        :key="problemId"
        ref="radioTree"
        url="/colligate/violDailyCheck/getOrgTree"
        :selectTree="selectTree"
        :params="params"
        @accept="orgList"
      />
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="getOrgTree">保存</el-button>
      </div>
    </el-dialog>

    <el-dialog
      :visible.sync="visibleFormDuty"
      width="40%"
      append-to-body
      title="人员岗位信息"
      classs="noWorkStyle"
      v-if="visibleFormDuty"
    >
      <OffDutyPersonnel
        :closeBtn="closeBtnDuty"
        :involve-dept-id="involveDeptId"
        :rowData="dutyRowData"
      />
    </el-dialog>

    <el-dialog
      :visible.sync="VisibleRadioTree2"
      width="60%"
      v-if="VisibleRadioTree2"
      append-to-body
      :title="title"
      @close="closeVisibleRadioTree2"
    >
      <radio-tree-2
        :key="problemId"
        ref="radioTree"
        :url="url"
        :select-tree="selectTree"
        @accept="acceptList"
      />
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="savePers2">保存</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import BlockCard from "@/components/BlockCard";
import Remind from "./../../components/remind";
import ScopeSituation from "./../scopeSituation/scopeSituationData"; // 范围情形展示
import {checkInvolve, deleteViolFile, generateInvolveItemModifyRecord} from "@/api/components/index";
import {deleteDailyHandoverRecord, saveHandoverInspectionRecord,} from "@/api/daily/process/taskTodoViewDisposal";
import OffDutyPersonnel from "./Off-duty-personnel";
import {
  cancelMainCompany,
  changeCadreType,
  checkAndSaveCheckInfo,
  delCheckGroupMember,
  delPersonInvolved,
  queryDailyCheckInfo,
  queryInvolveCompanyAndPerson,
  queryRelevpersonByType,
  saveCheckGroupMember,
  saveCheckInfo,
  saveCompanyDuty,
  saveMainCompany,
  savePersonPostName,
  saveSecondUnitLeaderOption
} from "@/api/daily/process/verificationRecord";
import File from "./../../components/fileUpload"; // 附件
import Tree from "./../tree"; // tree
import FourTree from "./../tree/fourIndex";
import PersList from "./../tree/persList"; // tree
import CheckTree from "./../tree/checkTree"; // checkTree
import radioTree2 from "./../tree/radioTree2";

import RadioTree from "./../tree/radioTree"; //tree
import {generateBusinessModifyRecord, generateSituationRangeModifyRecord,} from "@/api/daily/modifyRecord/modifyRecord"; //修改记录js方法
import modifyRecord from "./../modifyRecord"; //修改记录

export default {
  dicts: [
    "VIOLD_DAILY_SPEC",
    "VIOLD_ADVER_EFFECT_DES",
    "corresponding_adverse_effect",
    "viold_cadre_type",
  ],
  components: {
    Remind,
    Tree,
    FourTree,
    BlockCard,
    ScopeSituation,
    File,
    PersList,
    CheckTree,
    radioTree2,
    RadioTree,
    modifyRecord,
    OffDutyPersonnel,
  },
  props: {
    edit: {
      type: Boolean,
      default: false,
    },
    problemId: {
      type: String,
      default: () => {
      },
    },
    key: {
      type: String,
      default: () => {
      },
    },
  },
  data() {
    return {
      isHandoverInspectionDept: "",
      visibleModify: false,
      url: "/colligate/violDailyRelevperson/checkStaffOrgTree",
      selectTree: [],
      title: "",
      showTip: false,
      index: 1,
      params: {
        name: "",
        problemId: "",
        relevantTableId: "",
        relevantTableName: "",
        relevorgType: "CHECK_GROUP_LEADER",
      },
      rowData: "",
      involCompanyDuty: "", // 单位责任类型
      flag: false,
      fourTreeVisible: false,
      visible: false,
      visibleTree: false,
      VisibleCheckTree: false,
      VisibleRadioTree: false,
      formData: {
        actualFlag: "",
        adverseEffects: "",
        adverseEffectsDescList: [],
        applicableInvestigateExplain: "",
        auditCheckSituation: "",
        auditCode: "",
        checkGroupLeaderList: [],
        checkGroupMemberList: [],
        checkOrgId: "",
        checkOrgLevel: "",
        checkOrgName: "",
        checkResult: "",
        checkSubject: "",
        checkTime: "",
        acceptTime: "",
        employMechanismFlag: "",
        employMechanismSituation: "",
        expandFileTypeOptions: [],
        furtherVerificationFlag: "",
        id: "",
        investigateUpperlevelFlag: "",
        lossAmount: "",
        lossCategory: "",
        lossCategoryList: [],
        lossRisk: "",
        problemCode: "",
        problemDescribe: "",
        problemId: "",
        problemTitle: "",
        professionalDepartFlag: "",
        professionalDepartSituation: "",
        relevantTableName: "",
        seriousAdverseEffectsDesc: undefined,
        seriousAdverseEffectsFlag: "",
        specLists: [],
        specList: [],
        specSelectedList: [],
        upperlevelResponsibility: "",
        lossStateAssetsFlag: "",
        isHandoverInspectionDept: "",
        isAdverseEffect: "",
        correspondingAdverseEffects: []
      },
      list: [], // 涉及单位数据
      mainPassFlag: '1',//主要单位是否审批通过
      specList: [],
      rules: {},
      seriousAdverseEffectsFlagOptions: [
        {
          label: "是",
          value: 1,
        },
        {
          label: "否",
          value: 0,
        },
      ],
      radio2: [
        {
          label: "是",
          value: "1",
        },
        {
          label: "否",
          value: "0",
        },
      ],
      YesOrNo: [
        {
          label: "是",
          value: 1,
        },
        {
          label: "否",
          value: 0,
        },
      ],
      whetherEffectOptions: [
        {
          label: "是",
          value: 1,
        },
        {
          label: "否",
          value: 0,
        },
      ],
      problemSourceList: [],
      activeNames: [],
      leaderList: [],
      memberList: [],
      relevantTableId: "",
      relevantTableName: "",
      DailyHandoverRecordId: "",
      InspectionHandoverId: "",
      treeNew: 1,
      type2: 1,
      visibleFormDuty: false,
      dutyRowData: {},
      VisibleRadioTree2: false,
    //检查时间选择处理
    pickerOptions: {
        disabledDate: (time) => {
          // 禁用所有早于 acceptTime 受理时间的日期（不包括受理时间）
          const acceptTimestamp = new Date(this.formData.acceptTime);
          acceptTimestamp.setHours(0, 0, 0, 0);
          return time.getTime() < acceptTimestamp
        }
      },
    };
  },
  computed: {},
  watch: {},
  created() {
    this.queryDailyCheckInfo();
  },
  mounted() {
  },
  methods: {
    lossStateAssetsChanged() {
      if (this.formData.lossStateAssetsFlag !== undefined && this.formData.isAdverseEffect !== undefined) {
        if (!this.formData.lossStateAssetsFlag && !this.formData.isAdverseEffect) {
          this.$message.error("【是否产生资产损失】与【是否产生不良影响】全部选择“否”不符合填报规则！");
          this.formData.lossStateAssetsFlag = 1;
        }
      }
      if (!this.formData.lossStateAssetsFlag) {
        this.formData.lossAmount = 0
      }
    },
    isAdverseEffectChange(val) {
      if (this.formData.lossStateAssetsFlag !== undefined && this.formData.isAdverseEffect !== undefined) {
        if (!this.formData.lossStateAssetsFlag && !this.formData.isAdverseEffect) {
          this.$message.error("【是否产生资产损失】与【是否产生不良影响】全部选择“否”不符合填报规则！");
          this.formData.isAdverseEffect = 1;
        }
      }

      if (!val) {
        this.formData.seriousAdverseEffectsFlag = "";
      }
    },
    addNoWork(item) {
      console.log("添加不在职人员入参：", item);
      this.visibleFormDuty = true;
      this.dutyRowData = item;
    },
    closeBtnDuty() {
      this.visibleFormDuty = false;
      this.queryInvolveCompanyAndPerson();
    },
    saveSecondUnitLeaderOption(val, obj) {
      saveSecondUnitLeaderOption({id: obj.id, isSecondUnitLeader: val}).then(
        (response) => {
        }
      );
    },
    // 删除移交纪检部门
    changeDel(value) {
      this.formData.isHandoverInspectionDept = value;
      if (value == 0) {
        deleteDailyHandoverRecord(this.InspectionHandoverId).then(
          (response) => {
            if (response.code == 200) {
              this.DailyHandoverRecordId = response.data.id;
              if (this.formData.initialInspectionHandoverObj) {
                this.formData.initialInspectionHandoverObj.toUnitName = "";
                this.formData.initialInspectionHandoverObj.toUserName = "";
              } else if (
                this.formData.inspectionHandoverRecords &&
                this.formData.inspectionHandoverRecords[
                this.formData.inspectionHandoverRecords.length - 1
                  ].editEnable
              ) {
                this.formData.inspectionHandoverRecords[
                this.formData.inspectionHandoverRecords.length - 1
                  ].toUnitName = "";
                this.formData.inspectionHandoverRecords[
                this.formData.inspectionHandoverRecords.length - 1
                  ].toUserName = "";
                this.formData.inspectionHandoverRecords[
                this.formData.inspectionHandoverRecords.length - 1
                  ].fileName = "";
              }
            }
          }
        );
      }
    },
    seriousAdverseEffectsFlagChange() {
      if (this.formData.isAdverseEffect == "0") {
        if (this.formData.seriousAdverseEffectsFlag == "1") {
          this.$message.warning(
            "是否产生不良影响在选择否的情况下，是否产生严重不良影响只能选择否！"
          );
          this.formData.seriousAdverseEffectsFlag = 0;
        }
      }
    },
    /**下载文件*/
    fileDownload(obj) {
      this.download(
        "/sys/attachment/downloadSysAttachment/" + obj.attachmentId,
        {},
        obj.fileName
      );
    },
    //保存人员
    savePers() {
      this.$refs.checkTree.list();
    },
    savePers2() {
      this.$refs.radioTree.save();
    },
    //返回数据
    persList(data) {
      let list = [];
      this.index++;
      if (!data.length) return false;
      for (let i = 0; i < data.length; i++) {
        list.push(Number(data[i].id));
      }
      this.formData.checkGroupLeaderList = data;
      let query = {postIds: list};
      this.params.personType = this.params.relevorgType;
      saveCheckGroupMember({...query, ...this.params}).then((response) => {
        const {code, data} = response;
        if (code === 200) {
          this.QueryRelevpersonByType();
          this.VisibleCheckTree = false;
        }
      });
    },
    //查询组长与组员
    QueryRelevpersonByType() {
      //组长
      queryRelevpersonByType({
        problemId: this.problemId,
        relevantTableId: this.formData.id,
        relevantTableName: this.formData.relevantTableName,
        personType: "CHECK_GROUP_LEADER",
      }).then((response) => {
        const {code, data} = response;
        if (code === 200) {
          this.leaderList = data;
        }
      });
      //组员
      queryRelevpersonByType({
        problemId: this.problemId,
        relevantTableId: this.formData.id,
        relevantTableName: this.formData.relevantTableName,
        personType: "CHECK_GROUP_MEMBER",
      }).then((response) => {
        const {code, data} = response;
        if (code === 200) {
          this.memberList = data;
        }
      });
    },
    // 删除核查人员与组长
    deleteCheckGroupLeaderList(item, type) {
      delCheckGroupMember({
        ...item,
        ...{
          problemId: this.problemId,
          relevantTableId: this.formData.id,
          relevantTableName: this.formData.relevantTableName,
          personType: type ? "CHECK_GROUP_LEADER" : "CHECK_GROUP_MEMBER",
        },
      }).then((response) => {
        const {code, data} = response;
        if (code === 200) {
          this.QueryRelevpersonByType();
        }
      });
    },
    //选择人员
    treeOpen() {
      this.flag = !this.flag;
      this.visibleTree = true;
    },
    // 请求数据详情
    queryDailyCheckInfo() {
      queryDailyCheckInfo({problemId: this.problemId}).then((response) => {
        const {code, data} = response;
        if (code === 200) {
          var list = [];
          list = Object.assign(this.formData, data);
          const array = [];
          const specSelectedList = data.specSelectedList;
          for (let i = 0, len = specSelectedList.length; i < len; i++) {
            if (specSelectedList[i].specCode) {
              array.push(specSelectedList[i].specCode);
            }
          }
          this.params.problemId = this.problemId;
          this.params.relevantTableId = data.id;
          this.params.relevantTableName = data.relevantTableName;
          this.formData.specLists = array;
          this.formData = list;
          this.problemId = this.problemId;
          this.relevantTableId = data.id;
          this.relevantTableName = data.relevantTableName;
          this.specList = data.specList;
          this.QueryRelevpersonByType();
          this.$forceUpdate();
          this.queryInvolveCompanyAndPerson();
          if (this.formData.checkOrgName) {
            this.subjectOrgDiv = [{checkOrgName: this.formData.checkOrgName}];
          } else {
            this.subjectOrgDiv = [];
          }
          this.$nextTick(() => {
            //   this.$refs.pers.DueryDepartmentSelectInfo()
            this.$refs.file.ViolationFileItems();
          });

          // 新增加
          if (!data.initialInspectionHandoverObj) {
            if (
              data.inspectionHandoverRecords &&
              data.inspectionHandoverRecords.length
            ) {
              this.InspectionHandoverId =
                data.inspectionHandoverRecords[
                data.inspectionHandoverRecords.length - 1
                  ].id;
            }
          } else {
            this.InspectionHandoverId = data.initialInspectionHandoverObj.id;
          }

          if (!data.initialSecondaryHandoverObj) {
            if (
              data.secondaryHandoverRecords &&
              data.secondaryHandoverRecords.length
            ) {
              if (
                data.secondaryHandoverRecords[
                data.secondaryHandoverRecords.length - 1
                  ].editEnable
              ) {
                this.DailyHandoverRecordId =
                  data.secondaryHandoverRecords[
                  data.secondaryHandoverRecords.length - 1
                    ].id;
              } else {
              }
            }
          } else {
            this.DailyHandoverRecordId = data.initialSecondaryHandoverObj.id;
          }
          // 新增加

          this.$emit("closeLoading");
        }
      });
    },
    // 下载
    downloadLink(url, fileName) {
      this.download(url, {}, fileName);
    },
    // 组织树选择
    BstaffOrgTree(type, name) {
      this.treeNew = 2;
      this.type2 = type;
      this.title = "组织选择";
      this.url = "/colligate/violationDisposal/inspectionStaffOrgTree";
      if (name) {
        this.selectTree = [{name: name}];
      } else {
        this.selectTree = [];
      }
      this.VisibleRadioTree2 = true;
    },
    closeVisibleRadioTree2() {
      this.VisibleRadioTree2 = false;
    },
    // 调用保存
    acceptList(array) {
      this.VisibleRadioTree2 = false;
      if (array[0].id) {
        saveHandoverInspectionRecord({
          toUserPost: array[0].id,
          businessTableId: this.relevantTableId,
          id: this.InspectionHandoverId,
          problemId: this.problemId,
        }).then((response) => {
          if (response.code === 200) {
            if (this.formData.inspectionHandoverRecords.length > 0) {
              this.formData.inspectionHandoverRecords.forEach((item, i) => {
                if (item.id == response.data.id) {
                  item = response.data
                  this.$set(this.formData.inspectionHandoverRecords, i, response.data)
                  this.$forceUpdate()
                }
              })
            }
            // if (this.type2 === 1) {
            //   this.formData.initialInspectionHandoverObj.toUnitName =
            //     response.data.toUnitName;
            //   this.formData.initialInspectionHandoverObj.toUserName =
            //     response.data.toUserName;
            // } else {
            //   this.formData.inspectionHandoverRecords[
            //     this.formData.inspectionHandoverRecords.length - 1
            //   ].toUnitName = response.data.toUnitName;
            //   this.formData.inspectionHandoverRecords[
            //     this.formData.inspectionHandoverRecords.length - 1
            //   ].toUserName = response.data.toUserName;
            //   this.formData.inspectionHandoverRecords[
            //     this.formData.inspectionHandoverRecords.length - 1
            //   ].fileName = "";
            // }
            // this.formData.inspectionDeptCode = response.data.toDeptCode;
            // this.formData.inspectionDeptName = response.data.toDeptName;
            // this.formData.inspectionDeptReceiverName = response.data.toUserName;
            // this.formData.inspectionDeptReceiverPost = response.data.toUserPost;
          }
        });
      }
    },
    // 涉及单位查询
    queryInvolveCompanyAndPerson() {
      this.fourTreeVisible = false;
      queryInvolveCompanyAndPerson({
        problemId: this.problemId,
        relevantTableId: this.formData.id,
      }).then((response) => {
        const {code, data} = response;
        if (code === 200) {
          this.activeNames = [];
          data.forEach((ele1, index) => {
            this.activeNames.push(index);
            ele1.involPersonList.forEach((ele2) => {
              if (ele2.dutyType === null) {
                ele2.dutyType = [];
              } else {
                ele2.dutyType = [ele2.dutyType];
              }
            });
          });
          this.list = data;
          console.info(this.list.length);
          if (this.list.length > 0) {
            this.mainPassFlag = this.list[0].mainPassFlag;
          }
          console.info(this.mainPassFlag);

        }
      });
    },
    addTdClass({row, column}) {
      if (
        column.label === "职务" ||
        column.label === "责任认定原因标准" ||
        column.label === "是否适用从重处罚" ||
        column.label === "是否适用从轻处罚或免除处罚"
      ) {
        return "editStyle";
      }
    },
    // 切换input框的显示状态
    switchShow(index, tag, list, bigindex) {
      switch (tag) {
        case "tag1":
          list[index].is_show_tag1 = !list[index].is_show_tag1;
          break;
        case "tag2":
          list[index].is_show_tag2 = !list[index].is_show_tag2;
          break;

        case "tag3":
          list[index].is_show_tag3 = !list[index].is_show_tag3;
          break;

        case "tag4":
          list[index].is_show_tag4 = !list[index].is_show_tag4;
          break;
      }
      list = [...list];
      this.list.forEach((ele, index) => {
        if (bigindex === index) {
          ele.involPersonList = list;
        }
      });
      this.savePersonPostName(list[index]);
      this.$forceUpdate();
    },
    switchShow2(value, item) {
      let parameter = {
        id: item.id,
        cadreType: value
      };
      changeCadreType(parameter).then(res => {
        if (200 === res.code) {
        }
      });
    },
    openDuty(deptId) {
      this.involveDeptId = deptId;
      this.rowData = {};
      this.visibleForm = true;
    },
    // 显示input框, 使光标焦点当前input框
    changeEnddate(index, tag, list, bigindex) {
      this.switchShow(index, tag, list, bigindex);
      setTimeout(() => {
        this.$refs["enddateinput" + bigindex + tag + "&" + index].focus();
      }, 1);
    },
    //   是否适用从重处罚"
    heavierFlagFun(row) {
      this.savePersonPostName(row);
    },
    // 是否适用从轻处罚或免除处罚
    lighterImpunityFlagFun(row) {
      this.savePersonPostName(row);
    },
    handleDel(item, row) {
      this.$confirm("是否确认删除该涉及人员？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          let parameter = {
            problemId: row.problemId,
            relevantTableId: row.relevantTableId,
            relevantTableName: row.relevantTableName
          };
          if (!row.postId) {
            parameter.involvePersonId = row.id;
          } else {
            parameter.personList = [{id: row.postId}]
          }
          delPersonInvolved(parameter).then((response) => {
            const {code, msg} = response;
            if (code === 200) {
              this.$message({
                type: "success",
                message: msg,
              });
              this.$forceUpdate();
              this.queryInvolveCompanyAndPerson();
            }
          });
        })
        .catch(() => {
        });
    },
    //关闭弹窗
    closeTree() {
      this.$refs.persTree.CheckInvolve(); //校验
    },
    //关闭
    closeTreeFour() {
      this.$refs.fourTreeFun.CheckInvolve(); //校验
    },
    saveY() {
      this.visibleTree = false;
      this.fourTreeVisible = false;
      this.queryInvolveCompanyAndPerson();
    },
    // 保存职务
    savePersonPostName(row) {
      var rowData = JSON.parse(JSON.stringify(row));
      rowData.dutyType = rowData.dutyType[0];
      savePersonPostName(rowData).then((response) => {
      });
    },
    // 选择单位责任类型
    involCompanyDutyListFun(item, val) {
      this.saveCompanyDuty(item, val);
    },
    // 保存单位责任类型
    saveCompanyDuty(item, val) {
      saveCompanyDuty({
        dutyType: val,
        involCompany: item.involCompany,
        problemId: item.problemId,
        relevantTableId: item.relevantTableId,
      }).then((response) => {
      });
    },
    // 选择责任类型
    checkbox(row, item) {
      this.rowData = JSON.parse(JSON.stringify(row));
      row.dutyType = row.dutyType.includes(item) ? [item] : [];
      this.rowData.dutyType = item;
      this.$forceUpdate();
      this.savePersonPostName(this.rowData);
    },
    //校验单位、部门、人员
    async CheckInvolve() {
      let final = true;
      if (!this.formData.lossStateAssetsFlag && !this.formData.isAdverseEffect) {
        this.$message.error("【是否产生资产损失】与【是否产生不良影响】全部选择“否”不符合填报规则！");
        return false;
      }
      await checkInvolve(this.problemId).then((response) => {
        if (response.code == 200) {
          let companyString = "", //单位列表
            deptString = ""; //部门列表
          if (response.data.resultCode == "false") {
            this.$message.error("请选择单位！");
            final = false;
          } else if (response.data.resultCode == "company") {
            companyString = "";
            for (let i = 0; i < response.data.listCompany.length; i++) {
              companyString +=
                "【" + response.data.listCompany[i].INVOL_COMPANY_NAME + "】";
              if (i + 1 != response.data.listCompany.length) {
                companyString += "、";
              }
            }
            companyString = "" + companyString + "下未选择涉及部门！";
            this.$message.error(companyString);
            final = false;
          } else if (response.data.resultCode == "double") {
            companyString = "";
            deptString = "";
            for (let i = 0; i < response.data.listCompany.length; i++) {
              companyString +=
                "【" + response.data.listCompany[i].INVOL_COMPANY_NAME + "】";
              if (i + 1 != response.data.listCompany.length) {
                companyString += "、";
              }
            }
            for (let i = 0; i < response.data.listDept.length; i++) {
              deptString +=
                "【" + response.data.listDept[i].INVOL_ORG_NAME + "】";
              if (i + 1 != response.data.listDept.length) {
                deptString += "、";
              }
            }
            companyString = "1、" + companyString + "下未选择涉及部门！<br/>";
            deptString = "2、" + deptString + "下未选择涉及人员！";
            this.$message.error(companyString + deptString);
            final = false;
          } else if (response.data.resultCode == "dept") {
            deptString = "";
            for (let i = 0; i < response.data.listDept.length; i++) {
              deptString +=
                "【" + response.data.listDept[i].INVOL_ORG_NAME + "】";
              if (i + 1 != response.data.listDept.length) {
                deptString += "、";
              }
            }
            deptString = deptString + "下未选择涉及人员！";
            this.$message.error(deptString);
            final = false;
          } else {
            generateInvolveItemModifyRecord({
              problemId: this.problemId,
              businessId: this.relevantTableId,
            }).then((response) => {
            });
            final = true;
          }
        }
      });
      return final;
    },
    /** 提交数据*/
    async nextStep() {
      let volve = await this.CheckInvolve();
      if (!volve) {
        return false;
      }
      console.log(volve)
      this.formData.specList = [];
      const array = [];
      const specSelectedList = this.formData.specLists;
      const specList = this.specList;
      for (let i = 0, len = specSelectedList.length; i < len; i++) {
        for (let j = 0, leng = specList.length; j < leng; j++) {
          if (specList[j].dictValue === specSelectedList[i]) {
            specList[j].specCode = specList[j].dictValue;
            specList[j].specName = specList[j].dictLabel;
            array.push(specList[j]);
          }
        }
      }
      this.formData.specSelectedList = array;
      checkAndSaveCheckInfo(this.formData).then((response) => {
        let data = response.data;
        if (!data.validateFlag) {
          this.$modal.msgError(data.validateMsg);
        } else {
          this.$modal.msgSuccess("保存成功");
          this.Modify();
        }
      });
    },
    /** 保存数据*/
    publicSave() {
      this.formData.specList = [];
      const array = [];
      const specSelectedList = this.formData.specLists;
      const specList = this.specList;
      for (let i = 0, len = specSelectedList.length; i < len; i++) {
        for (let j = 0, leng = specList.length; j < leng; j++) {
          if (specList[j].dictValue === specSelectedList[i]) {
            specList[j].specCode = specList[j].dictValue;
            specList[j].specName = specList[j].dictLabel;
            array.push(specList[j]);
          }
        }
      }
      this.formData.specSelectedList = array;
      saveCheckInfo(this.formData).then((response) => {
        this.$modal.msgSuccess("保存成功");
      });
    },
    resetForm() {
      this.$refs["elForm"].resetFields();
    },
    // 打开弹窗
    show() {
      this.visible = true;
    },

    // 添加核查组组长
    addCheckGroupLeaderList(type) {
      this.selectTree = [];
      if (type === 1) {
        this.title = "核查组组长";
        this.params.relevorgType = "CHECK_GROUP_LEADER";
        this.selectTree = this.leaderList;
      } else if (type === 2) {
        this.title = "核查组成员";
        this.params.relevorgType = "CHECK_GROUP_MEMBER";
        this.selectTree = this.memberList;
      }
      this.VisibleCheckTree = true;
    },
    // 删除核查组成员
    deleteCheckGroupMemberList(item, index) {
      this.formData.checkGroupMemberList.splice(index, 1);
      this.$forceUpdate();
    },
    // 删除核查单位
    deleteSubjectOrgDiv(item, index) {
      this.formData.subjectOrgDiv.splice(index, 1);
      this.$forceUpdate();
    },
    // 添加核查单位
    addSubjectOrgDiv() {
      this.selectTree = [
        {name: this.formData.checkOrgName, id: this.formData.checkOrgId},
      ];
      this.VisibleRadioTree = true;
    },
    //保存核查单位
    getOrgTree() {
      this.$refs.radioTree.save();
    },
    //返回核查单位
    orgList(data) {
      let obj = data[0];
      if (data) {
        this.formData.subjectOrgDiv = [
          {
            checkOrgId: obj.id,
            checkOrgName: obj.name,
            checkOrgLevel: obj.orgGrade,
            checkSubject: obj.checkSubject,
          },
        ];
        this.formData.checkOrgId = obj.checkOrgId;
        if (obj.checkSubject === "" || obj.checkSubject == null) {
          if (obj.orgGrade === "P" || obj.orgGrade === "G") {
            this.formData.checkSubject = obj.name + "违规责任办公室";
          } else if (obj.orgGrade === "A") {
            this.formData.checkSubject = obj.name + "核查小组";
          }
        } else {
          this.formData.checkSubject = obj.checkSubject;
        }
        this.formData.checkOrgName = obj.name;
        this.formData.checkOrgLevel = obj.orgGrade;
      }
      this.subjectOrgDiv = [{checkOrgName: this.formData.checkOrgName}];
      this.VisibleRadioTree = false;
    },
    // 涉及单位及人员的认定信息
    department() {
    },
    // 修改涉及/单位人员
    departmentNew(value, involProvCode, involAreaCode) {
      this.fourTreeVisible = true;
      this.$nextTick(() => {
        if (involProvCode === "1") {
          this.$refs.fourTreeFun.PidsFunction([value], [involAreaCode]);
        } else {
          this.$refs.fourTreeFun.PidsFunction([value], [value]);
        }
      });
    },
    // 上传被处罚单位及人员意见
    uploadFileCompany() {
    },
    //删除附件
    DelFile(item, data) {
      let title = "确认删除该附件吗？";
      this.$modal
        .confirm(title)
        .then(function () {
          return deleteViolFile(data.id);
        })
        .then(() => {
          this.$modal.msgSuccess("删除成功");
          this.queryInvolveCompanyAndPerson();
        })
        .catch(() => {
        });
    },
    //调用修改记录保存
    modifyClose() {
      this.$refs.modify.save();
    },
    //修改记录保存后
    modifySave(type) {
      if (type) {
        this.visibleModify = false;
        var processData = {
          updateMainFlag: '0',
          mainPassFlag: this.mainPassFlag
        };
        this.$emit("handle", 1, processData);
      } else {
        this.visibleModify = false;
      }
    },
    //修改记录保存后--修改了主责单位
    modifySaveMainDept(type) {
      if (type) {
        this.visibleModify = false;
        var processData = {
          updateMainFlag: '1',
          mainPassFlag: this.mainPassFlag
        };
        this.$emit("handle", 1, processData);
      } else {
        this.visibleModify = false;
      }
    },
    //修改记录
    Modify() {
      //生成情形范围修改记录
      generateSituationRangeModifyRecord(this.problemId, this.relevantTableId);
      //生成业务数据修改记录
      generateBusinessModifyRecord(
        this.problemId,
        this.relevantTableId,
        this.relevantTableName
      ).then((response) => {
        let isExistDifferenceField = response.data.isExistDifferenceField;
        if (isExistDifferenceField) {
          this.visibleModify = true;
        } else {
          var processData = {
            updateMainFlag: '0',
            mainPassFlag: this.mainPassFlag
          };
          this.$emit("handle", 1, processData);
        }
      });
    },
    //设置主责单位,先提示，再修改
    saveMainCompany(item) {
      this.$confirm('主责单位为必填，且修改后需经过【主责单位审批】环节，由上级单位进行审批，是否确定修改？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          //保存修改
          this.realSaveMainCompany(item);
        })
    },
    realSaveMainCompany(item) {
      if (item.mainFlag === '1') {
        item.mainFlag = '0'
        let params = {
          id: item.involCompanyId
        }
        cancelMainCompany(params).then(res => {
          if (res.code != 200) {
            Message.error(res.msg)
          }
        }).catch(err => {
          Message.error(err)
        })
      } else {
        this.$confirm('是否确定将涉及单位【' + item.involCompanyName + '】设为主责单位？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            let params = {
              id: item.involCompanyId,
              problemId: this.problemId,
              relevantTableId: this.formData.id,
            }
            saveMainCompany(params).then(res => {
              if (res.code == 200) {
                this.list.forEach((ele, index) => {
                  if (ele.involCompanyId === item.involCompanyId) {
                    ele.mainFlag = '1';
                  } else {
                    ele.mainFlag = '0';
                  }
                });
              } else {
                Message.error(res.msg)
              }
            }).catch(err => {
              Message.error(err)
            })
          })
      }
    }
  },
};
</script>
<style scoped lang="scss">
.file-label {
  display: inline-block;
  padding: 10px 0px;
}

.input-btn {
  ::v-deep .el-form-item__content {
    display: flex;

    button {
    }
  }
}

.float-right {
  float: right;
}

.right-btn {
  position: absolute;
  right: 55px;
  top: -15px;
}

.edit-span {
  white-space: normal;
  overflow-y: auto;
  overflow-wrap: break-word;
  word-break: normal;
  height: 61px;
  line-height: 30px;
  text-align: left;
  padding: 0px 10px;
  display: block;
}

::v-deep .el-collapse-item__header {
  height: 40px;
  background: #e6f7ff;
  border-radius: 1px;
  color: #4e98ff;
  padding: 0 16px;
  box-sizing: border-box;
}

::v-deep .editStyle {
  padding: 0px !important;
}

::v-deep .editStyle div.cell {
  padding: 0px !important;
}

::v-deep .editStyle .el-input--mini .el-input__inner {
  height: 56px;
  line-height: 56px;
  border: 0px;
}

.list1 {
  overflow: hidden;

  .list1-one {
    background-color: #e6f7ff;
    color: #40a9ff;
    margin: 0 10px 2px 10px;
    float: left;
    height: 30px;
    line-height: 30px;
    padding: 0 12px 0 12px;
    border-radius: 2px;

    .close {
      padding: 8px;
      cursor: pointer;
    }
  }
}

.bottom-line {
  padding: 10px 0;
  text-align: center;
  border-top: 1px solid #d9d9d9;
  color: #f5222d !important;
}

.invol-company-content {
  margin-top: 6px;
  background: #f6f7f8;
  padding: 7px 14px;
  box-sizing: border-box;

  ::v-deep .el-form-item__label {
    text-align: left !important;
    width: 97px !important;
  }

  ::v-deep .el-form-item__content {
    margin-left: 97px !important;
  }

  .invol-company-content-2 {
    margin: 4px 0;

    ::v-deep .el-button--primary {
      color: #f5222d;
      background: #fee9ea;
      border-color: #fba7ab;
    }

    ::v-deep .el-button--primary:hover {
      background: #f74e57;
      border-color: #f74e57;
      color: #fff;
    }
  }

  .invol-company-content-3 {
    background: #fff;
    padding: 12px 22px;
    box-sizing: border-box;

    .invol-company-content-3-ul {
      .invol-company-content-3-li {
        margin-bottom: 16px;

        .invol-company-content-li-header {
          position: relative;

          .invol-company-header-left {
            position: relative;
            display: inline-block;
            font-size: 15px;
            font-weight: bold;
            color: #333333;
            padding: 0 12px;
            background: #fff;
            z-index: 99;
          }

          .invol-company-header-left::before {
            content: " ";
            position: absolute;
            left: 0;
            right: 0;
            top: 5px;
            z-index: 2;
            width: 4px;
            height: 16px;
            background: #f5222d;
            opacity: 1;
          }

          .invol-company-header-right {
            .el-button--text {
              font-size: 16px;
            }
          }
        }

        .invol-company-content-li-header::before {
          content: " ";
          position: absolute;
          left: 0;
          right: 0;
          width: calc(100% - 26px);
          top: 13px;
          z-index: 2;
          height: 1px;
          background: #ced3d8;
        }

        .invol-company-li-content {
          border: 1px solid #d9d9d9;
          margin-top: 10px;

          .invol-company-li-content-li:last-child {
            border-bottom: 0 solid #d9d9d9;
          }

          .invol-company-li-content-li {
            border-bottom: 1px solid #d9d9d9;

            .vio-file-border {
              border-right: 1px solid #e2e7f1;
            }

            .invol-company-content-label {
              float: left;
              width: 150px;
              height: 46px;
              line-height: 18px;
              background: #f4f8fc;
              opacity: 1;
              padding: 0 10px 0 20px;
              box-sizing: border-box;
              font-size: 14px;
              font-weight: 400;
              color: #333333;
              display: flex;
              align-items: center;
            }

            .invol-company-content-value {
              float: left;
              width: calc(100% - 150px);
              height: 46px;
              line-height: 46px;
              padding: 0 22px;
              box-sizing: border-box;
            }
          }
        }
      }
    }

    .flex {
      display: flex;
      align-items: center;
    }

    .vio-file-box {
      border: 1px solid #d9d9d9;

      .vio-file-div {
        display: flex;
        width: 100%;
        border-bottom: 1px solid #d9d9d9;

        .vio-file-border {
          border-right: 1px solid #d9d9d9;
        }

        .vio-file-type {
          background-color: #f4f8fc;
          color: #73777a;
          min-height: 48px;
          padding: 0 10px;
          box-sizing: border-box;

          .text-red {
            color: #f5222d !important;
          }
        }

        .vio-file-download {
          justify-content: center;

          .vio-file-down {
            padding: 0 4px;
            border-right: 1px solid #d9d9d9;
          }

          i {
            color: #f5222d;
          }

          .vio-file-down:last-child {
            border-right-width: 0;
          }
        }

        .vio-file-content {
          min-height: 48px;

          .vio-file-list {
            padding: 0;
            margin: 0;

            .vio-file-li {
              padding-left: 10px;
              box-sizing: border-box;
              border-bottom: 1px solid #d9d9d9;
              min-height: 48px;

              .vio-file-name {
                i {
                  margin-right: 6px;
                }
              }

              .vio-file-user,
              .vio-file-time {
                height: 48px;
                display: flex;
                align-items: center;
                color: #a9b0b4;
              }

              .vio-file-del {
                text-align: center;

                i {
                  color: #f5222d;
                  margin: 0 6px;
                }
              }
            }

            .vio-file-li:last-child {
              border-bottom-width: 0;
            }
          }
        }
      }

      .vio-file-div:last-child {
        border-bottom-width: 0;
      }

      ::v-deep.upload-file-uploader {
        margin-bottom: 0;
      }
    }
  }
}

::v-deep .duan_select .el-input--medium .el-input__inner {
  height: 28px;
  line-height: 28px;
}

::v-deep .duan_text_style .el-radio {
  margin-right: 12px
}

::v-deep .duan_text_style .el-input--mini .el-input__inner {
  font-size: 12px;
  padding: 0px 10px;
}

::v-deep .duan_text_style .el-input {
  font-size: 12px;
}

::v-deep .el-radio-group {
  flex: 1
}

.tree-body-dialog ::v-deep.el-dialog__body {
  padding: 0 20px;
}
</style>
