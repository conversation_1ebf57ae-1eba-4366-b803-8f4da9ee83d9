// 添加 钱 .00
const addZero = function(value) {
  var value = Math.round(parseFloat(value) * 100) / 100
  var xsd = value.toString().split('.')
  if (xsd.length == 1) {
    value = value.toString() + '.00'
    return value
  }
  if (xsd.length > 1) {
    if (xsd[1].length < 2) {
      value = value.toString() + '0'
    }
    return value
  }
}


const fromatComon = function(value, list) {
  let lastLabel = '-'

  if (value && list.length > 0) {
    list.forEach(element => {
      if (element.value == value) {
        lastLabel = element.label
      }
    })
  }
  return lastLabel
}
const fromatComonRestaurantList = function(value, list) {
  let lastLabel = '-'
  if (value && list.length > 0) {
    list.forEach(element => {
      if (element.id === value) {
        lastLabel = element.restaurantName
      }
    })
  }
  return lastLabel
}
const fromatComonParking = function(value, list) {
  let lastLabel = '-'
  if (value && list.length > 0) {
    list.forEach(element => {
      if (element.id === value) {
        lastLabel = element.parkName
      }
    })
  }
  return lastLabel
}

const fromatComonBusinessType = function(value, list) {
  let lastLabel = '-'
  if (value && list.length > 0) {
    list.forEach(element => {
      if (element.id === value) {
        lastLabel = element.typeName
      }
    })
  }
  return lastLabel
}





const numFilter = function(value) {
  // 截取当前数据到小数点后两位
  const realVal = parseFloat(value).toFixed(2)
  return realVal
}

// 金额使用 转换为两位小数
const formatNum = function(numValue) {
  if (numValue == '' || numValue == null) {
    numValue = 0
  }
  if (numValue === '-') {
    return numValue
  }
  let value = Math.round(parseFloat(numValue) * 100) / 100
  const xsd = value.toString().split('.')
  if (xsd.length == 1) { value = value.toString() + '.00' }
  if (xsd.length > 1) { if (xsd[1].length < 2) { value = value.toString() + '0' } }
  return value
}

//转换展示两位小数，空值直接输出空值
const formatNumContainNull = function(numValue){
  if (numValue === '' || numValue == null) {
    return '';
  }
  let value=Math.round(parseFloat(numValue)*100)/100;
  const xsd=value.toString().split(".");
  if(xsd.length==1){ value=value.toString()+".00"; }
  if(xsd.length>1){  if(xsd[1].length<2){ value=value.toString()+"0"; } }
  return value;
}

export default {
  addZero,
  fromatComon,
  numFilter,
  formatNum,
  formatNumContainNull,
  fromatComonRestaurantList,
  fromatComonParking,
  fromatComonBusinessType
}

