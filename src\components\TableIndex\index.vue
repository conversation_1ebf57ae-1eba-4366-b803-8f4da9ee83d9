<template>
  <div>
    {{index+(pageNum-1)*pageSize+1}}
  </div>
</template>

<script>
    export default {
        name: "index",
      props: {
        index: {
          type: Number,
          default: 0
        },
        pageNum: {
          type: Number,
          default: 0
        },
        pageSize: {
          type: Number,
          default: 0
        },
      }
    }
</script>

<style scoped>

</style>
