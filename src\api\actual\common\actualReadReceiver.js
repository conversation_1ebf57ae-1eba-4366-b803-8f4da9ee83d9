import request from '@/utils/request';

/**
 * 查询违规追责实时报送待阅接收人分组数据
 * @param data
 */
export function actualReadReceiverGroupData(data) {
  return request({
    url: '/colligate/violActualReadReceiver/actualReadReceiverGroupData',
    method: 'post',
    data: data
  });
}

/**
 * 删除违规追责实时报送待阅接收人
 * @param id
 */
export function deleteActualReadReceiver(id) {
  return request({
    url: '/colligate/violActualReadReceiver/deleteActualReadReceiver/' + id,
    method: 'post'
  });
}

/**
 * 查询违规追责实时报送待阅接收人候选项
 * @param data
 */
export function actualReadReceiverCandidate(data) {
  return request({
    url: '/colligate/violActualReadReceiver/actualReadReceiverCandidate',
    method: 'post',
    data: data
  });
}

/**
 * 保存违规追责实时报送待阅接收人
 * @param data
 */
export function saveActualReadReceiver(data) {
  return request({
    url: '/colligate/violActualReadReceiver/saveActualReadReceiver',
    method: 'post',
    data: data
  });
}
