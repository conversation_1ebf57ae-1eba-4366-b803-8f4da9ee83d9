<!--定期已办页-->
<template>
  <div>
    <!--已办-->
    <BlockCard
      :title="cardName"
    >
      <div class="position">
        <!--<div class="float-left position-select" v-if="formData.startGrade === 'G' && formData.regularReportStatus !== 'other'">-->
          <!--<el-button v-if="exportBtn"  type="primary" plain icon="el-icon-plus" size="mini" @click="violDailyDetail">{{exportBtn}}</el-button>-->
        <!--</div>-->
        <div class="float-left position-select" v-if="formData.backNum > 0">
          <el-button  type="primary" plain icon="el-icon-search" size="mini" @click="showHistoryInfo">{{historyBtn}}</el-button>
        </div>
      </div>
      <el-form ref="elForm"  size="medium" label-width="118px">
        <el-row>
          <el-col :span="6">
            <el-form-item label="报告类型" >
              <span>{{formData.regularReportStatus=='other'?'其他报告':'定期报告'}}</span>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="上报年度" >
              <span>{{formData.reportYear}}</span>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="上报区间" >
              <span>{{formData.reportStartTime}} - {{formData.reportEndTime}}</span>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="上报截止日期" >
              <span>{{formData.reportCloseTime}}</span>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="标题">
              <span>{{formData.reportTitle}}</span>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="上报要求">
              <span>{{formData.reportRequire}}</span>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </BlockCard>
    <!--<BlockCard title="附件列表">
      <FileUpload
        :edit='false'
        :problemId="formData.reportUnitId"
        :relevantTableId="formData.reportUnitId"
        :relevantTableName="formData.businessTable"
        flowType="VIOL_REGULAR"
        :problemStatus="formData.regularReportStatus"
        :isNoReport = "formData.isNoReport"
        :problemsIds = "formData.problemsIds"
        ref="file"
        :flowKey = "centerVariable.flowKey"
      ></FileUpload>
    </BlockCard>-->
    <BlockCard title="模板列表">
      <RegularTemplateFileUpload :key="formData" :edit='false' :problemId="formData.reportUnitId" :relevantTableId="formData.reportUnitId"
                                 :relevantTableName="formData.businessTable" flowType="VIOL_REGULAR" :problemStatus="formData.regularReportStatus"
                                 :isNoReport="formData.isNoReport" :problemsIds="formData.problemsIds" linkKey="a003" ref="flowTemplate"
                                 :flowKey="centerVariable.flowKey">
      </RegularTemplateFileUpload>
    </BlockCard>
    <BlockCard
      v-if="false"
      title="定期报告附表"
    >
      <el-form ref="elForm" size="medium" label-width="118px">
        <el-row class="problem_list margin-b10">
          <el-col :span="24">
            <el-form-item label="是否0报告">
              <span>{{formData.isNoReport == '1' ? '是' : '否'}}</span>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <el-table
      :class="formData.regularProblems.length>10?'el-table-change':''"
      :height="formData.regularProblems.length>10?'650':''"
        border
        :data="formData.regularProblems"
        @selection-change="handleSelectionChange"
        ref="table"
      >
        <el-table-column
          fixed
          align="center"
          label="序号"
          type="index"
          width="50">
        </el-table-column>
        <el-table-column  align="center" label="上报单位" show-overflow-tooltip prop="reportUnitName" width="200"/>
        <el-table-column  align="center" label="系统编号" show-overflow-tooltip prop="auditCode" width="200"/>
        <el-table-column align="center" label="涉及企业名称" show-overflow-tooltip prop="involveEnterprise" width="200"/>
        <el-table-column align="center" label="企业层级" show-overflow-tooltip prop="enterpriseGrade" width="200"/>

        <el-table-column align="center" label="违规问题线索有关详情" show-overflow-tooltip prop="prov1" width="2000">
          <el-table-column align="center" label="问题线索来源" show-overflow-tooltip prop="problemSource" width="200"/>
          <el-table-column align="center" label="问题受理时间" show-overflow-tooltip prop="acceptTime" width="200"/>
          <el-table-column align="center" label="是否为以前年度定期报告反映的问题" show-overflow-tooltip prop="isPreviousYear" width="200"/>
          <el-table-column align="center" label="问题描述" prop="problemDescribe" show-overflow-tooltip width="200"/>
          <el-table-column align="center" label="问题类别" prop="problemAspect" show-overflow-tooltip width="200"/>
          <el-table-column align="center" label="违反具体规定" prop="problemSituation" show-overflow-tooltip width="200"/>
          <el-table-column align="center" label="境内（外）" prop="domesticOrForeign" show-overflow-tooltip width="200"/>
          <el-table-column align="center" label="涉及损失及风险（万元）" show-overflow-tooltip prop="lossAmountRisk" width="200"/>
          <el-table-column align="center" label="损失风险类别（一般/较大/重大资产损失）" show-overflow-tooltip prop="lossRiskType" width="200"/>
          <el-table-column align="center" label="损失形成主要原因" show-overflow-tooltip prop="lossReason" width="200"/>
        </el-table-column>

        <el-table-column align="center" label="核查情况" show-overflow-tooltip prop="prov2" width="800">
          <el-table-column align="center" label="核查状态" show-overflow-tooltip prop="checkStatus" width="200"/>
          <el-table-column align="center" label="核查时间" show-overflow-tooltip prop="checkTime" width="200"/>
          <el-table-column align="center" label="核查主体" show-overflow-tooltip prop="checkSubject" width="200"/>
          <el-table-column align="left" label="未完成核查原因" show-overflow-tooltip prop="notCheckedReason" width="200"/>
        </el-table-column>
        <el-table-column align="center" label="责任追究工作开展情况" show-overflow-tooltip prop="prov3" width="2400">
          <el-table-column align="center" label="是否追责" show-overflow-tooltip prop="isAccountability" width="200"/>
          <el-table-column align="center" label="未追责原因" show-overflow-tooltip prop="notAccountabilityReason" width="200"/>
          <el-table-column align="center" label="责任追究时间" show-overflow-tooltip prop="accountabilityTime" width="200"/>
          <el-table-column align="center" label="追责总人数" show-overflow-tooltip prop="accountabilityPersonNumber" width="200"/>
          <el-table-column align="center" label="追责总人次" show-overflow-tooltip prop="accountabilityPersonItem" width="200"/>
          <el-table-column align="center" label="责任追究处理方式（人次）" show-overflow-tooltip prop="prov24" width="1400">
            <el-table-column align="center" label="组织处理（人次）" show-overflow-tooltip prop="orgHandleItem" width="200"/>
            <el-table-column align="center" label="扣减薪酬" show-overflow-tooltip prop="a2" width="400">
              <el-table-column align="center" label="人次" show-overflow-tooltip prop="deductionPayItem" width="200"/>
              <el-table-column align="center" label="金额（万元）" show-overflow-tooltip prop="deductionAmount" width="200"/>
            </el-table-column>
            <el-table-column align="center" label="党纪处分（人次）" show-overflow-tooltip prop="partyPunishmentItem" width="200"/>
            <el-table-column align="center" label="政务处分（人次）" show-overflow-tooltip prop="governmentPunishmentItem" width="200"/>
            <el-table-column align="center" label="禁入限制（人次）" show-overflow-tooltip prop="debarPersonItem" width="200"/>
            <el-table-column align="center" label="移送国家检察机关或司法机关（人次）" show-overflow-tooltip prop="transferPersonItem" width="200"/>
            <el-table-column align="center" label="其他（人次）" show-overflow-tooltip prop="processingOtherItem" width="150"/>
          </el-table-column>
        </el-table-column>
        <el-table-column align="center" label="问题整改情况" show-overflow-tooltip prop="prov4" width="1000">
          <el-table-column  align="center" label="是否完成整改" show-overflow-tooltip prop="isCompleteReform" width="200"/>
          <el-table-column align="center" label="完善制度情况" show-overflow-tooltip prop="prov44" width="400">
            <el-table-column align="center" label="数量（项）" show-overflow-tooltip prop="perfectSystemNumber" width="200"/>
            <el-table-column align="center" label="制度名称、文号（无文号请注明出台时间）" show-overflow-tooltip prop="perfectSystemName" width="200"/>
          </el-table-column>
          <el-table-column align="center" label="损失挽回情况" show-overflow-tooltip prop="prov44" width="400">
            <el-table-column align="center" label="金额（万元）" show-overflow-tooltip prop="retrieveLossAmount" width="200"/>
            <el-table-column align="center" label="采取的主要措施" show-overflow-tooltip prop="takeMainStep" width="200"/>
          </el-table-column>
        </el-table-column>
        <el-table-column align="center" label="备注" prop="remark" width="200"/>
      </el-table>
    </BlockCard>
    <!--查看-->
    <showRegular
      :key="newCode"
      ref="showInfo"
      v-on:ViolRegularList="refreshList"
      :regularReportId="newCode"
      title="上报详情"
    ></showRegular>
    <showHistory
      :key="reportUnitId"
      ref="showHistory"
      :reportUnitCode="reportUnitCode"
      :regularReportId="regularReportId"
      :orgGrade="orgGrade"
    >
    </showHistory>
  </div>
</template>

<script>
  import BlockCard from '@/components/BlockCard';
  import FileUpload from './../../components/fileUpload';//附件
  import RegularTemplateFileUpload from '@/views/regular/common/regularTemplateFileUpload';
  import {
    regularHandlerFillData
    ,savedHandlerFillData
    ,replacingProvincialData
    ,subordinateUnitRelatedData//保存表格
    ,checkReportTimeForReload
    ,saveHandlerFillData
  } from '@/api/regular/flow/taskTodoAreaHandler'
  import showRegular from './../details/regularDetail';
  import showHistory from './../details/showHistoryList';//显示历史数据
  export default {
    name: "taskTodoAreaHandler",
    components:{
      BlockCard
      ,FileUpload
      ,showRegular
      ,RegularTemplateFileUpload
      ,showHistory//显示历史版本数据
       },
    props: {
      selectValue: {
        type: Object
      },
      centerVariable: {
        type: Object
      },
    },
    data(){
      return{
        exportBtn:'',
        historyBtn:'查看历史版本',
        formData:{},
        edit:true,
        queryList:[],
        newCode:'',
        reportIntervals:[],
        reportIntervalsCode:'',
        reportData:{unitStatisticsData:[]},
        multipleSelection:[],//选中的值
        cardName:"上报说明",//块名称
        reportUnitCode:''
        ,regularReportId:''
        ,reportUnitId:''
        ,orgGrade:''
      }
    },
    created(){
      this.RegularHandlerFillData();
    },
    methods:{
      //初始数据获取
      RegularHandlerFillData(){
        savedHandlerFillData(this.centerVariable.busiKey).then(
          response => {
            if (response.code === 200){
              this.formData = response.data;
              if(!this.formData.regularProblems){
                this.formData.regularProblems = []
              }
              if(this.formData.backFlag == '1'){
                this.cardName = "上报说明（已退回）"
              }
              this.reportUnitCode = this.formData.reportUnitCode;
              this.regularReportId = this.formData.regularReportId;
              this.reportUnitId = this.formData.reportUnitId;
              this.orgGrade = this.formData.startGrade;

              this.$nextTick(() => {
                // this.$refs.file.ViolationFileItems();
                this.$refs.flowTemplate.regularReportTemplateItems();
              })
              this.violDailyAccept();
            }else{
              this.$modal.msgError(response.msg);
            }
          }
        )
      },
      //新增下级
      violDailyAccept(){
        checkReportTimeForReload(this.formData.regularReportId).then(response => {
          if (response.code === 200) {
            var data = response.data;
            this.exportBtn = '';
            if(data.codeFlag!==true){//校验通过
              this.exportBtn = '';
            }else{
              if(data.codeType === 0){
                this.exportBtn = '';
              }else{
                if(data.code){
                  this.exportBtn = '查看下级单位上报';
                  this.newCode = data.code;
                }else{
                  this.exportBtn = '';
                }
              }
            }
          }
        });
      },
      //点击查看下级
      violDailyDetail(){
            this.$refs.showInfo.show();
      },
      //显示历史数据+本条数据
      showHistoryInfo(){
        this.$refs.showHistory.show();
      },
      //流程提交
      nextStep() {
        this.$emit('handle',1,{provinceId:this.formData.problemProvCode,areaId:this.formData.problemAreaCode});
      },
      //保存
      save(){
        this.$modal.msgSuccess('保存成功！')
      },
    }
  }
</script>

<style scoped>
  .position-select {
    position: absolute;
    width: 100%;
    text-align: right;
    top: -54px;
  }
  .el-dialog__body{
    background: #fff !important;
  }
  .el-table-change{
    height: 650px!important;
  }
</style>
