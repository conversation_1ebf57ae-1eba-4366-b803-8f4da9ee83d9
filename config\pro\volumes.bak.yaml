apiVersion: v1
kind: Secret
metadata:
  name: supervisionnglog
  namespace: jtauditpro
data:
  admin: QVFBZ0s1UmVsNnR0SVJBQW5vbCtUbERaKzBrU3BCS2RNaGRNeUE9PQ==
  942325445240: QVFDNU5MUmkwbDloTFJBQU5iQXhkMXIyMC9RZnZzcjZDalhGK1E9PQ==

apiVersion: v1
kind: PersistentVolume
metadata:
  name: supervisionnglogpv
spec:
  capacity:
    storage: 20Gi
  accessModes:
    - ReadWriteMany
  mountOptions:
    - rw
    - discard
  persistentVolumeReclaimPolicy: Retain
  csi:
    driver: ckecsi
    volumeHandle: welkinbig.supervisionnglog-942325445240
    fsType: ext4
    volumeAttributes:
      monitors: 10.172.48.44:6789,10.172.48.45:6789,10.172.48.46:6789
      pool: welkinbig
      imageFormat: "2"
      imageFeatures: "layering"
      adminId: admin
      userId: '942325445240'
      volName: supervisionnglog-942325445240
      mounter: rbd
      '942325445240': AQC5NLRi0l9hLRAANbAxd1r20/Qfvsr6CjXF+Q==
      admin: AQAgK5Rel6ttIRAAnol+TlDZ+0kSpBKdMhdMyA==
    controllerPublishSecretRef:
      name: supervisionnglog
      namespace: jtauditpro
    nodeStageSecretRef:
      name: supervisionnglog
      namespace: jtauditpro
    nodePublishSecretRef:
      name: supervisionnglog
      namespace: jtauditpro

apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: supervisionnglogpvc ##pvc名称
  namespace: jtauditpro
spec:                ##pv的筛选条件
  accessModes:
    - ReadWriteMany ## pv读写模式
  volumeName: supervisionnglogpv  ##指定pv名称，此为可选字段，当指定特定的pv名称时，只有对应的名称的pv能被bound
  resources:
    requests:
      storage: 20Gi  ##pv大小
