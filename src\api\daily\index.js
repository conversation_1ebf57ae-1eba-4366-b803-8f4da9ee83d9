import request from '@/utils/request'

// 日常查询列表
export function violDailyList(query) {
  const pageParams = { pageNum: query.pageNum, pageSize: query.pageSize }
  return request({
    url: '/colligate/violQuery/violDailyList',
    method: 'post',
    data: query,
    params: pageParams
  })
}

// 获取新增信息
export function getAdd() {
  return request({
    url: '/finance/receivable/dispatch/newReceivableDispatch',
    method: 'get'
  })
}

// 查询调度详细信息
export function detailListBydispatchId(query) {
  return request({
    url: '/finance/receivable/dispatch/detailListBydispatchId',
    method: 'post',
    data: query
  })
}

// 角色数据权限
export function returnIListForDispatch(query) {
  return request({
    url: '/finance/receivable/dispatch/returnIListForDispatch',
    method: 'post',
    data: query
  })
}

// 删除调度
export function dropDispatchById(query) {
  return request({
    url: '/finance/receivable/dispatch/dropDispatchById',
    method: 'post',
    data: query
  })
}

// 05-1 更新调度内容信息
export function updateDispatchById(query) {
  return request({
    url: '/finance/receivable/dispatch/updateDispatchById',
    method: 'post',
    data: query
  })
}

// 删除
export function deleteDetailById(query) {
  return request({
    url: '/finance/receivable/dispatch/deleteDetailById',
    method: 'post',
    data: query
  })
}

// 保存选中的应付账款列表
export function saveReturnIListForDispatch(query) {
  return request({
    url: '/finance/receivable/dispatch/saveReturnIListForDispatch',
    method: 'post',
    data: query
  })
}

// 10 一键全选添加调度应收账款信息
export function saveAllReturnIListForDispatch(query) {
  return request({
    url: '/finance/receivable/dispatch/saveAllReturnIListForDispatch',
    method: 'post',
    data: query
  })
}

// 09 提交调度前校验
export function validateDispatchForSubmit(query) {
  return request({
    url: '/finance/receivable/dispatch/validateDispatchForSubmit',
    method: 'post',
    data: query
  })
}

// 08 提交调度
export function submitForDispatch(query) {
  return request({
    url: '/finance/receivable/dispatch/submitForDispatch',
    method: 'post',
    data: query
  })
}

// 问题统计
export function problemNumsList(query) {
  return request({
    url: '/colligate/violQuery/problemNumsList',
    method: 'post',
    data: query
  })
}

// 问题涉及单位统计
export function problemRankList(url, query) {
  return request({
    url: '/colligate/violQuery/' + url,
    method: 'post',
    data: query
  })
}

// 问题分布统计
export function problemEchartList(url) {
  return request({
    url: '/colligate/violQuery/' + url,
    method: 'post'
  })
}

// 问题状态
export function dailyStatus(data) {
  return request({
    url: '/colligate/violQuery/dailyStatus',
    method: 'post',
    data: data
  })
}

export function actualStatus() {
  return request({
    url: '/colligate/violQuery/actualStatus',
    method: 'post'
  })
}

// 省分
export function queryProvList() {
  return request({
    url: '/colligate/violQuery/queryProvList',
    method: 'post'
  })
}

// 地市
export function queryAreaList(query) {
  return request({
    url: '/colligate/violQuery/queryAreaList',
    method: 'post',
    data: query
  })
}

// 问题线索来源下拉
export function sourceSasacStatus() {
  return request({
    url: '/colligate/violQuery/sourceSasacStatus',
    method: 'post'
  })
}

// 提级查办
export function stepDeal(query) {
  return request({
    url: '/colligate/violQuery/stepDeal',
    method: 'post',
    data: query
  })
}

// 问题最新状态
export function problemStatus(data) {
  return request({
    url: '/colligate/violQuery/problemStatus',
    method: 'post',
    data: data
  })
}

// 超时问题列表
export function allTimeoutProblems(timeoutDate) {
  return request({
    url: '/colligate/violOrdinaryHomepage/allTimeoutProblems/' + timeoutDate,
    method: 'post'
  })
}

// 获取问题状态
export function initStatusList(data) {
  return request({
    url: '/colligate/violQuery/initStatusList',
    method: 'post',
    data
  })
}
