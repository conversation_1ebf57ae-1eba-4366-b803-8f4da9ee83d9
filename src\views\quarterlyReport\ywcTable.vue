
<!-- 季度报告--已完成数据 -->

<template>
  <div class="wai-container" style="background-color: #fff">
    <div class="layui-row width height">
      <div class="width height">
        <div class="common-wai-box" style="height: 100%">
          <div class="common-in-box" style="height: auto; min-height: 100%">
            <div class="tables tables_1">
              <el-table
                :data="tablesData"
                border
                height="600px"
                v-loading="tableLoading"
                style="width: 100%"
              >
              <el-table-column
          type="index"
          label="序号"
          align="center"
          sortable
          min-width="5%"
          :index="table_index"
        />

                <el-table-column
                  label="上报单位"
                  prop="reportUnitName"
                  min-width="10%"
                  align="center"
                >
                  <template slot-scope="scope">
                    <div
                      style="text-align: center"
                      class="overflowHidden-1"
                      :title="scope.row.reportUnitName"
                    >
                      {{ scope.row.reportUnitName || "" }}
                    </div>
                  </template>
                </el-table-column>
                <el-table-column
                  label="接口人"
                  align="center"
                  prop="nickName"
                  min-width="10%"
                />
                <el-table-column
                  label="邮箱"
                  align="center"
                  prop="email"
                  min-width="20%"
                />

                <el-table-column
                  label="联系电话"
                  align="center"
                  prop="phoneNumber"
                  min-width="15%"
                />

                <el-table-column
                  label="完成时间"
                  align="center"
                  prop="procEndTime"
                  min-width="20%"
                />

                <el-table-column
                  label="操作"
                  fixed="right"
                  min-width="15%"
                  align="center"
                  class-name="small-padding fixed-width"
                >
                  <template slot-scope="scope">

                    <el-button
                      size="mini"
                      type="text"
                      title="查看"
                      icon="el-icon-search"
                      @click="openViewItem(scope.row)"
                    ></el-button>
                    <el-button
                      size="mini"
                      type="text"
                      title="驳回"
                      icon="el-icon-sort"
                      @click="bhItemClick(scope.row)"
                    ></el-button>
                  </template>
                </el-table-column>
              </el-table>
              <pagination
                v-show="total > 0"
                :total="total"
                :page.sync="queryParams.pageNum"
                :limit.sync="queryParams.pageSize"
                @pagination="queryYwcList"
              />
            </div>
          </div>
        </div>
      </div>
    </div>

        <!-- 驳回原因 -->
        <el-dialog
      class="commons_popup"
      v-bind="$attrs"
      :visible.sync="bhVisible"
      width="40%"
      title="驳回"
      v-if="bhVisible"
      append-to-body
    >
      <div slot="title" class="el-popup-header-title">
        <svg-icon icon-class="edit_file" />
        <span class="el-dialog-header-name">{{ "驳回" }}</span>
      </div>
      <bhInput  :rowData="bhItem" :closeBtn="closeBtn"/>
    </el-dialog>

        <!-- 省分查看 -->
        <el-dialog
      class="commons_popup"
      v-bind="$attrs"
      :visible.sync="viewProvisible"
      width="90%"
      title="查看"
      v-if="viewProvisible"
      append-to-body
    >
      <div slot="title" class="el-popup-header-title">
        <svg-icon icon-class="edit_file" />
        <span class="el-dialog-header-name">{{ "查看" }}</span>
      </div>
      <viewProvince :rowData="proItem" />
    </el-dialog>
  </div>
</template>
    <script>
  import {
    getReportUnitUserData
  } from '@/api/quarterly-report/view';
import bhInput from "@/views/quarterlyReport/bhInput";
import viewProvince from "@/views/quarterlyReport/view-province";
export default {
  components: {bhInput,viewProvince},
  props: {
    //编辑内容
    rowData: {
      type: Object,
      default: () => {},
    },
  },
  dicts: [],
  data() {
    return {
      tableLoading: false, //表格loading
      tablesData: [], //列表数据
      //表格页码
      total: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      bhVisible:false, //驳回原因弹窗
      bhItem: {}, //驳回点击数据
      viewProvisible:false,
      proItem:{},
    };
  },
  created() {
    this.queryYwcList();
  },
  methods: {
    table_index(index) {
      return (this.queryParams.pageNum - 1) * this.queryParams.pageSize + index + 1
    },
    //查询列表
    queryYwcList(){
      this.tableLoading = true
      var params = {
        quarterReportId : this.rowData.quarterReportId,
        status:this.rowData.procStatus
      }
      getReportUnitUserData(params,this.queryParams).then((res)=>{
       this.tablesData = res.rows;
        this.total = res.total
       this.tableLoading = false
      })
    },
    //驳回
    bhItemClick(row) {
        this.bhVisible = true
        this.bhItem = row
    },
    openViewItem(row){
      this.viewProvisible = true
      this.proItem = row;
      this.proItem.quarterReportId = this.rowData.quarterReportId
    },
    closeBtn(){
        this.bhVisible = false
        this.queryYwcList();
    }
  },
};
</script>
<style lang="scss" scoped>
    @import "~@/assets/styles/quarterly-report/index.css";
</style>
