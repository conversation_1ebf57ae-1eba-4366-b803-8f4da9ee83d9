<template>
  <div class="version-record">
    <div class="vr-list">
      <div v-if="versionRecordlist.length==0" style="width:100%;height: 380px">
        <NoData/>
      </div>
      <div v-else class="vr-li row" v-for="(item,index) in versionRecordlist" :key="index">
        <div class="vr-li-header">
          <div class="vr-li-header-text vr-li-header-text1">{{item.versionName}}：</div>
          <div class="vr-li-header-text vr-li-header-text1" style="width: 145px;">创建人：{{item.createName}}</div>
          <div class="vr-li-header-text">创建时间：{{item.createTime}}</div>
          <div class="vr-li-header-text">最近使用时间：{{item.lastUseTime}}</div>
        </div>
       <div class="flex" style="align-items: center;">
         <div class="vr-li-left">
           <div class="vr-li-left-company" v-for="(obj,i) in item.subList" :key="obj">{{obj.unitName}}</div>
         </div>
         <div class="vr-li-right">
           <el-button size="mini" type="primary" plain @click="selectRecord(item)">选择</el-button>
           <el-button size="mini" type="primary" plain v-if="item.isDefault=='0'" @click="defaultRecord(item)">默认</el-button>
           <el-button size="mini" type="primary" plain v-if="item.isDelete=='1'" @click="deleteRecord(item)">删除</el-button>
         </div>
       </div>
      </div>
    </div>
  </div>

</template>

<script>
import {getProvVersionList,setDefaultProvVersion,deleteProvVersionById} from "@/api/regular/tree/versionRecord";
export default {
  name:'versionRecord',
  components: {
  },
  props: {},
  data() {
    return {
      versionRecordlist:[]
    }
  },
  created(){

  },
  methods: {
    //初始化数据
    init(){
      getProvVersionList().then(response => {
          this.versionRecordlist = response.data
       });
    },
    //删除
    deleteRecord(item){
      this.$confirm('是否确认删除"'+item.versionName+'"？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then( ()=> {
        deleteProvVersionById({id:item.id}).then(response =>{
          if(response.code == 200){
            this.$modal.msgSuccess("删除成功");
            this.init()
          }else{
            this.$modal.msgError(response.msg);
          }
        });
      }).catch(function () {});
    },
    //默认
    defaultRecord(item){
      this.$confirm('是否将"'+item.versionName+'"设置为默认？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then( ()=> {
        setDefaultProvVersion({id:item.id}).then(response => {
          if(response.code == 200){
            this.$modal.msgSuccess("操作成功");
            this.init()
          }else{
            this.$modal.msgError(response.msg);
          }
        });
      }).catch(function () {});
    },
    //选择
    selectRecord(item){
      this.$emit('selectRecord',item)
    }
  }
}
</script>
<style  rel="stylesheet/scss" scoped lang="scss">
.version-record {
  //height: 100%;
  //overflow: auto;
  .vr-list {
    display: inline-block;
    width: 100%;
    .vr-li {
      width: 100%;
      margin-bottom:20px;
      .vr-li-header {
        width: 100%;
        .vr-li-header-text {
          display: inline-block;
          width: 300px;
          line-height: 50px;
          &.vr-li-header-text1{
            width: 240px;
          }
        }
      }
      .vr-li-left {
        float:left;
        width: calc(100% - 300px);
        border:1px solid #ddd;
        padding:10px;

        .vr-li-left-company {
          display: inline-block;
          min-width: 75px;
          text-align: center;
          height: 30px;
          padding:0 10px;
          line-height: 30px;
          border: 1px solid #f5222d;
          border-radius: 2px;
          margin:0 10px 10px 0;
          color: #f5222d;
        }
      }
      .vr-li-right {
        float:right;
        text-align: center;
        width: 300px;
      }
    }
  }
}
</style>
