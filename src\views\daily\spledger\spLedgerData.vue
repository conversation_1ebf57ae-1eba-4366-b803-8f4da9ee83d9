<template>
  <div>
    <el-form-item label="关联专项追责台账" prop="problemAudit">
      <a @click="relationLedger('relation')" class="table-btn" style='color: #c20000;'>
       {{spLedgerInfo.problemAudit}}
      </a>
      <el-button size="mini" type="text" icon="el-icon-delete" v-if="edit && spLedgerInfo.problemAudit" style="margin-left: 5px;color: #2c3e50"   @click="deleteSpLedgerById()" title="删除"></el-button>
      <el-button type="primary" plain icon="el-icon-connection" size="mini" @click="relationLedger('un')" v-if="edit">关联追责台账</el-button>
    </el-form-item>
    <el-row>
      <RelationUnSpLedger
        ref="relation"
        :key="index"
        :problemId="problemId"
        :relationId="relationId"
        :queryType="queryType"
        @queryReLedgerInfoByProblemId="queryReLedgerInfoByProblemId"
      ></RelationUnSpLedger>
    </el-row>
  </div>
</template>

<script>
  import RelationUnSpLedger from './relationUnSpLedger';//关联台账选择页面
  import {queryReLedgerInfoByProblemId
  ,deleteSpLedgerById} from "@/api/daily/spledger/index";

    export default {
      name: "scopeSituationData",
      components: {
        RelationUnSpLedger
      },
      props: {
      edit: {
        type: Boolean,
        default:false
      },
        problemId:{
          type: String
        },
        saveFlag:{
        //日常问题是否保存标识；0：未保存；1：已保存
          type:String
        }
      },
      data(){
        return{
          index:0,
          spLedgerInfo:{
            id:'',//关联表主键
            problemAudit:'',//审计发现问题
            problemId:this.problemId,//日常问题主键；
          },
          queryType:'un',
          relationId:'',//关联表主键
        }
      },
      created() {
        this.queryReLedgerInfoByProblemId();
      },
      methods:{
        /** 获取关联的专项报告台账*/
        queryReLedgerInfoByProblemId() {
          queryReLedgerInfoByProblemId({problemId:this.problemId}).then(
            response => {
              if( response.data){
                this.spLedgerInfo = response.data;
              }else{
                this.spLedgerInfo = {
                  id:'',//关联表主键
                  problemAudit:'',//审计发现问题
                  problemId:this.problemId,//日常问题主键；
                }
              }

            }
          );
        },
        /** 关联、攥取专项报告台账*/
        relationLedger(queryType) {
          if(this.saveFlag && this.saveFlag == '0'){
            // this.$modal.msgWarning("请先保存日常问题");
            // return;
            //调用保存方法
            this.$emit('spLedgerSaveInfo',queryType);
          }else{
            this.queryLedgerList(queryType);
          }
        },
        queryLedgerList(queryType){
          if(queryType != 'un'){
            this.relationId = this.spLedgerInfo.id
          }else{
            this.relationId=''
          }
          this.queryType = queryType
          this.index++;
          this.$nextTick(()=> {
            this.$refs.relation.show();
          });
        },

        /** 删除操作 */
        deleteSpLedgerById() {
          let  title = '确认删除关联台账吗？';
          let data={
            id:this.spLedgerInfo.id
          };
          this.$modal.confirm(title).then(function() {
              return deleteSpLedgerById(data)
            }).then((response) => {
              if(response.code == 200){
                this.$modal.msgSuccess("删除成功");
                this.queryReLedgerInfoByProblemId();
              }else{
                this.$modal.msgError(response.msg);
              }

            }).catch(() => {});
        },
      }
    }
</script>

<style scoped>

</style>
