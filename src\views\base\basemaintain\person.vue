<template>
  <el-container class="padding_b60" style="border: none;">
    <el-main>
      <div style=" margin-bottom: 30px;">
        <span class="text-blue float-right margin-l10" v-show="dataDetails && dataDetails.commitFlag=='1'">【已提交】</span>
        <span class="text-red float-right margin-l10" v-show="dataDetails && dataDetails.commitFlag=='0'">【待提交】</span>
        <span class="base-warning"><i class="el-icon-info"></i>温馨提示：状态为已提交时，其他人员才能查询到</span>
      </div>
      <personDetailCommon v-if="!!dataDetails" :type="type" :dataDetailsProps="dataDetails" :onDetailsChange="onDetailsChange"></personDetailCommon>
      <div>
      </div>
    </el-main>
    <FooterBox>
      <div class="float-right">
        <el-button size="mini" @click="saveAreaPeraon(0)" :loading="buttonLoading=='save'">保存</el-button>
        <el-button size="mini" type="primary" @click="saveAreaPeraon(1)" :loading="buttonLoading=='sub'">提交</el-button>
      </div>
    </FooterBox>
  </el-container>
</template>

<script lang="ts">
  import {getAreaPersonBaseInfo, saveAreaPeraonBaseInfo} from "@/api/base/person";
  import personDetailCommon from "../common/personDetailCommon";

  export default {
    name: "person",
    components: { personDetailCommon },
    props: {
      dialogVisible: {
        type: Boolean,
        default: true
      },
      id: {
        type: String,
        default: ''
      },
    },
    data() {
      return {
        dataDetails: null,
        type: "edit",
        buttonLoading: null,
      };
    },
    created() {
      this.areaPersonBaseInfo();
    },
    methods: {
      /**查询企业基本信息详情*/
      areaPersonBaseInfo() {
       //this.loading = true;
        getAreaPersonBaseInfo().then(
          response => {
            this.dataDetails = response.data;
            this.buttonLoading = null;
          }
        );
      },
      /**关闭模态框*/
      handleClose() {
        this.$emit("closeModal");
      },
      /**保存或提交操作*/
      saveAreaPeraon(commitFlag){
        if(commitFlag==1){
          this.buttonLoading = 'sub';
        }else{
          this.buttonLoading = 'save';
        }
        saveAreaPeraonBaseInfo({
          ...this.dataDetails,
          commitFlag,
        }).then(
          response => {
            if(response.code == 200){
              this.$message({
                message: response.msg,
                type: 'success'
              });
              this.areaPersonBaseInfo();
            }
          }
        ).catch(err=>{
          this.buttonLoading = null;
        })
      },
      /** 详情变化时的回调 */
      onDetailsChange(data){
        this.dataDetails = data;
      },
    }
  };
</script>

<style>
  @import "../common/common.css";
  .text {
    font-size: 14px;
  }

  .item {
    margin-bottom: 18px;
  }

  .clearfix:before,
  .clearfix:after {
    display: table;
    content: "";
  }
  .clearfix:after {
    clear: both
  }
</style>
