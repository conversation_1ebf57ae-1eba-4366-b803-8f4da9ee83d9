#镜像源
FROM harbor.dcos.xixian.unicom.local/auditest/filebeat:7.6.2
#作者信息
MAINTAINER by audit (<EMAIL>)
#复制配置
ADD config/devlog/filebeat.yml /usr/share/filebeat/filebeat.yml
ADD config/devlog/filebeat.yml /var/log/nginx/filebeat.yml
#定义时区参数
ENV TZ=Asia/Shanghai
#设置时区
#RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo '$TZ' > /etc/timezone
#定义系统编码
ENV LANG C.UTF-8
#设置环境变量
ENV LC_ALL C.UTF-8
#定义开放的端口号
EXPOSE 8989
