<template>
    <div>
      待办示例
      {{centerVariable}}
      <button @click="passValidateJup()">跳过校验</button>
    </div>
</template>

<script>
    export default {
        name: "demoToDo",
    props: {
      centerVariable: {
        type: Object
      },
    },
      created(){
        this.$emit('collocation',{
          refreshAssigneeUrl:'/businessController',//业务url
          saveBtn:true,//保存按钮
        })
      },
      methods:{
        openLoading(){//打开加载...
          this.$emit('openLoading');
        },
        closeLoading(){//关闭加载...
          this.$emit('closeLoading');
        },
      //保存
      publicSave(){
        this.$modal.msgSuccess('保存成功！')
      },
        //额外参数
       loadProcessData(){
          return {branch:2}
       },
        //校验
        passValidate(){
          this.$modal.msgSuccess('校验通过！')
          return true;
        },
        //接口校验异步校验时
        passValidateJup(){
          this.$emit('nextStep',true)
        }
     }
    }
</script>

<style scoped>

</style>
