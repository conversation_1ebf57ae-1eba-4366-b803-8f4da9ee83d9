<template>
  <div>
    已办示例
    {{centerVariable}}
  </div>
</template>

<script>
  export default {
    name: "demoToDo",
    props: {
      centerVariable: {
        type: Object
      },
    },
    created(){
    },
    methods:{
      openLoading(){//打开加载...
        this.$emit('openLoading');
      },
      closeLoading(){//关闭加载...
        this.$emit('closeLoading');
      }
    }
  }
</script>

<style scoped>

</style>
