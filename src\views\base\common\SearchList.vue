<template>
 <div>
   <el-form :model="queryParams" ref="queryForm" size="medium"  label-width="80px" style="padding:10px 10px 0 0; background: #F4F4F4; margin-bottom: 10px;">
     <el-row>
       <el-col :span="6" v-for="(item, i) in searchList" :key="item.name">
         <el-form-item :label="item.label" >
           <el-input v-model="searchList[i].value" v-bind:placeholder="item.label" clearable :style="{width: '100%'}">
           </el-input>
         </el-form-item>
       </el-col>
       <el-col :span="spanCol">
         <el-form-item style="float: right">
           <el-button type="primary"  v-for="(item, i) in buttonGroup" :key="item.label" :icon="item.icon" size="mini" @click="item.onClickHandle">{{item.label}}</el-button>

           <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
           <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
         </el-form-item>
       </el-col>
     </el-row>
   </el-form>
 </div>

</template>


<script lang="ts">
  export default {
    name: "SearchList",
    props: {
      searchList: {
        type: Array,
        default: ()=>{ return []}
      },
      buttonGroup: {
        type: Array,
        default: ()=>{ return [] }
      },
    },
    data() {
      return {
        queryParams:{},
        spanCol: 6,
      }
    },
    created() {
      this.initData();
    },
    methods: {
      initData() {
        this.spanCol = 6 * (4 - this.searchList.length % 4);
      },
      /** 搜索按钮操作*/
      handleQuery() {
        if(this.searchList){
          this.searchList.map(item=>{
            this.queryParams[item.name]= item.value
          })
        }
        this.$emit('on-search', this.queryParams);
      },
      /**重置按钮操作*/
      resetQuery() {
        this.searchList.map(item=>{
          item.value= null
        })
        this.$emit('on-reset');
      },
    }
  };
</script>

<style scoped>
 ::v-deep.el-form-item{
    margin-bottom:10px;
  }
</style>
