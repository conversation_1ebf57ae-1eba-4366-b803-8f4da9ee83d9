(window["webpackJsonp"] = window["webpackJsonp"] || []).push([[34],{

/***/ "./src/api/system/user.js":
/*!********************************!*\
  !*** ./src/api/system/user.js ***!
  \********************************/
/*! exports provided: listUser, getUser, addUser, updateUser, delUser, resetUserPwd, changeUserStatus, getUserProfile, updateUserProfile, updateUserPwd, uploadAvatar, getAuthRole, updateAuthRole */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "listUser", function() { return listUser; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "getUser", function() { return getUser; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "addUser", function() { return addUser; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "updateUser", function() { return updateUser; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "delUser", function() { return delUser; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "resetUserPwd", function() { return resetUserPwd; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "changeUserStatus", function() { return changeUserStatus; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "getUserProfile", function() { return getUserProfile; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "updateUserProfile", function() { return updateUserProfile; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "updateUserPwd", function() { return updateUserPwd; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "uploadAvatar", function() { return uploadAvatar; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "getAuthRole", function() { return getAuthRole; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "updateAuthRole", function() { return updateAuthRole; });
/* harmony import */ var _utils_request__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/utils/request */ "./src/utils/request.js");
/* harmony import */ var _utils_ruoyi__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/utils/ruoyi */ "./src/utils/ruoyi.js");



// 查询用户列表
function listUser(query) {
  return Object(_utils_request__WEBPACK_IMPORTED_MODULE_0__["default"])({
    url: '/system/user/list',
    method: 'get',
    params: query
  });
}

// 查询用户详细
function getUser(userId) {
  return Object(_utils_request__WEBPACK_IMPORTED_MODULE_0__["default"])({
    url: '/system/user/' + Object(_utils_ruoyi__WEBPACK_IMPORTED_MODULE_1__["praseStrEmpty"])(userId),
    method: 'get'
  });
}

// 新增用户
function addUser(data) {
  return Object(_utils_request__WEBPACK_IMPORTED_MODULE_0__["default"])({
    url: '/system/user',
    method: 'post',
    data: data
  });
}

// 修改用户
function updateUser(data) {
  return Object(_utils_request__WEBPACK_IMPORTED_MODULE_0__["default"])({
    url: '/system/user',
    method: 'put',
    data: data
  });
}

// 删除用户
function delUser(userId) {
  return Object(_utils_request__WEBPACK_IMPORTED_MODULE_0__["default"])({
    url: '/system/user/' + userId,
    method: 'delete'
  });
}

// 用户密码重置
function resetUserPwd(userId, password) {
  var data = {
    userId: userId,
    password: password
  };
  return Object(_utils_request__WEBPACK_IMPORTED_MODULE_0__["default"])({
    url: '/system/user/resetPwd',
    method: 'put',
    data: data
  });
}

// 用户状态修改
function changeUserStatus(userId, status) {
  var data = {
    userId: userId,
    status: status
  };
  return Object(_utils_request__WEBPACK_IMPORTED_MODULE_0__["default"])({
    url: '/system/user/changeStatus',
    method: 'put',
    data: data
  });
}

// 查询用户个人信息
function getUserProfile() {
  return Object(_utils_request__WEBPACK_IMPORTED_MODULE_0__["default"])({
    url: '/system/user/profile',
    method: 'get'
  });
}

// 修改用户个人信息
function updateUserProfile(data) {
  return Object(_utils_request__WEBPACK_IMPORTED_MODULE_0__["default"])({
    url: '/system/user/profile',
    method: 'put',
    data: data
  });
}

// 用户密码重置
function updateUserPwd(oldPassword, newPassword) {
  var data = {
    oldPassword: oldPassword,
    newPassword: newPassword
  };
  return Object(_utils_request__WEBPACK_IMPORTED_MODULE_0__["default"])({
    url: '/system/user/profile/updatePwd',
    method: 'post',
    params: data
  });
}

// 用户头像上传
function uploadAvatar(data) {
  return Object(_utils_request__WEBPACK_IMPORTED_MODULE_0__["default"])({
    url: '/system/user/profile/avatar',
    method: 'post',
    data: data
  });
}

// 查询授权角色
function getAuthRole(userId) {
  return Object(_utils_request__WEBPACK_IMPORTED_MODULE_0__["default"])({
    url: '/system/user/authRole/' + userId,
    method: 'get'
  });
}

// 保存授权角色
function updateAuthRole(data) {
  return Object(_utils_request__WEBPACK_IMPORTED_MODULE_0__["default"])({
    url: '/system/user/authRole',
    method: 'put',
    params: data
  });
}

/***/ })

}]);
//# sourceMappingURL=34.1693388085916.js.map