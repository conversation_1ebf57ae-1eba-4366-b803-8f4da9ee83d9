<!-- 监督追责实时报送-30个工作日实时报告快报 -->
<template>
  <div class=" app-report">
    <el-dialog  title="30个工作日实时报告快报" class="app-report" :visible.sync="visible" width="80%" append-to-body>
      <Jscrollbar height="68vh">
        <el-row class="el-dialog-div">
          <el-col :span="24">
            <BlockCard
              title="基本信息"
            >
              <el-form ref="elForm" :model="detailInfo" :rules="rules" size="medium" label-width="150px">
                <el-row>
                  <el-col :span="8">
                    <el-form-item label="系统编号">
                      <span> {{detailInfo.auditCode}}</span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="问题编号">
                      <span> {{detailInfo.problemCode}}</span>
                    </el-form-item>
                  </el-col>

                  <el-col :span="24">
                    <el-form-item label="违规事项 ">
                      <span class="cursor text-red" @click="dailyDetail"> {{detailInfo.problemTitle}}</span>
                    </el-form-item>
                  </el-col>

                  <el-col :span="8">
                    <el-form-item label="发生时间" prop="findTime">
                      <el-date-picker v-model="detailInfo.findTime"   format="yyyy-MM-dd" value-format="yyyy-MM-dd"
                                      :style="{width: '100%'}" placeholder="请选择发生时间" clearable></el-date-picker>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="损失金额（万元）" prop="lossAmount">
                      <el-input-number
                        v-model="detailInfo.lossAmount"
                        :min="0"
                        :precision="2"
                        placeholder="损失金额（万元）"
                        controls-position="right"
                      />
                    </el-form-item>
                  </el-col>

                  <el-col :span="8">
                    <el-form-item label="损失风险（万元）" prop="lossRisk">
                      <el-input-number
                        v-model="detailInfo.lossRisk"
                        :min="0"
                        :precision="2"
                        placeholder="损失风险（万元）"
                        controls-position="right"
                      />
                    </el-form-item>
                  </el-col>

                  <el-col :span="8">
                    <el-form-item label="涉及企业名称" prop="detailInfo.otherside">
                      <div class="select-list">
                        <div v-for="(item,index) of unitData" :key="index" class="list-li">
                          <span>{{ item.involveUnitName }}</span>
                        </div>
                      </div>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="涉及企业级次" prop="involveUnitGrade">
                      <span>{{detailInfo.involveUnitGrade}}</span>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
            </BlockCard>
          </el-col>
          <el-col :span="24">
            <BlockCard
              title="30个工作日实时报告快报"
            >
              <el-form size="medium" label-width="150px">
                <el-row>
                  <el-col :span="24">
                    <el-form-item label="工作开展情况" prop="workDevelopment">
                      <el-input v-model="detailInfo.workDevelopment" type="textarea" placeholder="工作开展情况"
                                :autosize="{minRows: 4, maxRows: 4}" :style="{width: '100%'}" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="24">
                    <el-form-item label="资产损失及其他严重不良后果" prop="consequences">
                      <el-input v-model="detailInfo.consequences" type="textarea" placeholder="资产损失及其他严重不良后果"
                                :autosize="{minRows: 4, maxRows: 4}" :style="{width: '100%'}" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="24">
                    <el-form-item label="存在主要问题" prop="importProblem">
                      <el-input v-model="detailInfo.importProblem" type="textarea" placeholder="存在主要问题"
                                :autosize="{minRows: 4, maxRows: 4}" :style="{width: '100%'}" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="24">
                    <el-form-item label="初步核实违规违纪情况" prop="importReason">
                      <el-input v-model="detailInfo.violationsInfo" type="textarea" placeholder="初步核实违规违纪情况"
                                :autosize="{minRows: 4, maxRows: 4}" :style="{width: '100%'}" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="24">
                    <el-form-item label="初步核实是否属于责任追究范围" prop="isLiabilityRange">
                      <el-radio v-model="detailInfo.isLiabilityRange" label="1">是</el-radio>
                      <el-radio v-model="detailInfo.isLiabilityRange" label="2">否</el-radio>
                    </el-form-item>
                  </el-col>
                  <el-col :span="24">
                    <el-form-item label="有关方面处置建议和要求" prop="measuresTaken">
                      <el-input v-model="detailInfo.measuresTaken" type="textarea" placeholder="有关方面处置建议和要求"
                                :autosize="{minRows: 4, maxRows: 4}" :style="{width: '100%'}" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="24">
                    <el-form-item label="已开展的应对处置、成效" prop="developDisposal">
                      <el-input v-model="detailInfo.developDisposal" type="textarea" placeholder="已开展的应对处置、成效"
                                :autosize="{minRows: 4, maxRows: 4}" :style="{width: '100%'}" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="24">
                    <el-form-item label="提出处置意见后期工作安排" prop="nextWork">
                      <el-input v-model="detailInfo.nextWork" type="textarea" placeholder="提出处置意见后期工作安排"
                                :autosize="{minRows: 4, maxRows: 4}" :style="{width: '100%'}" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="24">
                    <el-form-item label="备注" prop="remark">
                      <el-input v-model="detailInfo.remark" type="textarea" placeholder="备注"
                                :autosize="{minRows: 4, maxRows: 4}" :style="{width: '100%'}" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="联系人" prop="companyContacts">
                      <el-input v-model="detailInfo.companyContacts" controls-position="right" placeholder="联系人"  />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="联系电话" prop="contactsTel">
                      <el-input maxlength="11" v-model="detailInfo.contactsTel" controls-position="right" placeholder="联系电话"  />
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
            </BlockCard>
          </el-col>
          <el-col :span="24">
            <BlockCard
              title="报告附件"
            >
              <FileUpload
                :edit='edit'
                :problemId="field"
                :relevantTableId="relevantTableId"
                :relevantTableName="relevantTableName"
                flowType="VIOL_ACTUAL"
                problemStatus="3"
                linkKey="a001"
                ref="file"
                flowKey = "SupervisionDailyReport"
                @fileDown="fileDown"
              ></FileUpload>
            </BlockCard>
          </el-col>
        </el-row>
      </Jscrollbar>
      <!--<div slot="footer" class="dialog-footer">-->
        <!--<el-button type="primary" @click="save">保存</el-button>-->
        <!--<el-button type="primary" @click="submitForm">提交</el-button>-->
        <!--<el-button @click="cancel">取消</el-button>-->
      <!--</div>-->
      <Process
        v-if="relevantTableId"
        @publicSave="save"
        slot="footer"
        ref="process"
        type="parent"
        :key="actualProblemId"
        tabFlag="6"
        @close="cancel"
        :selectValue="{
      busiKey:relevantTableId,
      title:detailInfo.problemTitle,
      }"
        :flowParamsUrl="flowParamsUrl"
        :problemStatus="3"
        :processDefinitionKey="processDefinitionKey"
        @nextStep="submitForm"
      ></Process>
    </el-dialog>
    <el-dialog :visible.sync="VisibleCheckTree" width="60%" append-to-body title="涉及企业名称">
      <CheckTree
        :key="selectTree"
        ref="checkTree"
        :url="url"
        :selectTree="selectTree"
        :params="{
        actualProblemId:actualProblemId,
        involveUnitName:'',
        relevantTableId:relevantTableId
        }"
        @list="persList"
      />
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="savePers">保存</el-button>
      </div>
    </el-dialog>
    <ModifyRecord
      ref="modify"
      :key="receiverGrade||actualProblemId"
      :actualProblemId="actualProblemId"
      :relevantTableId="relevantTableId"
      :relevantTableName="relevantTableName"
      :type="edit"
      @saveModify="saveModify"
    >
    </ModifyRecord>
    <el-dialog :visible.sync="dailyVisible" width="90%" :title="'日常问题-'+detailInfo.problemTitle" append-to-body>
      <Details
        :key="detailInfo"
        :selectValue="detailInfo"
        activeName="0"
      >
      </Details>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary"  @click="dailyClose" >确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
  import {waitHandleThirtyReport, saveThirtyReport, submitThirtyReport, thirtyReportCompareWithDailyProblem} from "@/api/actual/task/actualFifteenAndThirtyDaysReport";
  import {queryActualInvolveUnit, deleteActualInvolveUnit, saveActualInvolveUnitData} from '@/api/actual/common/actualInvolveUnit';
  import {selProblemInfo} from '@/api/actual/common/actualFlow';
  import {generateTemplateWithContent} from "@/api/components/index";
  import BlockCard from "@/components/BlockCard";
  import FileUpload from './../../components/fileUpload';//附件
  import CheckTree from './../common/checkTree';// checkTree
  import Recipient from './../common/recipient';// recipient
  import ModifyRecord from './../common/modifyRecord';// modifyRecord
  import Process from "@/components/Process/actual";
  import Details from '@/views/daily/actualDetail';//

  export default {
    components: {BlockCard,FileUpload,CheckTree,Recipient,ModifyRecord,Process,Details},
    props: {
      field:{
        type: String
      },
    },
    data() {
      return {
        dailyVisible:false,
        flowParamsUrl:'',
        selectTree:[],
        VisibleCheckTree:false,
        url:'colligate/violActualInvolveUnit/actualInvolveUnitTreeData',
        actualProblemId: "",
        relevantTableId: undefined,
        relevantTableName: undefined,
        edit: true,
        flag:false,
        visible:false,
        visibleTree:false,
        detailInfo:'',
        findTime: null,
        acceptTime: null,
        problemSource:null,
        problemTitle: null,
        problemDescribe: undefined,
        contactsTel: undefined,
        lossAmount: 0,
        lossRisk: 0,
        groupReceivers: undefined,
        provinceReceivers: undefined,
        seriousAdverseEffectsFlag: 1,
        otherSeriousAdverseEffects: undefined,
        illegalActivities: undefined,
        companyContacts: undefined,
        involveUnitGrade:'',
        specList: [],
        problemSourceList:[],
        unitData:[],
        groupData:{},//待阅接收人
        receiverGrade:'G',
        processDefinitionKey:''
      }
    },
    computed: {},
    watch: {
      "detailInfo.contactsTel": function(curVal, oldVal) {
        if (!curVal) {
          this.detailInfo.contactsTel = "";
          return false;
        }
        // 实时把非数字的输入过滤掉
        this.detailInfo.contactsTel = curVal.match(/\d/gi) ? curVal.match(/\d/gi).join("") : "";
      }
    },
    created() {
    },
    mounted() {
    },
    methods: {
      //附件下载
      fileDown(data){
        saveThirtyReport(this.detailInfo).then(response => {
          if (200 === response.code) {
            generateTemplateWithContent({
              problemId: this.actualProblemId,
              busiTableId: this.relevantTableId,
              busiTableName: this.relevantTableName,
              templateCode: data.fileTemplate
            }).then(response => {
              this.download('/sys/attachment/downloadSysAttachment/' + response.data, {}, data.modelFileName);
            });
          } else {
            this.$modal.alertError(response.msg);
          }
        });
      },
      cancel(){
        this.visible=false;
        this.$emit('reportList');
      },
      /**初始化数据*/
      show(){
        this.visible=true;
        waitHandleThirtyReport(this.field).then(
          response => {
            const { code, data } = response
            if (code === 200) {
              this.detailInfo = Object.assign({}, data);
              this.actualProblemId = this.detailInfo.actualProblemId;
              this.relevantTableId = this.detailInfo.id;
              this.relevantTableName = this.detailInfo.businessTable;
              this.detailInfo.businessTable = this.relevantTableName;
              this.$nextTick(()=>{
                this.$refs.file.ViolationFileItems();
              });
              this.QueryFiveReportInvolveUnit();
            }
          }
        );
      },
      //企业数据
      QueryFiveReportInvolveUnit(){
        queryActualInvolveUnit({actualProblemId: this.detailInfo.actualProblemId, relevantTableId: this.detailInfo.id}).then(
          response => {
            this.selectTree = [];
            this.detailInfo.involveUnitGrade = response.involveUnitGrade;
            this.unitData = response.data;
            for(let i=0;i<this.unitData.length;i++){
              this.selectTree.push({id:this.unitData[i].compareId,name:this.unitData[i].involveUnitName})
            }
          }
        );
      },
      //企业删除
      unitDel(item) {
        deleteActualInvolveUnit(item.id).then(response => {
          if (200 === response.code) {
            this.$modal.msgSuccess('删除成功');
            this.QueryFiveReportInvolveUnit();
          } else {
            this.$modal.alertError(response.msg);
          }
        });
      },
      //点击保存企业
      savePers(){
        this.$refs.checkTree.list();
      },
      //返回数据
      persList(data){
        let list=[];
        this.index++;
        if(!data.length)
          return false;
        for (let i = 0; i < data.length; i++) {
          list.push(data[i].id);
        }

        let parameter = {
          actualProblemId: this.detailInfo.actualProblemId,
          relevantTableId: this.relevantTableId,
          relevantTableName: this.relevantTableName,
          waitSaveUnitCodes: list
        };

        saveActualInvolveUnitData(parameter).then(response => {
          if (200 === response.code) {
            this.$modal.msgSuccess('保存成功');
            this.QueryFiveReportInvolveUnit();
            this.VisibleCheckTree = false;
          } else {
            this.$modal.alertError(response.msg);
          }
        });
      },
      /**提交数据*/
      submitForm() {
        const reg = /^[1][2,3,4,5,6,7,8,9][0-9]{9}$/;
        if (this.detailInfo.contactsTel == '') {
          this.$modal.msgError("【联系电话】不能为空！");
          return false;
        }else if ((!reg.test(this.detailInfo.contactsTel))) {
          this.$modal.msgError("【联系电话】格式不正确！");
          return false;
        } else {
          saveThirtyReport(this.detailInfo).then(response => {
            if (200 === response.code) {
              this.modifyRecord();
            } else {
              this.$modal.alertError(response.msg);
            }
          });
        }
      },
      //修改记录
      modifyRecord() {
        thirtyReportCompareWithDailyProblem(this.detailInfo).then(response => {
          if (200 === response.code) {
            if (response.data.findDifferences) {
              this.$refs.modify.show(response.data);
            } else {
              this.submitReport();
            }
          } else {
            this.$modal.alertError(response.msg);
          }
        });
      },
      //修改记录保存
      saveModify(){
        this.submitReport();
      },
      //提交
      submitReport(){
        submitThirtyReport(this.detailInfo).then(response => {
          if (200 === response.code) {
            this.selProblemInfo();
          } else {
            this.$modal.alertError(response.msg);
          }
        });
      },
      //下一步
      selProblemInfo(){
        selProblemInfo(this.actualProblemId).then(response => {
          if (response.data.procInsId) {
            this.$modal.msgError("该项目流程已经发起，不可再次发起！");
          } else {
            if ("G" === response.data.orgGrade) {
              this.processDefinitionKey = "AccountabilityForViolationsJT";
            } else if ("P" === response.data.orgGrade) {
              this.processDefinitionKey = "AccountabilityForViolationsSF";
            } else {
              this.processDefinitionKey = "AccountabilityForViolationsDS"
            }
            this.startProcessBtn();
          }
        });
      },
      //流程提交
      startProcessBtn(){
        this.flowParamsUrl="/colligate/violActual/flowParams";
        this.$nextTick(()=>{
          this.$refs.process.handle(1);
        })
      },
      //保存
      save() {
        saveThirtyReport(this.detailInfo).then(response => {
          if (200 === response.code) {
            this.$modal.msgSuccess("保存成功");
          } else {
            this.$modal.alertError(response.msg);
          }
        });
      },
      resetForm() {
        this.$refs['elForm'].resetFields()
      },
      //选择企业单位
      addCheckTree(){
        this.VisibleCheckTree=true;
      },
      //选择人员
      treeOpen(){
        this.flag = !this.flag;
        this.visibleTree = true;
      },
      //日常详情
      dailyDetail(){
        this.dailyVisible=true;
      },
      //日常关闭
      dailyClose(){
        this.dailyVisible=false;
      }
    }
  }

</script>
<style lang="scss" scoped>
  .dialog-body {
    height: 70vh;
  }
  .depart_li {
    min-width: 84px;
    height: auto;
    position: relative;
    background-color: #e6f7ff;
    color: #40a9ff;
    line-height: 30px;
    margin: 0 6px 0;
    display: inline-block;
    padding: 0 30px 0 12px;
    border-radius: 2px;
    box-sizing: border-box;
    .icon {
      float: right;
      cursor: pointer;
      position: absolute;
      right: 8px;
      top: 6px;
      font-size: 16px;
    }
  }
</style>
