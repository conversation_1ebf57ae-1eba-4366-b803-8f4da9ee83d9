<!--7:审核页面-->
<template>
  <div>
    <div>
      <ModifyrecordBtn
        :key="problemId||relevantTableId||relevantTableName"
        :problemId="problemId"
        :relevantTableId="relevantTableId"
        :relevantTableName="relevantTableName"
        :problemStatus="7"
      ></ModifyrecordBtn>
      <BlockCard title="违规问题线索有关情况">
        <el-row>
          <el-form
            ref="elForm"
            :model="formData"
            :rules="rules"
            size="medium"
            label-width="138px"
          >
            <el-col :span="8">
              <el-form-item label="系统编号" prop="auditCode">
                <span>{{ formData.auditCode }}</span>
              </el-form-item>
            </el-col>

            <el-col :span="8">
              <el-form-item label="问题编号" prop="problemCode">
                <span>{{ formData.problemCode }}</span>
              </el-form-item>
            </el-col>
            <p></p>
            <el-col :span="8">
              <el-form-item label="发现日期" prop="findTime">
                <span>{{ formData.findTime }}</span>
              </el-form-item>
            </el-col>

            <el-col :span="8">
              <el-form-item label="受理日期" prop="acceptTime">
                <span>{{ formData.acceptTime }}</span>
              </el-form-item>
            </el-col>

            <el-col :span="24">
              <el-form-item label="违规事项" prop="problemTitle">
                <span>{{ formData.problemTitle }}</span>
              </el-form-item>
            </el-col>

            <el-col :span="24">
              <el-form-item label="问题线索描述" prop="problemDescribe">
                <span>{{ formData.problemDescribe }}</span>
              </el-form-item>
            </el-col>

            <el-col :span="24">
              <el-form-item label="涉及专业线" prop="specLists">
                <el-checkbox-group
                  :key="formData.specLists"
                  v-model="formData.specLists"
                  size="medium"
                  disabled="true"
                >
                  <el-checkbox
                    v-for="item in specList"
                    :key="item.specCode"
                    border
                    :label="item.specCode"
                  >{{ item.specName }}</el-checkbox>
                </el-checkbox-group>
              </el-form-item>
            </el-col>

            <el-col :span="24">
              <el-form-item  label="涉及单位/部门/人员" prop="field107">
                <PersList
                  :key="problemId||relevantTableId||relevantTableName"
                  :edit='edit'
                  :problemId="problemId"
                  :relevantTableId="relevantTableId"
                  :relevantTableName="relevantTableName"
                  ref="pers"
                ></PersList>
              </el-form-item>
            </el-col>
          </el-form>
        </el-row>
      </BlockCard>
      <Remind
        :key="formData.actualFlag"
        :actualFlag="formData.actualFlag"
      ></Remind>
      <BlockCard title="责任追究">
        <el-row>
          <el-form
            ref="elForm"
            :model="formData"
            :rules="rules"
            size="medium"
            label-width="138px"
          >
            <el-col :span="6">
              <el-form-item label="追责总人次" prop="auditCode">
                <span>{{ formData.accountabilityNumber }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="责任追究结果" prop="accountabilityResult">
                <span>{{formData.accountabilityResult}}</span>
              </el-form-item>
            </el-col>
          </el-form>
        </el-row>
      </BlockCard>
      <BlockCard title="整改信息">
        <el-row>
          <el-form
            ref="elForm"
            :model="formData"
            :rules="rules"
            size="medium"
            label-width="138px"
          >

            <el-col :span="6">
              <el-form-item label="整改完成时间" prop="reformFinishDate">
               {{formData.reformFinishDate}}
              </el-form-item>
            </el-col>

            <el-col :span="24">
              <el-form-item label="整改责任单位" prop="reformResponsibleUnits" class="input-btn">
                <div class="list1">
                  <div
                    v-for="(item, index) of formData.reformResponsibleUnits"
                    :key="index"
                    class="list1-one"
                  >
                    <span>{{ item.reformUnitName }}</span>
                  </div>
                </div>
              </el-form-item>
            </el-col>

            <el-col :span="24">
              <el-form-item label="整改责任人" prop="reformResponsiblePersons" class="input-btn">
                <div class="list1">
                  <div
                    v-for="(item, index) of formData.reformResponsiblePersons"
                    :key="index"
                    class="list1-one"
                  >
                    <span>{{ item.reformPersonName }}</span>
                  </div>
                </div>
              </el-form-item>
            </el-col>

            <el-col :span="6">
              <el-form-item label="完善制度数量" prop="perfectSystemNumber">
                <span>{{ formData.perfectSystemNumber }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="挽回损失金额(万元)" prop="retrieveLossAmount">
                <span>{{ formData.retrieveLossAmount }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="降低损失风险(万元)" prop="reduceLossRisk">
                <span>{{ formData.reduceLossRisk }}</span>
              </el-form-item>
            </el-col>

            <el-col :span="24">
              <el-form-item label="完善制度名称" prop="perfectSystemName">
                <span>{{ formData.perfectSystemName }}</span>
              </el-form-item>
            </el-col>

            <el-col :span="24">
              <el-form-item label="采取的主要措施" prop="reformMeasure">
                <span>{{ formData.reformMeasure }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="整改效果" prop="reformEffect">
                <span>{{ formData.reformEffect }}</span>
              </el-form-item>
            </el-col>

            <el-col :span="24">
              <el-form-item label="整改复核情况" prop="reformReview">
                <span>{{ formData.reformReview }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="其他工作成效" prop="otherAchievement">
                <span>{{ formData.otherAchievement }}</span>
              </el-form-item>
            </el-col>
          </el-form>
        </el-row>
      </BlockCard>
      <BlockCard
        title="附件列表"
      >
        <FileUpload
          :edit='edit'
          :key="problemId||formData.id||formData.businessTable"
          :problemId="problemId"
          :relevantTableId="formData.id"
          :busiTable = "formData.businessTable"
          :relevantTableName="formData.businessTable"
          flowType="VIOL_DAILY"
          problemStatus="6"
          flowKey = "SupervisionDailyReport"
          ref="file"
        ></FileUpload>

      </BlockCard>

    </div>
    <el-dialog :visible.sync="VisibleCheckTree" width="60%" append-to-body title="整改责任单位">
      <Check2Tree
        v-if="VisibleCheckTree"
        :key="index"
        ref="checkTree"
        :url="url1"
        :selectTree="selectTree"
        :params="{
        unitName:'',
        problemId:problemId,
        businessId:formData.id
        }"
        @list="persList"
      />
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="savePers">保存</el-button>
      </div>
    </el-dialog>

    <el-dialog :visible.sync="VisibleCheckTree2" width="60%" append-to-body title="整改责任人">
      <CheckTree
        v-if="VisibleCheckTree2"
        :key="index"
        ref="checkTree"
        :url="url2"
        :selectTree="selectTree"
        :params="{
        userName:'',
        problemId:problemId,
        businessId:formData.id
        }"
        @list="persLists"
      />
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="savePer">保存</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { submitHisData } from '@/api/daily/historyQuestionEnter/index'
  import BlockCard from '@/components/BlockCard'
  import Remind from '@/views/components/remind';
  import FileUpload from '@/views/components/fileUpload';//附件
  import Check2Tree from '@/views/daily/tree/check2Tree'// checkTree
  import CheckTree from '@/views/daily/tree/check2Tree'// checkTree
  import PersList from '@/views/daily/tree/persList';//tree
  import ModifyrecordBtn from '@/views/daily/modifyRecord/btn'
  import { queryDailyCheckInfo,temporarySaveViolationReformRecord,saveReformResponsibleUnit,saveReformResponsiblePerson,deleteReformResponsibleUnit,deleteReformResponsiblePerson } from '@/api/daily/process/rectificationRecord'

export default {
    components: {
      BlockCard,
      Remind,
      Check2Tree,
      CheckTree,FileUpload,ModifyrecordBtn,PersList
    },
    dicts: ['VIOLD_DAILY_SPEC', 'VIOLD_ADVER_EFFECT_DES'],
    props: {
      isShow:{
        type: String,
        default: '0'
      },
      procInsId:{
        type: String
      },
      problemId: {
        type: String
      },
    },
    data() {
      return {
        relevantTableId:'',
        relevantTableName:'',
        rules: {},
        edit:false,
        url1:'/colligate/violationDailyReform/responsibleUnitCheckedTree',
        url2:'/colligate/violationDailyReform/nationalOrganizationPersonTree',
        flag: false,
        visible: false,
        visibleTree: false,
        VisibleCheckTree:false,
        VisibleCheckTree2:false,
        formData: {
          accountabilityFlag: '',
          actualFlag: '',
          appealFlag: '',
          appealResult: '',
          appealResultList: [],
          auditCode: '',
          createBy: '',
          createByOrg: '',
          createLoginName: '',
          createPostId: '',
          createTime: '',
          dealPostIdPre: '',
          deletedFlag: '',
          expandFileTypeOptions: [],
          id: '',
          investigateTime: '',
          isCompleted: '',
          problemAreaCode: '',
          problemAreaCodePre: '',
          problemAreaName: '',
          problemAreaNamePre: '',
          problemCode: '',
          problemDescribe: '',
          problemId: '',
          problemProvCode: '',
          problemProvCodePre: '',
          problemProvName: '',
          problemProvNamePre: '',
          problemTitle: '',
          relevantTableName: '',
          relevorgList: [],
          reviewerList: [],
          specSelectedList: [],
          unInvestigateReason: '',
          updateBy: '',
          updateByOrg: '',
          updateLoginName: '',
          updatePostId: '',
          updateTime: ''
        },
        specList: [],

        seriousAdverseEffectsFlagOptions: [
          {
            label: '是',
            value: 1
          },
          {
            label: '否',
            value: 0
          }
        ]
      }
    },
    computed: {},
    watch: {},
    created() {
      this.queryDailyCheckInfo();
    },
    mounted() {},
    methods: {
      //保存数据
      savePers(){
        this.$refs.checkTree2.list();
      },
      //数据整改责任单位
      persList(data){
        let list=[];
        if(!data.length)
          return false;
        for (let i = 0; i < data.length; i++) {
          list.push(data[i].id);
        }
        let query = {
          id: this.formData.id,
          problemId:this.problemId,
          waitSaveReformResponsibleCodes:list
        };
        saveReformResponsibleUnit(query).then(
          response => {
            const { code, data } = response;
            if (code === 200) {
              this.VisibleCheckTree = false;
              this.queryDailyCheckInfoPer();
            }
          }
        )
      },
      //保存数据
      savePer(){
        this.$refs.checkTree.list();
      },
      //数据整改责任人
      persLists(data){
        let list=[];
        if(!data.length)
          return false;
        for (let i = 0; i < data.length; i++) {
          list.push(data[i].id);
        }
        let query = {
          id: this.formData.id,
          problemId:this.problemId,
          waitSaveReformResponsibleCodes:list
        };
        saveReformResponsiblePerson(query).then(
          response => {
            const { code, data } = response;
            if (code === 200) {
              this.VisibleCheckTree2 = false;
              this.queryDailyCheckInfoPer();
            }
          }
        )
      },
      /**初始化数据*/
      queryDailyCheckInfo () {
        this.loading = true;
        let array = [];
        queryDailyCheckInfo(this.problemId).then(
          response => {
            let specSelectedList = response.data.involveProfessionalLines;
            this.formData = {...this.formData, ...response.data};
            for (let i = 0, len = specSelectedList.length; i < len; i++) {
              array.push(specSelectedList[i].specCode);
            }
            this.actualFlag = response.data.actualFlag;
            this.lossRiskTypeOptions = response.data.lossRiskTypeOptions;
            this.formData.specLists = array;
            this.specList = specSelectedList;
            this.relevantTableId = response.data.id;
            this.problemSourceList = response.data.problemSourceList;
            this.relevantTableName = response.data.businessTable;
            this.loading = false;
            this.$nextTick(() => {
               this.$refs.pers.DueryDepartmentSelectInfo();
              this.$refs.file.ViolationFileItems();
            });
            this.$emit('closeLoading');
          }
        );
      },
      /**初始化整改责任单位与整改责任人*/
      queryDailyCheckInfoPer () {
        queryDailyCheckInfo(this.problemId).then(
          response => {
            this.formData.reformResponsibleUnits = response.data.reformResponsibleUnits;
            this.formData.reformResponsiblePersons = response.data.reformResponsiblePersons;
          }
        );
      },
      /** 保存数据*/
      publicSave() {

      },
      /**提交数据*/
      nextStep() {
        this.$emit('handle',1,{
          orgGrade: this.formData.orgGrade,
          isJTHandover: this.formData.isGroupHandover
        });
      },
      resetForm() {
        this.$refs['elForm'].resetFields()
      },
      // 打开弹窗
      show() {
        this.visible = true
      },
      addReformResponsibleUnits() {
        this.VisibleCheckTree = true;
      },
      addReformResponsiblePersons() {
        this.VisibleCheckTree2 = true;
      },
      //删除单位
      deletetReformResponsibleUnits(item,index){
        deleteReformResponsibleUnit(item.id).then((response) => {
          this.queryDailyCheckInfoPer();
        })
      },
      //删除责任人人
      deletetReformResponsiblePersons(item,index){
        deleteReformResponsiblePerson(item.id).then((response) => {
          this.queryDailyCheckInfoPer();
        })
      },
      //最终提交
      submitHisDataFun(){
        this.$confirm('是否提交？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          const loading = this.$loading({
            spinner: 'el-icon-loading', // 自定义加载图标类名
            text: '正在加载...', // 显示在加载图标下方的加载文案
            lock: false, // lock的修改符--默认是false
          });
          submitHisData(this.problemId).then(
            response => {
              loading.close();
              this.$modal.msgSuccess("提交成功");
              this.closeEmit();
            }
          ).catch(err => {
            loading.close();
          })
        })
      },
      //流程提交后推进到下一个环节
      closeEmit(){
        this.$emit('hisNext')
      }

    }
  }
</script>
<style scoped lang="scss">
  .input-btn {
    ::v-deep .el-form-item__content {
      display: flex;
      button {
        margin-left: 8px;
        height: 35px;
      }
    }
  }
  .float-right {
    float: right;
  }
  .edit-span {
    white-space: normal;
    overflow-y: auto;
    overflow-wrap: break-word;
    word-break: normal;
    height: 61px;
    line-height: 30px;
    text-align: left;
    padding: 0px 10px;
    display: block;
  }
  ::v-deep .editStyle {
    padding: 0px !important;
  }
  ::v-deep .editStyle div.cell {
    padding: 0px !important;
  }

  ::v-deep .editStyle .el-input--mini .el-input__inner {
    height: 56px;
    line-height: 56px;
    border: 0px;
  }
  .list1 {
    overflow: hidden;
    .list1-one {
      background-color: #e6f7ff;
      color: #40a9ff;
      margin: 0 10px 10px 10px;
      float: left;
      height: 30px;
      line-height: 30px;
      padding: 0 12px 0 12px;
      border-radius: 2px;
      .close {
        padding: 8px;
        cursor: pointer;
      }
    }
  }
  .list2 {
    display: flex;
    align-items: center;
    .list2-one {
      width: 250px;
      height: 30px;
      line-height: 30px;
      margin: 0 0 12px 0;
      display: inline-block;
      padding: 0 6px 0 12px;
      border-radius: 2px;
      box-sizing: border-box;
      position: relative;
      margin-right: 35px;
      span {
        text-align: left;
        display: inline-block;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        word-break: break-all;
        max-width: 100%;
        padding-right: 6px;
        box-sizing: border-box;
      }
    }
    .list2-one-bg {
      background: #f4f4f4;
      border-radius: 4px;
      color: #000;
      width: 250px;
      height: 30px;
      line-height: 30px;
      margin: 0 0 12px 0;
      display: inline-block;
      padding: 0 6px 0 12px;
      border-radius: 2px;
      box-sizing: border-box;
      position: relative;
      margin-right: 35px;

      span {
        text-align: left;
        display: inline-block;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        word-break: break-all;
        max-width: 100%;
        padding-right: 6px;
        box-sizing: border-box;
      }
      &::before {
        position: absolute;
        content: "";
        right: -30px;
        top: 15px;
        width: 25px;
        height: 1px;
        background: #d9d9d9;
      }
    }
  }
  .bottom-line {
    padding: 10px 0;
    text-align: center;
    border-top: 1px solid #d9d9d9;
    color: #f5222d !important;
  }
</style>
