<template>
  <div>
    <!--查看-->
    <div v-if="type=='actual'">
      <ActualThirtyDaysReportRead
        ref="todo"
        v-if="status==3"
        :detail="detail"
        :field="actualProblemId"
        @handle="handle"
      ></ActualThirtyDaysReportRead>

      <ActualProgressReportRead
        ref="todo"
        v-if="status==4"
        :detail="detail"
        :field="actualProblemId"
        @handle="handle"
      ></ActualProgressReportRead>

      <ActualCheckDisReportRead
        ref="todo"
        v-if="status==5"
        :detail="detail"
        :field="actualProblemId"
        @handle="handle"
      ></ActualCheckDisReportRead>
    </div>
    <!--编辑-->
    <div v-else>
      <ActualThirtyDaysReport
        ref="todo"
        v-if="status==3"
        :detail="detail"
        :field="actualProblemId"
        @handle="handle"
      ></ActualThirtyDaysReport>

      <ActualProgressReport
        ref="todo"
        v-if="status==4"
        :detail="detail"
        :field="actualProblemId"
        @handle="handle"
      ></ActualProgressReport>

      <ActualCheckDisReport
        ref="todo"
        v-if="status==5"
        :detail="detail"
        :field="actualProblemId"
        @handle="handle"
      ></ActualCheckDisReport>
    </div>
  </div>
</template>

<script>
  import {getProcessStatus} from '@/api/actual/index';
  import ActualThirtyDaysReportRead from './../detail/actualThirtyDaysReportRead';//30日
  import ActualProgressReportRead from './../detail/actualProgressReportRead';//后续工作进展报告
  import ActualCheckDisReportRead from './../detail/actualCheckDisReportRead';//核查处置结果报告

  import ActualThirtyDaysReport from './actualThirtyDaysReport';//30日
  import ActualProgressReport from './actualProgressReport';//后续工作进展报告
  import ActualCheckDisReport from './actualCheckDisReport';//核查处置结果报告
  export default {
    name: "index",
    props: {
      selectValue: {
        type: Object
      },
      centerVariable: {
        type: Object
      },
      type: {
        type: String
      },
    },
    components: {
      ActualThirtyDaysReportRead,
      ActualProgressReportRead,
      ActualCheckDisReportRead,
      ActualThirtyDaysReport,
      ActualProgressReport,
      ActualCheckDisReport
    },
    data() {
      return {
        detail: true,
        status:'',
        actualProblemId: ''
      }
    },
    created() {
      // 初始化跳转页面
      this.GetProcessStatus();
      if(this.type=='actual'){//查看去掉保存
        this.$emit('saveBtn',false);
      }else{
        this.$emit('saveBtn',true);
      }
    },
    methods: {
      GetProcessStatus() {
        getProcessStatus(this.selectValue.processInstanceId).then(
          response => {
            const {code, data} = response
            if (code === 200) {
              this.status = data.status;
              this.actualProblemId = data.actualProblemId;
              this.$nextTick(()=>{
                this.$refs.todo.show();
              })
            }
          }
        );
      },
      //保存
      publicSave(){
        this.$refs.todo.save();
      },
      //流程提交
      nextStep() {
        this.$refs.todo.nextStep();
      },
      //流程提交
      handle(type) {
        this.$emit('handle', type);
      },
    }
  }
</script>

<style scoped>

</style>
