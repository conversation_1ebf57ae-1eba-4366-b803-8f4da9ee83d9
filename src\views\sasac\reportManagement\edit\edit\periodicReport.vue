<template>
  <div>
    <el-dialog  :visible.sync="visible" width="90%" append-to-body  @close="close" title="定期报告">
    <BlockCard title="上报说明">
      <el-form class="common-card padding10_0" size="medium"  label-width="160px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="定期报告">
              <el-select
                         :style="{width: '100%'}"
                         v-model="reportIntervalsCode"
              @change="changeCode"
               value="reportIntervalsCode">
                <el-option
                  label="请选择"
                  value=""
                />
                <el-option
                  v-for="(item, index) in reportIntervals"
                  :key="index"
                  :label="item.dictLabel"
                  :value="item.dictValue"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24"></el-col>
          <el-col :span="12">
            <el-form-item label="定期报告名称">
              <el-input  v-model="reportData.fixDateReportName"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="报送次数"><span>{{reportData.reportNumber}}</span></el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="报送年度"><span>{{reportData.reportYear}}</span></el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </BlockCard>
    <BlockCard
      title="定期报告附表"
    >
<!--      <el-row class="problem_list margin-b10">-->
<!--        <div class="problem_list_left float-left" v-if="reportData.unitStatisticsData">-->
<!--        <span>{{reportData.unitStatisticsData[0].unitName}}（{{reportData.unitStatisticsData[0].problemNumber}}）-->
<!--        <br>-->
<!--            ({{reportData.unitStatisticsData[0].reportProblemNumber}})-->
<!--        </span>-->
<!--        </div>-->
<!--        <div class="problem_list_right float-left">-->
<!--          <span v-for="(item,index) in reportData.unitStatisticsData" v-if="index>0">{{item.unitName}}（{{item.problemNumber}}）</span>-->
<!--        </div>-->
<!--      </el-row>-->
      <div>
        <div  class="dingqi-title" v-if="reportData.unitStatisticsData">
        <span  class="dingqi-text">全部定期报告数为：<b>{{reportData.unitStatisticsData[0].problemNumber}}</b>，</span>
        <div v-if="reportData.unitStatisticsData.length>1"  class="dingqi-text"> 包含：<div v-for="(item,index) in reportData.unitStatisticsData"  v-if="index>0" class="dingqi-text">{{item.unitName}}（<b>{{item.problemNumber}}</b>）<span v-show="index!=reportData.unitStatisticsData.length-1">、</span></div>
         </div>
          <br>
         <span class="dingqi-text">已选数据：<b>{{reportData.unitStatisticsData[0].reportProblemNumber}}</b></span>
        </div>
      </div>
      <el-table
        border
        :data="reportData.regularProblems"
        @selection-change="handleSelectionChange"
        ref="table"
        height="60vh"
      >
        <el-table-column
          fixed
          align="center"
          type="selection"
          width="40">
        </el-table-column>
        <el-table-column
          fixed
          align="center"
          label="序号"
          type="index"
          width="50">
        </el-table-column>

        <el-table-column fixed align="center" label="审计系统编号" prop="systemCode" width="200" show-overflow-tooltip/>
        <el-table-column align="center" label="问题编号" prop="problemCode" width="200" show-overflow-tooltip/>
        <el-table-column align="center" label="涉及企业名称" prop="involveCompany" width="200" show-overflow-tooltip/>
        <el-table-column align="center" label="企业层级" prop="involveCompanyLevel" width="200" show-overflow-tooltip/>

        <el-table-column align="center" label="违规问题线索有关详情" prop="prov1" width="2000" show-overflow-tooltip>
          <el-table-column align="center" label="问题线索来源" prop="problemClues" width="200" show-overflow-tooltip/>
          <el-table-column align="center" label="问题受理时间" prop="acceptDate" width="200" show-overflow-tooltip/>
          <el-table-column align="center" label="是否为以前年度定期报告反映的问题" prop="carryover" width="200" show-overflow-tooltip/>
          <el-table-column align="center" label="问题描述" prop="problemDescribtion" width="200" show-overflow-tooltip/>
          <el-table-column align="center" label="问题类别" prop="problemType" width="200" show-overflow-tooltip/>
          <el-table-column align="center" label="违反具体规定" prop="violationRules" width="200" show-overflow-tooltip/>
          <el-table-column align="center" label="境内（外）" prop="abroad" width="200" show-overflow-tooltip>
            <template slot-scope="scope">
            <div v-if="scope.row.isGroupProblem">
              <el-radio-group v-model="scope.row.abroad" @change="replacingProvincialDat(1,scope.row.id,scope.row.abroad)">
                <el-radio label="境外" value="境外"></el-radio>
                <el-radio label="境内" value="境内"></el-radio>
              </el-radio-group>
            </div>
            <span v-else>{{scope.row.abroad}}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" label="涉及损失及风险（万元）" prop="involveRiskLoss" width="200" show-overflow-tooltip/>
          <el-table-column align="center" label="损失风险类别（一般/较大/重大资产损失）" prop="lossLossType" width="200" show-overflow-tooltip/>
          <el-table-column align="center" label="损失形成主要原因" prop="lossMainCauses" width="200" show-overflow-tooltip/>
        </el-table-column>

        <el-table-column align="center" label="核查情况" prop="prov2" width="800" show-overflow-tooltip>
          <el-table-column align="center" label="核查状态" prop="auditStatus" width="200" show-overflow-tooltip/>
          <el-table-column align="center" label="核查时间" prop="auditDate" width="200" show-overflow-tooltip/>
          <el-table-column align="center" label="核查主体" prop="auditMainBody" width="200" show-overflow-tooltip/>
          <el-table-column align="center" label="未完成核查原因" prop="auditNonCompletionReasons" width="200" show-overflow-tooltip>
            <template slot-scope="scope">
              <div v-if="scope.row.isGroupProblem&&scope.row.needFillNotCheckReason">
                <el-input  v-model="scope.row.auditNonCompletionReasons" @blur="replacingProvincialDat(2,scope.row.id,scope.row.auditNonCompletionReasons)"></el-input>
              </div>
              <span v-else>
               {{scope.row.auditNonCompletionReasons}}
              </span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column align="center" label="责任追究工作开展情况" prop="prov3" width="2400" show-overflow-tooltip>
          <el-table-column align="center" label="是否追责" prop="zrzjIsNot" width="200" show-overflow-tooltip/>
          <el-table-column align="center" label="未追责原因" prop="zrzjNonReason" width="200" show-overflow-tooltip/>
          <el-table-column align="center" label="责任追究时间" prop="zrzjDate" width="200" show-overflow-tooltip/>
          <el-table-column align="center" label="追责总人数" prop="zrzjTotalPersons" width="200" show-overflow-tooltip/>
          <el-table-column align="center" label="追责总人次" prop="zrzjTotalPertime" width="200" show-overflow-tooltip/>
          <el-table-column align="center" label="责任追究处理方式（人次）" prop="prov24" width="1400" show-overflow-tooltip>
            <el-table-column align="center" label="组织处理（人次）" prop="zrzjOrgHandle" width="200" show-overflow-tooltip/>
            <el-table-column align="center" label="扣减薪酬" prop="a2" width="400" show-overflow-tooltip>
              <el-table-column align="center" label="人次" prop="zrzjCutSalary" width="200" show-overflow-tooltip/>
              <el-table-column align="center" label="金额（万元）" prop="zrzjCutSalaryMoney" width="200" show-overflow-tooltip/>
            </el-table-column>
            <el-table-column align="center" label="党纪处分（人次）" prop="zrzjPartyDiscipline" width="200" show-overflow-tooltip/>
            <el-table-column align="center" label="政务处分（人次）" prop="zrzjGovAffairs" width="200" show-overflow-tooltip/>
            <el-table-column align="center" label="禁入限制（人次）" prop="zrzjLimitPertime" width="200" show-overflow-tooltip/>
            <el-table-column align="center" label="移送国家检察机关或司法机关（人次）" prop="zrzjTransferJjjc" width="200" show-overflow-tooltip/>
            <el-table-column align="center" label="其他（人次）" show-overflow-tooltip prop="processingOtherItem" width="150"/>
          </el-table-column>
        </el-table-column>
        <el-table-column align="center" label="问题整改情况" prop="prov4" width="1000">
          <el-table-column  align="center" label="是否完成整改" prop="rectifyIsComplete" width="200" show-overflow-tooltip/>
          <el-table-column align="center" label="完善制度情况" prop="prov44" width="400" show-overflow-tooltip>
            <el-table-column align="center" label="数量（项）" prop="rectifyRulesNumber" width="200" show-overflow-tooltip/>
            <el-table-column align="center" label="制度名称、文号" prop="rectifyRulesName" width="200" show-overflow-tooltip/>
          </el-table-column>
          <el-table-column align="center" label="损失挽回情况" prop="prov44" width="400">
            <el-table-column align="center" label="金额（万元）" prop="rectifyRetrieveLoss" width="200" show-overflow-tooltip/>
            <el-table-column align="center" label="采取的主要措施" prop="rectifyMainMeasures" width="200" show-overflow-tooltip/>
          </el-table-column>
        </el-table-column>
        <el-table-column align="center" label="备注" prop="remark" width="200">
          <template slot-scope="scope">
            <div v-if="scope.row.isGroupProblem">
              <el-input  v-model="scope.row.remark" @blur="replacingProvincialDat(3,scope.row.id,scope.row.remark)"></el-input>
            </div>
            <span v-else>
               {{scope.row.remark}}
              </span>
          </template>
        </el-table-column>
      </el-table>
    </BlockCard>
    <BlockCard title="定期报告">
      <FieldList
        :key="reportData.regularReportId"
        :edit="edit"
        :id="reportData.id"
        :problemId="problemId"
        @fileDown="fileDown"
        :fileList="reportData.regularAttachmentItems"
      ></FieldList>
    </BlockCard>
      <div slot="footer">
        <el-button size="mini" @click="close()">取消</el-button>
        <el-button size="mini" type="primary" @click="save()">保存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import BlockCard from '@/components/BlockCard'
  import FieldList from '@/views/components/fileUpload/periodic'
  import {
    regularReportData,waitEditRegularReportData, switchReportInterval, saveEditableCell, saveEditedRegularReportData
  } from '@/api/sasac/reportManagement/edit/detail/index'
  import {downloadFilledDataTemplate} from "@/api/sasac/reportManagement/edit/detail/index";
  export default {
    name: "periodicReport",
    components:{BlockCard,FieldList},
    props: {
      problemId: {
        type: String
      }
    },
    data(){
      return{
        visible:false,
        formData:{},
        edit:true,
        reportIntervals:[],
        reportIntervalsCode:'',
        reportData:{},
        multipleSelection:[],//选中的值
      }
    },
    created(){
    },
    methods: {
      fileDown(data) {
        if(!this.reportData.fixDateReportName){
          this.$message.error('【定期报告名称】不能为空！');
          return false;
        }
        let tableData = this.reportData.regularProblems;
        let obj={
          fixDateReportName:this.reportData.fixDateReportName,
          id:this.reportData.id,
          regularProblems:tableData,
          checkedFixProblemIds:this.multipleSelection
        };
        saveEditedRegularReportData(obj).then(response => {
          if (200 === response.code) {
            this.$modal.msgSuccess('保存成功！');
            downloadFilledDataTemplate({reportSasacId: this.problemId, templateCode: data.fileTemplate, id: this.reportData.id}).then(response => {
              if (200 === response.code) {
                this.download('/colligate/violFile/downloadTemplateWithContent/' + response.data, {}, "定期报告模板.docx");
              } else {
                this.$modal.alertError(response.msg);
              }
            });
          } else {
            this.$modal.alertError(response.msg);
          }
        });

      },

      close(){
        this.visible=false;
        this.$emit('editClose',1);
      },
      // 获取数据
      onShow() {
        regularReportData(this.problemId).then(response => {
          this.formData = response.data;
          this.visible=true;
          this.WaitEditRegularReportData();
        });
      },
      //获取基本信息
      WaitEditRegularReportData(){
        waitEditRegularReportData(this.formData.id).then(response => {
          this.reportData = response.data;
          this.reportIntervals = this.reportData.reportIntervals;
          for(let i=0;i<this.reportIntervals.length;i++){
            if(this.reportIntervals[i].isDefault === 'Y'){
              this.reportIntervalsCode = this.reportIntervals[i].dictValue;
            }
          }

          this.$nextTick(()=>{
          this.reportData.regularProblems.forEach(row => {
              if(row.isReport==1){
                this.$refs.table.toggleRowSelection(row, true);
              }else{}
          });
          });

        });
      },
      //定期报告选择
      changeCode(val){
        this.reportIntervalsCode = val;
        if(val){
          switchReportInterval({id: this.formData.id, reportSasacId: this.problemId, regularReportId: val}).then(response => {
            if (200 === response.code) {
              this.onShow();
            } else {
              this.$modal.alertError(response.msg);
            }
          });
        }
      },
      //保存表格数据
      replacingProvincialDat(type, id, value) {
        let data = {};
        if (type === 1) {//修改境内（外）
          data = {
            id: id,
            isSaveDomesticOrForeign: true,
            abroad: value,
            needFillNotCheckReason: false,
            isSaveRemark: false
          }
        } else if (type === 2) {//未核查原因
          data = {
            id: id,
            isSaveDomesticOrForeign: false,
            needFillNotCheckReason: true,
            auditNonCompletionReasons: value,
            isSaveRemark: false
          }
        } else if (type === 3) {//备注
          data = {
            id: id,
            isSaveDomesticOrForeign: false,
            needFillNotCheckReason: false,
            isSaveRemark: true,
            remark: value
          }
        }
        saveEditableCell(data).then(response => {
          if (200 === response.code) {
            this.$modal.msgSuccess(response.msg);
          } else {
            this.$modal.alertError(response.msg);
          }
        });
     },
      //保存选中结果
      handleSelectionChange(val) {
        this.multipleSelection = [];
        for(let i=0;i<val.length;i++){
          this.multipleSelection.push(val[i].id);
        }
      },
      /**保存或提交*/
      save(){
        if(!this.reportData.fixDateReportName){
          this.$message.error('【定期报告名称】不能为空！');
          return false;
        }
        let tableData = this.reportData.regularProblems;
        let data={
          fixDateReportName:this.reportData.fixDateReportName,
          id:this.reportData.id,
          regularProblems:tableData,
          checkedFixProblemIds:this.multipleSelection
        };
        console.log("传参：", data)
        saveEditedRegularReportData(data).then(response => {
          if (200 === response.code) {
            this.$modal.msgSuccess('保存成功！');
            this.close();
          } else {
            this.$modal.alertError(response.msg);
          }
        });
      }
    }
  }
</script>

<style scoped lang="scss">
.dingqi-title{
  margin-bottom:10px;
  display: inline-block;
  padding:4px 10px;
  color:#000;
  background-color: #FFF0F0;
  .dingqi-text{
    display: inline-block;
    b{
      font-weight: 900;
    }
  }
}
  .problem_list{
    width: 100%;
  }
  .problem_list_left{
    width: 90px;
    height: 40px;
  }
  .problem_list_right{
    width: calc(100% - 90px);
  }
  .problem_list span{
    width: 90px;
    height: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    float: left;
    border:1px solid #ddd;
    text-align: center;
  }
</style>
