import request from '@/utils/request'


// 下一环节名称
export function flowParams(url) {
  return request({
    url: url,
    method: 'post'
  })
}

// 下一环节名称
export function processLinkData(processDefinitionKey) {
  return request({
    url: '/workflowRestController/tasklinkforstart/'+processDefinitionKey,
    method: 'get'
  })
}

// 下一环节处理人
export function refreshNextAssignee(data) {
  return request({
    url: '/colligate/violActual/refreshNextAssignee',
    method: 'post',
    data: data
  })
}

// 通过或者退回下一环节名称
export function tasklink(processInstanceId,linkKey,processDefinitionKey,flowKeyReV,handleType) {
  return request({
    url: '/workflowRestController/tasklink/'+processInstanceId+'/'+linkKey+'/'+processDefinitionKey+'/'+flowKeyReV+'/'+handleType,
    method: 'get'
  })
}

// 退回下一环节处理人
export function backAssignee(data) {
  return request({
    url: '/workflowRestController/refreshBackAssignee',
    method: 'post',
    data: data
  })
}


// 流程启动并送审
export function startAndSubmitProcess(data) {
  return request({
    url: '/colligate/violActual/startProcess',
    method: 'post',
    data: data
  })
}

// 流程推进
export function pushProcess(data) {
  return request({
    url: '/colligate/violActual/pushProcess',
    method: 'post',
    data: data
  })
}

// 退回
export function backProcess(data) {
  return request({
    url: '/colligate/violActual/backProcess',
    method: 'post',
    data: data
  })
}

// 已办撤回
export function withdrawProcess(data) {
  return request({
    url: '/workflowRestController/withdrawProcess',
    method: 'post',
    data: data
  })
}

// 根据所在环节查询需展现的自定义标签
export function taburls(processDefinitionId,taskDefinitionKey,tabFlag) {
  return request({
    url: '/workflowRestController/taburls/'+processDefinitionId+'/'+taskDefinitionKey+'/'+tabFlag,
    method: 'get'
  })
}

// 获取待办页面业务主页面及参数
export function tasktodopath(data) {
  return request({
    url: '/colligate/violActual/tasktodopath/'+data.processInstanceId+'/'+data.linkKey+'/'+data.taskId+'/'+data.typeId,
    method: 'get'
  })
}

// 获取已办页面业务主页面及参数
export function taskhasdonepath(data) {
  return request({
    url: '/workflowRestController/taskhasdonepath/'+data.processInstanceId+'/'+data.linkKey+'/'+data.taskId+'/'+data.typeId,
    method: 'get'
  })
}

// 获取流程图地址
export function flowChatData(data) {
  return request({
    url: '/workflowRestController/getProcessChartByProcInstId/'+data.processInstanceId,
    method: 'get'
  })
}

// 获取日常环节
export function selectStatusAndType(data) {
  return request({
    url: '/colligate/violDaily/selectStatusAndType',
    method: 'post',
    data: data
  })
}

// 获取日常环节
export function selectViolationStatus(data) {
  return request({
    url: '/colligate/violDaily/selectViolationStatus',
    method: 'post',
    data: data
  })
}

// 获取环节名称
export function selectDailyFlowInfo(data) {
  return request({
    url: '/colligate/violQuery/selectDailyFlowInfo',
    method: 'post',
    data: data
  })
}

