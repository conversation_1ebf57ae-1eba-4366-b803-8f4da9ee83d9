<template>
  <div>
    <el-row>
          <el-card class="box-card"  shadow="never">
            <div slot="header" class="clearfix">
              <el-row :gutter="10">
                <el-col :span="12">
                  <el-input
                    placeholder="输入关键字进行过滤"
                    v-model="query.userName">
                  </el-input>
                </el-col>
                <el-col :span="4" style="padding-top:4px;">
                  <el-button  type="primary" icon="el-icon-search" size="mini" @click="treeQuery">搜索</el-button>
                </el-col>
              </el-row>
            </div>
            <el-col :span="16" class="tree-box">
              <el-tree ref="tree"
                       :data="data"
                       lazy
                       show-checkbox
                       :default-checked-keys="defaultTree"
                       node-key="id"
                       check-strictly
                       @check-change="checkChange"
                       :load="loadnode"
                       :props="defaultProps"
                       @node-click="nodeclick">
              </el-tree>
            </el-col>
            <el-col :span="8" class="tree-box">
              <TreeSelect
                :key="index"
                :selectTree="selectTree"
                @noCheck="noCheck"
              >
              </TreeSelect>
            </el-col>
          </el-card>
    </el-row>
  </div>

</template>

<script>
  import {treeUrl} from "@/api/components/index";
  import TreeSelect from '@/components/TreeSelect/checked';

  export default {
    name:'checkTree',
    components: {
      TreeSelect
    },
    props: {
      selectTree:[],
      url:'',
      params:{}
    },
    data() {
      return {
        data:[],
        defaultTree:[],
        index:0,
        query:{
          selectBtn:false,
          userName:'',
          name:'',
          areaCode:'',
          isParent:'',
          provCode:'',
          checked:'',
          id:'',
          pId:'',
          isAll:false,
          open:false,
          nocheck:'',
          userId:'',
          selectName:'',
        },
        defaultProps: {//树对象属性对应关系
          children: 'children',
          label: 'name',
          isLeaf:function(data, node){
            return !data.isParent
          },
          disabled:function(data, node){
            return data.isParent
          }
        }
      }
    },
    created(){},
    methods: {
      //获取数据返回
      list(){
        this.$emit('list',this.selectTree);
      },
      //查询人员姓名
      treeQuery(){
        if(this.query.userName){
           this.query.selectBtn = true;
        }else{
          this.query.selectBtn = false;
        }
        treeUrl(this.url,{...this.query,...this.params}).then(
          response => {
            this.data=response
          }
        );
      },
      loadnode(node,resolve){
        //如果展开第一级节点，从后台加载一级节点列表
        if(node.level==0)
        {
          this.index++;
          this.loadfirstnode(resolve);
        }
        //如果展开其他级节点，动态从后台加载下一级节点列表
        if(node.level>=1)
        {
          this.loadchildnode(node,resolve);
        }
      },
      //加载第一级节点
      loadfirstnode(resolve){
        treeUrl(this.url,{...this.query,...this.params}).then(
          response => {
            for(let i = 0;i<this.selectTree.length;i++){
              this.selectTree[i].id = this.selectTree[i].postId;
              this.defaultTree[i] = this.selectTree[i].postId;
            }
            resolve(response);
          }
        );
      },
      //加载节点的子节点集合
      loadchildnode(node,resolve){
        treeUrl(this.url,{...node.data,...this.params,...{userName:this.query.selectBtn?this.query.userName:''}}).then(
          response => {
            resolve(response);
          }
        );
      },
      //点击节点上触发的事件，传递三个参数，数据对象使用第一个参数
      nodeclick(data,dataObj,self)
      {},
      //修改状态
      checkChange(node,type){
        if(type){//选中
          this.selectTree.push(node);
          this.defaultTree.push(node.id);
        }else{
          this.selectTree.splice(this.selectTree.findIndex(item => item.id === node.id), 1);
          this.defaultTree.splice(this.defaultTree.findIndex(item => item === node.id), 1)
        }
        console.log(this.defaultTree)
      },
      //删除某节点
      noCheck(obj){
        if(this.$refs.tree.getNode(obj.id)){
          this.$refs.tree.setChecked(obj.id,false);
        }else{
          this.defaultTree.splice(this.defaultTree.findIndex(item => item === obj.id), 1);
          this.selectTree.splice(this.selectTree.findIndex(item => item.id === obj.id), 1);
        }
      }
    }
  }
</script>
<style  rel="stylesheet/scss" scoped lang="scss">
  .is-disabled{
    display: none !important;
  }
  .tree-box{
    height: calc(100vh - 400px);
    overflow: auto;

  }
</style>
