<!--基础数据查询-->
<template>
    <div id="basequery" class="padding10_20">
      <el-scrollbar>
        <el-tabs v-model="activeName" class="height100" @tab-click="handleClick">
            <el-tab-pane label="企业基本信息" name="area">
              <areaInfo  class="height100" v-if="activeName=='area'"></areaInfo>
            </el-tab-pane>
            <el-tab-pane label="企业联系人" name="person"><personList v-if="activeName=='person'"></personList></el-tab-pane>
            <el-tab-pane label="企业规章制度" name="law"><lawList v-if="activeName=='law'"></lawList></el-tab-pane>
            <el-tab-pane label="禁入限制人员" name="limit"><limitPersonList v-if="activeName=='limit'"></limitPersonList></el-tab-pane>
            <el-tab-pane label="发件箱" name="sendBox"><sendBox v-if="activeName=='sendBox'"></sendBox></el-tab-pane>
        </el-tabs>
      </el-scrollbar>
    </div>
</template>

<script>
  import areaInfo from "./areaInfo";
  import lawList from "./law/lawList";
  import personList from "./person";
  import limitPersonList from "./limitPerson/limitPersonList";
  import sendBox from "./sendBox";

  export default {
    name: "Basemaintain",
    components: { areaInfo, lawList, sendBox, limitPersonList, personList,},
    data() {
        return {
            activeName: 'area',
        }
    },
    methods: {
        handleClick(tab, event) {
            this.activeName = tab.name
        },
    },
  };
</script>
<style rel="stylesheet/scss" scoped lang="scss">
  ::v-deep.el-scrollbar__wrap {
    overflow-x: hidden;
    height: 100%;
  }
</style>







