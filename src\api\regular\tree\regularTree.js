import request from '@/utils/request'

//初始化上报单位
export function getunitList(regularReportId){
  return request({
    url: '/colligate/violRegular/report/getunitList/'+regularReportId,
    method: 'post'
  })
}

//删除上报单位
export function delReportData(regularReportId,id){
  return request({
    url: '/colligate/violRegular/report/delReportData',
    method: 'post',
    data: JSON.stringify({
      regularReportId: regularReportId,
      id: id
    })
  })
}

//保存上报单位
export function saveReportUnitByEdit(regularReportId,unitList){
  return request({
    url: '/colligate/violRegular/report/saveReportUnitByEdit/'+regularReportId,
    method: 'post',
    data: JSON.stringify(unitList)
  })
}


//新增上报保存接口人信息
export function savePersonById(regularReportId,id,postId){
  return request({
    url: '/colligate/violRegular/report/savePersonById',
    method: 'post',
    data: JSON.stringify({
      regularReportId: regularReportId,
      id: id,
      postId: postId
    })
  })
}
//查询待上报单位
export function getNotUnitList(regularReportId){
  return request({
    url: '/colligate/violRegular/report/getNotUnitList/'+regularReportId,
    method: 'post'
  })
}
//补录保存上报单位
export function saveAddReportUnitByEdit(regularReportId,unitList){
  return request({
    url: '/colligate/violRegular/report/saveAddReportUnitByEdit/'+regularReportId,
    method: 'post',
    data: JSON.stringify(unitList)
  })
}