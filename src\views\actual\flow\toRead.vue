<template>
  <div>
    <!--查看-->
    <div>
      <ActualFiveDaysReportRead
        ref="todo"
        v-if="type=='actual1'"
        :detail="detail"
        :field="actualProblemId"
        @handle="handle"
      ></ActualFiveDaysReportRead>

      <ActualFifteenDaysReportRead
        ref="todo"
        v-if="type=='actual2'"
        :detail="detail"
        :field="actualProblemId"
        @handle="handle"
      ></ActualFifteenDaysReportRead>

    </div>
  </div>
</template>

<script>
  import {getProcessStatus} from '@/api/actual/index';
  import ActualFiveDaysReportRead from './../detail/actualFiveDaysReportRead';//5日
  import ActualFifteenDaysReportRead from './../detail/actualFifteenDaysReportRead';//15日
  export default {
    name: "index",
    props: {
      selectValue: {
        type: Object
      },
      centerVariable: {
        type: Object
      },
      type: {
        type: String
      },
    },
    components: {
      ActualFiveDaysReportRead,
      ActualFifteenDaysReportRead
    },
    data() {
      return {
        detail: true,
        status:'',
        actualProblemId: ''
      }
    },
    created() {
      // 初始化跳转页面
      this.GetProcessStatus();
    },
    methods: {
      GetProcessStatus() {
          this.actualProblemId = this.selectValue.busiId;
          this.$nextTick(()=>{
              this.$refs.todo.show();
          })
      },
      //保存
      publicSave(){
        this.$refs.todo.save();
      },
      //流程提交
      nextStep() {
        this.$refs.todo.nextStep();
      },
      //流程提交
      handle(type) {
        this.$emit('handle', type);
      },
    }
  }
</script>

<style scoped>

</style>
