
<!-- 季度报告--地市季度报告流程环节--省分审批人复核-部门总经理 -->

<template>
    <div class="wai-container" style="background-color: #fff">
      <div class="layui-row width height">
        <div class="width height">
          <div class="common-wai-box" style="height: 100%">
            <div class="common-in-box" style="height: auto; min-height: 100%">
              <div class="common-in-box-header">
                <div class="common-in-box-header-line" />
                <div class="common-in-box-header-text">基本信息</div>
                <div class="flex-1" />
              </div>

              <div class="common-in-box-content">
                <div class="top-search">
                  <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left">上报年度</div>

                      <div class="layui-form-value">{{ infoData.reportYear }}</div>
                    </div>
                  </el-col>

                  <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left">上报季度</div>
                      <div class="layui-form-value">
                        {{
                        infoData.reportQuarter
                        | fromatComon(dict.type.REPORT_QUARTER)
                        }}
                      </div>
                    </div>
                  </el-col>

                  <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left">上报截止日期</div>

                      <div class="layui-form-value">
                        <el-date-picker
                          format="yyyy-MM-dd"
                          value-format="yyyy-MM-dd"
                          v-model="infoData.reportCloseTime"
                          type="datetime"
                          placeholder=""
                          readonly
                        >
                        </el-date-picker>
                      </div>
                    </div>
                  </el-col>
                </div>

                <div class="top-search">
                  <div class="layui-col-md12 layui-col-sm12 layui-col-lg12">
                    <div class="layui-form">
                      <div class="layui-form-left">上报标题</div>

                      <div class="layui-form-value">
                        {{ infoData.reportTitle }}
                      </div>
                    </div>
                  </div>
                </div>

                <div class="top-search">
                  <div class="layui-col-md12 layui-col-sm12 layui-col-lg12">
                    <div class="layui-form">
                      <div class="layui-form-left">上报要求</div>
                      <div id="uploadAsk" class="layui-form-value">
                        {{ infoData.reportRequire }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div class="common-in-box-header" style="margin-top: 10px">
                <div class="common-in-box-header-line" />
                <div class="common-in-box-header-text">附件列表</div>
              </div>

              <div class="tables tables_1">
                <el-table
                  v-loading="tableLoading"
                  :data="filesData"
                  border
                  style="width: 100%"
                >
                  <el-table-column
                    label="序号"
                    type="index"
                    min-width="5%"
                    align="center"
                  />
                  <el-table-column label="文件名" prop="fileName" min-width="50%">
                    <template slot-scope="scope">
                      <div
                        style="text-align: left"
                        class="overflowHidden-1"
                        :title="scope.row.fileName"
                      >
                        {{ scope.row.fileName || "" }}
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column
                    label="上传人"
                    align="center"
                    prop="createUserName"
                    min-width="10%"
                  />
                  <el-table-column
                    label="上传时间"
                    align="center"
                    prop="createTime"
                    min-width="20%"
                  />

                  <el-table-column
                    label="操作"
                    min-width="15%"
                    align="center"
                    class-name="small-padding fixed-width"
                  >
                    <template slot-scope="scope">
                      <el-button
                        size="mini"
                        type="text"
                        title="下载"
                        icon="el-icon-bottom"
                        @click="fileDownload(scope.row)"
                      ></el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>

              <div class="common-in-box-header" style="margin-top: 10px">
                <div class="common-in-box-header-line" />
                <div class="common-in-box-header-text">本单位汇总信息</div>
              </div>

              <div class="common-in-box-header" style="margin-top: 10px; border: 0px; padding-left: 10px" >
                <div class="common-in-box-header-text">工作部署情况</div>
              </div>

              <div class="common-in-box-content">
                <div class="top-search">
                  <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left width-label-2">
                        本季度召开领导小组会议（次）
                      </div>
                      <div class="layui-form-value">{{infoData.quarterTeamMeetingTime}}</div>
                    </div>
                  </el-col>

                  <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left width-label-2">
                        本季度召开领导小组办公室会议（次）
                      </div>
                      <div class="layui-form-value">{{infoData.quarterTeamOfficeMeetingTime}}</div>
                    </div>
                  </el-col>

                  <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left width-label-2">
                        本季度召开专题会议（次）
                      </div>
                      <div class="layui-form-value">{{infoData.quarterSpecialMeetingTime}}</div>
                    </div>
                  </el-col>
                </div>

                <div class="top-search">
                  <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left width-label-2">
                        当年累计召开领导小组会议（次）
                      </div>
                      <div class="layui-form-value">{{infoData.totalLeaderTeamMeetingTime}}</div>
                    </div>
                  </el-col>

                  <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left width-label-2">
                        当年累计召开领导小组办公室会议（次）
                      </div>
                      <div class="layui-form-value">{{infoData.totalTeamOfficeMeetingTime}}</div>
                    </div>
                  </el-col>

                  <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left width-label-2">
                        当年累计召开专题会议（次）
                      </div>
                      <div class="layui-form-value">{{infoData.totalSpecialMeetingTime}}</div>
                    </div>
                  </el-col>
                </div>
              </div>

              <div class="common-in-box-header" style="margin-top: 10px; border: 0px; padding-left: 10px" >
                <div class="common-in-box-header-text">体系建设情况</div>
              </div>

              <div class="common-in-box-content">
                <div class="top-search">
                  <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left width-label-2">
                        2019年至今累计印发责任追究相关制度数量（项）
                      </div>
                      <div class="layui-form-value">{{infoData.totalAccountabilitySystemNumber}}</div>
                    </div>
                  </el-col>

                  <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left width-label-2">
                        2019年至今累计专职人员数量 （人）
                      </div>
                      <div class="layui-form-value">{{infoData.totalProfessionalNumber}}</div>
                    </div>
                  </el-col>
                </div>

                <div class="top-search">
                  <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left width-label-2">
                        当年累计新增配套制度（项）
                      </div>
                      <div class="layui-form-value">{{infoData.totalNewSupportingSystem}}</div>
                    </div>
                  </el-col>

                  <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left width-label-2">
                        当年累计新增工作机制（项）
                      </div>
                      <div class="layui-form-value">{{infoData.totalNewWorkSystem}}</div>
                    </div>
                  </el-col>

                  <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left width-label-2">
                        当年累计新增专职人员数量（人）
                      </div>
                      <div class="layui-form-value">{{infoData.totalNewSpecialPersonNumber}}</div>
                    </div>
                  </el-col>
                </div>

                <div class="top-search">
                  <el-col :span="24" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left width-label-2">
                        新增配套制度名称
                      </div>
                      <div class="layui-form-value">{{infoData.newSupportingName}}</div>
                    </div>
                  </el-col>
                </div>

                <div class="top-search">
                  <el-col :span="24" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left width-label-2">
                        新增工作机制名称
                      </div>
                      <div class="layui-form-value">{{infoData.newWorkName}}</div>
                    </div>
                  </el-col>
                </div>

                <div class="top-search">
                  <el-col :span="12" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left width-label-2">主要部门</div>
                      <div class="layui-form-value">{{infoData.groupMainDept}}</div>
                    </div>
                  </el-col>
                </div>

                <div
                  class="common-in-box-header"
                  style="margin-top: 10px; border: 0px; padding-left: 10px"
                >
                  <div class="common-in-box-header-text">
                    违规问题线索查办情况
                  </div>
                </div>

                <div class="common-in-box-content">
                  <div class="top-search">
                    <el-col :span="8" class="height">
                      <div class="layui-form">
                        <div class="layui-form-left width-label-2">
                          本季度新受理问题线索数量（件）
                        </div>
                        <div class="layui-form-value">{{infoData.quarterNewProblemNumber}}</div>
                      </div>
                    </el-col>

                    <el-col :span="8" class="height">
                      <div class="layui-form">
                        <div class="layui-form-left width-label-2">
                          本季度涉及资产损失（万元）
                        </div>
                        <div class="layui-form-value">{{infoData.lossAmount}}</div>
                      </div>
                    </el-col>

                    <el-col :span="8" class="height">
                      <div class="layui-form">
                        <div class="layui-form-left width-label-2">
                          本季度涉及资产损失风险（万元）
                        </div>
                        <div class="layui-form-value">{{infoData.lossRisk}}</div>
                      </div>
                    </el-col>
                  </div>

                  <div class="top-search">
                    <el-col :span="8" class="height">
                      <div class="layui-form">
                        <div class="layui-form-left width-label-2">
                          当年累计受理问题线索数量（件）
                        </div>
                        <div class="layui-form-value">{{infoData.totalProblemSourceNumber}}</div>
                      </div>
                    </el-col>

                    <el-col :span="8" class="height">
                      <div class="layui-form">
                        <div class="layui-form-left width-label-2">
                          上年结转问题线索数量（件）
                        </div>
                        <div class="layui-form-value">{{infoData.lastYearProblemSourceNumber}}</div>
                      </div>
                    </el-col>
                  </div>

                  <div class="top-search">
                    <el-col :span="8" class="height">
                      <div class="layui-form">
                        <div class="layui-form-left width-label-2">
                          其中:未启动核查（件）
                        </div>
                        <div class="layui-form-value">{{infoData.checkNoStartedNumber}}</div>
                      </div>
                    </el-col>

                    <el-col :span="8" class="height">
                      <div class="layui-form">
                        <div class="layui-form-left width-label-2">
                          其中: 正在核查（件）
                        </div>
                        <div class="layui-form-value">{{infoData.checkInProcessNumber}}</div>
                      </div>
                    </el-col>

                    <el-col :span="8" class="height">
                      <div class="layui-form">
                        <div class="layui-form-left width-label-2">
                          其中: 完成核查（件）
                        </div>
                        <div class="layui-form-value">{{infoData.checkCompletedNumber}}</div>
                      </div>
                    </el-col>
                  </div>
                </div>

                <div
                  class="common-in-box-header"
                  style="margin-top: 10px; border: 0px; padding-left: 10px"
                >
                  <div class="common-in-box-header-text">追责整改工作成效</div>
                </div>

                <div class="common-in-box-content">
                  <div class="top-search new-change-height-bottom-view">
                    <el-col :span="8" class="height">
                      <div class="layui-form">
                        <div class="layui-form-left width-label-2">
                          当年累计完成追责问题数量（件）
                        </div>
                        <div class="layui-form-value">{{infoData.totalCompletedProblemNumber}}</div>
                      </div>
                    </el-col>


                  </div>
                  <div class="top-search">
                    <el-col :span="8" class="height">
                      <div class="layui-form">
                        <div class="layui-form-left width-label-2">
                          当年累计追责总人数（人）
                        </div>
                        <div class="layui-form-value">{{infoData.totalAccountabilityPersonNumber}}</div>
                      </div>
                    </el-col>

                    <el-col :span="8" class="height" v-show="infoData.orgGrade=='G'">
                      <div class="layui-form">
                        <div class="layui-form-left width-label-2">
                          其中: 中央企业负责人（人）
                        </div>
                        <div class="layui-form-value">{{infoData.enterpriseManagementNumber}}</div>
                      </div>
                    </el-col>
                    <el-col :span="8" class="height" v-show="infoData.orgGrade=='G'">
                      <div class="layui-form">
                        <div class="layui-form-left  width-label-2">其中: 集团管理干部（人）</div>
                        <div class="layui-form-value">{{infoData.groupManagementNumber}}</div>
                      </div>
                    </el-col>


                  </div>
                  <div class="top-search new-change-height-bottom-view">
                    <el-col :span="8" class="height">
                      <div class="layui-form">
                        <div class="layui-form-left  width-label-2">其中: 子企业管理干部（人）</div>
                        <div class="layui-form-value">{{infoData.subManagementNumber}}</div>
                      </div>
                    </el-col>
                  </div>
                  <div class="top-search ">



                    <el-col :span="8" class="height">
                      <div class="layui-form">
                        <div class="layui-form-left width-label-2">
                          当年累计追责总人次（人次）
                        </div>
                        <div class="layui-form-value">{{infoData.totalAccountabilityPersonTime}}</div>
                      </div>
                    </el-col>

                    <el-col :span="8" class="height">
                      <div class="layui-form">
                        <div class="layui-form-left width-label-2">
                          其中: 组织处理 （人次）
                        </div>
                        <div class="layui-form-value">{{infoData.orgHandleTime}}</div>
                      </div>
                    </el-col>

                    <el-col :span="8" class="height">
                      <div class="layui-form">
                        <div class="layui-form-left width-label-2">
                          其中: 扣减薪酬 （人次）
                        </div>
                        <div class="layui-form-value">{{infoData.deductionSalaryTime}}</div>
                      </div>
                    </el-col>
                    </div>
                    <div class="top-search ">



                    <el-col :span="8" class="height">
                      <div class="layui-form">
                        <div class="layui-form-left width-label-2">
                          其中: 党纪处分（人次）
                        </div>
                        <div class="layui-form-value">{{infoData.partyPunishmentTime}}</div>
                      </div>
                    </el-col>


                    <el-col :span="8" class="height" v-show="infoData.orgGrade=='G'">
                      <div class="layui-form">
                        <div class="layui-form-left  width-label-2">其中：政务处分 （人次）</div>
                        <div class="layui-form-value">{{infoData.governmentPunishmentTime}}</div>
                      </div>
                    </el-col>

                    <el-col :span="8" class="height" v-show="infoData.orgGrade=='G'">
                      <div class="layui-form">
                        <div class="layui-form-left  width-label-2">其中：禁入限制（人次）</div>
                        <div class="layui-form-value">{{infoData.prohibitTime}}</div>
                      </div>
                    </el-col>
                  </div>




                  <div class="top-search new-change-height-bottom-view">

                    <el-col :span="8" class="height">
                      <div class="layui-form">
                        <div class="layui-form-left width-label-2">
                          其中:移送监察机关或司法机关 （人次）
                        </div>
                        <div class="layui-form-value">{{infoData.transferAuthorityTime}}</div>
                      </div>
                    </el-col>
                    <el-col :span="8" class="height">
                      <div class="layui-form">
                        <div class="layui-form-left width-label-2">
                          其中: 其他 （人次）
                        </div>
                        <div class="layui-form-value">{{infoData.processingOtherItem}}</div>
                      </div>
                    </el-col>


                  </div>
                  <div class="top-search new-change-height-bottom-view">
                    <el-col :span="8" class="height">
                      <div class="layui-form">
                        <div class="layui-form-left width-label-2">
                          当年累计扣减薪酬金额（万元）
                        </div>
                        <div class="layui-form-value">{{infoData.totalDeductionSalary}}</div>
                      </div>
                    </el-col>
                  </div>
                  <div class="top-search new-change-height-bottom-view">
                    <el-col :span="8" class="height">
                      <div class="layui-form">
                        <div class="layui-form-left width-label-2">
                          责任约谈-当年累计责任约谈次数（次）
                        </div>
                        <div class="layui-form-value">{{infoData.dutyInterviewNumber}}</div>
                      </div>
                    </el-col>


                    <el-col :span="8" class="height">
                      <div class="layui-form">
                        <div class="layui-form-left width-label-2">
                          责任约谈-当年累计责任约谈总人次（人次）
                        </div>
                        <div class="layui-form-value">{{infoData.dutyInterviewPersonTime}}</div>
                      </div>
                    </el-col>
                  </div>

                  <div class="top-search">
                    <el-col :span="8" class="height">
                      <div class="layui-form">
                        <div class="layui-form-left width-label-2">
                          当年累计挽回资产损失（万元）
                        </div>
                        <div class="layui-form-value">{{infoData.totalRetrieveLossAmount}}</div>
                      </div>
                    </el-col>

                    <el-col :span="8" class="height">
                      <div class="layui-form">
                        <div class="layui-form-left width-label-2">
                          当年累计降低损失风险（万元）
                        </div>
                        <div class="layui-form-value">{{infoData.totalReduceLossRisk}}</div>
                      </div>
                    </el-col>

                    <el-col :span="8" class="height">
                      <div class="layui-form">
                        <div class="layui-form-left width-label-2">
                          当年累计制修订管理制度（项）
                        </div>
                        <div class="layui-form-value">{{infoData.totalPerfectSystemNumber}}</div>
                      </div>
                    </el-col>
                  </div>

                  <div class="top-search">
                    <el-col :span="24" class="height">
                      <div class="layui-form">
                        <div class="layui-form-left width-label-2">
                          其他工作成效
                        </div>
                        <div class="layui-form-value">{{infoData.otherAchievement}}</div>
                      </div>
                    </el-col>
                  </div>

                  <div
                    class="common-in-box-header"
                    style="margin-top: 10px; border: 0px; padding-left: 10px"
                  >
                    <div class="common-in-box-header-text">其他</div>
                  </div>

                  <div class="top-search">
                    <el-col :span="24" class="height">
                      <div class="layui-form">
                        <div class="layui-form-left">备注</div>
                        <div class="layui-form-value">{{infoData.remark}}</div>
                      </div>
                    </el-col>
                  </div>

                  <div class="top-search">
                    <el-col :span="8" class="height">
                      <div class="layui-form">
                        <div class="layui-form-left">追责部门填报人</div>
                        <div class="layui-form-value">{{infoData.informantName}}</div>
                      </div>
                    </el-col>

                    <el-col :span="8" class="height">
                      <div class="layui-form">
                        <div class="layui-form-left">联系电话</div>
                        <div class="layui-form-value">{{infoData.informantPhone}}</div>
                      </div>
                    </el-col>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <subordinateReportDetail  ref="subordinateReport" />
    </div>
</template>
<script>
import {
    queryQuarterReportFileList
  } from '@/api/quarterly-report'
import subordinateReportDetail from "@/views/quarterlyReport/subordinateReport/detail";
 import {queryQuarterReportProv} from '@/api/quarterly-report/view'
export default {
  components: { subordinateReportDetail },
  props: {
    //流程参数
    centerVariable: {
        type: Object
      },
  },
  dicts: ['REPORT_QUARTER'],
  data() {
    return {
      infoData: {}, // 基本信息
      tableLoading: false, // 表格loading
      filesData: [], // 附件列表
      id : '',//主键
    }
  },
  created() {
    this.$emit('collocation',{
        refreshAssigneeUrl: '/quarter/report/area/flow', // 下环节自定义业务url
        saveBtn:false,//保存按钮
      });
    this.id = this.centerVariable.busiKey;
    //查询上报信息
    this.queryReportProvInfo();
  },
  methods: {
    //根据主键查询业务信息
    queryReportProvInfo(){
      var params = {
        quarterReportProvId:this.id
      }
      queryQuarterReportProv(params).then((res)=>{
        this.infoData = res.data;
        this.$forceUpdate();
        //查询附件列表
        this.queryFileList();
      })
    },
    //查询附件列表
    queryFileList(){
      this.tableLoading = true
      queryQuarterReportFileList(this.infoData.id).then((res)=>{
        this.filesData = res.data;
        this.tableLoading = false
      })
    },
    /** 下载文件*/
    fileDownload(obj) {
      this.download(
        '/sys/attachment/downloadSysAttachment/' + obj.attachmentId,
        {},
        obj.fileName
      )
    },
    fromatComon (value, list) {
      let lastLabel = '-'

      if (value && list.length > 0) {
        list.forEach(element => {
          if (element.value == value) {
            lastLabel = element.label
          }
        })
      }
      return lastLabel
    },
   //流程 方法
    openLoading(){//打开加载...
      this.$emit('openLoading');
    },
    closeLoading(){//关闭加载...
      this.$emit('closeLoading');
    },
    //额外参数
   loadProcessData(){
      return {}
   },
    //校验
    passValidate(){
      return true;
    },
    //接口校验异步校验时
    passValidateJup(){
      this.$emit('nextStep', true)
    },

    //下级单位数据上报
    subordinateReport() {
      this.$refs.subordinateReport.open(this.infoData.id);
    },
  }
}
</script>
<style lang="scss" scoped>
  @import "~@/assets/styles/quarterly-report/index.css";
</style>
