
export function filterNum(numValue) {
    if (numValue == "" || numValue == null) {
      numValue = 0;
    }
    if (numValue === "-") {
      return numValue;
    }
    var value = Math.round(parseFloat(numValue) * 100) / 100;
    var xsd = value.toString().split(".");
    if (xsd.length == 1) {
      value = value.toString() + ".00";
    }
    if (xsd.length > 1) {
      if (xsd[1].length < 2) {
        value = value.toString() + "0";
      }
    }
    return value;
  }




  export function fromatComon (value, list) {
    let lastLabel = '-'

    if (value && list.length > 0) {
      list.forEach(element => {
        if (element.value == value) {
          lastLabel = element.label
        }
      })
    }
    return lastLabel
  }

export function fromatComonDict (value, list) {
  let lastLabel = '-'

  if (value && list.length > 0) {
    list.forEach(element => {
      if (element.dictValue == value) {
        lastLabel = element.dictLabel
      }
    })
  }
  return lastLabel
}

// 金额使用 转换为两位小数
export function formatNum (numValue) {
  if (numValue == '' || numValue == null) {
    numValue = 0
  }
  if (numValue === '-') {
    return numValue
  }
  let value = Math.round(parseFloat(numValue) * 100) / 100
  const xsd = value.toString().split('.')
  if (xsd.length == 1) { value = value.toString() + '.00' }
  if (xsd.length > 1) { if (xsd[1].length < 2) { value = value.toString() + '0' } }
  return value
}

//转换展示两位小数，空值直接输出空值
export function formatNumContainNull (numValue){
  if (numValue === '' || numValue == null) {
    return '';
  }
  let value=Math.round(parseFloat(numValue)*100)/100;
  const xsd=value.toString().split(".");
  if(xsd.length==1){ value=value.toString()+".00"; }
  if(xsd.length>1){  if(xsd[1].length<2){ value=value.toString()+"0"; } }
  return value;
}
