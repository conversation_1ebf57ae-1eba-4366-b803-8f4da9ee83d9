<template>
  <div class="cursor modifyRecord">
    <img :src="require('@/assets/images/colligate/modifyRecord.png')" @click="Modify">
    <!--修改记录-->
    <el-dialog :visible.sync="visibleModify" width="80%"  append-to-body title="修改记录">
      <ModifyRecord v-if="visibleModify"
                    ref="modify"
                    :edit='edit'
                    :key="problemId||relevantTableId||relevantTableName"
                    :problemId="problemId"
                    :relevantTableId="relevantTableId"
                    :relevantTableName="relevantTableName"
                    :problemStatus="problemStatus"
      ></ModifyRecord>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="modifyClose">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import ModifyRecord from './index';//修改记录
    export default {
      components: {ModifyRecord},
      name: "btn",
      props: {
        problemId: {
          type: String
        },
        relevantTableId: {
          type: String
        },
        relevantTableName: {
          type: String
        },
        problemStatus: {
          type: String
        },
      },
      data(){
        return{
          visibleModify:false,
        }
      },
      methods:{
        Modify(){
          this.visibleModify=true;
        },
        modifyClose(){
          this.visibleModify=false;
        }
      }
    }
</script>

<style scoped>

</style>
