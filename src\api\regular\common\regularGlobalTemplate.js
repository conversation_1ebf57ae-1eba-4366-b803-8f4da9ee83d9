import request from '@/utils/request';

/**
 * 获取已上传的定期报告全局模板
 * @param regularReportId
 */
export function regularGlobalTemplates(regularReportId) {
  return request({
    url: "/colligate/violRegularGlobalTemplate/regularGlobalTemplates/" + regularReportId,
    method: "post"
  });
}

/**
 * 删除定期报告模板
 * @param id
 */
export function deleteRegularGlobalTemplate(id) {
  return request({
    url: "/colligate/violRegularGlobalTemplate/deleteRegularGlobalTemplate/" + id,
    method: "post"
  });
}

/**
 * 获取定期报告模板项
 * @param regularUnitId
 */
export function regularReportTemplateItems(regularUnitId) {
  return request({
    url: "/colligate/violRegularGlobalTemplate/regularReportTemplateItems/" + regularUnitId,
    method: "post"
  });
}
