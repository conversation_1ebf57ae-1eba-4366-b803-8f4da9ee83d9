import request from '@/utils/request'

/**
 * 查询待阅内容
 * @param projectId
 */
export function queryReadInfo(configKey){
  return request({
    url: '/spr/read/queryReadInfo/'+ configKey,
    method: 'post'
  })
}

/**
 * 查询项目信息
 * @param projectId
 */
export function queryProjectInfo(projectId){
  return request({
    url: '/spr/queryProjectInfo',
    method: 'post',
    data:{
      projectId:projectId
    }
  })
}

/**
 * 查询审计报告信息
 * @param projectId
 */
export function queryAuditFile(projectId){
  return request({
    url: '/spr/attachment/queryAuditFile',
    method: 'post',
    data:{
      projectId:projectId
    }
  })
}

//刷新台账列表
export function queryLedgersList(projectId){
  return request({
    url: '/spr/ledgers/queryLedgersList',
    method: 'post',
    data:{
      projectId:projectId
    }
  })
}

//刷新台账列表
export function queryReportFileList(projectId){
  return request({
    url: '/spr/attachment/queryReportFileList',
    method: 'post',
    data:{
      projectId:projectId
    }
  })
}

//删除手动新增的台账
export function delLedgers(params){
  return request({
    url: '/spr/ledgers/delLedgers',
    method: 'post',
    data:JSON.stringify(params)
  })
}

//接口同步
export function syncData(projectId){
  return request({
    url: '/spr/syncData',
    method: 'post',
    data:{
      projectId:projectId
    }
  })
}
//手动调用定时任务发起待办
export function handleStartFlow(){
  return request({
    url: '/spr/startSpecialReportProcess',
    method: 'post'
  })
}

//删除附件
export function delSprAttachmentById(params){
  return request({
    url: '/spr/attachment/delSprAttachmentById',
    method: 'post',
    data:JSON.stringify(params)
  })
}

//保存新增台账
export function saveLedgersInfo(params){
  return request({
    url: '/spr/ledgers/saveLedgersInfo',
    method: 'post',
    data:JSON.stringify(params)
  })
}
//根据台账主键查询台账信息
export function queryLedgersInfoById(ledgerId){
  return request({
    url: '/spr/ledgers/queryLedgersInfoById/'+ledgerId,
    method: 'post'
  })
}

//保存台账列表（单选按钮保存）
export function saveLedgersList(params){
  return request({
    url: '/spr/ledgers/saveLedgersList',
    method: 'post',
    data:JSON.stringify({
      ledgerReturnList:params
    })
  })
}

//提交校验
export function validateInfo(params){
  return request({
    url: '/spr/validateInfo',
    method: 'post',
    data:JSON.stringify(params)
  })
}

//查询初核专项报告列表
export function querySpecialReportList(params){
  let pageParams = {pageNum: params.pageNum, pageSize: params.pageSize};
  return request({
    url: '/spr/querySpecialReportList',
    method: 'post',
    data:JSON.stringify(params),
    params: pageParams
  })
}
//查询审计报告附件（带附件类型）
export function queryAuditFileList(projectId){
  return request({
    url: '/spr/attachment/queryAuditFileList',
    method: 'post',
    data:{
      projectId:projectId
    }
  })
}

//新增获取主键
export function getNewProjectId(){
  return request({
    url: '/spr/getNewProjectId',
    method: 'post',
  })
}

//保存专项报告信息
export function saveSpecialReportInfo(params){
  return request({
    url: '/spr/saveSpecialReportInfo',
    method: 'post',
    data:JSON.stringify(params)
  })
}

//查询审计报告附件（带附件类型）
export function queryStageFileList(projectId){
  return request({
    url: '/spr/attachment/queryStageFileList',
    method: 'post',
    data:{
      projectId:projectId
    }
  })
}

//保存提交阶段性报告后的项目状态
export function toSaveStageFile(projectId){
  return request({
    url: '/spr/toSaveStageFile',
    method: 'post',
    data:{
      id:projectId
    }
  })
}
//删除专项报告
export function specialReportDel(projectId){
  return request({
    url: '/spr/specialReportDel',
    method: 'post',
    data:{
      id:projectId
    }
  })
}
//发起流程
export function startAndSubmitProcess(data) {
  return request({
    url: '/spr/flow/startProcess',
    method: 'post',
    data: data
  })
}

//查询流程参数
export function flowParams() {
  return request({
    url: '/spr/flow/flowParams',
    method: 'post'
  })
}
//查询初核专项报告统计
export function querySprStaticInfo(){
  return request({
    url: '/spr/querySprStaticInfo',
    method: 'post'
  })
}
//查询审计单位排行
export function queryAuditOrgRank(orderFlag){
  return request({
    url: '/spr/queryAuditOrgRank',
    method: 'post',
    data:JSON.stringify({
      orderFlag:orderFlag
    })
  })
}

//查询审计单位排行
export function queryProjectEchartList(echartType){
  return request({
    url: '/spr/queryProjectEchartList',
    method: 'post',
    data:JSON.stringify({
      echartType:echartType
    })
  })
}

export function handleReadFlow(params){
  return request({
    url: '/spr/readflow/handleReadFlow',
    method: 'post',
    data:JSON.stringify(params)
  })
}

