<template>
  <div>
    <div v-if="actualFlag==1" class="bottom-line">
      温馨提示：该流程已发起实时报送，请及时在【实时问题录入】功能中处理。
    </div>
    <div v-else class="top-line">
      温馨提示：该流程未发起实时报送
    </div>
  </div>
</template>

<script>

  export default {
    name: "remind",
    components: {
    },
    props: {
      actualFlag: {
        type: Number
      }
    },
    data(){
      return{
        type:false,
        status:'',
        showTip:false,
        fieldList:[],
        uploadUrl: '/colligate/violFile/uploadViolFile',
        downloadUrl:'/jtauditwo/files/downLoad/',//下载地址
      }
    }
  }
</script>

<style scoped lang="scss">
  .flex{
    display: flex;
    align-items: center;
  }
  .bottom-line {
    padding: 10px 0;
    text-align: center;
    border-top: 1px solid #d9d9d9;
    color: #f5222d !important;
  }
  .top-line {
    padding: 10px 0;
    text-align: center;
    border-bottom: 1px solid #d9d9d9;
    color: #a9b0b4 !important;
  }
</style>
