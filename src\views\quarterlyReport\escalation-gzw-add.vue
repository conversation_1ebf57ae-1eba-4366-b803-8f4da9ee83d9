<!-- 季度报告--上报国资委--新增和修改 -->

<template>
  <div class="wai-container" style="background-color: #fff">
    <div class="layui-row width height">
      <div class="width height">
        <div class="common-wai-box" style="height: 100%">
          <div class="common-in-box" style="height: auto; min-height: 100%">
            <div class="common-in-box-header">
              <div class="common-in-box-header-line"></div>
              <div class="common-in-box-header-text">基本信息</div>
            </div>

            <div class="common-in-box-content">
              <div class="top-search">
                <el-col :span="16" class="height">
                  <div class="layui-form">
                    <div class="layui-form-left">公司名称</div>

                    <div class="layui-form-value">
                      {{ infoData.companyName }}
                    </div>
                  </div>
                </el-col>

                <el-col :span="8" class="height">
                  <div class="layui-form">
                    <div class="layui-form-left">所属行业</div>

                    <div class="layui-form-value">
                      {{ infoData.industryName }}
                    </div>
                  </div>
                </el-col>
              </div>

              <div class="top-search">
                <el-col :span="8" class="height">
                  <div class="layui-form">
                    <div class="layui-form-left">
                      <span class="must-icon">*</span>上报年度
                    </div>

                    <el-date-picker
                      format="yyyy"
                      value-format="yyyy"
                      v-model="infoData.reportYear"
                      @change="reportYearChange"
                      type="year"
                      placeholder="请选择"
                    >
                    </el-date-picker>
                  </div>
                </el-col>

                <el-col :span="8" class="height">
                  <div class="layui-form">
                    <div class="layui-form-left">
                      <span class="must-icon">*</span>上报季度
                    </div>

                    <el-select
                      @change="reportQuarterChange"
                      v-model="infoData.reportQuarter"
                      placeholder="请选择"
                    >
                      <el-option
                        v-for="(item, index) in reportQuarterList"
                        :key="index"
                        :label="item.dictLabel"
                        :value="item.dictValue"
                      ></el-option>
                      <!-- <el-option v-for="(item, index) in dict.type.corresponding_adverse_effect" :key="index" :label="item.label" :value="item.value"></el-option> -->
                    </el-select>
                  </div>
                </el-col>
              </div>
            </div>



            <div class="common-in-box-header" style="margin-top: 10px">
              <div class="common-in-box-header-line"></div>
              <div class="common-in-box-header-text">本单位汇总信息</div>
            </div>

            <div
              class="common-in-box-header"
              style="margin-top: 10px; border: 0px; padding-left: 10px"
            >
              <div class="common-in-box-header-text">工作部署情况</div>
            </div>

            <div class="common-in-box-content">
              <div class="top-search">
                <el-col :span="8" class="height">
                  <div class="layui-form">
                    <div class="layui-form-left width-label-2">
                      本季度召开领导小组会议（次）
                    </div>
                    <el-input
                        readonly
                      type="text"
                      v-limit-input-number
                      v-model="formData.quarterTeamMeetingTime"
                      placeholder=""
                      class="num-input"
                    />
                  </div>
                </el-col>

                <el-col :span="8" class="height">
                  <div class="layui-form">
                    <div class="layui-form-left width-label-2">
                      本季度召开领导小组办公室会议（次）
                    </div>
                    <el-input
                        readonly
                      type="text"
                      v-limit-input-number
                      v-model="formData.quarterTeamOfficeMeetingTime"
                      placeholder=""
                      class="num-input"
                    />
                  </div>
                </el-col>

                <el-col :span="8" class="height">
                  <div class="layui-form">
                    <div class="layui-form-left width-label-2">
                      本季度召开专题会议（次）
                    </div>
                    <el-input
                        readonly
                      type="text"
                      v-limit-input-number
                      v-model="formData.quarterSpecialMeetingTime"
                      placeholder=""
                      class="num-input"
                    />
                  </div>
                </el-col>
              </div>

              <div class="top-search">
                <el-col :span="8" class="height">
                  <div class="layui-form">
                    <div class="layui-form-left width-label-2">
                      当年累计召开领导小组会议（次）
                    </div>
                    <el-input
                        readonly
                      type="text"
                      v-limit-input-number
                      v-model="formData.totalLeaderTeamMeetingTime"
                      placeholder=""
                      class="num-input"
                    />
                  </div>
                </el-col>

                <el-col :span="8" class="height">
                  <div class="layui-form">
                    <div class="layui-form-left width-label-2">
                      当年累计召开领导小组办公室会议（次）
                    </div>
                    <el-input
                        readonly
                      type="text"
                      v-limit-input-number
                      v-model="formData.totalTeamOfficeMeetingTime"
                      placeholder=""
                      class="num-input"
                    />
                  </div>
                </el-col>

                <el-col :span="8" class="height">
                  <div class="layui-form">
                    <div class="layui-form-left width-label-2">
                      当年累计召开专题会议（次）
                    </div>
                    <el-input
                        readonly
                      type="text"
                      v-limit-input-number
                      v-model="formData.totalSpecialMeetingTime"
                      placeholder=""
                      class="num-input"
                    />
                  </div>
                </el-col>
              </div>
            </div>

            <div
              class="common-in-box-header"
              style="margin-top: 10px; border: 0px; padding-left: 10px"
            >
              <div class="common-in-box-header-text">体系建设情况</div>
            </div>

            <div class="common-in-box-content">
              <div class="top-search">
                <el-col :span="8" class="height">
                  <div class="layui-form">
                    <div class="layui-form-left width-label-2">
                      2019年至今累计印发责任追究相关制度数量（项）
                    </div>
                    <el-input
                        readonly
                      type="text"
                      v-limit-input-number
                      v-model="formData.totalAccountabilitySystemNumber"
                      placeholder=""
                      class="num-input"
                    />
                  </div>
                </el-col>

                <el-col :span="8" class="height">
                  <div class="layui-form">
                    <div class="layui-form-left width-label-2">
                      2019年至今累计专职人员数量 （人）
                    </div>
                    <el-input
                        readonly
                      type="text"
                      v-limit-input-number
                      v-model="formData.totalProfessionalNumber"
                      placeholder=""
                      class="num-input"
                    />
                  </div>
                </el-col>
              </div>

              <div class="top-search">
                <el-col :span="8" class="height">
                  <div class="layui-form">
                    <div class="layui-form-left width-label-2">
                      当年累计新增配套制度（项）
                    </div>
                    <el-input
                        readonly
                      type="text"
                      v-limit-input-number
                      v-model="formData.totalNewSupportingSystem"
                      placeholder=""
                      class="num-input"
                    />
                  </div>
                </el-col>

                <el-col :span="8" class="height">
                  <div class="layui-form">
                    <div class="layui-form-left width-label-2">
                      当年累计新增工作机制（项）
                    </div>
                    <el-input
                        readonly
                      type="text"
                      v-limit-input-number
                      v-model="formData.totalNewWorkSystem"
                      placeholder=""
                      class="num-input"
                    />
                  </div>
                </el-col>

                <el-col :span="8" class="height">
                  <div class="layui-form">
                    <div class="layui-form-left width-label-2">
                      当年累计新增专职人员数量（人）
                    </div>
                    <el-input
                        readonly
                      type="text"
                      v-limit-input-number
                      v-model="formData.totalNewSpecialPersonNumber"
                      placeholder=""
                      class="num-input"
                    />
                  </div>
                </el-col>
              </div>

              <div class="top-search">
                <el-col :span="24" class="height">
                  <div class="layui-form">
                    <div class="layui-form-left width-label-2">
                      新增配套制度名称
                    </div>
                    <el-input
                        readonly
                      type="text"
                      v-model="formData.newSupportingName"
                      placeholder=""
                    />
                  </div>
                </el-col>
              </div>

              <div class="top-search">
                <el-col :span="24" class="height">
                  <div class="layui-form">
                    <div class="layui-form-left width-label-2">
                      新增工作机制名称
                    </div>
                    <el-input
                        readonly
                      type="text"
                      v-model="formData.newWorkName"
                      placeholder=""
                    />
                  </div>
                </el-col>
              </div>

              <div class="top-search">
                <el-col :span="12" class="height">
                  <div class="layui-form">
                    <div class="layui-form-left width-label-2">
                      集团主责部门
                    </div>
                    <el-input
                        readonly
                      type="text"
                      v-model="formData.groupMainDept"
                      placeholder=""
                    />
                  </div>
                </el-col>
              </div>

              <div
                class="common-in-box-header"
                style="margin-top: 10px; border: 0px; padding-left: 10px"
              >
                <div class="common-in-box-header-text">
                  违规问题线索查办情况-全企业
                </div>
              </div>

              <div class="common-in-box-content">
                <div class="top-search">
                  <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left width-label-2">
                        本季度新受理问题线索数量（件）
                      </div>
                      <el-input
                        readonly
                        type="text"
                        v-limit-input-number
                        v-model="formData.quarterNewProblemNumber"
                        placeholder=""
                        class="num-input"
                      />
                    </div>
                  </el-col>

                  <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left width-label-2">
                        本季度涉及资产损失（万元）
                      </div>
                      <el-input
                        readonly
                        type="text"
                         @blur="dottedClear"
                        v-limit-input-money
                        v-model="formData.lossAmount"
                        placeholder=""
                      />
                    </div>
                  </el-col>

                  <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left width-label-2">
                        本季度涉及资产损失风险（万元）
                      </div>
                      <el-input
                        readonly
                        type="text"
                        @blur="dottedClear"
                        v-limit-input-money
                        v-model="formData.lossRisk"
                        placeholder=""
                      />
                    </div>
                  </el-col>
                </div>

                <div class="top-search">
                  <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left width-label-2">
                        当年累计受理问题线索数量（件）
                      </div>
                      <el-input
                        readonly
                        type="text"
                        v-limit-input-number
                        v-model="formData.totalProblemSourceNumber"
                        placeholder=""
                        class="num-input"
                      />
                    </div>
                  </el-col>

                  <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left width-label-2">
                        上年结转问题线索数量（件）
                      </div>
                      <el-input
                        readonly
                        type="text"
                        v-limit-input-number
                        v-model="formData.lastYearProblemSourceNumber"
                        placeholder=""
                        class="num-input"
                      />
                    </div>
                  </el-col>
                </div>

                <div class="top-search">
                  <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left width-label-2">
                        其中:未启动核查（件）
                      </div>
                      <el-input
                        readonly
                        type="text"
                        v-limit-input-number
                        v-model="formData.checkNoStartedNumber"
                        placeholder=""
                        class="num-input"
                      />
                    </div>
                  </el-col>

                  <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left width-label-2">
                        其中: 正在核查（件）
                      </div>
                      <el-input
                        readonly
                        type="text"
                        v-limit-input-number
                        v-model="formData.checkInProcessNumber"
                        placeholder=""
                        class="num-input"
                      />
                    </div>
                  </el-col>

                  <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left width-label-2">
                        其中: 完成核查（件）
                      </div>
                      <el-input
                        readonly
                        type="text"
                        v-limit-input-number
                        v-model="formData.checkCompletedNumber"
                        placeholder=""
                        class="num-input"
                      />
                    </div>
                  </el-col>
                </div>
              </div>

              <div
                class="common-in-box-header"
                style="margin-top: 10px; border: 0px; padding-left: 10px"
              >
                <div class="common-in-box-header-text">
                  违规问题线索查办情况-集团本部
                </div>
              </div>

              <div class="common-in-box-content">
                <div class="top-search">
                  <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left width-label-2">
                        本季度新受理问题线索数量（件）
                      </div>
                      <el-input
                        readonly
                        type="text"
                        v-limit-input-number
                        v-model="formData.quarterNewProblemNumberGroup"
                        placeholder=""
                        class="num-input"
                      />
                    </div>
                  </el-col>

                  <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left width-label-2">
                        本季度涉及资产损失（万元）
                      </div>
                      <el-input
                        readonly
                        type="text"
                        v-limit-input-money
                        @blur="dottedClear"
                        v-model="formData.lossAmountGroup"
                        placeholder=""
                      />
                    </div>
                  </el-col>

                  <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left width-label-2">
                        本季度涉及资产损失风险（万元）
                      </div>
                      <el-input
                        readonly
                        type="text"
                        v-limit-input-money
                        @blur="dottedClear"
                        v-model="formData.lossRiskGroup"
                        placeholder=""
                      />
                    </div>
                  </el-col>
                </div>

                <div class="top-search">
                  <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left width-label-2">
                        当年累计受理问题线索数量（件）
                      </div>
                      <el-input
                        readonly
                        type="text"
                        v-limit-input-number
                        v-model="formData.totalProblemSourceNumberGroup"
                        placeholder=""
                        class="num-input"
                      />
                    </div>
                  </el-col>

                  <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left width-label-2">
                        上年结转问题线索数量（件）
                      </div>
                      <el-input
                        readonly
                        type="text"
                        v-limit-input-number
                        v-model="formData.lastYearProblemSourceNumberGroup"
                        placeholder=""
                        class="num-input"
                      />
                    </div>
                  </el-col>
                </div>

                <div class="top-search">
                  <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left width-label-2">
                        其中:未启动核查（件）
                      </div>
                      <el-input
                        readonly
                        type="text"
                        v-limit-input-number
                        v-model="formData.checkNoStartedNumberGroup"
                        placeholder=""
                        class="num-input"
                      />
                    </div>
                  </el-col>

                  <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left width-label-2">
                        其中: 正在核查（件）
                      </div>
                      <el-input
                        readonly
                        type="text"
                        v-limit-input-number
                        v-model="formData.checkInProcessNumberGroup"
                        placeholder=""
                        class="num-input"
                      />
                    </div>
                  </el-col>

                  <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left width-label-2">
                        其中: 完成核查（件）
                      </div>
                      <el-input
                        readonly
                        type="text"
                        v-limit-input-number
                        v-model="formData.checkCompletedNumberGroup"
                        placeholder=""
                        class="num-input"
                      />
                    </div>
                  </el-col>
                </div>
              </div>

              <div
                class="common-in-box-header"
                style="margin-top: 10px; border: 0px; padding-left: 10px"
              >
                <div class="common-in-box-header-text">追责整改工作成效</div>
              </div>

              <div class="common-in-box-content">
                <div class="top-search">
                  <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left width-label-2">
                        当年累计完成追责问题数量（件）
                      </div>
                      <el-input
                        readonly
                        type="text"
                        v-limit-input-number
                        v-model="formData.totalCompletedProblemNumber"
                        placeholder=""
                        class="num-input"
                      />
                    </div>
                  </el-col>

                  <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left width-label-2">
                        当年累计追责总人数（人）
                      </div>
                      <el-input
                        readonly
                        type="text"
                        v-limit-input-number
                        v-model="formData.totalAccountabilityPersonNumber"
                        placeholder=""
                        class="num-input"
                      />
                    </div>
                  </el-col>

                  <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left width-label-2">
                        当年累计追责总人次（人次）
                      </div>
                      <el-input
                        readonly
                        type="text"
                        v-limit-input-number
                        v-model="formData.totalAccountabilityPersonTime"
                        placeholder=""
                        class="num-input"
                      />
                    </div>
                  </el-col>


                </div>

                <div class="top-search">
                    <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left width-label-2">
                        其中:
                        集团管理干部（人）
                      </div>
                      <el-input
                        readonly
                        type="text"
                        v-limit-input-number
                        v-model="formData.groupManagementNumber"
                        placeholder=""
                        class="num-input"
                      />
                    </div>
                  </el-col>

                  <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left width-label-2">
                        其中:
                        子企业管理干部（人）
                      </div>
                      <el-input
                        readonly
                        type="text"
                        v-limit-input-number
                        v-model="formData.subManagementNumber"
                        placeholder=""
                        class="num-input"
                      />
                    </div>
                  </el-col>
                    <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left width-label-2">
                        其中:
                        中央企业负责人（人）
                      </div>
                      <el-input
                        readonly
                        type="text"
                        v-limit-input-number
                        v-model="formData.enterpriseManagementNumber"
                        placeholder=""
                        class="num-input"
                      />
                    </div>
                  </el-col>




                </div>
                <div class="top-search">

                    <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left width-label-2">
                        其中: 组织处理 （人次）
                      </div>
                      <el-input
                        readonly
                        type="text"
                        v-limit-input-number
                        v-model="formData.orgHandleTime"
                        placeholder=""
                        class="num-input"
                      />
                    </div>
                  </el-col>

                  <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left width-label-2">
                        其中: 扣减薪酬 （人次）
                      </div>
                      <el-input
                        readonly
                        type="text"
                        v-limit-input-number
                        v-model="formData.deductionSalaryTime"
                        placeholder=""
                        class="num-input"
                      />
                    </div>
                  </el-col>

                  <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left width-label-2">
                        其中: 党纪处分 （人次）
                      </div>
                      <el-input
                        readonly
                        type="text"
                        v-limit-input-number
                        v-model="formData.partyPunishmentTime"
                        placeholder=""
                        class="num-input"
                      />
                    </div>
                  </el-col>
                </div>

                <div class="top-search">
                  <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left width-label-2">
                        其中: 政务处份（人次）
                      </div>
                      <el-input
                        readonly
                        type="text"
                        v-limit-input-number
                        v-model="formData.governmentPunishmentTime"
                        placeholder=""
                        class="num-input"
                      />
                    </div>
                  </el-col>

                  <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left width-label-2">
                        其中: 禁入限制（人次）
                      </div>
                      <el-input
                        readonly
                        type="text"
                        v-limit-input-number
                        v-model="formData.prohibitTime"
                        placeholder=""
                        class="num-input"
                      />
                    </div>
                  </el-col>


                  <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left width-label-2">
                        其中:移送监察机关或司法机关 （人次）
                      </div>
                      <el-input
                        readonly
                        type="text"
                        v-limit-input-number
                        v-model="formData.transferAuthorityTime"
                        placeholder=""
                        class="num-input"
                      />
                    </div>
                  </el-col>


                </div>

                <div class="top-search">

                    <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left width-label-2">
                        其中: 其他 （人次）
                      </div>
                      <el-input
                        readonly
                        type="text"
                        v-limit-input-number
                        v-model="formData.processingOtherItem"
                        placeholder=""
                        class="num-input"
                      />
                    </div>
                  </el-col>

                  <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left width-label-2">
                        当年累计扣减薪酬金额（万元）
                      </div>
                      <el-input
                        readonly
                        type="text"
                        v-limit-input-money
                        @blur="dottedClear"
                        v-model="formData.totalDeductionSalary"
                        placeholder=""
                      />
                    </div>
                  </el-col>


                </div>

                <div class="top-search">
                    <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left width-label-2">
                        责任约谈-当年累计责任约谈次数（次）
                      </div>
                      <el-input
                        readonly
                        type="text"
                        v-limit-input-number
                        v-model="formData.dutyInterviewNumber"
                        placeholder=""
                        class="num-input"
                      />
                    </div>
                  </el-col>

                  <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left width-label-2">
                        责任约谈-当年累计责任约谈总人次（人次）
                      </div>
                      <el-input
                        readonly
                        readonly
                        type="text"
                        v-limit-input-number
                        v-model="formData.dutyInterviewPersonTime"
                        placeholder=""
                        class="num-input"
                      />
                    </div>
                  </el-col>
                </div>

                <div class="top-search">
                  <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left width-label-2">
                        当年累计挽回资产损失（万元）
                      </div>
                      <el-input
                        readonly
                        type="text"
                        v-limit-input-money
                        @blur="dottedClear"
                        v-model="formData.totalRetrieveLossAmount"
                        placeholder=""
                      />
                    </div>
                  </el-col>

                  <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left width-label-2">
                        当年累计降低损失风险（万元）
                      </div>
                      <el-input
                        readonly
                        type="text"
                        v-limit-input-money
                        @blur="dottedClear"
                        v-model="formData.totalReduceLossRisk"
                        placeholder=""
                      />
                    </div>
                  </el-col>

                  <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left width-label-2">
                        当年累计制修订管理制度（项）
                      </div>
                      <el-input
                        readonly
                        type="text"
                        v-limit-input-number
                        v-model="formData.totalPerfectSystemNumber"
                        placeholder=""
                        class="num-input"
                      />
                    </div>
                  </el-col>
                </div>

                <div class="top-search">
                  <el-col :span="24" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left width-label-2">
                        <span class="must-icon">*</span>其他工作成效
                      </div>
                      <el-input
                        readonly
                        type="text"
                        v-model="formData.otherAchievement"
                        placeholder=""
                      />
                    </div>
                  </el-col>
                </div>

                <div
                  class="common-in-box-header"
                  style="margin-top: 10px; border: 0px; padding-left: 10px"
                >
                  <div class="common-in-box-header-text">其他</div>
                </div>

                <div class="top-search">
                  <el-col :span="24" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left">备注</div>
                      <el-input
                        type="text"
                        v-model="formData.remark"
                        placeholder="请输入"
                      />
                    </div>
                  </el-col>
                </div>

                <div class="top-search">
                  <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left">
                        <span class="must-icon">*</span>追责部门填报人
                      </div>
                      <el-input
                        type="text"
                        v-model="formData.informantName"
                        placeholder="请输入"
                      />
                    </div>
                  </el-col>

                  <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left">
                        联系电话
                      </div>
                      <el-input
                        type="text"
                        v-model="formData.informantPhone"
                        placeholder="请输入"
                      />
                    </div>
                  </el-col>
                </div>
              </div>

              <div class="common-in-box-header" style="margin-top: 10px">
              <div class="common-in-box-header-line"></div>
              <div class="common-in-box-header-text">附件列表</div>
              <div class="flex-1"></div>

              <el-upload
                ref="upload"
                class="upload-demo"
                :action="files.actionUrl"
                :headers="files.myHeaders"
                :on-success="handleFileSuccess"
                :data="{
                  busiTableId: files.busiTableId,
                  busiTableName: files.busiTableName,
                }"
                :show-file-list="false"
              >
                <el-button
                  size="mini"
                  type="primary"
                  icon="el-icon-upload2"
                  >附件上传
                </el-button>
              </el-upload>
            </div>

            <div class="tables tables_1">
              <el-table
                :data="filesData"
                border
                v-loading="tableLoading"
                style="width: 100%"
              >
                <el-table-column
                  label="序号"
                  type="index"
                  min-width="5%"
                  align="center"
                />
                <el-table-column label="文件名" prop="fileName" min-width="50%">
                  <template slot-scope="scope">
                    <div
                      style="text-align: left"
                      class="overflowHidden-1"
                      :title="scope.row.fileName"
                    >
                      {{ scope.row.fileName || "" }}
                    </div>
                  </template>
                </el-table-column>
                <el-table-column
                  label="上传人"
                  prop="createUserName"
                  min-width="10%"
                />
                <el-table-column
                  label="上传时间"
                  prop="createTime"
                  min-width="20%"
                />

                <el-table-column
                  label="操作"
                  fixed="right"
                  min-width="15%"
                  align="center"
                  class-name="small-padding fixed-width"
                >
                  <template slot-scope="scope">
                    <el-button
                      size="mini"
                      type="text"
                      title="下载"
                      icon="el-icon-bottom"
                      @click="fileDownload(scope.row)"
                    ></el-button>
                    <el-button
                      size="mini"
                      type="text"
                      title="删除"
                      icon="el-icon-delete"
                      @click="delFile(scope.row)"
                    ></el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
            </div>

          </div>
          <div class="bottom-btn">
            <div class="left-empty" />
            <el-button size="mini" type="primary" @click="syncData">同步数据</el-button>
            <el-button size="mini" @click="saveForm">保存</el-button>
          </div>
        </div>
      </div>
    </div>


  </div>
</template>
  <script>
// import {} from '@/api/views/quarterly-report'
import { getToken } from "@/utils/auth";
import {
  addQuerySasacQuarterInfo, getSasacQuarterInfo, queryQuarterReportParam,saveSasacQuarterInfo,syncSasacQuarterInfo
} from "@/api/quarterly-report/escalation-gzw";
import {
  delQuarterReportFile,
  queryDefaultInfo,
  queryQuarterReportFileList,
  saveQuarterReportInfo
} from "@/api/quarterly-report";
export default {
  name: "addGroup",
  components: {  },
  props: {
    closeBtn: {
      type: Function,
      default: null,
    },
    // add 为新增 edit 为编辑
    editType: {
      type: String,
      default: "",
    },
    //编辑内容
    rowData: {
      type: Object,
      default: () => {},
    },
  },
  dicts: [],
  data() {
    return {
      infoData: {}, //基本信息
      reportQuarterName: "", //第几季度名称
      reportQuarterList: [],
      tableLoading: false, //表格loading
      filesData: [], //附件列表
      //附件上传
      files: {
        busiTableId: "",
        busiTableName: "t_col_viol_sasac_quarter_report",
        actionUrl: process.env.VUE_APP_BASE_API + "/quarter/file/uploadFiledQuarterReportFile", // 上传地址
        myHeaders: { Authorization: "Bearer " + getToken() }, // 上传header
      },
      //本单位汇总信息
      formData: {},
      saveParams:{},//保存参数

    };
  },
  created() {
    // this.loadTips();
    this.queryQuarterReportParam();
    console.log(this.rowData.id)
    if(this.editType === 'edit'){
      this.formData.sasacQuarterReportId = this.rowData.id;
      this.getSasacQuarterInfo(this.formData);
    }

  },
  methods: {
       //清楚输入.
       dottedClear(evt) {
      if (evt.target.value.indexOf(".") != -1) {
        var length = evt.target.value.toString().split(".")[1].length;
        if (length == "0") {
          evt.target.value = evt.target.value + "00";
        }
        if (length == "1") {
          evt.target.value = evt.target.value + "0";
        }
      } else {
        evt.target.value = evt.target.value + ".00";
      }
    },
    //基础信息
    queryQuarterReportParam(obj) {
      queryQuarterReportParam(obj).then((response) => {
        if( response.data){
          this.reportQuarterList = response.data.reportQuarterList;
          if (this.editType == 'add'){
            this.infoData = response.data;
          }
        }
      });
    },
    getSasacQuarterInfo(obj) {
      getSasacQuarterInfo(obj).then((response) => {
        if( response.data){
          // this.reportQuarterList = response.data.reportQuarterList;
          this.infoData = response.data;
          this.formData = response.data;

          this.files.busiTableId = this.infoData.id;
          //查询附件列表
          this.queryFileList();
        }
      });
    },
    //季度报告改变事件
    reportQuarterChange() {
      //如果年度已经选择则去请求 校验 是否存在数据
      if (this.infoData.reportYear) {
        this.addQuerySasacQuarterInfo();
      }
    },
    //年度改变事件
    reportYearChange(val) {
      if (val && this.infoData.reportQuarter) {
        this.addQuerySasacQuarterInfo();
      }
    },
    addQuerySasacQuarterInfo() {
      var params = {
        reportYear:this.infoData.reportYear
        ,reportQuarter : this.infoData.reportQuarter
      }
      addQuerySasacQuarterInfo(params).then((res) => {
          if(res.code !== 200){
            this.$alert(res.msg, {
              confirmButtonText: "确定",
              callback: (action) => {
                this.infoData.reportCloseTime = "";
                this.infoData.reportTitle = "";
                this.infoData.reportQuarter = "";
                this.infoData.reportRequire = "";
              },
            });
          }else{
            this.formData = res.data;
            this.files.busiTableId =  res.data.id;
          }
      });
    },
    //查询附件列表
    queryFileList(){
      this.tableLoading = true
      queryQuarterReportFileList(this.files.busiTableId).then((res)=>{
        this.filesData = res.data;
        this.tableLoading = false
      })
    },
    // 附件上传成功
    handleFileSuccess(res, file, fileList) {
      const loading = this.$loading({
        lock: true,//lock的修改符--默认是false
        text: '正在上传中',//显示在加载图标下方的加载文案
        background: 'transparent',
        spinner: 'el-icon-loading',//自定义加载图标类名
        target: document.querySelector('#table')//loadin覆盖的dom元素节点
      });
      if (res.code == 200) {
        this.queryFileList()
        loading.close()
      } else {
        this.$message.error(res.msg);
        loading.close()
      }
    },
    /** 附件删除操作 */
    delFile(row) {
      this.$modal.confirm("确认删除该附件吗？").then( ()=> {
        return delQuarterReportFile(row.id);
      }).then(()=>{
        this.queryFileList();
        this.$modal.msgSuccess("删除成功");
      }) .catch(() => {});
    },

    /**下载文件*/
    fileDownload(obj) {
      this.download(
        "/sys/attachment/downloadSysAttachment/" + obj.attachmentId,
        {},
        obj.fileName
      );
    },
    // 关闭
    cancel() {
      this.closeBtn();
    },
    refreshParams(){
      this.saveParams = this.formData;
      this.saveParams.companyName = this.infoData.companyName;
      this.saveParams.industryName = this.infoData.industryName;
      this.saveParams.industryCode = this.infoData.industryCode;
    },
    //保存
    saveForm() {
      this.refreshParams()
      saveSasacQuarterInfo(this.saveParams).then((res) => {
        if(res.code == 200){
          this.$modal.msgSuccess('保存成功！');
          this.cancel();
        }else{
          this.$modal.msgSuccess('保存失败，请联系管理员');
        }
      });

    },
    //同步数据
    syncData() {
      if (this.infoData.reportQuarter || this.infoData.reportYear){
        this.formData.sasacQuarterReportId = this.formData.id;
        syncSasacQuarterInfo(this.formData).then((res) => {
          if(res.code == 200){
            this.getSasacQuarterInfo(this.formData);
            this.$modal.msgSuccess('同步成功！');
          }else{
            this.$modal.msgSuccess('保存失败，请联系管理员');
          }
        });
      }else{
        this.$modal.msgWarning('请选择上报年度和上报季度！');
      }

    },

  },
};
</script>
  <style lang="scss" scoped>
@import "~@/assets/styles/quarterly-report/index.css";
</style>
