<template>
  <div>
    <BlockCard title="上报说明">
      <el-form class="common-card padding10_0" size="medium"  label-width="160px">
        <el-row>
          <el-col :span="16">
            <el-form-item label="央企集团名称"><span>{{formData.groupName}}</span></el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="集团简称"><span>{{formData.groupSortName}}</span></el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="行业名称"><span>{{formData.industryName}}</span></el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="行业代码"><span>{{formData.industryCode}}</span></el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="统一社会信用代码"><span>{{formData.uscCode}}</span></el-form-item>
          </el-col>
          <el-col :span="16">
            <el-form-item label="定期报告名称"><span>{{formData.fixDateReportName}}</span></el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="定期报告编码"><span>{{formData.fixDateReportCode}}</span></el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="报送次数"><span>{{formData.reportNumber}}</span></el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="报告日期"><span>{{formData.reportDate}}</span></el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="报送年度"><span>{{formData.reportYear}}</span></el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </BlockCard>
    <BlockCard
    title="定期报告附表"
    >
      <div style="height:calc(70vh - 190px);">
        <el-table
          border
          :data="formData.regularProblems"
          ref="table.regularProblems"
          height="100%"
        >
          <el-table-column
            fixed
            align="center"
            label="序号"
            type="index"
            width="50">
          </el-table-column>

          <el-table-column align="center" show-overflow-tooltip label="审计系统编号" prop="systemCode" width="200"/>
          <el-table-column align="center" show-overflow-tooltip label="问题编号" prop="problemCode" width="200"/>
          <el-table-column align="center" show-overflow-tooltip label="涉及企业名称" prop="involveCompany" width="200"/>
          <el-table-column align="center" show-overflow-tooltip label="企业层级" prop="involveCompanyLevel" width="200"/>

          <el-table-column align="center" show-overflow-tooltip label="违规问题线索有关详情" prop="prov1" width="2000">
            <el-table-column align="center" show-overflow-tooltip label="问题线索来源" prop="problemClues" width="200"/>
            <el-table-column align="center" show-overflow-tooltip label="问题受理时间" prop="acceptDate" width="200"/>
            <el-table-column align="center" show-overflow-tooltip label="是否为以前年度定期报告反映的问题" prop="carryover" width="200"/>
            <el-table-column align="center" show-overflow-tooltip label="问题描述" prop="problemDescribtion" width="200"/>
            <el-table-column align="center" show-overflow-tooltip label="问题类别" prop="problemType" width="200"/>
            <el-table-column align="center" show-overflow-tooltip label="违反具体规定" prop="violationRules" width="200"/>
            <el-table-column align="center" show-overflow-tooltip label="境内（外）" prop="abroad" width="200"/>
            <el-table-column align="center" show-overflow-tooltip label="涉及损失及风险（万元）" prop="involveRiskLoss" width="200"/>
            <el-table-column align="center" show-overflow-tooltip label="损失风险类别（一般/较大/重大资产损失）" prop="lossLossType" width="200"/>
            <el-table-column align="center" show-overflow-tooltip label="损失形成主要原因" prop="lossMainCauses" width="200"/>
          </el-table-column>

          <el-table-column align="center" show-overflow-tooltip label="核查情况" prop="prov2" width="800">
            <el-table-column align="center" show-overflow-tooltip label="核查状态" prop="auditStatus" width="200"/>
            <el-table-column align="center" show-overflow-tooltip label="核查时间" prop="auditDate" width="200"/>
            <el-table-column align="center" show-overflow-tooltip label="核查主体" prop="auditMainBody" width="200"/>
            <el-table-column align="center" show-overflow-tooltip label="未完成核查原因" prop="auditNonCompletionReasons" width="200"/>
          </el-table-column>
          <el-table-column align="center" show-overflow-tooltip label="责任追究工作开展情况" prop="prov3" width="2400">
            <el-table-column align="center" show-overflow-tooltip label="是否追责" prop="zrzjIsNot" width="200"/>
            <el-table-column align="center" show-overflow-tooltip label="未追责原因" prop="zrzjNonReason" width="200"/>
            <el-table-column align="center" show-overflow-tooltip label="责任追究时间" prop="zrzjDate" width="200"/>
            <el-table-column align="center" show-overflow-tooltip label="追责总人数" prop="zrzjTotalPersons" width="200"/>
            <el-table-column align="center" show-overflow-tooltip label="追责总人次" prop="zrzjTotalPertime" width="200"/>
            <el-table-column align="center" show-overflow-tooltip label="责任追究处理方式（人次）" prop="prov24" width="1400">
              <el-table-column align="center" show-overflow-tooltip label="组织处理（人次）" prop="zrzjOrgHandle" width="200"/>
              <el-table-column align="center" show-overflow-tooltip label="扣减薪酬" prop="a2" width="400">
                <el-table-column align="center" show-overflow-tooltip label="人次" prop="zrzjCutSalary" width="200"/>
                <el-table-column align="center" show-overflow-tooltip label="金额（万元）" prop="zrzjCutSalaryMoney" width="200"/>
              </el-table-column>
              <el-table-column align="center" show-overflow-tooltip label="党纪处分（人次）" prop="zrzjPartyDiscipline" width="200"/>
              <el-table-column align="center" show-overflow-tooltip label="政务处分（人次）" prop="zrzjGovAffairs" width="200"/>
              <el-table-column align="center" show-overflow-tooltip label="禁入限制（人次）" prop="zrzjLimitPertime" width="200"/>
              <el-table-column align="center" show-overflow-tooltip label="移送国家检察机关或司法机关（人次）" prop="zrzjTransferJjjc" width="200"/>
              <el-table-column align="center" label="其他（人次）" show-overflow-tooltip prop="processingOtherItem" width="150"/>
            </el-table-column>
          </el-table-column>
          <el-table-column align="center" show-overflow-tooltip label="问题整改情况" prop="prov4" width="1000">
            <el-table-column  align="center" show-overflow-tooltip label="是否完成整改" prop="rectifyIsComplete" width="200"/>
            <el-table-column align="center" show-overflow-tooltip label="完善制度情况" prop="prov44" width="400">
              <el-table-column align="center" show-overflow-tooltip label="数量（项）" prop="rectifyRulesNumber" width="200"/>
              <el-table-column align="center" show-overflow-tooltip label="制度名称、文号" prop="rectifyRulesName" width="200"/>
            </el-table-column>
            <el-table-column align="center" show-overflow-tooltip label="损失挽回情况" prop="prov44" width="400">
              <el-table-column align="center" show-overflow-tooltip label="金额（万元）" prop="rectifyRetrieveLoss" width="200"/>
              <el-table-column align="center" show-overflow-tooltip label="采取的主要措施" prop="rectifyMainMeasures" width="200"/>
            </el-table-column>
          </el-table-column>
          <el-table-column align="center" label="备注" prop="remark" width="200"/>
        </el-table>
      </div>
    </BlockCard>
    <BlockCard title="定期报告">
      <FieldList
        :key="formData.regularAttachmentItems"
        :fileList="formData.regularAttachmentItems"
      ></FieldList>
    </BlockCard>
  </div>
</template>

<script>
  import BlockCard from '@/components/BlockCard'
  import FieldList from '@/views/components/fileUpload/regular'
  import {regularReportData} from '@/api/sasac/reportManagement/edit/detail/index'
    export default {
        name: "periodicReport",
        components:{BlockCard,FieldList},
      props: {
        problemId: {
          type: String
        },
        height:{
          type: String
        }
      },
      data(){
        return{
          formData:{}
        }
      },
      created(){
          this.RegularReportData();
      },
      methods: {
        // 获取查询页数据
        RegularReportData() {
          regularReportData(this.problemId).then(response => {
            this.formData = response.data;
          });
        },
        onRefresh(){
          this.RegularReportData();
        }
      }
    }
</script>

<style scoped>
::v-deep.el-table th.el-table__cell.is-leaf {
  height: 40px;
  background: #F4F8FC;
}
</style>
