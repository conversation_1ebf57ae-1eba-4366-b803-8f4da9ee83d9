<template>
  <el-row class="height">
    <el-col :span="24" class="tree-box">
      <el-tree ref="tree"
               :key="index"
               :data="data"
               lazy
               show-checkbox
               :default-checked-keys="defaultTree"
               node-key="id"
               check-strictly
               @check-change="checkChange"
               :load="loadnode"
               :props="defaultProps"
               @node-click="nodeclick">
      </el-tree>
    </el-col>
    <el-col :span="24" class="tree-bottom">
      <div class="flex border-top">
        <p>已选部门</p>
        <p>已选<span>{{selectTree.length}}</span>个</p>
      </div>
      <TreeSelect
        type="dept"
        :selectTree="selectTree"
        @noCheck="noCheck"
      >
      </TreeSelect>
    </el-col>
  </el-row>
</template>

<script>
  import {queryDepartmentInvolved,saveDepartmentInvolved,delDepartmentInvolved} from "@/api/daily/tree";
  import TreeSelect from '@/components/TreeSelect';

  export default {
    components: {
      TreeSelect
    },
    props: {
      problemId:{
        type: String
      },
      Pids:{
        type:Array
      },
      InvolCompanys:{
        type:Array
      },
      relevantTableId:{
        type: String
      },
      relevantTableName:{
        type: String
      },
    },
    data() {
      return {
        height:0,
        data:[],
        index:0,
        selectTree:[],
        defaultTree:[],
        defaultProps: {//树对象属性对应关系
          children: 'children',
          label: 'name',
          isLeaf:function(data, node){
            return !data.isParent
          },
          disabled:function(data, node){
            return data.nocheck
          }
        }
      }
    },
    methods: {
      Refresh(){
        this.index++
      },
      loadnode(node,resolve){
        if(!this.Pids.length){
          return false;
        }
        //如果展开第一级节点，从后台加载一级节点列表
        if(node.level==0)
        {
          this.loadfirstnode(resolve);
        }
        //如果展开其他级节点，动态从后台加载下一级节点列表
        if(node.level>=1)
        {
          this.loadchildnode(node,resolve);
        }
      },
      //加载第一级节点
      loadfirstnode(resolve){
        this.defaultTree=[];
        let query={
          problemId:this.problemId,
          relevantTableId:this.relevantTableId,
          relevantTableName:this.relevantTableName,
          departmentlist:[],
          pIds:this.Pids,
          involCompany: this.InvolCompanys[0],
          type:'DEPT'
        };
        queryDepartmentInvolved(query).then(
          response => {
            this.selectTree = response.data.selectedList;
            for (let i = 0, len = this.selectTree.length; i < len; i++) {
              this.defaultTree.push(this.selectTree[i].involOrgId);
            }
            resolve(response.data.resultList);
            this.$emit("newDept", {selectTree:this.selectTree,defaultTree:this.defaultTree});
          }
        );
      },
      //加载节点的子节点集合
      loadchildnode(node,resolve){
        let query={
          problemId:this.problemId,
          relevantTableId:this.relevantTableId,
          relevantTableName:this.relevantTableName,
          departmentlist:[],
          involCompany: this.Pids[0],
          pIds:[],
          type:'DEPT',
          orgType: node.data.orgGrade,
          pId:node.data.id
        };
        queryDepartmentInvolved(query).then(
          response => {
            resolve(response.data.resultList);
          }
        );
      },
      //点击节点上触发的事件，传递三个参数，数据对象使用第一个参数
      nodeclick(data,dataObj,self)
      {},
      //修改状态
      checkChange(node,type){
        if(type){//选中
          this.SaveViolateInfo(node);
        }else{
          let treeArry = [{id: node.id}];
          this.DelDepartmentInvolved(treeArry);
        }
      },
      //删除某节点
      noCheck(obj){
        if(this.$refs.tree.getNode(obj.involOrgId)){
          this.$refs.tree.setChecked(obj.involOrgId,false);
        }else{
          let treeArry = [{id: obj.involOrgId}];
          this.DelDepartmentInvolved(treeArry);
        }
      },
      /**
       * 保存
       * @return {boolean}
       */
      SaveViolateInfo(treeNode){
        for (let i = 0, len = this.selectTree.length; i < len; i++) {
          if(this.selectTree[i].involOrgId===treeNode.id){
            return false
          }
        }
        treeNode.involOrgName = treeNode.name;
        treeNode.involOrgId = treeNode.id;
        treeNode.selected = true;
        this.selectTree.push(treeNode);
        this.defaultTree.push(treeNode.id);
        treeNode.involAreaCode = treeNode.id;
        let treeArry = [{id: treeNode.id, name: treeNode.name, pId: treeNode.pid, pName: treeNode.pname}];
        let query={
          problemId:this.problemId,
          relevantTableId:this.relevantTableId,
          relevantTableName:this.relevantTableName,
          departmentList:treeArry
        };
        saveDepartmentInvolved(query).then(
          response => {
            this.$emit("newDept", {selectTree:this.selectTree,defaultTree:this.defaultTree});
          }
        );
      },
      /**
       * 删除
       */
      DelDepartmentInvolved(treeArry){
        let obj = treeArry[0];
        let query={
          problemId:this.problemId,
          relevantTableId:this.relevantTableId,
          relevantTableName:this.relevantTableName,
          departmentList:treeArry
        };
        delDepartmentInvolved(query).then(
          response => {
            this.selectTree.splice(this.selectTree.findIndex(item => item.involOrgId === obj.id), 1);
            this.defaultTree.splice(this.defaultTree.findIndex(item => item === obj.id), 1);
            this.$emit("newDept", {selectTree:this.selectTree,defaultTree:this.defaultTree});
          }
        );
      }
    }
  }
</script>
<style  rel="stylesheet/scss" scoped lang="scss">
  .is-disabled{
    display: none !important;
  }
  .height{
    height: 100%;
  }
  .tree-body{
    height: 100%;
    .public-box-content{
      height: 60vh !important;
    }
    .tree-height{
      height: calc(100% - 40px);
      .tree-box{
        height: calc(100% - 100px);
        overflow: auto;
      }
      .tree-bottom{
        height: 140px;
        overflow: auto;
      }
    }
  }
</style>
