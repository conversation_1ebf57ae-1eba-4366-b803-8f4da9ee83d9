<!--2：初步核实-复核-->
<template>
  <div>
    <div>
      <ModifyrecordBtn
        :key="problemId||relevantTableId||relevantTableName"
        :problemId="problemId"
        :relevantTableId="relevantTableId"
        :relevantTableName="relevantTableName"
        :problemStatus="2"
      ></ModifyrecordBtn>
      <opinion
        :processInstanceId="procInsId"
        :isShow="isShow"
      />
      <el-form ref="elForm" :model="formData" :rules="rules" size="medium" label-width="160px">
        <BlockCard
          title="基本信息"
        >
          <el-row>
            <el-col :span="8">
              <el-form-item label="系统编号" prop="findTime">
                <span>{{formData.auditCode}}</span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="问题编号" prop="problemCode">
                <span>{{formData.problemCode}}</span>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="违规事项" prop="problemTitle">
                <span>{{formData.problemTitle}}</span>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="问题线索描述" prop="problemDescribe">
                <span>{{formData.problemDescribe}}</span>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="涉及专业线" prop="specLists">
                <el-checkbox-group v-model="formData.specLists" disabled :key="formData.specLists" size="medium">
                  <el-checkbox v-for="(item, index) in specList" :key="item.dictValue" border :label="item.dictValue"
                  >{{item.dictLabel}}
                  </el-checkbox>
                </el-checkbox-group>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item  label="涉及单位/部门/人员" prop="field107">
                <PersList
                  :edit='edit'
                  :problemId="problemId"
                  :relevantTableId="relevantTableId"
                  :relevantTableName="relevantTableName"
                  ref="pers"
                ></PersList>
              </el-form-item>
            </el-col>
          </el-row>
        </BlockCard>
        <Remind
          :key="actualFlag"
          :actualFlag="actualFlag"
        ></Remind>
        <BlockCard
          title="初核信息"
        >
          <el-row>
            <el-col :span="8">
              <el-form-item label="初核时间" prop="verifyDate">
                <span>{{formData.verifyStartDate | timeYear}}至{{formData.verifyEndDate | timeYear}}</span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="移交材料是否齐全" prop="isHandoverDataComplete">
                <el-radio-group v-model="formData.isHandoverDataComplete" size="medium">
                  <el-radio disabled v-for="(item, index) in YesOrNo" :key="index"
                            :label="item.value">{{item.label}}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>

            <el-col :span="8">
              <el-form-item label="是否产生资产损失" prop="isCauseLoss">
                <el-radio-group v-model="formData.isCauseLoss" size="medium">
                  <el-radio disabled v-for="(item, index) in YesOrNo" :key="index"
                            :label="item.value">{{item.label}}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="损失关联关系" prop="seriousAdverseEffectsDesc">
                <el-select disabled v-model="formData.lossType" placeholder="请选择损失关联关系" clearable
                           :style="{width: '100%'}" value="formData.lossType">
                  <el-option v-for="(item, index) in lossTypeOptions" :key="index" :label="item.dictLabel"
                             :value="item.dictValue">{{item.dictLabel}}</el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="预估损失金额（万元）" prop="lossAmount">
               <span>{{ (formData.lossAmount).toFixed(2) }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="预估损失风险（万元）" prop="lossRisk">
                <span>{{ (formData.lossRisk).toFixed(2) }}</span>
              </el-form-item>
            </el-col>

            <el-col :span="24" />
            <el-col :span="8">
              <el-form-item label="是否产生不良影响" prop="isAdverseEffect">
                 <el-radio-group v-model="formData.isAdverseEffect" size="medium">
                    <el-radio v-for="(item, index) in whetherEffectOptions" :key="index" :label="item.value" @change="radioEffectChanged"
                              disabled>{{item.label}}</el-radio>
                  </el-radio-group>
              </el-form-item>
            </el-col>
             <el-col :span="16">
                <el-form-item label="对应不良影响" prop="correspondingAdverseEffects" v-if="formData.isAdverseEffect">
                  <el-select v-model="formData.correspondingAdverseEffects" :style="{width: '100%'}" clearable="clearable" multiple="multiple" value="" disabled>
                    <el-option v-for="(item, index) in dict.type.corresponding_adverse_effect" :key="index" :label="item.label" :value="item.value"></el-option>
                  </el-select>
                </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="损失形成主要原因" prop="lossReason">
                <span>{{formData.lossReason}}</span>
              </el-form-item>
            </el-col>
             <el-col :span="24">
              <el-form-item label="造成的不良影响" prop="adverseEffects">
                 <span>{{formData.adverseEffects}}</span>
              </el-form-item>
            </el-col>

             <el-col :span="8">
              <el-form-item label="是否产生严重不良影响" prop="seriousAdverseEffectsFlag">
                <el-radio-group v-model="formData.seriousAdverseEffectsFlag" size="medium">
                  <el-radio v-for="(item, index) in YesOrNo" :key="index"
                            :label="item.value" disabled>{{item.label}}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col v-show="formData.seriousAdverseEffectsFlag" :span="24">
              <el-form-item label="严重不良影响描述" prop="seriousAdverseEffectsDesc">
                <el-select disabled v-model="formData.seriousAdverseEffectsDesc" placeholder="请选择严重不良影响描述" clearable
                           :style="{width: '100%'}" value="formData.seriousAdverseEffectsDesc">
                  <el-option v-for="(item, index) in dict.type.VIOLD_ADVER_EFFECT_DES" :key="index"
                             :label="item.label" :value="item.value" :disabled="item.disabled"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="涉嫌违规" prop="isViolation">
                <el-radio-group v-model="formData.isViolation" size="medium">
                  <el-radio disabled v-for="(item, index) in YesOrNo" :key="index"
                            :label="item.value">{{item.label}}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>

            <el-col :span="8">
              <el-form-item label="涉嫌违纪违法" prop="isIllegal">
                <el-radio-group v-model="formData.isIllegal" size="medium">
                  <el-radio disabled v-for="(item, index) in YesOrNo" :key="index"
                            :label="item.value" >{{item.label}}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="24" />
            <el-col :span="24">
              <ScopeSituation
                v-if="scopeSituation"
                :key="problemId||relevantTableId||relevantTableName"
                :edit='edit'
                :problemId="problemId"
                :relevantTableId="relevantTableId"
                :relevantTableName="relevantTableName"
                ref="scope"
              ></ScopeSituation>
            </el-col>
            <el-col :span="24">
              <el-form-item label="其他说明事项" prop="otherExplainMatter">
               <span>{{formData.otherExplainMatter}}</span>
              </el-form-item>
            </el-col>
          </el-row>
        </BlockCard>
      </el-form>
      <BlockCard
        title="附件列表"
      >
        <FileUpload
          :edit='edit'
          :key="problemId||relevantTableId||relevantTableName"
          :problemId="problemId"
          :relevantTableId="relevantTableId"
          :relevantTableName="relevantTableName"
          flowType="VIOL_DAILY"
          problemStatus="2"
          ref="file"
        ></FileUpload>

      </BlockCard>
    </div>
  </div>
</template>
<script>
  import {queryVerifyRecord, saveViewVerrify, saveViolationVerifyRecord} from "@/api/daily/process/taskTodoViewVerify";
  import BlockCard from '@/components/BlockCard';
  import ScopeSituation from './../scopeSituation/scopeSituationData';//范围情形展示
  import ModifyrecordBtn from '../modifyRecord/btn';
  import FileUpload from './../../components/fileUpload';//附件
  import PersList from './../tree/persList';//tree
  import TaskTodoViewAccept from './../process/taskTodoViewAccept';//tree
  import Process from "@/components/Process/daily";
  import Remind from './../../components/remind';
  import moment from "moment";
  import opinion from '../modifyRecord/opinion';

  export default {
    components: {BlockCard, ScopeSituation, FileUpload, PersList, Process, TaskTodoViewAccept,Remind,ModifyrecordBtn,opinion},
    dicts: ['VIOLD_DAILY_SPEC', 'VIOLD_ADVER_EFFECT_DES','corresponding_adverse_effect'],
    props: {
      isShow:{
        type: String,
        default: '0'
      },
      procInsId:{
        type: String
      },
      problemId: {
        type: String
      }
    },
    filters: {
      timeYear:function (value) {
        if (!value) return '';
        return moment(value).format('YYYY-MM-DD');
      }
    },
    data() {
      return {
        relevantTableId:'',
        relevantTableName:'',
        actualFlag:1,
        edit: false,
        scopeSituation: false,
        flag: false,
        visible: false,
        visibleTree: false,
        formData: {
          verifyDate: undefined,
          problemTitle: null,
          problemDescribe: undefined,
          field107: undefined,
          lossAmount: undefined,
          lossRisk: undefined,
          lossReason: undefined,
          adverseEffects: undefined,
          seriousAdverseEffectsFlag: 1,
          seriousAdverseEffectsDesc: undefined,
          otherNotes: undefined,
          specLists: []
        },
        specList: [],
        rules: {},
        YesOrNo: [{
          "label": "是",
          "value": 1
        }, {
          "label": "否",
          "value": 0
        }],
                whetherEffectOptions: [{
        "label": "是",
        "value": 1
      }, {
        "label": "否",
        "value": 0
      }],
        problemSourceList: [],
        lossTypeOptions:[]
      }
    },
    computed: {},
    watch: {},
    created() {
    },
    mounted() {
      this.QueryVerifyRecord();
    },
    methods: {
      formatDateTime(date) {
        date  = new Date(date.replace(/-/,"/"));
        if(date.getFullYear()){
          var y = date.getFullYear();
          var m = date.getMonth() + 1;
          m = m < 10 ? ('0' + m) : m;
          var d = date.getDate();
          d = d < 10 ? ('0' + d) : d;
          return y + '-' + m + '-' + d;
        }else{
          return date;
        }
      },
      //关闭
      close() {
        this.visible = false;
        this.$emit('close');
      },
      /**初始化数据*/
      QueryVerifyRecord() {
        this.loading = true;
        let array = [];
        queryVerifyRecord(this.problemId).then(
          response => {
            let specSelectedList = response.data.involveProfessionalLines;
            this.formData = {...this.formData, ...response.data};
            for (let i = 0, len = specSelectedList.length; i < len; i++) {
              array.push(specSelectedList[i].specCode);
            }
            this.formData.verifyDate = [
              response.data.verifyStartDate,
              response.data.verifyEndDate
            ];
            this.actualFlag = response.data.actualFlag;
            this.lossTypeOptions = response.data.lossTypeOptions;
            this.formData.specLists = array;
            this.specList = response.data.professionalLineOptions;
            this.relevantTableId = response.data.id;
            this.problemSourceList = response.data.problemSourceList;
            this.relevantTableName = response.data.relevantTableName;
            this.loading = false;
            this.$nextTick(() => {
              this.scopeSituation = true;
              this.$refs.pers.DueryDepartmentSelectInfo();
              this.$refs.file.ViolationFileItems();
            });
            this.$emit('closeLoading');
          }
        );
      },
      /**提交数据*/
      nextStep() {
        this.$emit('handle',1);
      },
      /**保存数据*/
      publicSave() {

      },
      resetForm() {
        this.$refs['elForm'].resetFields()
      },
      //打开弹窗
      show() {
        this.visible = true;
      },
      //关闭弹窗
      closeTree() {
        this.visibleTree = false;
        this.$refs.pers.DueryDepartmentSelectInfo();
      },
      //选择人员
      treeOpen() {
        this.flag = !this.flag;
        this.visibleTree = true;
      }
    }
  }

</script>
<style>
</style>
