<template>
  <div class="iconfont-demo">
    <div class="demo-header">
      <h1>Iconfont 图标库使用演示</h1>
      <p>展示本地iconfont图标的使用方法</p>
    </div>

    <!-- 基础用法 -->
    <el-card class="demo-section">
      <div slot="header">
        <span>基础用法 - CSS类名方式</span>
      </div>
      <div class="icon-grid">
        <div class="icon-item">
          <i class="iconfont icon-shouye1"></i>
          <span>icon-shouye1</span>
        </div>
        <div class="icon-item">
          <i class="iconfont icon-yonghu1"></i>
          <span>icon-yonghu1</span>
        </div>
        <div class="icon-item">
          <i class="iconfont icon-xitongguanli"></i>
          <span>icon-xitongguanli</span>
        </div>
        <div class="icon-item">
          <i class="iconfont icon-chaxun"></i>
          <span>icon-chaxun</span>
        </div>
        <div class="icon-item">
          <i class="iconfont icon-bianji"></i>
          <span>icon-bianji</span>
        </div>
        <div class="icon-item">
          <i class="iconfont icon-shanchu"></i>
          <span>icon-shanchu</span>
        </div>
        <div class="icon-item">
          <i class="iconfont icon-tianjia"></i>
          <span>icon-tianjia</span>
        </div>
        <div class="icon-item">
          <i class="iconfont icon-daochu"></i>
          <span>icon-daochu</span>
        </div>
      </div>
    </el-card>

    <!-- Vue组件用法 -->
    <el-card class="demo-section">
      <div slot="header">
        <span>Vue组件用法</span>
      </div>
      <div class="component-demo">
        <div class="component-item">
          <IconFont icon="shouye1" :size="20" color="#409eff" />
          <span>&lt;IconFont icon="shouye1" :size="20" color="#409eff" /&gt;</span>
        </div>
        <div class="component-item">
          <IconFont icon="yonghu1" :size="24" color="#67c23a" />
          <span>&lt;IconFont icon="yonghu1" :size="24" color="#67c23a" /&gt;</span>
        </div>
        <div class="component-item">
          <IconFont icon="xitongguanli" :size="18" color="#e6a23c" />
          <span>&lt;IconFont icon="xitongguanli" :size="18" color="#e6a23c" /&gt;</span>
        </div>
        <div class="component-item">
          <IconFont icon="shanchu" :size="22" color="#f56c6c" clickable @click="handleIconClick" />
          <span>&lt;IconFont icon="shanchu" :size="22" color="#f56c6c" clickable @click="handleIconClick" /&gt;</span>
        </div>
      </div>
    </el-card>

    <!-- 不同大小 -->
    <el-card class="demo-section">
      <div slot="header">
        <span>不同大小</span>
      </div>
      <div class="size-demo">
        <div class="size-item">
          <i class="iconfont icon-xingxing" style="font-size: 12px;"></i>
          <span>12px</span>
        </div>
        <div class="size-item">
          <i class="iconfont icon-xingxing" style="font-size: 14px;"></i>
          <span>14px</span>
        </div>
        <div class="size-item">
          <i class="iconfont icon-xingxing" style="font-size: 16px;"></i>
          <span>16px (默认)</span>
        </div>
        <div class="size-item">
          <i class="iconfont icon-xingxing" style="font-size: 18px;"></i>
          <span>18px</span>
        </div>
        <div class="size-item">
          <i class="iconfont icon-xingxing" style="font-size: 20px;"></i>
          <span>20px</span>
        </div>
        <div class="size-item">
          <i class="iconfont icon-xingxing" style="font-size: 24px;"></i>
          <span>24px</span>
        </div>
      </div>
    </el-card>

    <!-- 不同颜色 -->
    <el-card class="demo-section">
      <div slot="header">
        <span>不同颜色</span>
      </div>
      <div class="color-demo">
        <div class="color-item">
          <i class="iconfont icon-shoucang" style="font-size: 20px; color: #409eff;"></i>
          <span>主色调 (#409eff)</span>
        </div>
        <div class="color-item">
          <i class="iconfont icon-shoucang" style="font-size: 20px; color: #67c23a;"></i>
          <span>成功色 (#67c23a)</span>
        </div>
        <div class="color-item">
          <i class="iconfont icon-shoucang" style="font-size: 20px; color: #e6a23c;"></i>
          <span>警告色 (#e6a23c)</span>
        </div>
        <div class="color-item">
          <i class="iconfont icon-shoucang" style="font-size: 20px; color: #f56c6c;"></i>
          <span>危险色 (#f56c6c)</span>
        </div>
        <div class="color-item">
          <i class="iconfont icon-shoucang" style="font-size: 20px; color: #909399;"></i>
          <span>信息色 (#909399)</span>
        </div>
      </div>
    </el-card>

    <!-- 按钮中使用 -->
    <el-card class="demo-section">
      <div slot="header">
        <span>按钮中使用</span>
      </div>
      <div class="button-demo">
        <el-button type="primary">
          <i class="iconfont icon-tianjia"></i>
          新增
        </el-button>
        <el-button type="success">
          <i class="iconfont icon-bianji"></i>
          编辑
        </el-button>
        <el-button type="warning">
          <i class="iconfont icon-daochu"></i>
          导出
        </el-button>
        <el-button type="danger">
          <i class="iconfont icon-shanchu"></i>
          删除
        </el-button>
        <el-button type="info">
          <i class="iconfont icon-chaxun"></i>
          查询
        </el-button>
      </div>
    </el-card>

    <!-- 常用业务图标 -->
    <el-card class="demo-section">
      <div slot="header">
        <span>常用业务图标</span>
      </div>
      <div class="business-icons">
        <div class="icon-category">
          <h4>系统管理</h4>
          <div class="icon-list">
            <div class="icon-item">
              <i class="iconfont icon-xitongguanli"></i>
              <span>系统管理</span>
            </div>
            <div class="icon-item">
              <i class="iconfont icon-yonghu1"></i>
              <span>用户管理</span>
            </div>
            <div class="icon-item">
              <i class="iconfont icon-jiaoseguanli"></i>
              <span>角色管理</span>
            </div>
            <div class="icon-item">
              <i class="iconfont icon-zidian"></i>
              <span>字典管理</span>
            </div>
          </div>
        </div>
        
        <div class="icon-category">
          <h4>监督管理</h4>
          <div class="icon-list">
            <div class="icon-item">
              <i class="iconfont icon-jiandujiancha"></i>
              <span>监督检查</span>
            </div>
            <div class="icon-item">
              <i class="iconfont icon-fengxianguanli"></i>
              <span>风险管理</span>
            </div>
            <div class="icon-item">
              <i class="iconfont icon-yujing"></i>
              <span>预警管理</span>
            </div>
            <div class="icon-item">
              <i class="iconfont icon-zhenggaiguanli"></i>
              <span>整改管理</span>
            </div>
          </div>
        </div>
        
        <div class="icon-category">
          <h4>文件操作</h4>
          <div class="icon-list">
            <div class="icon-item">
              <i class="iconfont icon-file-word"></i>
              <span>Word文档</span>
            </div>
            <div class="icon-item">
              <i class="iconfont icon-file-excel"></i>
              <span>Excel表格</span>
            </div>
            <div class="icon-item">
              <i class="iconfont icon-file-pdf"></i>
              <span>PDF文件</span>
            </div>
            <div class="icon-item">
              <i class="iconfont icon-tupian"></i>
              <span>图片文件</span>
            </div>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
export default {
  name: 'IconFontDemo',
  methods: {
    handleIconClick() {
      this.$message.success('图标被点击了！')
    }
  }
}
</script>

<style lang="scss" scoped>
.iconfont-demo {
  padding: 20px;
  
  .demo-header {
    text-align: center;
    margin-bottom: 30px;
    
    h1 {
      color: #303133;
      margin-bottom: 10px;
    }
    
    p {
      color: #606266;
      font-size: 14px;
    }
  }
  
  .demo-section {
    margin-bottom: 20px;
  }
  
  .icon-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 20px;
    
    .icon-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 20px;
      border: 1px solid #ebeef5;
      border-radius: 4px;
      transition: all 0.3s;
      
      &:hover {
        border-color: #409eff;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
      }
      
      i {
        font-size: 24px;
        margin-bottom: 10px;
        color: #606266;
      }
      
      span {
        font-size: 12px;
        color: #909399;
        text-align: center;
      }
    }
  }
  
  .component-demo, .size-demo, .color-demo {
    display: flex;
    align-items: center;
    gap: 30px;
    flex-wrap: wrap;
    
    .component-item, .size-item, .color-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 10px;
      
      span {
        font-size: 12px;
        color: #909399;
        text-align: center;
      }
    }
  }
  
  .button-demo {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
    
    .el-button {
      i {
        margin-right: 5px;
      }
    }
  }
  
  .business-icons {
    .icon-category {
      margin-bottom: 30px;
      
      h4 {
        color: #303133;
        margin-bottom: 15px;
        padding-bottom: 8px;
        border-bottom: 2px solid #409eff;
      }
      
      .icon-list {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
        gap: 15px;
        
        .icon-item {
          display: flex;
          flex-direction: column;
          align-items: center;
          padding: 15px;
          border: 1px solid #ebeef5;
          border-radius: 4px;
          transition: all 0.3s;
          
          &:hover {
            border-color: #409eff;
            background-color: #f0f9ff;
          }
          
          i {
            font-size: 20px;
            margin-bottom: 8px;
            color: #409eff;
          }
          
          span {
            font-size: 12px;
            color: #606266;
          }
        }
      }
    }
  }
}
</style>
