import request from '@/utils/request'

// 日常查询列表
export function violDailyDraftList(query) {
  const pageParams = { pageNum: query.pageNum, pageSize: query.pageSize }
  return request({
    url: '/colligate/violQuery/violDailyDraftList',
    method: 'post',
    data: query,
    params: pageParams
  })
}


// 删除一条日常数据
export function violDailyDraftDelete(query) {
  return request({
    url: '/colligate/violQuery/violDailyDraftDelete',
    method: 'post',
    data: query
  })
}
