import request from '@/utils/request'

// 查询日常问题对应的台账-审计发现问题
export function queryReLedgerInfoByProblemId(data) {
  return request({
    url: '/spr/relationledger/queryReLedgerInfoByProblemId',
    method: 'post',
    data:data
  })
}
//日常问题查询未关联的专项报告台账
export function queryUnRelationLedgerList(data){
  const pageParams = { pageNum: data.pageNum, pageSize: data.pageSize }
  return request({
    url: '/spr/relationledger/queryUnRelationLedgerList',
    method: 'post',
    data:data,
    params:pageParams
  })
}

//日常问题关联专项报告台账
export function dailyRelationLedger(params){
  return request({
    url: '/spr/relationledger/dailyRelationLedger',
    method: 'post',
    data:params
  })
}
//删除关联台账
export function deleteSpLedgerById(params){
  return request({
    url: '/spr/relationledger/deleteSpLedgerById',
    method: 'post',
    data:params
  })
}
//攥取查询台账信息
export function queryRelationLedger(params){
  return request({
    url: '/spr/relationledger/queryRelationLedger',
    method: 'post',
    data:params
  })
}

//查询未关联问题列表
export function queryUnRelationProblemList(params){
  const pageParams = { pageNum: params.pageNum, pageSize: params.pageSize }
  return request({
    url: '/spr/relationledger/queryUnRelationProblemList',
    method: 'post',
    data:params,
    params:pageParams
  })
}


//专项报告台账关联日常问题
export function ledgerRelationDaily(params){
  return request({
    url: '/spr/relationledger/ledgerRelationDaily',
    method: 'post',
    data:params
  })
}

//查询已关联问题列表
export function queryRelationProblemList(params){
  const pageParams = { pageNum: params.pageNum, pageSize: params.pageSize }
  return request({
    url: '/spr/relationledger/queryRelationProblemList',
    method: 'post',
    data:params,
    params:pageParams
  })
}

//删除已关联问题
export function delRelationDaily(params){
  return request({
    url: '/spr/relationledger/delRelationDaily',
    method: 'post',
    data:params
  })
}

//查询问题线索（只返回可以关联台账的数据）
export function queryProblemSource(params){
  return request({
    url: '/spr/relationledger/queryProblemSource',
    method: 'post'
  })
}

