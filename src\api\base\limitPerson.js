import request from '@/utils/request'

// 基础数据查询--禁入限制人员列表查询
export function getBaseInfo(query) {
  return request({
    url: '/colligate/baseInfo/getBaseInfo',
    method: 'post',
    data: query
  })
}


// 基础数据查询-禁入限制人员详情查询
export function queryLimitedPersonInfoById(query) {
  return request({
    url: '/colligate/baseInfo/queryLimitedPersonInfoById',
    method: 'post',
    data: query
  })
}

// 基础数据维护--禁入限制人员列表查询
export function getLimitedPersonPage(query) {
  return request({
    url: '/colligate/baseInfo/getLimitedPersonPage',
    method: 'post',
    data: query
  })
}

// 基础数据维护--禁入限制人员删除
export function delLimitedPersonInfo(query) {
  return request({
    url: '/colligate/baseInfo/delLimitedPersonInfo',
    method: 'post',
    data: query
  })
}

// 基础数据维护--禁入限制人员保存或提交
export function saveLimitPerson(query) {
  return request({
    url: '/colligate/baseInfo/saveLimitPerson',
    method: 'post',
    data: query
  })
}

// 基础数据维护--获取禁入限制人员最新版本
export function getBaseLimitPersonNewVerson(query) {
  return request({
    url: '/colligate/baseInfo/getBaseLimitPersonNewVerson',
    method: 'post',
    data: query
  })
}
