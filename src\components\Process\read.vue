<template>
  <div class="process">
    <div>
      <el-button type="primary" icon="el-icon-tickets" @click="read" v-if="edit" size="mini" plain>已阅</el-button>
      <el-button @click="closeEmit" icon="el-icon-close" size="mini">关闭</el-button>
    </div>
  </div>
</template>
<script>
  import { read } from "@/api/components/process";

  export default {
    inheritAttrs: false,
    components: {},
    props: {
      selectValue: {
        type: Object
      },
      centerVariable:{
        type: Object
      },
      // 1表示待办已办通用，2表示待办，3表示已办，4表示待阅，5表示已阅，6表示我的申请，默认为1
      tabFlag: {
        type: String
      },
      edit: {
        type: Boolean
      }
    },
    data() {
      return {
      }
    },
    computed: {},
    watch: {},
    created() {
    },
    mounted() {},
    methods: {
      onOpen() {},
      closeEmit() {
        this.$emit('close');
      },
      //调用已阅
      read() {
        let dataForm = {readerId:this.selectValue.readerId};
        this.$modal.confirm('【已阅】确认后，当前待阅信息将转至已阅信息，是否确认？').then(function() {
          return read(dataForm)
        }).then(() => {
          this.$modal.msgSuccess("操作成功！");
          setTimeout(() => {
            this.closeEmit();
          },1500);
        }).catch(() => {
        })
      },
    }
  }

</script>
<style scoped lang="scss">
  .process{
    ::v-deep.el-dialog__body{
      padding-top: 16px !important;
      height: auto  !important;
      background: #fff !important;
      padding-bottom:0 !important;
    }
  }
</style>
