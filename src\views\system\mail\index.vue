<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="接收邮箱" prop="mailTos">
        <el-input
          v-model="queryParams.mailTos"
          placeholder="多个用,拼接"
          clearable
          size="small"
          style="width: 240px"
        />
      </el-form-item>
      <el-form-item label="抄送邮箱" prop="mailCcs">
        <el-input
          v-model="queryParams.mailCcs"
          placeholder="多个用,拼接"
          clearable
          size="small"
          style="width: 240px"
        />
      </el-form-item>
      <el-form-item label="邮件标题" prop="mailTitle">
        <el-input
          v-model="queryParams.mailTitle"
          placeholder="多个用,拼接"
          clearable
          size="small"
          style="width: 240px"
        />
      </el-form-item>
      <el-form-item label="邮件内容" prop="mailContent">
        <el-input
          v-model="queryParams.mailContent"
          placeholder="邮件内容"
          clearable
          size="small"
          style="width: 240px"
        />
      </el-form-item>
      <el-form-item>
        <el-button size="small" type="primary" @click="submitFill">确定</el-button>
      </el-form-item>
    </el-form>

  </div>
</template>

<script>
  import { toSendMail} from "@/api/system/mail";

  export default {
    name: "sysMail",
    data() {
      return {
        // 显示搜索条件
        showSearch: true,
        // 查询参数
        queryParams: {
          mailTos: undefined,
          mailCcs:undefined,
          mailTitle:undefined,
          mailContent:undefined
        },

        // 表单参数
        form: {},
      };
    },
    methods: {
      /**  */
      sendMail() {
        toSendMail(this.queryParams).then(res => {
            this.$modal.msgSuccess("发送成功");
        });
      },



      /** 搜索按钮操作 */
      submitFill() {
        if(!this.queryParams.mailTos){
          this.$message.error('接收邮箱为空')
        }
        if(!this.queryParams.mailTitle){
          this.$message.error('邮件标题为空')
        }
        if(!this.queryParams.mailContent){
          this.$message.error('邮件内容为空')
        }
        this.sendMail();
      },



    }
  };
</script>
