import request from '@/utils/request'


export function violDailyList(query) {
  let pageParams = {pageNum: query.pageNum, pageSize: query.pageSize};
  return request({
    url: '/colligate/violQuery/violDailyList',
    method: 'post',
    data: query,
    params: pageParams
  })
}

export function reportSasacRecords(data) {
  return request({
    url: '/colligate/violSasacReportManagement/reportSasacRecords',
    method: 'post',
    data: data,
    params: {pageNum: data.pageNum, pageSize: data.pageSize}
  });
}

export function reportSasacStatisticsData() {
  return request({
    url: '/colligate/violSasacReportManagement/reportSasacStatisticsData',
    method: 'post'
  });
}

export function deleteReportRecord(id) {
  return request({
    url: '/colligate/violSasacReportManagement/deleteReportRecord/' + id,
    method: 'post'
  });
}

export function queryDicListByType() {
  return request({
    url: '/system/dict/data/type/VIOLD_SASAC_REPORT_CONTENT',
    method: 'get'
  });
}

//再次发起，复制旧数据的上报相关信息
export function copySasacReportInfos(reportRecordId){
  return request({
    url: '/colligate/violSasacReportManagement/copySasacReportInfos',
    method: 'post',
    data:JSON.stringify({
      id:reportRecordId
    })
  });
}






