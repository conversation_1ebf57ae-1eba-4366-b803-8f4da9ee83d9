import request from '@/utils/request';

/**
 * 查询实时报送核查处置结果报告
 * @param actualProblemId
 */
export function waitHandleCheckDis(actualProblemId) {
  return request({
    url: '/colligate/violActualCheckDis/waitHandleCheckDis/' + actualProblemId,
    method: 'post'
  });
}

/**
 * 保存查询实时报送核查处置结果报告
 * @param data
 */
export function saveCheckDis(data) {
  return request({
    url: '/colligate/violActualCheckDis/saveCheckDis',
    method: 'post',
    data: data
  });
}

/**
 * 提交查询实时报送核查处置结果报告
 * @param data
 */
export function submitCheckDis(data) {
  return request({
    url: '/colligate/violActualCheckDis/submitCheckDis',
    method: 'post',
    data: data
  });
}

/**
 * 违规追责实时报送核查处置报告与日常报送问题比较
 * @param data
 */
export function checkReportCompareWithDailyProblem(data) {
  return request({
    url: '/colligate/violActualCompareResult/checkReportCompareWithDailyProblem',
    method: 'post',
    data: data
  });
}
