import request from '@/utils/request'

// 实时查询列表
export function violActualDraftList(query) {
  let pageParams = {pageNum: query.pageNum, pageSize: query.pageSize};
  return request({
    url: '/colligate/violQuery/violActualDraftList',
    method: 'post',
    data: query,
    params: pageParams
  })
}
// 查询填报状态字典数据
export function actualStatus() {
  return request({
    url: '/colligate/violQuery/actualStatus',
    method: 'post'
  })
}
//查询实时问题对应的报告
export function openReportList(query) {
    return request({
      url: '/colligate/actualProblem/fill',
      method: 'post',
      data: query
    })
}
export function selectActualFlowInfo(data) {
  return request({
    url: '/colligate/violQuery/selectActualFlowInfo',
    method: 'post',
    data: data
  });
}

export function refreshTurnAssignee() {
  return request({
    url: '/workflowRestController/refreshTurnAssignee',
    method: 'post'
  });
}

export function actualTransfer(data) {
  return request({
    url: '/colligate/violQuery/actualTransfer',
    method: 'post',
    data: data
  });
}

export function getProcessStatus(procInsId) {
  return request({
    url:"/colligate/violActual/getProcessStatus/" + procInsId,
    method: 'post'
  });
}

export function dailyProblemInfo(data) {
  return request({
    url: '/colligate/violActual/dailyProblemInfo',
    method: 'post',
    data: data
  });
}
