<!--适用于页面滚动的流转历史-->
<template>
  <div>
    <div class="float-right">
      <div class="float-popover"  v-show="ispop">
        <div class="float-popover-arrow"></div>
        <div ref="main" class="popover-opinion">
          <el-timeline ref="time">
            <el-timeline-item
              v-for="(activity, index) in activities"
              :key="index"
              color="#f5222d"
            >
              <el-row class="opinion-li">
                <el-col :span="9" class="opinion-label">处理时间</el-col>
                <el-col :span="15" class="opinion-value">{{ activity.senderDateStr }}</el-col>
              </el-row>
              <el-row class="opinion-li">
                <el-col :span="9" class="opinion-label">处理人</el-col>
                <el-col :span="15" class="opinion-value">{{ activity.performerName }}</el-col>
              </el-row>
              <el-row class="opinion-li">
                <el-col :span="9" class="opinion-label">所属环节</el-col>
                <el-col :span="15" class="opinion-value">{{ activity.linkName }}</el-col>
              </el-row>
              <el-row class="opinion-li">
                <el-col :span="9" class="opinion-label">类型</el-col>
                <el-col :span="15" class="opinion-value">{{ activity.handleTypeName }}</el-col>
              </el-row>
              <el-row class="opinion-li">
                <el-col :span="9" class="opinion-label">处理意见</el-col>
                <el-col :span="15" class="opinion-value">{{ activity.comment }}</el-col>
              </el-row>
            </el-timeline-item>
          </el-timeline>
        </div>
      </div>
      <el-button
        v-if="isShow=='1'"
        slot="reference"
        @click="ispop=!ispop"
        class="float-right opinion"
        icon="el-icon-notebook-2"
        size="mini"
      >审批意见
      </el-button>
    </div>
  </div>
</template>

<script>
  import { histoicflow } from '@/api/components/process'
export default {
  name: 'Option',
  props: {
    processInstanceId: {
      type: String
    },
    isShow: {
      type: String,
    }
  },
  data() {
    return {
      isShowOption: false,
      activities:[],
      ispop:false,
    }
  },
  created(){
  },
  mounted(){
    this.Histoicflow();
  },
  methods: {
    /** 主要数据*/
    Histoicflow() {
      console.log('ssss');
      histoicflow(this.processInstanceId).then(
        response => {
          this.activities = response;
        }
      )
    },
    leave() {

    }

  }
}
</script>

<style scoped  lang="scss">
  .opinion{
    position: absolute;
    right: 0;
    top: -50px;
    line-height: 26px;
    padding: 0 10px;
    border-radius: 2px;
    border: 1px solid #f5222d;
    color: #f5222d;
    z-index: 100;
  }
  .float-popover{
    position: absolute;
    width: 340px;
    top: -10px;
    right: 0;
    background: #fff;
    transform-origin: center top;
    z-index: 2007;
    min-width: 150px;
    border: 1px solid #EBEEF5;
    padding: 12px;
    color: #606266;
    line-height: 1.4;
    text-align: justify;
    font-size: 14px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, .1);
    word-break: break-all;
    .float-popover-arrow {
      position: absolute;
      top: -6px;
      left: 90%;
      border: 6px solid #EBEEF5;
      margin-right: 3px;
      border-top-width: 0;
      border-bottom-color: #fff;
      &::before {
        position: absolute;
        top: 1px;
        border: 6px solid #EBEEF5;
        margin-left: -6px;
        border-top-width: 0;
        border-bottom-color: #FFF;
      }
    }
  }
  .popover-opinion{
    max-height: 56vh;
    padding:10px 20px;
    overflow: auto;
    box-sizing: border-box;
    .opinion-li{
      margin-bottom: 12px;
      .opinion-label{
        float:left;
        display: inline-block;
        color: #888D92;
      }
      .opinion-value{
        font-size: 14px;
        font-weight: 400;
        color: #333333;
        display: inline-block;
      }
    }
  }
  .modifyRecord {

   }

</style>
