import request from '@/utils/request'
import { praseStrEmpty } from "@/utils/ruoyi";

//缓存token
export function cacheSession() {
  return request({
    url: '/sys/preview/cacheSession',
    method: 'post'
  })
}

//获取文件类型
export function getFileType(query) {
  return request({
    url: '/sys/preview/getFileType',
    method: 'post',
    params: query
  })
}

//获取预览连接
export function getPreviewInfo(query) {
  return request({
    url: '/sys/preview/getPreviewInfo',
    method: 'post',
    params: query
  })
}


