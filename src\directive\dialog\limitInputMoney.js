
/**
* v-limitInputdollor  只允许输入金额
* Copyright (c) 2019 ruoyi
*/


export default {
    bind (el) {
        el.oninput = () => {
            el.children[0].value = el.children[0].value.replace(/[^\d.]/g,""); //清除"数字"和"."以外的字符
            el.children[0].value = el.children[0].value.replace(/^\./g,""); //验证第一个字符是数字
            el.children[0].value = el.children[0].value.replace(/\.{2,}/g,"."); //只保留第一个, 清除多余的
            el.children[0].value = el.children[0].value.replace(".","$#$").replace(/\./g,"").replace("$#$",".");
            el.children[0].value = el.children[0].value.replace(/^(\-)*(\d+)\.(\d\d).*$/,'$1$2.$3'); //只能输入两个小数
        }
    }
}