<template>
  <div :style="{ height: height, width: width }">
    <div
      :id="id"
      ref="chart"
      :class="className"
      :style="{ height: height, width: width }"
    ></div>
  </div>
</template>
<script>
import echarts from "echarts";
import "echarts/map/js/china";
import "./mapCopy/all-province";
import { fontSizeEchars } from "./mixins/fontSizeEchars";
import resize from "./mixins/resize";

require("echarts/theme/macarons");
export default {
  mixins: [resize],
  props: {
    charsData: {
      type: null,
      default: null,
    },
    id: {
      type: String,
      default: "myChart",
    },
    className: {
      type: String,
      default: "chart",
    },
    width: {
      type: String,
      default: "100%",
    },
    height: {
      type: String,
      default: "100%",
    },
  },
  data() {
    return {
      chart: null,
    };
  },
  watch: {
    charsData: {
      handler(val, oldVal) {
        this.chart.clear();
        setTimeout(() => {
          this.changeData();
        }, 1000);
      },
      deep: true,
    },
  },
  mounted() {
    this.changeData();
  },
  beforeDestroy() {
    if (!this.chart) {
      return;
    }
    this.chart.clear();
    this.chart.dispose();
    this.chart = null;
  },
  methods: {
    changeData() {
      this.initChart(
        this.charsData.data.chinaMapItems,
        this.charsData.bigValue,
        this.charsData.mapType == "全国" ? "china" : this.charsData.mapType
      );
    },

  
    initChart(data, bigValue, mapName) {
      this.chart = echarts.init(this.$refs.chart, "macarons");
      this.chart.setOption(
        {
          tooltip: {
            trigger: "item",
            formatter: function (params) {
              return params.name + "：" + params.value;
            },
          },
          visualMap: {
            show: true,
            min: 0,
            max: bigValue,
            left: "5%",
            top: "bottom",
            itemWidth: 15,
            itemHeight: 120,
            calculable: false,
            seriesIndex: [1],
            inRange: {
              color: ["#F9D4D2", "#FFB4AD", "#FC8586"], // 蓝绿
            },
          },
          selectedMode: "single",

          geo: {
            show: true,
            map: mapName,
            label: {
              normal: {
                show: false,
              },
              emphasis: {
                show: false,
              },
            },
            roam: false,
            itemStyle: {
              normal: {
                areaColor: "#F9D4D2",
                borderColor: "#fff",
              },
              emphasis: {
                areaColor: "#ff4d4e",
              },
            },
          },
          series: [
            {
              name: "散点",
              type: "scatter",
              coordinateSystem: "geo",
            },
            {
              type: "map",
              mapType: "china",
              geoIndex: 0,
               aspectScale: 0.85, //长宽比
              showLegendSymbol: false, // 存在legend时显示
              label: {
                normal: {
                  show: false,
                },
                emphasis: {
                  show: false,
                  textStyle: {
                    color: "#fff",
                  },
                },
              },
              roam: true,
              itemStyle: {
                normal: {
                  areaColor: "#031525",
                  borderColor: "#3B5077",
                },
                emphasis: {
                  areaColor: "#2B91B7",
                },
              },
              animation: true,
              data: data,
            },
          ],
        },
        true
      );
    },
  },
};
</script>
<style lang="scss" scoped>
#myChart {
  width: 100%;
  height: 100%;
  div {
    width: 100%;
    height: 100%;
  }
}
</style>
