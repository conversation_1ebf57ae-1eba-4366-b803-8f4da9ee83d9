<!--定期已完成-查看-->
<template>
  <div>
    <el-dialog  :visible.sync="visible" @open="querySavedHandlerFillData"  append-to-body  title="详情" width="90%">
      <el-form ref="detailForm" :model="formData" :rules="rules" size="medium" label-width="118px">
        <el-row>
          <el-col :span="24">
            <el-tabs v-model="activeName" @tab-click="handleClick" >
              <el-tab-pane label="上报详情" name="0"></el-tab-pane>
              <el-tab-pane label="流转历史" name="1"></el-tab-pane>
            </el-tabs>
          </el-col>
        </el-row>
      </el-form>
      <el-form v-show="sbTab">
      <BlockCard title="上报说明">
        <el-form ref="elForm"  size="medium" label-width="118px">
          <el-row>
            <!--基本信息-->
            <el-col :span="24">
              <ReportBaseInfo
                v-if="baseInfoFlag"
                :key="index"
                :regularReportId="regularReportId"
                ref="baseInfo"
              ></ReportBaseInfo>
            </el-col>
          </el-row>
        </el-form>
      </BlockCard>
      <!--<BlockCard title="定期报告">-->
        <!--<FileUpload-->
          <!--:edit='edit'-->
          <!--:problemId="formData.reportUnitId"-->
          <!--:relevantTableId="formData.reportUnitId"-->
          <!--:relevantTableName="formData.businessTable"-->
          <!--flowType="VIOL_REGULAR"-->
          <!--:problemStatus="formData.regularReportStatus"-->
          <!--:isNoReport = "formData.isNoReport"-->
          <!--:problemsIds = "formData.problemsIds"-->
          <!--ref="file"-->
        <!--&gt;</FileUpload>-->
      <!--</BlockCard>-->
      <BlockCard title="附件列表">
        <RegularTemplateFileUpload :edit='false' :problemId="formData.reportUnitId" :relevantTableId="formData.reportUnitId"
                                   :relevantTableName="formData.businessTable" flowType="VIOL_REGULAR" :problemStatus="formData.regularReportStatus"
                                   :isNoReport="formData.isNoReport" :problemsIds="formData.problemsIds" linkKey="a003" ref="flowTemplate">
        </RegularTemplateFileUpload>
      </BlockCard>
      <BlockCard
        v-if="formData.regularReportStatus !== 'other'&&orgGrade=='G'"
        title="定期报告附表"
      >
        <template v-slot:right>
          <el-button size="mini" type="primary" v-show="check" @click="isCheck()" plain>校验</el-button>
        </template>
        <el-form ref="elForm" size="medium" label-width="118px">
          <el-row class="problem_list margin-b10">
            <el-col :span="24">
              <el-form-item label="是否0报告">
                <span>{{formData.isNoReport == '1' ? '是' : '否'}}</span>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <el-table
          border
          :data="formData.regularProblems"
          @selection-change="handleSelectionChange"
          ref="table"
          height="60vh"
        >
          <el-table-column
            fixed
            align="center"
            label="序号"
            type="index"
            width="50">
          </el-table-column>
          <el-table-column align="center" label="上报单位" show-overflow-tooltip prop="reportUnitName" width="200"/>
          <el-table-column align="center" label="系统编号" show-overflow-tooltip prop="auditCode" width="200"/>
          <el-table-column align="center" label="涉及企业名称" show-overflow-tooltip prop="involveEnterprise" width="200"/>
          <el-table-column align="center" label="企业层级" show-overflow-tooltip prop="enterpriseGrade" width="200"/>

          <el-table-column align="center" label="违规问题线索有关详情" show-overflow-tooltip prop="prov1" width="2000">
            <el-table-column align="center" label="问题线索来源" show-overflow-tooltip prop="problemSource" width="200"/>
            <el-table-column align="center" label="问题受理时间" show-overflow-tooltip prop="acceptTime" width="200"/>
            <el-table-column align="center" label="是否为以前年度定期报告反映的问题" show-overflow-tooltip prop="isPreviousYear" width="200"/>
            <el-table-column align="center" label="问题描述" prop="problemDescribe" show-overflow-tooltip width="200"/>
            <el-table-column align="center" label="问题类别" prop="problemAspect" show-overflow-tooltip width="200"/>
            <el-table-column align="center" label="违反具体规定" prop="problemSituation" show-overflow-tooltip width="200"/>
            <el-table-column align="center" label="境内（外）" prop="domesticOrForeign" show-overflow-tooltip width="200"/>
            <el-table-column align="center" label="涉及损失及风险（万元）" show-overflow-tooltip prop="lossAmountRisk" width="200"/>
            <el-table-column align="center" label="损失风险类别（一般/较大/重大资产损失）" show-overflow-tooltip prop="lossRiskType" width="200"/>
            <el-table-column align="center" label="损失形成主要原因" show-overflow-tooltip prop="lossReason" width="200"/>
          </el-table-column>

          <el-table-column align="center" label="核查情况" show-overflow-tooltip prop="prov2" width="800">
            <el-table-column align="center" label="核查状态" show-overflow-tooltip prop="checkStatus" width="200"/>
            <el-table-column align="center" label="核查时间" show-overflow-tooltip prop="checkTime" width="200"/>
            <el-table-column align="center" label="核查主体" show-overflow-tooltip prop="checkSubject" width="200"/>
            <el-table-column align="left" label="未完成核查原因" show-overflow-tooltip prop="notCheckedReason" width="200"/>
          </el-table-column>
          <el-table-column align="center" label="责任追究工作开展情况" show-overflow-tooltip prop="prov3" width="2400">
            <el-table-column align="center" label="是否追责" show-overflow-tooltip prop="isAccountability" width="200"/>
            <el-table-column align="center" label="未追责原因" show-overflow-tooltip prop="notAccountabilityReason" width="200"/>
            <el-table-column align="center" label="责任追究时间" show-overflow-tooltip prop="accountabilityTime" width="200"/>
            <el-table-column align="center" label="追责总人数" show-overflow-tooltip prop="accountabilityPersonNumber" width="200"/>
            <el-table-column align="center" label="追责总人次" show-overflow-tooltip prop="accountabilityPersonItem" width="200"/>
            <el-table-column align="center" label="责任追究处理方式（人次）" show-overflow-tooltip prop="prov24" width="1550">
              <el-table-column align="center" label="组织处理（人次）" show-overflow-tooltip prop="orgHandleItem" width="200"/>
              <el-table-column align="center" label="扣减薪酬" show-overflow-tooltip prop="a2" width="400">
                <el-table-column align="center" label="人次" show-overflow-tooltip prop="deductionPayItem" width="200"/>
                <el-table-column align="center" label="金额（万元）" show-overflow-tooltip prop="deductionAmount" width="200"/>
              </el-table-column>
              <el-table-column align="center" label="党纪处分（人次）" show-overflow-tooltip prop="partyPunishmentItem" width="200"/>
              <el-table-column align="center" label="政务处分（人次）" show-overflow-tooltip prop="governmentPunishmentItem" width="200"/>
              <el-table-column align="center" label="禁入限制（人次）" show-overflow-tooltip prop="debarPersonItem" width="200"/>
              <el-table-column align="center" label="移送国家检察机关或司法机关（人次）" show-overflow-tooltip prop="transferPersonItem" width="200"/>
              <el-table-column align="center" label="其他（人次）" show-overflow-tooltip prop="processingOtherItem" width="150"/>
            </el-table-column>
          </el-table-column>
          <el-table-column align="center" label="问题整改情况" show-overflow-tooltip prop="prov4" width="1000">
            <el-table-column  align="center" label="是否完成整改" show-overflow-tooltip prop="isCompleteReform" width="200"/>
            <el-table-column align="center" label="完善制度情况" show-overflow-tooltip prop="prov44" width="400">
              <el-table-column align="center" label="数量（项）" show-overflow-tooltip prop="perfectSystemNumber" width="200"/>
              <el-table-column align="center" label="制度名称、文号（无文号请注明出台时间）" show-overflow-tooltip prop="perfectSystemName" width="200"/>
            </el-table-column>
            <el-table-column align="center" label="损失挽回情况" show-overflow-tooltip prop="prov44" width="400">
              <el-table-column align="center" label="金额（万元）" show-overflow-tooltip prop="retrieveLossAmount" width="200"/>
              <el-table-column align="center" label="采取的主要措施" show-overflow-tooltip prop="takeMainStep" width="200"/>
            </el-table-column>
          </el-table-column>
          <el-table-column align="center" label="备注" prop="remark" width="200"/>
        </el-table>
      </BlockCard>
      </el-form>
      <div v-show="lzTab"  style="min-height: calc(100vh - 300px)">
        <div>
          <History
            :key="activities"
            :activities="activities"
          />
        </div>
      </div>
    </el-dialog>
    <el-dialog  :visible.sync="visibleCheck" class="problem_dialog"  append-to-body  title="提示" width="90%">
      <el-row class="problem_list margin-b10">
        <el-col :span="24">
          <span class="problem_dialog_title">以下数据存在定期报告数据小于四季度季度报告数据不一致情况。</span>
        </el-col>
        <el-col :span="8" style="padding:0 10px;">
          <el-table
            border
            :data="checkList1"
            ref="table"
            height="59vh"
          >
            <el-table-column align="center" label="对比数据" show-overflow-tooltip prop="validateName" min-width="40%"/>
            <el-table-column align="center" label="季度报告" show-overflow-tooltip prop="reportVal"  min-width="30%"/>
            <el-table-column align="center" label="定期报告" show-overflow-tooltip prop="regularVal"  min-width="30%"/>
          </el-table>
        </el-col>
        <el-col :span="8" style="padding:0 10px;">
          <el-table
            border
            :data="checkList2"
            ref="table"
            height="59vh"
          >
            <el-table-column align="center" label="对比数据" show-overflow-tooltip prop="validateName" min-width="40%"/>
            <el-table-column align="center" label="季度报告" show-overflow-tooltip prop="reportVal"  min-width="30%"/>
            <el-table-column align="center" label="定期报告" show-overflow-tooltip prop="regularVal"  min-width="30%"/>
          </el-table>
        </el-col>
        <el-col :span="8" style="padding:0 10px;">
          <el-table
            border
            :data="checkList3"
            ref="table"
            height="59vh"
          >
            <el-table-column align="center" label="对比数据" show-overflow-tooltip prop="validateName" min-width="40%"/>
            <el-table-column align="center" label="季度报告" show-overflow-tooltip prop="reportVal"  min-width="30%"/>
            <el-table-column align="center" label="定期报告" show-overflow-tooltip prop="regularVal"  min-width="30%"/>
          </el-table>
        </el-col>
      </el-row>
      <div slot="footer">
        <el-button @click="visibleCheck=false" size="mini">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import BlockCard from '@/components/BlockCard';
  import FileUpload from './../../components/fileUpload';//附件
  import {savedHandlerFillData
  } from '@/api/regular/details/regularDetail';
  import ReportBaseInfo from './regularBaseDetail';//基本数据
  import RegularTemplateFileUpload from '@/views/regular/common/regularTemplateFileUpload';
  import {
    selectReportInfo//加载数据
  } from '@/api/regular/add/addAndEditRegular.js';
  import {validateRegularAndQuarterInfo} from '@/api/regular/flow/taskTodoAreaHandler'
  import History from '@/views/workflow/tasklist/common/history.vue'
  import { histoicflow } from '@/api/components/process'

  export default {
    name: "provHandlerDetail",
    components:{BlockCard,FileUpload,ReportBaseInfo,RegularTemplateFileUpload,History},
    props: {
      reportUnitId: {
        type: String
        ,default:''
      },
      check:{
        type:Boolean,
        default:true
      },
      regularReportId:{
        type:String,
        default:''
      },
      procInsId:{
        type:String,
        default:''
      }
    },
    data(){
      return{
        visibleCheck:false,//校验
        formData:{},
        orgGrade:'',
        edit:false,
        visible:false,
        baseInfoFlag:false,//加载基本信息
        activeName: '0',
        sbTab:true,
        lzTab:false,
        activities: [],
        checkList1:[],
        checkList2:[],
        checkList3:[],
      }
    },
    created(){
    },
    methods:{
      //校验
      isCheck(){

        this.getThreeArray()
      },
      //将数组checkList平均分成三份
      getThreeArray(){
        validateRegularAndQuarterInfo(this.reportUnitId).then(
          response => {
            if(response.code === 200){
              let arr =response.data.dataList;
              if(!response.data.flag){
                if(arr.length){
                  this.visibleCheck = true;
                  let len = arr.length;
                  let chunkSize = Math.ceil(len / 3);
                  let result = [];

                  for (let i = 0; i < len; i += chunkSize) {
                    result.push(arr.slice(i, i + chunkSize));
                  }
                  this.checkList1 = result[0]?result[0]:[]
                  this.checkList2 = result[1]?result[1]:[]
                  this.checkList3 = result[2]?result[2]:[]
                }else if(response.data.msg){
                  this.$alert(response.data.msg, '提示', {
                    confirmButtonText: '确定',
                    callback: action => {
                    }
                  });
                }
              }else{
                if(response.data.msg){
                  this.$alert(response.data.msg, '提示', {
                    confirmButtonText: '确定',
                    callback: action => {
                    }
                  });
                }else{
                  this.$alert('校验通过', '提示', {
                    confirmButtonText: '确定',
                    callback: action => {
                    }
                  });
                }
              }
            }else{
              this.$modal.msgError(response.msg);
            }
          }
        )
      },
      handleClick(obj) {
        this.type = obj.name;
        if(this.type === '0'){
          this.sbTab = true;
          this.lzTab = false;
        }else{
          this.Histoicflow();
          this.sbTab = false;
          this.lzTab = true;
        }
      },
      show(){
        this.visible = true;
      },
      Histoicflow() {
        histoicflow(this.procInsId).then(
          response => {
            this.activities = response;
          }
        )
      },
      //初始数据获取
      querySavedHandlerFillData(){
        selectReportInfo(this.regularReportId).then(
          response => {
            if(response.code === 200){
              this.formData = response.data;
              console.log(this.formData)
              this.formData.reportTime = [];
              this.formData.reportTime[0] = this.formData.reportStartTime;
              this.formData.reportTime[1] = this.formData.reportEndTime;
              this.baseInfoFlag = true;
              this.orgGrade = response.data.orgGrade
              this.$nextTick(()=>{
                this.$refs.baseInfo.showInfo(this.formData);
                //加载定期附表
                savedHandlerFillData(this.reportUnitId).then(
                  response => {
                    if (response.code === 200){
                      this.formData = response.data;
                      this.$nextTick(() => {
                        //加载附件
                        // this.$refs.file.ViolationFileItems();
                        this.$refs.flowTemplate.regularReportTemplateItems();
                      })
                    }else{
                      this.$modal.msgError(response.msg);
                    }
                  }
                )
              })
            }else{
              this.$modal.msgError(response.msg);
            }
          }
        )

      },
    }
  }
</script>

<style scoped lang="scss">
  .position-select {
    position: absolute;
    width: 100%;
    text-align: right;
    top: -54px;
  }
  .el-dialog__body{
    background: #fff !important;
  }
  .problem_dialog {
  ::v-deep.el-dialog__body{
    padding-top:4px;
  }
    .problem_dialog_title {
      display: inline-block;
      padding: 4px 10px;
      margin-left:10px;
      margin-bottom:10px;
      color: #000;
      background-color: #FFF0F0;
    }
  }
</style>
