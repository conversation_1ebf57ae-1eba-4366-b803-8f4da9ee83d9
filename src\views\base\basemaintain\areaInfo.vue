<template>
  <el-container style="border: none;" class="height100 padding_b60">
    <el-main class="height100">
      <div style=" margin-bottom: 20px;">
        <span class="text-blue float-right margin-l10" v-show="dataDetails.commitFlag=='1'">【已提交】</span>
        <span class="text-red float-right margin-l10" v-show="dataDetails.commitFlag=='0'">【待提交】</span>
        <span class="base-warning">
          <i class="el-icon-info"></i> 温馨提示：状态为已提交时，其他人员才能查询到</span>
      </div>
      <el-form ref="dataDetails" :model="dataDetails" label-width="120px" style="padding: 20px 300px; ">
        <el-form-item label="企业名称"
          prop="involOrgName"
          :rules="[
            { required: true, message: '企业名称不能为空'},
            { min: 1, max: 128, message: '长度在 1 到 128 个字符', trigger: 'blur' }
          ]"
        >
          <el-input v-model="dataDetails.involOrgName"></el-input>
        </el-form-item>
        <el-form-item label="企业简称"
          prop="involOrgNameBak"
          :rules="[
            { required: true, message: '企业简称不能为空'},
            { min: 1, max: 32, message: '长度在 1 到 32 个字符', trigger: 'blur' }
          ]"
        >
          <el-input v-model="dataDetails.involOrgNameBak"></el-input>
        </el-form-item>
        <el-form-item label="社会信用代码"
          prop="socialCreditCode"
          :rules="[
            { required: true, message: '社会信用代码不能为空'},
            { min: 1, max: 64, message: '长度在 1 到 64 个字符', trigger: 'blur' }
          ]"
        >
          <el-input v-model="dataDetails.socialCreditCode"></el-input>
        </el-form-item>
        <el-form-item label="所属行业名称"
          prop="industryName"
          :rules="[
            { required: true, message: '所属行业名称不能为空'},
            { min: 1, max: 255, message: '长度在 1 到 255 个字符', trigger: 'blur' }
          ]"
        >
          <el-input v-model="dataDetails.industryName"></el-input>
        </el-form-item>
        <el-form-item label="行业代码"
          prop="industryCode"
          :rules="[
            { required: true, message: '行业代码不能为空'},
            { min: 1, max: 64, message: '长度在 1 到 64 个字符', trigger: 'blur' }
          ]"
        >
          <el-input v-model="dataDetails.industryCode"></el-input>
        </el-form-item>
      </el-form>
    </el-main>
    <FooterBox>
      <div class="float-right">
          <el-button size="mini" @click="saveAreaBaseInfo(0)" :loading="buttonLoading=='save'">保存</el-button>
          <el-button size="mini" type="primary" @click="saveAreaBaseInfo(1)" :loading="buttonLoading=='sub'">提交</el-button>
      </div>
    </FooterBox>
  </el-container>
</template>

<script lang="ts">
  import {getAreaBaseInfoOne, addAreaBaseInfo } from "@/api/base/area";

  export default {
    name: "areaInfo",
    data() {
      return {
        dataDetails: {},
        buttonLoading: null,
      };
    },
    created() {
      this.areaBaseInfo();
    },
    methods: {
      /**查询企业基本信息详情*/
      areaBaseInfo() {
       //this.loading = true;
        getAreaBaseInfoOne().then(
          response => {
            if(response.code == 200 && response.data){
              this.dataDetails = response.data;
            }
            this.buttonLoading = null;
          }
        );
      },
      /**保存或提交*/
      saveAreaBaseInfo(commitFlag){
        this.$refs["dataDetails"].validate((valid) => {
          if(commitFlag==1){
            this.buttonLoading = 'sub';
          }else{
            this.buttonLoading = 'save';
          }
          const params = {
            ...this.dataDetails,
            commitFlag
          };
          addAreaBaseInfo(params).then(
            response => {
              if(response.code === 200){
                this.$message({
                  message: response.msg,
                  type: 'success'
                });
                this.areaBaseInfo();
              }
            }
          ).catch(err=>{
            this.buttonLoading = null;
          });
        })
      }
    }
  };
</script>

<style lang="scss" scoped>
</style>
