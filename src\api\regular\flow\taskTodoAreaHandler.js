import request from '@/utils/request'

//初始数据
export function regularHandlerFillData(reportUnitId){
  return request({
    url: '/colligate/violRegularHandlerFill/regularHandlerFillData/'+reportUnitId,
    method: 'post'
  })
}

//初始数据
export function savedHandlerFillData(reportUnitId){
  return request({
    url: '/colligate/violRegularHandlerFill/savedHandlerFillData/'+reportUnitId,
    method: 'post'
  })
}

//保存表格
export function replacingProvincialData(data) {
  return request({
    url: '/colligate/violRegularHandlerFill/replacingProvincialData',
    method: 'post',
    data: JSON.stringify(data)
  })
}

//查询下级单位相关数据
export function subordinateUnitRelatedData(data) {
  return request({
    url: '/colligate/violRegularHandlerFill/subordinateUnitRelatedData',
    method: 'post',
    data: JSON.stringify(data)
  })
}

//校验跳转新增下级单位或详情页面
export function checkReportTimeForReload(regularReportId) {
  return request({
    url: '/colligate/violRegular/report/checkReportTimeForReload/'+regularReportId,
    method: 'post',
    data: JSON.stringify({
      regularReportId: regularReportId
    })
  })
}

//保存
export function saveHandlerFillData(data) {
  return request({
    url: '/colligate/violRegularHandlerFill/saveHandlerFillData',
    method: 'post',
    data:JSON.stringify(data)
  })
}

//定期填报环节提交增加与季度报告数据校验
export function validateRegularAndQuarterInfo(reportUnitId){
  return request({
    url: '/colligate/violRegularHandlerFill/validateRegularAndQuarterInfo/'+reportUnitId,
    method: 'post'
  })
}
