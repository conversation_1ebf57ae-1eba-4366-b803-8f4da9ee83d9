<template>
  <div class="public-box">
    <div class="public-box-element">
      <div class="public-box-header">
        <div class="float-left">
          <span class="public-box-header-title">{{title}}</span>
          <div class="public-box-header-left">
            <slot name="left"></slot>
          </div>
        </div>
        <div class="public-box-header-right">
          <slot name="right"></slot>
        </div>
      </div>
      <div class="public-box-content" :style="{height:height?height+'px':'auto'}">
        <slot></slot>
      </div>
    </div>
  </div>
</template>

<script>
  export default {
    name: "BlockCard",
    props: {
      title: {
        type: String
      },
      height: {
        type: Number,
        default: 0
      },
    }
  }
</script>

<style rel="stylesheet/scss" lang="scss">
    .public-box{
      padding:4px;
      box-sizing: border-box;
      .public-box-element{
        width: 100%;
        height:100%;
        background: #FFFFFF;
        border-radius: 4px;
        padding:0 20px;
        box-sizing: border-box;
        .public-box-header{
          height: 45px;
          line-height: 45px;
          border-bottom:1px solid #EEEEEE;
          position: relative;
          .public-box-header-title{
            font-size: 16px;
            font-weight: bold;
            color: #333333;
            position: relative;
            padding-left: 12px;
            &::before{
              content: " ";
              position: absolute;
              left: 0;
              right: 0;
              top: 0;
              z-index: 2;
              width: 4px;
              height: 16px;
              background: #F5222D;
              opacity: 1;
            }
          }
          .public-box-header-left{
            display: inline-block;
          }
          .public-box-header-right{
            display: inline-block;
            float: right;
            .iconfont{
              font-size: 18px;
              color: #c20000;
              margin-left: 10px;
              font-weight: 600;
            }
          }
        }
        .public-box-content{
          padding:14px 0;
          box-sizing: border-box;
        }
      }
    }
</style>
