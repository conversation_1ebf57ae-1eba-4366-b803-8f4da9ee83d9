<!--定期报告-->
<template>
  <div>
    <div class="vio-file-box">
        <el-row class="vio-file-div" v-for="item in fieldList">
        <el-col :span="4" class="vio-file-type flex vio-file-border">
          <span class="text-red" v-show="edit&&item.fileTypeCode!=='0'">*</span>
          <span>{{item.fileTypeName}}</span>
        </el-col>
        <el-col :span="edit?4:0" class="vio-file-download flex vio-file-border">
          <div class="mr5">
            <el-button  size="mini" type="primary" v-if="item.fileTemplate&&edit" @click="handleDownload(item.fileTemplate,item.modelFileName)"  title="下载" plain>模板下载</el-button>
          </div>
          <FileUpload
            v-if="edit"
            :isShowTip=showTip
            :fileUrl="uploadUrl"
            btnTitle="上传附件"
            :param="{
                     linkKey:linkKey,
                     fileType: item.fileTypeCode,
                     problemId:problemId,
                     busiTable:relevantTableName,
                     relevantTableId:relevantTableId,
                     relevantTableName:relevantTableName,
                     busiTableId: relevantTableId,
                     flowKey:flowKey
                     }"
            @handleUploadSuccess="handleUploadSuccess"
          >
            文件上传
          </FileUpload>
        </el-col>
        <el-col :span="edit?16:20" class="vio-file-content">
          <ul class="vio-file-list">
            <el-row class="vio-file-li ry-row flex" v-for="obj in item.violationFiles">
              <el-col :span="12"  class="vio-file-name">
                <i class="el-icon-tickets"></i>
                <span>{{obj.fileName}}</span>
              </el-col>
              <el-col :span="2" class="vio-file-user icon-grey">
                <span>{{obj.uploaderName}}</span>
              </el-col>
              <el-col :span="6" class="vio-file-time layui-col-md3 layui-col-sm3 icon-grey">{{obj.createTime}}</el-col>
              <el-col :span="4" class="vio-file-del layui-col-md2 layui-col-sm2 text-center">
                <a href="javascript:void(0);" @click="fileDownload(obj)" class="table-btn tip-edit" title="下载">
                  <i class="el-icon-bottom"></i>
                </a>
                <!--<a href="javascript:void(0);" class="table-btn tip-edit" title="预览" @click="openPreView('6cfe8d7314ba4929adb6cf7eabbcaa70','logo_看图王.png')">-->
                  <!--<i class="el-icon-view"></i>-->
                <!--</a>-->
                <a href="javascript:void(0);" class="table-btn tip-edit" v-if="edit" title="删除" @click="delFile(obj.id)">
                  <i class="el-icon-delete"></i>
                </a>
              </el-col>
            </el-row>
          </ul>
        </el-col>
        </el-row>
    </div>
  </div>
</template>

<script>
  import {violationFileItems,deleteViolFile,generateHandlerFillTemplate} from "@/api/components/index";

  export default {
    name: "regularIndex",
    components: {
    },
    props: {
      edit: {
        type: Boolean,
        default:false
      },
      problemId:{
        type: String,
        default:''
      },
      linkKey:{
        type: String,
        default:''
      },
      relevantTableId:{
        type: String,
        default:''
      },
      relevantTableName:{
        type: String,
        default:''
      },
      flowType:{
        type: String,
        default:''
      },
      problemStatus:{
        type: String
      },
      flowKey:{
        type: String,
        default:''
      },
      isNoReport:{
        type: String,
        default:''
      },
      problemsIds:{
        type:Array,
        default:[]
      }
    },
    data(){
      return{
        type:false,
        status:'',
        showTip:false,
        fieldList:[],
        uploadUrl: '/colligate/violFile/uploadViolFile',
        downloadUrl:'/jtauditwo/files/downLoad/',//下载地址
      }
    },
    methods:{
      /** 获取数据*/
      ViolationFileItems() {
        let query = {
          problemId: this.problemId,
          relevantTableId: this.relevantTableId,
          relevantTableName: this.relevantTableName,
          flowType:this.flowType,
          problemStatus:this.problemStatus
        };
        violationFileItems(query).then(
          response => {
            this.fieldList = response.data;
          }
        );
      },

      /** 删除操作 */
      delFile(id) {
        let  title = '确认删除该附件吗？';
        this.$modal.confirm(title).then(function() {
          return deleteViolFile(id);
        }).then(() => {
          this.$modal.msgSuccess("删除成功");
          this.ViolationFileItems();
        }).catch(() => {});
      },
      // 上传成功回调
      handleUploadSuccess(res, file) {
        // this.$modal.msgSuccess('上传成功');
        this.ViolationFileItems();
      },
      //下载模板
      handleDownload(fileTemplate,modelFileName){
        //定期报告正文模板.docx
        let ajaxData = {
          reportUnitId: this.problemId,
          isNoReport: this.isNoReport,
          problemsIds: this.problemsIds,
          templateCode: fileTemplate,
          businessTable: this.relevantTableName,
          regularReportStatus: this.problemStatus,
          fileName:modelFileName
        };
        generateHandlerFillTemplate(ajaxData).then(
          response => {
            if (response.code === 200) {
              let fileObj = {
                attachmentId : response.msg,
                fileName : modelFileName
              }
              this.fileDownload(fileObj);
            } else {
              this.$modal.msgError(response.msg);
            }
          }
        )
      },
      /**下载文件*/
      fileDownload(obj){
        this.download('/sys/attachment/downloadSysAttachment/'+obj.attachmentId, {
        },obj.fileName)
      },
    }
  }
</script>

<style scoped lang="scss">
  .flex{
    display: flex;
    align-items: center;
  }
  .vio-file-box{
    border: 1px solid #d9d9d9;
    .vio-file-div{
      display: flex;
      width: 100%;
      border-bottom: 1px solid #d9d9d9;
      .vio-file-border{
        border-right: 1px solid #d9d9d9;
      }
      .vio-file-type{
        background-color: #F4F8FC;
        color: #73777a;
        min-height: 48px;
        padding: 0 10px;
        box-sizing: border-box;
        .text-red{
          color: #f5222d !important;
        }
      }
      .vio-file-download{
        justify-content: center;
        .vio-file-down{
          padding: 0 4px;
          border-right: 1px solid #d9d9d9;
        }
        i{
          color: #f5222d;
        }
        .vio-file-down:last-child{
          border-right-width: 0;
        }
      }

      .vio-file-content{
        min-height: 48px;
        .vio-file-list{
          padding:0;
          margin:0;
          .vio-file-li{
            padding-left: 10px;
            box-sizing: border-box;
            border-bottom: 1px solid #d9d9d9;
            min-height: 48px;
            .vio-file-name{
              i{
                margin-right:6px;
              }
            }
            .vio-file-user,.vio-file-time{
              height: 48px;
              display: flex;
              align-items: center;
              color: #a9b0b4;
            }
            .vio-file-del{
              text-align: center;
              i{
                color:#f5222d;
                margin:0 6px;
              }
            }
          }
          .vio-file-li:last-child {
            border-bottom-width: 0;
          }
        }
      }
    }
    .vio-file-div:last-child {
      border-bottom-width: 0;
    }
    ::v-deep.upload-file-uploader{
      margin-bottom: 0;
    }
  }

</style>
