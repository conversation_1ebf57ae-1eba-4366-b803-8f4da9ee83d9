<!--禁入限制人员编辑-->
<template>
  <el-container>
    <el-dialog  :visible.sync="visible" width="90%" append-to-body  @close="close" title="禁入限制人员">
      <div style="height:calc(70vh - 100px)">
      <el-table
        border
        :data="tableList"
        ref="table"
        height="100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column
          fixed
          align="center"
          type="selection"
          width="40">
        </el-table-column>
        <el-table-column
          fixed
          align="center"
          label="序号"
          type="index"
          width="50">
        </el-table-column>
        <el-table-column label="央企集团名称" prop="groupName" width="200"/>
        <el-table-column label="集团简称" prop="groupSortName" width="200"/>
        <el-table-column label="统一社会信用代码" prop="uscCode" width="200"/>
        <el-table-column label="行业名称" prop="industryName" width="200"/>
        <el-table-column label="行业代码" prop="industryCode" width="200"/>
        <el-table-column label="上报时间" prop="reportDate" width="200"/>
        <el-table-column label="禁入人姓名" prop="userName" width="200"/>
        <el-table-column label="禁入编码" prop="uniqueCode" width="200"/>
        <el-table-column label="性别" prop="classifyText" width="200">
          <template slot-scope="scope">
            {{ scope.row.sex==1?'男':'女'}}
          </template>
        </el-table-column>
        <el-table-column label="身份证件号" prop="idCard"  width="200"/>

        <el-table-column label="处理前职务" prop="postName"  width="200"/>
        <el-table-column label="干部类别" prop="cadreCategory"  width="200"/>
        <el-table-column label="现任企业" prop="orgGradeName"  width="200"/>
        <el-table-column label="企业层级" prop="involAreaName"  width="200"/>
        <el-table-column label="工作简历" prop="workResume"  width="200" show-overflow-tooltip/>
        <el-table-column label="违规问题" prop="violationsProblem"  width="200" show-overflow-tooltip/>
        <el-table-column label="不良后果" prop="adverseConsequences"  width="200" show-overflow-tooltip/>
        <el-table-column label="责任认定情况" prop="responIdenty"  width="200" show-overflow-tooltip/>
        <el-table-column label="责任追究处理情况" prop="accountabHandle" width="200" show-overflow-tooltip/>
        <el-table-column label="禁入限制期间开始" prop="limitStartTime" :formatter="dateFormat"  width="200"/>
        <el-table-column label="禁入限制期间结束" prop="limitEndTime" :formatter="dateFormat"  width="200"/>
        <el-table-column label="责任追究处理部门" prop="accountabDepartment"  width="200"/>
        <el-table-column label="责任处理联系人" prop="contactsName"  width="200"/>
        <el-table-column label="责任处理联系方式" prop="contactInformation"  width="200"/>
        <el-table-column label="文件" prop="personPostalCode" align="center" width="100">
          <template slot-scope="scope">
          <span class="text-red cursor underline" @click="fileDialog( scope.row.files)">
            {{ scope.row.files.length?scope.row.files.length:0}}
          </span>
          </template>
        </el-table-column>
        <el-table-column label="备注" prop="remark"  width="200" show-overflow-tooltip/>
        <el-table-column label="操作" prop="del" width="150" fixed="right"
                         align="center"
        >
          <template slot-scope="scope">
            <span v-if="scope.row.del=='2'">新增</span>
            <div v-else>
              <el-select v-model="scope.row.del"
                         :style="{width: '100%'}"
              >
                <el-option label="编辑" key="3" :value="3"></el-option>
                <el-option label="删除" key="1" :value="1"></el-option>
              </el-select>
            </div>
          </template>
        </el-table-column>
      </el-table>
      </div>
      <div slot="footer">
        <el-button size="mini" @click="close()">取消</el-button>
        <el-button size="mini" type="primary" @click="save()">保存</el-button>
      </div>
    </el-dialog>
    <el-dialog  :visible.sync="visibleFile" width="800px"  append-to-body  @close="closeFile" title="附件列表">
      <el-table
        border
        :data="fileList"
        ref="table2"
        height="100%"
      >
        <el-table-column
          fixed
          align="center"
          label="序号"
          type="index"
          width="50">
        </el-table-column>
        <el-table-column label="文档名称" prop="fileName" width="225"/>
        <el-table-column label="上传时间" prop="createTime" width="225" align="center"/>
        <el-table-column label="文档类型" prop="fileDocumentType" width="100"/>
        <el-table-column label="操作" prop="del" width="150" fixed="right"
                         align="center"
        >
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-bottom"
              title="下载"
              @click="downloadFile(scope.row)"
            >
            </el-button>
            <!-- <el-button
              size="mini"
              type="text"
              icon="el-icon-search"
            >预览
            </el-button> -->
          </template>
        </el-table-column>
      </el-table>
      <div slot="footer">
        <el-button size="mini" @click="closeFile()">取消</el-button>
      </div>
    </el-dialog>
  </el-container>
</template>

<script>
  import {getReportLimitPersonAll, saveReportLimitPerson} from "@/api/sasac/reportManagement/edit/detail/index";

  import moment from "moment"
  export default {
    name: "restricted",
    props: {
      problemId: {
        type: String
      }
    },
    data() {
      return {
        visibleFile:false,
        fileList:[],
        hasSelectList:[],//选中的值
        tableList:[],
        visible:false,
        commitFlag: 0,
      };
    },
    created() {
    },
    methods: {
      /**查询禁入限制人员*/
      onShow() {
        this.visible = true;
        //this.loading = true;
        //接口：/colligate/baseInfo/report/getReportLimitPersonAll
        getReportLimitPersonAll({reportId: this.problemId}).then(
          response => {
            this.tableList = response.data;
        let tableChecked = response.data;
        this.hasSelectList = [];
        this.$nextTick(()=> {
          tableChecked.forEach(row => {
            if (row.checked) {
              this.hasSelectList.push(row);
              this.$refs.table.toggleRowSelection(row, true);
            }
          });
        });
        //     this.loading = false;
          }
        );
      },
      //关闭弹框
      close(){
        this.visible = false;
        this.$emit('editClose', this.commitFlag);
        this.commitFlag = 0;
      },
      //弹出附件
      fileDialog(files){
        this.fileList = files;
        this.$nextTick(()=>{
          this.visibleFile=true;
        })
      },
      //关闭附件
      closeFile(){
        this.visibleFile=false;
      },
      //选中的值
      handleSelectionChange(val){
        console.log(val);
        let selectList = [];
        val.forEach(function (item) {
          selectList.push(item)
        });
        this.hasSelectList = selectList;
      },
      /** 下载附件 */
      downloadFile(row) {
        this.download('/sys/documentTemplate/downloadByAttachmentId', { id: row.id }, row.fileName)
      },
      /**保存或提交*/
      save(){
        this.commitFlag = 1;
        const params = {
          reportId:this.problemId,
          limitPersonList:this.hasSelectList
        };
        console.log(params);
        //接口：/colligate/baseInfo/report/saveReportLimitPerson
        saveReportLimitPerson(params).then(
          response => {
            if(response.code === 200){
              this.$modal.msgSuccess('保存成功！');
              this.close();
            }else{
              this.$message.error(response.msg);
            }
          }
        );
      },
      /*日期处理*/
      dateFormat(row,column){
        var date = row[column.property];
        if(date === undefined){
          return ''
        }
        return moment(date).format("YYYY-MM-DD")
      },
    }
  };
</script>

<style>
  .text-red {
    color: #f5222d;
  }

  .icon-orange {
    color: #fa8b16;
  }
</style>
