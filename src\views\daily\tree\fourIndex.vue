<template>
  <el-row class="tree-body">
    <el-col  :span="24">
      <el-form ref="basicInfoForm" >
        <div class="tree-search">
          <el-radio-group v-model="queryType">
            <el-radio label="userName">人员姓名</el-radio>
            <el-radio label="loginName">人员账号</el-radio>
          </el-radio-group>
          <div class="position">
            <el-input v-model="queryVal" placeholder=""  clearable />
            <div class="tree-search-list" v-show="queryShow" id="header_sty">
              <div class="tree-search-li" v-for="(item,index) in queryList" :key="index" :title="item.userOrgName" @click="saveAccuratePersonInfo(item)">{{item.userOrgName}}</div>
              <div class="tree-search-li no-personnel text-center" v-if="queryList.length==0">未找到查询人员</div>
            </div>
          </div>

          <el-button size="small" type="primary" @click="queryAccuratePersonList" icon="el-icon-search" >精准查询</el-button>
        </div>
      </el-form>
    </el-col>
    <el-col :span="12">
      <BlockCard
        title="选择部门"
      >
        <div class="tree-height">
          <DeptTree
            ref="dept"
            :key="deptTreeKey()"
            :Pids="Upids"
            :InvolCompanys="InvolCompanys"
            :problemId="problemId"
            :relevantTableId="relevantTableId"
            :relevantTableName="relevantTableName"
            @newDept="newDept"
          ></DeptTree>
        </div>
      </BlockCard>
    </el-col>
    <el-col :span="12">
      <BlockCard
        title="选择人员"
      >
        <div class="tree-height">
          <PersTree
            ref="presTree"
            :key="persTreeKey()"
            :Dpids="Dpids"
            :defaultTree="defaultTree"
            :problemId="problemId"
            :relevantTableId="relevantTableId"
            :relevantTableName="relevantTableName"
          ></PersTree>
        </div>
      </BlockCard>
    </el-col>
  </el-row>
</template>

<script>
import {userTree,checkInvolve,generateInvolveItemModifyRecord,queryAccuratePersonList,saveAccuratePersonInfo} from "@/api/components/index";

import BlockCard from '@/components/BlockCard';
import UnitTree from './unitTree';
import DeptTree from './Dept2Tree';
import PersTree from './persTree';

export default {
  components: {
    BlockCard,
    UnitTree,
    DeptTree,
    PersTree
  },
  props: {
    problemId:{
      type: String
    },
    relevantTableId:{
      type: String
    },
    relevantTableName:{
      type: String
    },
  },
  data() {
    return {
      Upids:[],
      InvolCompanys:[],
      Dpids:[],
      defaultTree:[],
      index:1,
      index2:1,
      queryShow:false,//搜索框列表显示
      queryType:'userName',//搜索类型
      queryVal:'',//搜索 名字
      queryIndex:0,
      queryList:[],//搜索出的人员列表
    }
  },
  created(){
    window.addEventListener('click', this.handleClickOutside, true)
  },
  methods: {
    deptTreeKey(){
      return  this.index +''+ this.queryIndex
    },
    persTreeKey(){
      return  this.index2 + this.queryIndex
    },
    handleClickOutside (event) {
      const divToHide = document.getElementById('header_sty')
      if (divToHide && !divToHide.contains(event.target)) {
        this.queryShow = false
      }
    },
    //精准查询
    queryAccuratePersonList(){
      queryAccuratePersonList({ queryType:this.queryType,queryVal:this.queryVal}).then(
        response => {
          this.queryList = response.data;
          this.queryShow = true;
        })
    },
    //2、保存精准查询的涉及人员
    saveAccuratePersonInfo(item){
      saveAccuratePersonInfo({postId:item.postId,problemId: this.problemId, relevantTableId: this.relevantTableId,relevantTableName:this.relevantTableName}).then(
        response => {
          this.$modal.msgSuccess("操作成功");
          this.queryShow = false;
          this.queryIndex++;
        })
    },
    // 返回的单位id
    PidsFunction(array1,array2){
      this.InvolCompanys = array1;
      this.Upids=array2;
      this.index++;
      this.$nextTick(()=>{
        this.$refs.dept.Refresh();
      })
    },
    // 返回的部门
    newDept(array){
      this.Dpids=array.selectTree;
      this.defaultTree=array.defaultTree;
      this.index2++;
      this.$nextTick(()=>{
        this.$refs.presTree.refresh();
      })
    },
    //修改记录保存
    GenerateInvolveItemModifyRecord(){
      generateInvolveItemModifyRecord({problemId: this.problemId, businessId: this.relevantTableId}).then(
        response => {})
    },
    //校验
    /**
     * @return {boolean}
     */
    CheckInvolve(){
      let final=true;
      checkInvolve(this.problemId).then(
        response => {
          if (response.code == 200) {
            let companyString = '',//单位列表
              deptString = '';//部门列表
            if (response.data.resultCode == 'false') {
              this.$message.error('请选择单位！');
              final = false;
            } else if (response.data.resultCode == 'company') {
              companyString = '';
              for(let i =0;i<response.data.listCompany.length;i++){
                companyString += '【' + response.data.listCompany[i].INVOL_COMPANY_NAME + '】';
                if (i + 1 != response.data.listCompany.length) {
                  companyString += '、';
                }
              }
              companyString = '' + companyString + '下未选择涉及部门！';
              this.$message.error(companyString);
              final = false;
            } else if (response.data.resultCode == 'double') {
              companyString = '';
              deptString = '';
              for(let i =0;i<response.data.listCompany.length;i++){
                companyString += '【' + response.data.listCompany[i].INVOL_COMPANY_NAME + '】';
                if (i + 1 != response.data.listCompany.length) {
                  companyString += '、';
                }
              }
              for(let i =0;i<response.data.listDept.length;i++){
                deptString += '【' + response.data.listDept[i].INVOL_ORG_NAME + '】';
                if (i + 1 != response.data.listDept.length) {
                  deptString += '、';
                }
              }
              companyString = '1、' + companyString + '下未选择涉及部门！<br/>';
              deptString = '2、' + deptString + '下未选择涉及人员！';
              this.$message.error(companyString + deptString);
              final = false;
            } else if (response.data.resultCode == 'dept') {
              deptString = '';
              for(let i =0;i<response.data.listDept.length;i++){
                deptString += '【' + response.data.listDept[i].INVOL_ORG_NAME + '】';
                if (i + 1 != response.data.listDept.length) {
                  deptString += '、';
                }
              }
              deptString = deptString + '下未选择涉及人员！';
              this.$message.error(deptString);
              final = false;
            } else {
              final = true;
              this.GenerateInvolveItemModifyRecord();
              this.$emit('save');
            }
          }
        }
      );
    }
  }
}
</script>
<style  rel="stylesheet/scss" scoped lang="scss">
.border-top{
  border-top:1px solid #ddd;
}
.is-disabled{
  display: none !important;
}
.height{
  height: 100%;
}
.height-36{
  height: calc(100% - 36px);
}
.tree-body{
  height: 100%;
  .tree-search{
    display: flex;
    justify-content: center;
    align-items: center;
    .el-radio-group{
      display: flex;
      flex:none;
      align-items: center;
    }
    .position{
      margin:0 15px 0 30px;
      position: relative;
      .el-input{
        width: 350px;
      }
      .tree-search-list{
        width: 100%;
        position: absolute;
        z-index: 99;
        border: 1px solid #e4e7ed;
        border-radius: 4px;
        background-color: #fff;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, .1);
        box-sizing: border-box;
        margin: 5px 0;
        overflow: auto;
        max-height:380px;
        list-style: none;
        padding: 6px 0;
        box-sizing: border-box;
        .tree-search-li{
          font-size: 14px;
          padding: 0 20px;
          position: relative;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          color: #606266;
          height: 34px;
          line-height: 34px;
          box-sizing: border-box;
          cursor: pointer;
          &:hover{
            background-color: #f5f7fa;
          }
          &.no-personnel{
            color: rgba(96, 98, 102, 0.4);
          }
        }
      }
    }

  }

  ::v-deep .public-box-content{
    height: 60vh !important;
  }
  .tree-height{
    height: calc(100% - 40px);
    .tree-box{
      height: calc(100% - 100px);
      overflow: auto;
    }
    .tree-bottom{
      height: 140px;
      overflow: auto;
    }
  }
  .flex{
    width: 100%;
    display: flex;
    justify-content: space-between;
    line-height: 42px;
    p{
      span{
        color:#40a9ff;
      }
    }
  }
}
</style>
