<template>
  <el-form ref="form" :model="form" label-width="80px">
    <el-form-item label="涉及部门">
      <el-select v-model="form.involveDeptId" placeholder="请选择涉及部门">
        <el-option v-for="(item, index) in involveDepartments" :key="index" :label="item.involOrgName" :value="item.id"></el-option>
      </el-select>
    </el-form-item>
    <el-form-item label="姓名">
      <el-input v-model="form.userName" :disabled="form.postId"></el-input>
    </el-form-item>

    <el-form-item label="职务">
      <el-input v-model="form.postName"></el-input>
    </el-form-item>
    <el-form-item label="干部类型">
      <el-select v-model="form.cadreType" placeholder="请选择干部类型">
        <el-option v-for="(item, index) in dict.type.viold_cadre_type" :key="index" :label="item.label" :value="item.value"></el-option>
      </el-select>
    </el-form-item>
    <el-form-item>
      <div class="flex">
        <div class="flex-1"></div>
        <el-button size="mini" type="primary" @click="onSubmit">确定</el-button>
        <el-button size="mini" @click="cancel">取消</el-button>
      </div>
    </el-form-item>
  </el-form>
</template>

<script>
import {savePersonCadreType, notPostInvolveDepartments} from '@/api/daily/tree';
export default {
  components: {},
  dicts: ['viold_cadre_type'],
  props: {
    closeBtn: {
      type: Function,
      default: () => {},
    },
    rowData:{
     type: Object,
     default: {}
    }
  },
  data() {
    return {
      involveDepartments: [],
      form: {
        userName: "",
        postName: "",
        cadreType: "",
        involveDeptId: ""
      },
    };
  },
  created(){
    this.form = Object.assign({}, this.rowData);
    this.notPostInvolveDepartments();
  },
  methods: {
    notPostInvolveDepartments() {
      let parameter = {
        problemId: this.rowData.problemId,
        businessId: this.rowData.relevantTableId
      };
      notPostInvolveDepartments(parameter).then(res => {
        this.involveDepartments = res.data;
      });
    },
    onSubmit() {
      if (!this.form.involveDeptId) {
        this.$message.error('请选择涉及部门！');
      } else if (!this.form.userName) {
        this.$message.error('请输入姓名！');
      } else if (!this.form.postName) {
        this.$message.error('请输入职务！');
      } else if (!this.form.cadreType) {
        this.$message.error('请输入干部类型！');
      } else {
        this.$confirm("确定要保存吗?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }).then(() => {
          savePersonCadreType(this.form).then(response => {
            if (200 === response.code) {
              this.$message({
                type: "success",
                message: "保存成功!",
              });
              this.closeBtn();
            }
          })
        }).catch(() => {});
      }
    },
    cancel() {
      this.closeBtn();
    },
  },
};
</script>
<style  rel="stylesheet/scss" scoped lang="scss">
.el-select{
  width: 100%;
  .el-input{
     width: 100%;
  }
}

</style>
