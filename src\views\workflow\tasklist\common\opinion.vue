<template>
  <div class="float-right">
    <el-popover
      ref="content"
      placement="bottom-end"
      width="340"
      trigger="click"
      @show="enter"
      @hide="leave"
    >
      <div ref="main" class="popover-opinion">
        <el-timeline ref="time">
          <el-timeline-item
            v-for="(activity, index) in activities"
            :key="index"
            color="#f5222d"
          >
            <el-row class="opinion-li">
              <el-col :span="9" class="opinion-label">处理时间</el-col>
              <el-col :span="15" class="opinion-value">{{ activity.senderDateStr }}</el-col>
            </el-row>
            <el-row class="opinion-li">
              <el-col :span="9" class="opinion-label">处理人</el-col>
              <el-col :span="15" class="opinion-value">{{ activity.performerName }}</el-col>
            </el-row>
            <el-row class="opinion-li">
              <el-col :span="9" class="opinion-label">所属环节</el-col>
              <el-col :span="15" class="opinion-value">{{ activity.linkName }}</el-col>
            </el-row>
            <el-row class="opinion-li">
              <el-col :span="9" class="opinion-label">类型</el-col>
              <el-col :span="15" class="opinion-value">{{ activity.handleTypeName }}</el-col>
            </el-row>
            <el-row class="opinion-li">
              <el-col :span="9" class="opinion-label">处理意见</el-col>
              <el-col :span="15" class="opinion-value">{{ activity.comment }}</el-col>
            </el-row>
          </el-timeline-item>
        </el-timeline>
      </div>
      <el-button
        slot="reference"
        class="float-right opinion"
        icon="el-icon-notebook-2"
        size="mini"
      >审批意见
      </el-button>
    </el-popover>
  </div>
</template>

<script>
export default {
  name: 'Option',
  props: {
    activities: {
      type: Array
    },
    type: {
      type: String
    }
  },
  data() {
    return {
      isShowOption: false
    }
  },
  created(){
  },
  mounted(){
    if(this.type=='daily')
    this.$refs.content.doShow()
  },
  watch: {

  },
  methods: {
    enter() {
      this.$nextTick(() => {
        this.$refs.main.scrollTop = 150 * this.activities.length
      })
    },
    leave() {

    }

  }
}
</script>

<style scoped  lang="scss">
  .opinion{
    width: 135px;
    height: 36px;
    background: #FFFFFF;
    line-height: 36px;
    padding: 0;
    border: 0;
    border-radius: 0;
    color: #f5222d;
    z-index: 100;
  }
  .popover-opinion{
    max-height: 56vh;
    padding:10px 20px;
    overflow: auto;
    box-sizing: border-box;
    .opinion-li{
      margin-bottom: 12px;
      .opinion-label{
        float:left;
        display: inline-block;
        color: #888D92;
      }
      .opinion-value{
        font-size: 14px;
        font-weight: 400;
        color: #333333;
        display: inline-block;
      }
    }
  }

</style>
