#请根据注释修改，其余内容不修改
apiVersion: apps/v1
kind: Deployment
metadata:
  name: supervisionfilebeat        #[1]必选，Deployment模板名称，可填写服务名称
  namespace: jtaudit-uat
  labels:
    app: supervisionfilebeat       #[2] 必选，标签名，可填写服务名称
spec:
  replicas: 1
  selector:
    matchLabels:
      app: supervisionfilebeat  #[3] 必选，标签名，可填写服务名称
  strategy: {}
  template:
    metadata:
      labels:
        app: supervisionfilebeat     #[4] 必填，pod标签名称，可填写服务名称
    spec:
      containers:
      - image: harbor.dcos.xixian.unicom.local/auditest/filebeat:latest   #[6] 镜像名称，请将“nglogsupervisionfilebeat”替换为服务名，其余不变
        name: supervisionfilebeat        #[5] 必选，容器名称，可填写服务名称
        resources:
          limits:
            memory: 2Gi
        volumeMounts:
        - name: filebeatlog
          mountPath: /var/log/nginx
      volumes:
      - name: filebeatlog
        persistentVolumeClaim:
          claimName: jtauditnglog-pvc
