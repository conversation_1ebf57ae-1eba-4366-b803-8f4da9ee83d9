import request from '@/utils/request';

/**
 * 获取违规追责实时报送后续工作进展数据
 * @param data
 */
export function actualProgressData(data) {
  return request({
    url: '/colligate/violActualProgress/actualProgressData',
    method: 'post',
    data: data
  });
}

/**
 * 保存违规追责实时报送后续工作进展情况报告数据
 * @param data
 */
export function saveProgressReport(data) {
  return request({
    url: '/colligate/violActualProgress/saveProgressReport',
    method: 'post',
    data: data
  });
}

/**
 * 提交违规追责实时报送后续工作进展情况报告
 * @param data
 */
export function submitProgressReport(data) {
  return request({
    url: '/colligate/violActualProgress/submitProgressReport',
    method: 'post',
    data: data
  });
}

/**
 * 违规追责实时报送后续工作报告与日常报送问题比较
 * @param data
 */
export function progressReportCompareWithDailyProblem(data) {
  return request({
    url: '/colligate/violActualCompareResult/progressReportCompareWithDailyProblem',
    method: 'post',
    data: data
  });
}
