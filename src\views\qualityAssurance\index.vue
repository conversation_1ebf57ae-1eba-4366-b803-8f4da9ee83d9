<!--数据质量管理-->
<template>
  <div class="app-container app-report">
    <el-form :model="queryParams" ref="queryForm" id="queryParams" v-show="showSearch" :inline="true" label-width="55px">
      <el-form-item label="年度" prop="auditYear">
        <el-date-picker
          style="width: 100%"
          v-model="queryParams.auditYear"
          type="year"
          value-format="yyyy"
          format="yyyy"
          placeholder="请选择年度"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="季度">
        <el-select
          v-model="queryParams.auditQuarter"
          :style="{width: '100%'}"
          clearable
        >
          <el-option
            v-for="(item, index) in dict.type.REPORT_QUARTER"
            :key="index"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <div class="float-right">
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
      </div>
    </el-form>
    <el-form   style="height: calc(100vh - 320px)">
      <el-table  ref="table" border v-loading="loading" :data="tableList"  height="100%">
        <el-table-column label="序号" type="index" min-width="6%" align="center" >
          <template slot-scope="scope">
            <table-index
              :index="scope.$index"
              :page-num="queryParams.pageNum"
              :page-size="queryParams.pageSize"
            />
          </template>
        </el-table-column>
        <el-table-column label="回头看标题" prop="auditTitle" min-width="32%"  show-overflow-tooltip align="left"/>
        <el-table-column label="年度" prop="auditYear" show-overflow-tooltip align="center"  min-width="10%"/>
        <el-table-column label="季度" prop="auditQuarterName" show-overflow-tooltip align="center"  min-width="10%"/>
        <el-table-column label="不一致省分" v-if="orgGrade=='G'" prop="differentNCount" min-width="15%"  show-overflow-tooltip align="center"/>
        <el-table-column label="完成分析省分" v-if="orgGrade=='G'"  prop="completeCount" min-width="15%"  show-overflow-tooltip align="center"/>
        <el-table-column label="是否不一致" v-if="orgGrade!='G'"  prop="differentFlagName" min-width="15%"  show-overflow-tooltip align="center"/>
        <el-table-column label="生成时间" prop="auditMonth" min-width="10%"  show-overflow-tooltip align="center"/>
        <el-table-column label="操作"  min-width="8%"  align="center" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button
              size="mini"
              v-preventReClick
              type="text"
              title="查看"
              icon="el-icon-search"
              @click="detail(scope.row)"
            ></el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-form>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="queryAuditTableList"
    />
    <!--查看-->
    <el-dialog v-bind="$attrs" :visible.sync="visible" width="90%" title="查看">
      <groupDetail  v-if="orgGrade=='G'&&visible" :key="index" :select-value="rows"/>
      <proDetail v-if="orgGrade !='G'&&visible" :key="index" :select-value="rows"/>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="close">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {queryAuditTableList } from "@/api/qualityAssurance/index";
import proDetail from './detail/proDetail'// 省分详情
import groupDetail from './detail/groupDetail'// 集团详情
export default {
  name: "qualityAssurance",
  components: {
    proDetail,groupDetail
  },
  dicts: ["REPORT_QUARTER"],
  data() {
    return {
      loading:false,
      //详情页遮罩层
      visible:false,
      rows:{},
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 表格数据
      tableList: [],
      //查询 参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        auditYear:'',//年度
        auditQuarter:''//季度
      },
      orgGrade:'',//是否为集团人员  G：集团  p:省分
      index:0
    };
  },
  created() {
    this.orgGrade = this.$store.getters.orgGrade;
    if(this.$store.getters.name=="mansystri"){
      this.orgGrade = "G"
    }
    this.queryAuditTableList();
  },
  activated() {
    this.$nextTick(() => {
      this.$refs.table.doLayout(); //解决表格错位
    });
  },
  filters: {},
  methods: {
    /**数据质量管理列表*/
    queryAuditTableList() {
      this.loading = true;
      queryAuditTableList(this.queryParams).then(
        response => {
          this.tableList = response.rows;
          this.total = response.total;
          this.loading = false;
        }
      );
    },
    /** 搜索按钮操作*/
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.queryAuditTableList();
    },
    /** 查看 */
    detail(row) {
      this.index++
      this.rows = row
      this.visible = true
    },
    //关闭查看
    close(){
      this.visible = false
    },
    /**重置按钮操作*/
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        auditYear:'',
        auditQuarter:''
      };
      this.queryAuditTableList();
    }
  }
};
</script>
<style rel="stylesheet/scss" lang="scss" scoped>

</style>
