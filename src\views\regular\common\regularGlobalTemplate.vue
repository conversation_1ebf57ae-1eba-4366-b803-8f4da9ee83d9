<template>
  <div>
    <el-form-item label="模板列表">
      <el-button v-show="edit" type="primary" icon="el-icon-plus" size="mini" plain @click="show">上传模板</el-button>
      <div class="vio-file-box"  v-if="templateList.length">
        <el-row class="vio-file-div" v-for="(item, index) in templateList" :key="index">
          <el-col :span="4" class="vio-file-type flex vio-file-border">
            <span>{{item.templateName}}</span>
          </el-col>
          <el-col class="vio-file-content" :span="20">
            <ul class="vio-file-list">
              <el-row class="vio-file-li ry-row flex">
                <el-col :span="12"  class="vio-file-name">
                  <i class="el-icon-tickets"></i>
                  <span>{{item.attachmentName}}</span>
                </el-col>
                <el-col :span="2" class="vio-file-user icon-grey">
                  <span>{{item.nickName}}</span>
                </el-col>
                <el-col :span="6" class="vio-file-time layui-col-md3 layui-col-sm3 icon-grey">{{item.createTime}}</el-col>
                <el-col :span="4" class="vio-file-del layui-col-md2 layui-col-sm2 text-center">
                  <a href="javascript:void(0);" @click="fileDownload(item)" class="table-btn tip-edit" title="下载">
                    <i class="el-icon-bottom"></i>
                  </a>
                  <a href="javascript:void(0);" class="table-btn tip-edit" v-if="edit" title="删除" @click="delFile(item.id)">
                    <i class="el-icon-delete"></i>
                  </a>
                </el-col>
              </el-row>
            </ul>
          </el-col>
        </el-row>
      </div>
      <div class="no-list" v-else>
        无模板
      </div>
    </el-form-item>
    <el-dialog :visible.sync="visible" @close="onClose" append-to-body :title="title" width="50%">
      <el-form ref="templateForm" :model="rowData" size="medium" label-width="118px" style="height: calc(80vh - 200px)">
        <el-col :span="12">
          <el-form-item label="模板名称" prop="templateName">
            <el-input v-model="rowData.templateName" placeholder="模板名称" clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item>
            <FileUpload :isShowTip="false" :fileUrl="uploadUrl" btnTitle="上传附件" :key="rowData.templateName"
                        :param="{id: rowData.id, regularReportId: regularReportId, templateName: rowData.templateName}"
                        @handleUploadSuccess="handleUploadSuccess">
            </FileUpload>
          </el-form-item>
        </el-col>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
  import {regularGlobalTemplates, deleteRegularGlobalTemplate} from "@/api/regular/common/regularGlobalTemplate";
  export default {
      name: "regularGlobalTemplate",
      props: {
        regularReportId: {
          type: String
        },
        edit:{
          type: Boolean
        }
      },
      data() {
        return {
          templateList: [],
          loading:false,
          visible: false,
          title: "",
          rowData: {id: "", templateName: ""},
          uploadUrl: "/colligate/violRegularGlobalTemplate/replaceRegularReportTemplate"
        }
      },
      created() {
        this.regularGlobalTemplates();
      },
      methods: {
        regularGlobalTemplates() {
          if (this.regularReportId) {
            this.loading = true;
            regularGlobalTemplates(this.regularReportId).then(response => {
              this.templateList = response.data;
              this.loading = false;
            });
          }
        },

        delFile(id) {
          let  title = '确认删除该模板吗？';
          this.$modal.confirm(title).then(function() {
            return deleteRegularGlobalTemplate(id);
          }).then(() => {
            this.$modal.msgSuccess("删除成功");
            this.regularGlobalTemplates();
          }).catch(() => {});
        },

        fileDownload(obj){
          this.download('/sys/attachment/downloadSysAttachment/'+obj.templateId, {
          },obj.attachmentName);
        },

        handleUploadSuccess(res, file) {
          this.regularGlobalTemplates();
          this.visible = false;
        },

        show() {
          this.visible = true;
        },

        onClose(){
          this.rowData = {id: "", templateName: ""};
          this.visible = false;
        }
      }
    }
</script>

<style scoped lang="scss">
  .flex{
    display: flex;
    align-items: center;
  }
  .no-list{
    border: 1px solid #d9d9d9;
    text-align: center;
    line-height: 40px;
    color:#666;
  }
  .vio-file-box{
    border: 1px solid #d9d9d9;
    .vio-file-div{
      display: flex;
      width: 100%;
      border-bottom: 1px solid #d9d9d9;
      .vio-file-border{
        border-right: 1px solid #d9d9d9;
      }
      .vio-file-type{
        background-color: #F4F8FC;
        color: #73777a;
        min-height: 48px;
        padding: 0 10px;
        box-sizing: border-box;
        .text-red{
          color: #f5222d !important;
        }
      }
      .vio-file-download{
        justify-content: center;
        .vio-file-down{
          padding: 0 4px;
          border-right: 1px solid #d9d9d9;
        }
        i{
          color: #f5222d;
        }
        .vio-file-down:last-child{
          border-right-width: 0;
        }
      }

      .vio-file-content{
        min-height: 48px;
        .vio-file-list{
          padding:0;
          margin:0;
          .vio-file-li{
            padding-left: 10px;
            box-sizing: border-box;
            border-bottom: 1px solid #d9d9d9;
            min-height: 48px;
            .vio-file-name{
              i{
                margin-right:6px;
              }
            }
            .vio-file-user,.vio-file-time{
              height: 48px;
              display: flex;
              align-items: center;
              color: #a9b0b4;
            }
            .vio-file-del{
              text-align: center;
              i{
                color:#f5222d;
                margin:0 6px;
              }
            }
          }
          .vio-file-li:last-child {
            border-bottom-width: 0;
          }
        }
      }
    }
    .vio-file-div:last-child {
      border-bottom-width: 0;
    }
    ::v-deep.upload-file-uploader{
      margin-bottom: 0;
    }
  }
</style>
