<template>
  <div class="app-container">
    <!-- 模板配置主页面-->
    <el-form v-show="showSearch" ref="queryForm" :model="queryParams" :inline="true" label-width="68px">
      <el-form-item label="文档编码" prop="docCode">
        <el-input
          v-model="queryParams.docCode"
          placeholder="请输入文档编码"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="文档编号" prop="docNumber">
        <el-input
          v-model="queryParams.docNumber"
          placeholder="请输入文档编号"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="功能名称" prop="functionModel">
        <el-input
          v-model="queryParams.functionModel"
          placeholder="请输入功能名称"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="业务主表" prop="businessTable">
        <el-input
          v-model="queryParams.businessTable"
          placeholder="请输入业务主表"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="创建者" prop="createUserName">
        <el-input
          v-model="queryParams.createUserName"
          placeholder="请输入创建者"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable size="small">
          <el-option
            v-for="dict in dict.type.sys_normal_disable"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:main:add']"
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
        >新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:main:remove']"
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
        >删除
        </el-button>
      </el-col>
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>

    <el-table v-loading="loading" :data="mainList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="文档编码" align="center" prop="docCode" />
      <el-table-column label="文档编号" align="center" prop="docNumber" />
      <el-table-column label="功能名称" align="center" prop="functionModel" />
      <el-table-column label="功能描述" align="center" prop="functionDesc" />
      <el-table-column label="业务主表" align="center" prop="businessTable" />
      <el-table-column label="状态" align="center" width="55" prop="status">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.sys_normal_disable" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column label="创建者" align="center" prop="createUserName" />
      <el-table-column label="修改时间" sortable align="center" prop="updateTime" width="180" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            v-hasPermi="['system:main:edit']"
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
          >修改
          </el-button>
          <el-button
            v-hasPermi="['system:main:remove']"
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
          >删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParam.pageNum"
      :limit.sync="queryParam.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改模板配置主对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="1000px" append-to-body>
      <el-row class="el-dialog-div">
        <el-form ref="form" :model="form" :rules="rules" label-width="80px">

          <el-col :span="12">
            <el-form-item label="功能名称" prop="functionModel">
              <el-input v-model="form.functionModel" placeholder="请输入功能名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="功能描述" prop="functionDesc">
              <el-input v-model="form.functionDesc" placeholder="请输入功能描述" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="业务主表" prop="businessTable">
              <el-input v-model="form.businessTable" placeholder="请输入业务主表" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="业务标识" prop="businessCode">
              <el-input v-model="form.businessCode" placeholder="请输入业务标识" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="备注" prop="remarks">
              <el-input v-model="form.remarks" type="textarea" placeholder="请输入内容" />
            </el-form-item>
          </el-col>
        </el-form>
      </el-row>
      <el-row class="el-dialog-div">
        <el-col :span="1.5">
          <FileUpload
            :is-show-tip="isShowTip"
            :file-url="uploadUrl"
            btn-title="上传附件"
            :param="form"
            @handleUploadSuccess="handleUploadSuccess"
          />
        </el-col>
        <el-table v-loading="loading" :data="fileList" height="100%">
          <el-table-column label="版本号" prop="docVersion" width="100" align="center" />
          <el-table-column label="文档名称" prop="docName" width="300" align="center" />
          <el-table-column label="状态" prop="status" width="100" align="center">
            <template slot-scope="scope">
              <dict-tag :options="dict.type.sys_normal_disable" :value="scope.row.status" />
            </template>
          </el-table-column>
          <el-table-column label="文档大小" prop="docSize" width="100" align="center" />
          <el-table-column label="创建人" prop="userName" width="100" align="center" />
          <el-table-column label="创建时间" prop="createTime" width="150" align="center">
            <template slot-scope="scope">
              <span>{{ scope.row.createTime }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" fixed="right" align="center" class-name="small-padding fixed-width" width="120">
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                title="下载"
                icon="el-icon-download"
                @click="downloadFile(scope.row)"
              >
              </el-button>

              <el-button
                size="mini"
                type="text"
                title="预览"
                icon="el-icon-search"
                @click="handelView(scope.row)"
              >
              </el-button>
              <el-button
                size="mini"
                type="text"
                title="删除"
                icon="el-icon-delete"
                @click="handleDeleteFile(scope.row)"
              >
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="editTotal>0"
          :total="editTotal"
          :page.sync="queryParam.pageNum"
          :limit.sync="queryParam.pageSize"
          @pagination="getFileList"
        />
      </el-row>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">保 存</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <pdfView  v-if="fileData.Isview" :data="fileData" @close="closePdf"/>
  </div>
</template>

<script>
import {
  dropFilesById,
  dropTemplateById,
  queryFileList,
  queryMainTemplateById,
  queryMainTemplateList,
  updateTemplateById
} from '@/api/system/docment/docment.js'
import pdfView from '@/components/pdfView/index'
export default {
  name: 'Main',
  dicts: ['sys_normal_disable'],
  components: {
    pdfView
  },
  data() {
    return {
      // 是否展示提示
      isShowTip: false,
      // 遮罩层
      loading: true,
      // 编辑功能
      isEdit: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 编辑总条数
      editTotal: 0,
      // 模板配置主表格数据
      mainList: [],
      // 模板配置弹框附件表格数据
      fileList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParam: {
        pageNum: 1,
        pageSize: 10
      },
      mainId: null,
      // 查询参数
      queryParams: {
        docCode: null,
        docNumber: null,
        functionModel: null,
        functionDesc: null,
        businessTable: null,
        businessCode: null,
        status: null,
        createUserName: null
      },
      uploadUrl: '/sys/documentTemplateFile/uploadAndSaveFile',
      // 表单参数
      form: {
        id: null,
        functionModel: null,
        functionDesc: null,
        businessTable: null,
        businessCode: null,
        remarks: ''
      },
      // 表单校验
      rules: {
        id: [
          { required: true, message: '主键不能为空', trigger: 'blur' }
        ],
        docCode: [
          { required: true, message: '业务标识不能为空', trigger: 'blur' }
        ],
        functionModel: [
          { required: true, message: '功能名称不能为空', trigger: 'blur' }
        ]
      },
      isUpLoad: false,
      upLoadSecc: false,


      // 预览参数
      fileData:{
        id: '',
        waterMark: '仅限国内使用',
        fileName: '',
        Isview: false
      },
    }
  },
  created() {
    this.getList()
  },
  methods: {
    handelView(row) {
      this.fileData.id = row.attachmentId
      this.fileData.fileName = row.docName
      this.$nextTick(()=>{
        this.fileData.Isview = true
      })
    },
    //关闭预览
    closePdf(){
      this.fileData.Isview = false
    },
    /** 查询模板配置主列表 */
    getList() {
      this.loading = true
      queryMainTemplateList(this.queryParam, this.queryParams).then(response => {
        this.mainList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParam.pageNum = 1
      this.getList()
    },
    reset() {
      this.resetForm('queryForm')
      this.form = {
        id: null,
        functionModel: null,
        functionDesc: null,
        businessTable: null,
        businessCode: null,
        remarks: null
      }
      this.fileList = null
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.reset()
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },

    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.isEdit = true
      this.open = true
      this.title = '添加模板'
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      this.isEdit = true
      const id = row.id
      this.mainId = row.id
      this.form.id = row.id
      queryMainTemplateById(id).then(response => {
        this.form = response.data
        this.form.params = null
        this.mainId = this.form.id
        this.getFileList()
        this.open = true
        this.title = '修改模板'
      })
    },

    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },

    /** 查询模板配置主列表 */
    getFileList() {
      this.loading = true
      queryFileList(this.queryParam, { mainId: this.mainId }).then(response => {
        this.fileList = response.rows
        this.editTotal = response.total
        this.loading = false
      })
    },
    /** 提交按钮 */
    submitForm() {
      if (this.isUpLoad) {
        if (this.upLoadSecc == true) {
          this.$modal.msgSuccess('保存成功！')
          this.open = false
          this.getList()
        } else {
          this.$modal.msgError('保存失败！')
          this.open = true
          this.getList()
        }
      } else {
        this.$refs['form'].validate(valid => {
          if (valid) {
            if (this.form.id != null) {
              updateTemplateById(this.form).then(response => {
                this.$modal.msgSuccess('修改成功')
                this.open = false
                this.getList()
              })
            } else {
              updateTemplateById(this.form).then(response => {
                this.$modal.msgSuccess('新增成功')
                this.open = false
                this.getList()
              })
            }
          }
        })
      }
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const docNumber = row.docNumber
      row.deletedFlag = '1'
      this.$modal.confirm('是否确认删除文档编号为"' + docNumber + '"的数据项？').then(function() {
        return dropTemplateById(row)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess('删除成功')
      }).catch(() => {
      })
    },
    /** 下载附件 */
    downloadFile(row) {
      this.download('/sys/documentTemplate/downloadByAttachmentId', { id: row.attachmentId }, row.docName)
    },
    /** 删除附件按钮操作 */
    handleDeleteFile(row) {
      const id = row.id
      this.$modal.confirm('是否确认删除？').then(function() {
        return dropFilesById({ id: id })
      }).then(() => {
        this.getFileList()
        this.$modal.msgSuccess('删除成功')
      }).catch(() => {
      })
    },

    // 上传失败
    handleUploadError(err) {
      this.isUpLoad = true
      this.upLoadSecc = false
      this.$message.error('上传失败, 请重试')
    },
    // 上传成功回调
    handleUploadSuccess(res, file) {
      this.isUpLoad = true
      this.upLoadSecc = true
      this.getFileList()
      this.$modal.msgSuccess('上传成功')
    }

  }
}
</script>
