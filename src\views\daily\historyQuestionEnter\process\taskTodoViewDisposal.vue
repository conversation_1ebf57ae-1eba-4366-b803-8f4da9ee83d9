<!--3：分类处置-经办-->
<template>
  <div>
    <div>
      <el-form ref="elForm" :model="formData" :rules="rules" size="medium" label-width="180px">
        <BlockCard
          title="基本信息"
        >
          <el-row>
            <el-col :span="6">
              <el-form-item label="系统编号" prop="findTime">
                <span>{{ formData.auditCode }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="问题编号" prop="problemCode">
                <span>{{ formData.problemCode }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="违规事项" prop="problemTitle">
                <span>{{ formData.problemTitle }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="问题线索描述" prop="problemDescribe">
                <el-input
                  v-model="formData.problemDescribe"
                  :readonly="!edit"
                  type="textarea"
                  placeholder="请输入问题线索描述"
                  :autosize="{minRows: 4, maxRows: 4}"
                  :style="{width: '100%'}"
                />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="涉及专业线" prop="specLists">
                <el-checkbox-group :key="formData.specLists" v-model="formData.specLists" size="medium">
                  <el-checkbox
                    v-for="(item, index) in specList"
                    :key="item.dictValue"
                    border
                    :label="item.dictValue"
                  >{{ item.dictLabel }}
                  </el-checkbox>
                </el-checkbox-group>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="涉及单位/部门/人员" prop="field107">
                <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="treeOpen">添加部门人员</el-button>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item>
                <PersList
                  ref="pers"
                  :edit="edit"
                  :problem-id="problemId"
                  :relevant-table-id="relevantTableId"
                  :relevant-table-name="relevantTableName"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </BlockCard>
        <Remind
          :key="actualFlag"
          :actual-flag="actualFlag"
        />
        <BlockCard
          title="分类处置信息"
        >
          <el-row>

            <el-col :span="8">
              <el-form-item label="是否产生资产损失" prop="lossStateAssetsFlag">
                <el-radio-group v-model="formData.lossStateAssetsFlag" size="medium" @change="lossStateAssetsChanged">
                  <el-radio
                    v-for="(item, index) in YesOrNo"
                    :key="index"
                    :label="item.value"
                  >{{ item.label }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>

              <el-col :span="8">
              <el-form-item label="预估损失金额（万元）" prop="lossAmount">
                <el-input-number
                  v-model="formData.lossAmount"
                  :min="0"
                  :precision="2"
                  placeholder="预估损失金额（万元）"
                  controls-position="right"
                  :disabled="!formData.lossStateAssetsFlag"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="预估损失风险（万元）" prop="lossRisk">
                <el-input-number
                  v-model="formData.lossRisk"
                  :min="0"
                  :precision="2"
                  placeholder="预估损失风险（万元）"
                  controls-position="right"
                />
              </el-form-item>
            </el-col>

             <el-col :span="8">
              <el-form-item label="损失风险类别" prop="lossRiskType">
                <el-select
                  v-model="formData.lossRiskType"
                  placeholder="请选择损失风险类别"
                  clearable
                  :style="{width: '100%'}"
                  value="formData.lossType"
                >
                  <el-option
                    v-for="(item, index) in lossRiskTypeOptions"
                    :key="index"
                    :label="item.dictLabel"
                    :value="item.dictValue"
                  >{{ item.dictLabel }}
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="24" />
            <el-col :span="24">
              <el-form-item label="损失形成主要原因" prop="lossReason">
                <el-input
                  v-model="formData.lossReason"
                  :style="{width: '100%'}"
                  :autosize="{minRows: 4, maxRows: 4}"
                  type="textarea"
                  clearable
                />
              </el-form-item>
            </el-col>
             <el-col :span="8">
              <el-form-item label="是否产生不良影响" prop="isAdverseEffect">
                 <el-radio-group v-model="formData.isAdverseEffect" size="medium" @change="radioEffectChanged">
                    <el-radio v-for="(item, index) in whetherEffectOptions" :key="index" :label="item.value"
                              :disabled="item.disabled">{{item.label}}</el-radio>
                  </el-radio-group>
              </el-form-item>
            </el-col>
             <el-col :span="16">
                <el-form-item label="对应不良影响" prop="correspondingAdverseEffects" v-if="formData.isAdverseEffect">
                  <el-select v-model="formData.correspondingAdverseEffects" :style="{width: '100%'}" clearable="clearable" multiple="multiple" value="">
                    <el-option v-for="(item, index) in dict.type.corresponding_adverse_effect" :key="index" :label="item.label" :value="item.value"></el-option>
                  </el-select>
                </el-form-item>
            </el-col>
            <el-col :span="24" />
            <el-col :span="24">
              <el-form-item label="造成的不良影响" prop="adverseEffects" v-if="formData.isAdverseEffect">
                <el-input
                  v-model="formData.adverseEffects"
                  :style="{width: '100%'}"
                    :autosize="{minRows: 4, maxRows: 4}"
                    type="textarea"
                  clearable
                />
              </el-form-item>
            </el-col>

              <el-col :span="8">
              <el-form-item label="是否产生严重不良影响" prop="seriousAdverseEffectsFlag" v-show="formData.isAdverseEffect">
                <el-radio-group v-model="formData.seriousAdverseEffectsFlag" size="medium" @change="seriousAdverseEffectsFlagChange">
                  <el-radio
                    v-for="(item, index) in YesOrNo"
                    :key="index"
                    :label="item.value"
                    :disabled="item.disabled"
                  >{{ item.label }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col v-show="formData.seriousAdverseEffectsFlag" :span="16">
              <el-form-item label="严重不良影响描述" prop="seriousAdverseEffectsDesc">
                <el-select
                  v-model="formData.seriousAdverseEffectsDesc"
                  placeholder="请选择严重不良影响描述"
                  clearable
                  :style="{width: '100%'}"
                  value="formData.seriousAdverseEffectsDesc"
                >
                  <el-option
                    v-for="(item, index) in dict.type.VIOLD_ADVER_EFFECT_DES"
                    :key="index"
                    :label="item.label"
                    :value="item.value"
                    :disabled="item.disabled"
                  />
                </el-select>
              </el-form-item>
            </el-col>
             <el-col :span="24" />

            <el-col :span="8">
              <el-form-item label="是否上报上级单位" prop="isReportSuperiorUnit">
                <el-radio-group v-model="formData.isReportSuperiorUnit" size="medium">
                  <el-radio
                    v-for="(item, index) in YesOrNo"
                    :key="index"
                    :label="item.value"
                  >{{ item.label }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>

             <el-col :span="8">
              <el-form-item label="涉及集团总部" prop="isInvolveGroup">
                <el-radio-group v-model="formData.isInvolveGroup" size="medium">
                  <el-radio
                    v-for="(item, index) in YesOrNo"
                    :key="index"
                    :label="item.value"
                  >{{ item.label }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>

             <el-col :span="8">
              <el-form-item label="涉及所属二级单位领导人员" prop="isInvolveSecondUnitLeader">
                <el-radio-group v-model="formData.isInvolveSecondUnitLeader" size="medium">
                  <el-radio
                    v-for="(item, index) in YesOrNo"
                    :key="index"
                    :label="item.value"
                  >{{ item.label }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>



            <el-col :span="8">
              <el-form-item label="由审计部核查" prop="isAuditDeptCheck">
                <el-radio-group v-model="formData.isAuditDeptCheck" size="medium">
                  <el-radio
                    v-for="(item, index) in YesOrNo"
                    :key="index"
                    :label="item.value"
                  >{{ item.label }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>




            <el-col :span="8">
              <el-form-item label="需要重点督办" prop="isEmphasizeSupervise">
                <el-radio-group v-model="formData.isEmphasizeSupervise" size="medium">
                  <el-radio
                    v-for="(item, index) in YesOrNo"
                    :key="index"
                    :label="item.value"
                  >{{ item.label }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>

            <el-col :span="8">
              <el-form-item label="移送国家监察或司法机关" prop="isHandoverNationalJudiciary">
                <el-radio-group v-model="formData.isHandoverNationalJudiciary" size="medium">
                  <el-radio
                    v-for="(item, index) in YesOrNo"
                    :key="index"
                    :label="item.value"
                  >{{ item.label }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col v-show="formData.isShowHandoverDiv" :span="24">
              <el-form-item :label="formData.labelName" prop="isHandoverSecondUnitHandle">
                <el-radio-group v-model="formData.isHandoverSecondUnitHandle" size="medium" @change="changeHandler">
                  <el-radio
                    v-for="(item, index) in YesOrNo"
                    :key="index"
                    :label="item.value"
                  >{{ item.label }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>

            <el-col v-show="formData.isShowHandoverDiv" :span="24">
              <el-form-item>
                <div class="layui-form-item layui-form-item-sm">
                  <el-col class="layui-input-block">
                    <el-col class="transfer-box">
                      <el-row v-for="(item,index) in formData.secondaryHandoverRecords" class="transfer-li ry-row">
                        <div v-if="formData.isHandoverSecondUnitHandle||!item.editEnable">
                          <el-col :span="8" class="transfer-li-info">
                            <el-row>
                              <el-col :span="16" class="ovflowHidden text-left"><span>{{ item.fromUnitName }}</span></el-col>
                              <el-col :span="8" class="text-right"><span>{{ item.fromUserName }}</span></el-col>
                            </el-row>
                          </el-col>
                          <el-col :span="2" class="transfer-li-img">——</el-col>
                          <el-col :span="8" class="transfer-li-info ry-row cursor editSecondary">
                            <el-row>
                              <el-col :span="16" class="ovflowHidden text-left">
                                <span class="toUnitName" @click="item.editEnable==1&&AstaffOrgTree(2,item.toUserName)">{{ item.toUnitName||'请选择被移送单位及人员信息' }}</span>
                              </el-col>
                              <el-col :span="8" class="text-right">
                                <span class="toUserName" @click="item.editEnable==1&&AstaffOrgTree(2,item.toUserName)">{{ item.toUserName }}</span>
                              </el-col>
                            </el-row>
                          </el-col>
                          <el-col :span="6" class="transfer-li-edit text-right">
                            <span
                              v-show="item.downloadLink"
                              style="padding-left: 20px;"
                              class="table-btn tip-edit text-red float-right ovflowHidden cursor"
                              :title="item.fileName"
                              @click="downloadLink(item.downloadLink,item.fileName)"
                            >{{ item.fileName }}</span>
                          </el-col>
                        </div>
                      </el-row>

                      <el-row v-if="formData.initialSecondaryHandoverObj&&formData.isHandoverSecondUnitHandle=='1'" class="transfer-li ry-row">
                        <el-col :span="8" class="transfer-li-info">
                          <el-row>
                            <el-col :span="16" class="ovflowHidden text-left"><span>{{ formData.initialSecondaryHandoverObj.fromUnitName }}</span></el-col>
                            <el-col :span="8" class="text-right"><span>{{ formData.initialSecondaryHandoverObj.fromUserName }}</span></el-col>
                          </el-row>
                        </el-col>
                        <el-col :span="2" class="transfer-li-img">——</el-col>
                        <el-col :span="8" class="transfer-li-info ry-row cursor editSecondary">
                          <el-row>
                            <el-col :span="16" class="ovflowHidden text-left">
                              <span class="toUnitName" @click="AstaffOrgTree(1,formData.initialSecondaryHandoverObj.toUserName)">{{ formData.initialSecondaryHandoverObj.toUnitName||'请选择被移送单位及人员信息' }}</span>
                            </el-col>
                            <el-col :span="8" class="text-right">
                              <span class="toUserName" @click="AstaffOrgTree(1,formData.initialSecondaryHandoverObj.toUserName)">{{ formData.initialSecondaryHandoverObj.toUserName }}</span>
                            </el-col>
                          </el-row>
                        </el-col>
                      </el-row>
                    </el-col>
                  </el-col>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="移送纪检部门" prop="isHandoverInspectionDept">
                <el-radio-group v-model="formData.isHandoverInspectionDept" size="medium" @change="changeDel">
                  <el-radio
                    v-for="(item, index) in YesOrNo"
                    :key="index"
                    :label="item.value"
                  >{{ item.label }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item>
                <div class="layui-form-item layui-form-item-sm">
                  <el-col class="layui-input-block">
                    <el-col class="transfer-box">
                      <el-row v-for="(item,index) in formData.inspectionHandoverRecords" class="transfer-li ry-row" style="">
                        <div v-if="formData.isHandoverInspectionDept||!item.editEnable">
                          <el-col :span="8" class="transfer-li-info">
                            <el-row>
                              <el-col :span="16" class="ovflowHidden text-left"><span>{{ item.fromUnitName }}</span></el-col>
                              <el-col :span="8" class="text-right"><span>{{ item.fromUserName }}</span></el-col>
                            </el-row>
                          </el-col>
                          <el-col :span="2" class="transfer-li-img">——</el-col>
                          <el-col :span="8" class="transfer-li-info ry-row cursor editSecondary">
                            <el-row>
                              <el-col :span="16" class="ovflowHidden text-left">
                                <span class="toUnitName" @click="item.editEnable==1&&BstaffOrgTree(2,item.toUserName)">{{ item.toDeptName||'请选择被移送纪检部门及人员信息' }}</span>
                              </el-col>
                              <el-col :span="8" class="text-right">
                                <span class="toUserName" @click="item.editEnable==1&&BstaffOrgTree(2,item.toUserName)">{{ item.toUserName }}</span>
                              </el-col>
                            </el-row>
                          </el-col>
                          <el-col :span="6" class="transfer-li-edit text-right">
                            <span
                              v-show="item.downloadLink"
                              style="padding-left: 20px;"
                              class="table-btn tip-edit float-right text-red ovflowHidden cursor"
                              :title="item.fileName"
                              @click="downloadLink(item.downloadLink,item.fileName)"
                            >{{ item.fileName }}</span>
                          </el-col>
                        </div>

                      </el-row>

                      <el-row v-if="formData.initialInspectionHandoverObj&&formData.isHandoverInspectionDept=='1'" class="transfer-li ry-row">
                        <el-col :span="8" class="transfer-li-info">
                          <el-row>
                            <el-col :span="16" class="ovflowHidden text-left"><span>{{ formData.initialInspectionHandoverObj.fromUnitName }}</span></el-col>
                            <el-col :span="8" class="text-right"><span>{{ formData.initialInspectionHandoverObj.fromUserName }}</span></el-col>
                          </el-row>
                        </el-col>
                        <el-col :span="2" class="transfer-li-img">——</el-col>
                        <el-col :span="8" class="transfer-li-info ry-row cursor editSecondary">
                          <el-row>
                            <el-col :span="16" class="ovflowHidden text-left">
                              <span class="toUnitName" @click="BstaffOrgTree(1,formData.initialInspectionHandoverObj.toUserName)">{{ formData.initialInspectionHandoverObj.toUnitName||'请选择被移送单位及人员信息' }}</span>
                            </el-col>
                            <el-col :span="8" class="text-right">
                              <span class="toUserName" @click="BstaffOrgTree(1,formData.initialInspectionHandoverObj.toUserName)">{{ formData.initialInspectionHandoverObj.toUserName }}</span>
                            </el-col>
                          </el-row>
                        </el-col>
                      </el-row>
                    </el-col>
                  </el-col>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
        </BlockCard>
      </el-form>
      <BlockCard
        title="附件列表"
      >
        <FileUpload
          v-if="relevantTableId!=''&&relevantTableName!=''"
          :key="problemId||relevantTableId||relevantTableName"
          ref="file"
          :edit="edit"
          :problem-id="problemId"
          :relevant-table-id="relevantTableId"
          :relevant-table-name="relevantTableName"
          flow-type="VIOL_DAILY"
          problem-status="3"
          flow-key="SupervisionDailyReport"
        />
        <el-dialog :visible.sync="visibleTree" class="tree-body-dialog" width="90%" append-to-body :before-close="saveY" title="人员选择">
          <Tree
            v-if="visibleTree"
            ref="persTree"
            :key="problemId||relevantTableId||relevantTableName"
            :problem-id="problemId"
            :relevant-table-id="relevantTableId"
            :relevant-table-name="relevantTableName"
            :is-edit="true"
            @save="saveY"
          />
          <div slot="footer" class="dialog-footer">
            <el-button size="mini" type="primary" @click="closeTree">保存</el-button>
          </div>
        </el-dialog>

        <el-dialog :visible.sync="VisibleRadioTree" width="60%" append-to-body :title="title">
          <radio-tree
            v-if="VisibleRadioTree"
            :key="problemId"
            ref="radioTree"
            :url="url"
            :select-tree="selectTree"
            @accept="acceptList"
          />
          <div slot="footer" class="dialog-footer">
            <el-button size="mini" type="primary" @click="savePers">保存</el-button>
          </div>
        </el-dialog>

      </BlockCard>
      <!--修改记录-->
      <el-dialog :visible.sync="visibleModify" width="80%" append-to-body title="修改记录">
        <modifyRecord
          v-if="visibleModify"
          ref="modify"
          :key="problemId||relevantTableId||relevantTableName"
          edit="true"
          :problem-id="problemId"
          :relevant-table-id="relevantTableId"
          :relevant-table-name="relevantTableName"
          :problem-status="3"
          @modifySave="modifySave"
        />
        <div slot="footer" class="dialog-footer">
          <el-button size="mini" type="primary" @click="modifyClose">保存</el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>
<script>
import { submitHisData } from '@/api/daily/historyQuestionEnter/index'
import {
  queryVerifyRecord,
  saveViewVerrify,
  saveViolationVerifyRecord,
  saveHandoverSecondaryRecord,
  saveHandoverInspectionRecord,
  deleteDailyHandoverRecord
} from '@/api/daily/process/taskTodoViewDisposal'
import { checkInvolve } from '@/api/components/index'
import BlockCard from '@/components/BlockCard'
import Remind from '@/views/components/remind'
import FileUpload from '@/views/components/fileUpload'// 附件
import PersList from '@/views/daily/tree/persList'// tree
import Tree from '@/views/daily/tree'// tree
import radioTree from '@/views/components/tree/radioTree'// tree
import TaskTodoViewAccept from '@/views/daily/process/taskTodoViewAccept'// tree
import Process from '@/components/Process/daily'
import {
  // 生成情形范围修改记录
  generateSituationRangeModifyRecord
  // 生成业务数据修改记录
  , generateBusinessModifyRecord } from '@/api/daily/modifyRecord/modifyRecord'// 修改记录js方法
import modifyRecord from '@/views/daily/modifyRecord'// 修改记录
import { generateInvolveItemModifyRecord } from '@/api/components/index'

export default {
  components: { BlockCard, FileUpload, Tree, PersList, Process, TaskTodoViewAccept, radioTree, Remind, modifyRecord },
  dicts: ['VIOLD_DAILY_SPEC', 'VIOLD_ADVER_EFFECT_DES','corresponding_adverse_effect'],
  props: {
    problemId: {
      type: String
    }
  },
  data() {
    return {
      lossAmountDisabled: false,
      showAdverseEffectFlag: false,
      relevantTableId: '',
      relevantTableName: '',
      visibleModify: false,
      actualFlag: 1,
      treeNew: 1,
      type1: 1,
      type2: 1,
      url: '',
      title: '人员选择',
      VisibleRadioTree: false,
      edit: true,
      flag: false,
      visible: false,
      visibleTree: false,
      selectTree: [],
      formData: {
        isHandoverSecondUnitHandle: null,
        secondaryHandoverRecords: [],
        inspectionHandoverRecords: [],
        isReportSuperiorUnit: undefined,
        problemTitle: null,
        problemDescribe: undefined,
        field107: undefined,
        lossReason: undefined,
        adverseEffects: undefined,
        specLists: [],
        seriousAdverseEffectsFlag: undefined,
        lossRiskType: undefined,
        isAuditDeptCheck: undefined,
        isInvolveGroup: undefined,
        isInvolveSecondUnitLeader: undefined,
        isEmphasizeSupervise: undefined,
        isHandoverNationalJudiciary: undefined,
        isHandoverInspectionDept: undefined
      },
      specList: [],
      rules: {
        // problemDescribe: [{
        //   required: true,
        //   message: '请输入问题线索描述',
        //   trigger: 'blur'
        // }],
        // verifyDate: [{
        //   required: true,
        //   message: '请输入初核时间',
        //   trigger: 'blur'
        // }],
        // specLists: [{
        //   required: true,
        //   type: 'array',
        //   message: '请至少选择一个涉及专业线',
        //   trigger: 'change'
        // }],
        // isHandoverDataComplete: [{
        //   required: true,
        //   message: '是否移交材料是否齐全',
        //   trigger: 'change'
        // }],
      },
      YesOrNo: [{
        'label': '是',
        'value': 1
      }, {
        'label': '否',
        'value': 0
      }],
        whetherEffectOptions: [{
        "label": "是",
        "value": 1
      }, {
        "label": "否",
        "value": 0
      }],
      DailyHandoverRecordId: '',
      InspectionHandoverId: '',
      problemSourceList: [],
      lossRiskTypeOptions: []
    }
  },
  computed: {},
  watch: {},
  created() {
  },
  mounted() {
    if (this.problemId) {
      this.QueryVerifyRecord()
    }
  },
  methods: {
    lossStateAssetsChanged() {
      if (this.formData.lossStateAssetsFlag !== undefined && this.formData.isAdverseEffect !== undefined) {
        if (!this.formData.lossStateAssetsFlag && !this.formData.isAdverseEffect) {
          this.$message.error("【是否产生资产损失】与【是否产生不良影响】全部选择“否”不符合填报规则！");
          this.formData.lossStateAssetsFlag = 1;
        }
      }
      this.lossAmountDisabled = !this.formData.lossStateAssetsFlag;
      if (!this.formData.lossStateAssetsFlag) {
        this.formData.lossAmount = 0
      }
    },
    radioEffectChanged() {
      if (this.formData.lossStateAssetsFlag !== undefined && this.formData.isAdverseEffect !== undefined) {
        if (!this.formData.lossStateAssetsFlag && !this.formData.isAdverseEffect) {
          this.$message.error("【是否产生资产损失】与【是否产生不良影响】全部选择“否”不符合填报规则！");
          this.formData.isAdverseEffect = 1;
        }
      }
      this.showAdverseEffectFlag = this.formData.isAdverseEffect;
    },
    isAdverseEffectChange(){
      this.formData.seriousAdverseEffectsFlag = ''
    },
    seriousAdverseEffectsFlagChange(){
      if(this.formData.isAdverseEffect == '0'){
        if(this.formData.seriousAdverseEffectsFlag == '1'){
          this.$message.warning('是否产生不良影响在选择否的情况下，是否产生严重不良影响只能选择否！')
          this.formData.seriousAdverseEffectsFlag = 0
        }
      }
    },
    // 下载
    downloadLink(url, fileName) {
      this.download(url, {
      }, fileName)
    },
    // 移送下级单位处理选中否删除
    changeHandler(value) {
      if (value == 0) {
        if (this.DailyHandoverRecordId) {
          deleteDailyHandoverRecord(this.DailyHandoverRecordId).then(
            response => {
              if (response.code == 200) {
                this.DailyHandoverRecordId = response.data.id
                if (this.formData.initialSecondaryHandoverObj) {
                  this.formData.initialSecondaryHandoverObj.toUnitName = ''
                  this.formData.initialSecondaryHandoverObj.toUserName = ''
                } else if (this.formData.secondaryHandoverRecords && this.formData.secondaryHandoverRecords[this.formData.secondaryHandoverRecords.length - 1].editEnable) {
                  this.formData.secondaryHandoverRecords[this.formData.secondaryHandoverRecords.length - 1].toUnitName = ''
                  this.formData.secondaryHandoverRecords[this.formData.secondaryHandoverRecords.length - 1].toUserName = ''
                  this.formData.secondaryHandoverRecords[this.formData.secondaryHandoverRecords.length - 1].fileName = ''
                }
              }
            })
        }
      }
    },
    // 删除移交纪检部门
    changeDel(value) {
      if (value == 0) {
        deleteDailyHandoverRecord(this.InspectionHandoverId).then(
          response => {
            if (response.code == 200) {
              this.DailyHandoverRecordId = response.data.id
              if (this.formData.initialInspectionHandoverObj) {
                this.formData.initialInspectionHandoverObj.toUnitName = ''
                this.formData.initialInspectionHandoverObj.toUserName = ''
              } else if (this.formData.inspectionHandoverRecords && this.formData.inspectionHandoverRecords[this.formData.inspectionHandoverRecords.length - 1].editEnable) {
                this.formData.inspectionHandoverRecords[this.formData.inspectionHandoverRecords.length - 1].toUnitName = ''
                this.formData.inspectionHandoverRecords[this.formData.inspectionHandoverRecords.length - 1].toUserName = ''
                this.formData.inspectionHandoverRecords[this.formData.inspectionHandoverRecords.length - 1].fileName = ''
              }
            }
          })
      }
    },
    // 人员选择
    AstaffOrgTree(type, name) {
      this.treeNew = 1
      this.type1 = type
      this.title = '人员选择'
      this.url = '/colligate/violationDisposal/staffOrgTree'
      if (name) {
        this.selectTree = [{ name: name }]
      } else {
        this.selectTree = []
      }
      this.VisibleRadioTree = true
    },
    // 组织树选择
    BstaffOrgTree(type, name) {
      this.treeNew = 2
      this.type2 = type
      this.title = '组织选择'
      this.url = '/colligate/violationDisposal/inspectionStaffOrgTree'
      if (name) {
        this.selectTree = [{ name: name }]
      } else {
        this.selectTree = []
      }
      this.VisibleRadioTree = true
    },
    // 关闭
    close() {
      this.visible = false
      this.$emit('close')
    },
    // 确定选择的人员
    savePers() {
      this.$refs.radioTree.save()
    },
    // 调用保存
    acceptList(array) {
      this.VisibleRadioTree = false
      if (array[0].id) {
        if (this.treeNew === 1) {
          saveHandoverSecondaryRecord({
            toUserPost: array[0].id,
            businessTableId: this.relevantTableId,
            id: this.DailyHandoverRecordId,
            problemId: this.problemId
          }).then(
            response => {
              if (response.code === 200) {
                if (this.type1 === 1) {
                  this.formData.initialSecondaryHandoverObj.toUnitName = response.data.toUnitName
                  this.formData.initialSecondaryHandoverObj.toUserName = response.data.toUserName
                } else {
                  this.formData.secondaryHandoverRecords[this.formData.secondaryHandoverRecords.length - 1].toUnitName = response.data.toUnitName
                  this.formData.secondaryHandoverRecords[this.formData.secondaryHandoverRecords.length - 1].toUserName = response.data.toUserName
                  this.formData.secondaryHandoverRecords[this.formData.secondaryHandoverRecords.length - 1].fileName = ''
                }
                this.formData.secondaryUnitCode = response.data.toUnitCode
                this.formData.secondaryUnitName = response.data.toUnitName
                this.formData.secondaryUnitReceiverName = response.data.toUserName
                this.formData.secondaryUnitReceiverPost = response.data.toUserPost
              }
            })
        } else {
          saveHandoverInspectionRecord({
            toUserPost: array[0].id,
            businessTableId: this.relevantTableId,
            id: this.InspectionHandoverId,
            problemId: this.problemId
          }).then(
            response => {
              if (response.code === 200) {
                if (this.type2 === 1) {
                  this.formData.initialInspectionHandoverObj.toUnitName = response.data.toUnitName
                  this.formData.initialInspectionHandoverObj.toUserName = response.data.toUserName
                } else {
                  this.formData.inspectionHandoverRecords[this.formData.inspectionHandoverRecords.length - 1].toUnitName = response.data.toUnitName
                  this.formData.inspectionHandoverRecords[this.formData.inspectionHandoverRecords.length - 1].toUserName = response.data.toUserName
                  this.formData.inspectionHandoverRecords[this.formData.inspectionHandoverRecords.length - 1].fileName = ''
                }
                this.formData.inspectionDeptCode = response.data.toDeptCode
                this.formData.inspectionDeptName = response.data.toDeptName
                this.formData.inspectionDeptReceiverName = response.data.toUserName
                this.formData.inspectionDeptReceiverPost = response.data.toUserPost
              }
            })
        }
      }
    },
    /** 初始化数据*/
    QueryVerifyRecord() {
      this.loading = true
      const array = []
      queryVerifyRecord(this.problemId).then(
        response => {
          const specSelectedList = response.data.involveProfessionalLines
          this.formData = { ...this.formData, ...response.data }
          console.log("初始化的formData：", this.formData);
          for (let i = 0, len = specSelectedList.length; i < len; i++) {
            array.push(specSelectedList[i].specCode)
          }
          this.actualFlag = response.data.actualFlag
          this.lossRiskTypeOptions = response.data.lossRiskTypeOptions
          this.formData.specLists = array
          this.specList = response.data.professionalLineOptions
          this.relevantTableId = response.data.id
          this.problemSourceList = response.data.problemSourceList
          this.relevantTableName = response.data.businessTable
          this.loading = false
          if (!response.data.initialSecondaryHandoverObj) {
            if (response.data.secondaryHandoverRecords && response.data.secondaryHandoverRecords.length) {
              if (response.data.secondaryHandoverRecords[response.data.secondaryHandoverRecords.length - 1].editEnable) {
                this.DailyHandoverRecordId = response.data.secondaryHandoverRecords[response.data.secondaryHandoverRecords.length - 1].id
              } else {

              }
            }
          } else {
            this.DailyHandoverRecordId = response.data.initialSecondaryHandoverObj.id
          }

          if (!response.data.initialInspectionHandoverObj) {
            if (response.data.inspectionHandoverRecords && response.data.inspectionHandoverRecords.length) {
              this.InspectionHandoverId = response.data.inspectionHandoverRecords[response.data.inspectionHandoverRecords.length - 1].id
            }
          } else {
            this.InspectionHandoverId = response.data.initialInspectionHandoverObj.id
          }
          this.$nextTick(() => {
            this.$refs.pers.DueryDepartmentSelectInfo()
            this.$refs.file.ViolationFileItems()
          })
          this.$emit('closeLoading')
        }
      )
    },
    // 校验单位、部门、人员
    CheckInvolve() {
      let final = true;
      if (!this.formData.lossStateAssetsFlag && !this.formData.isAdverseEffect) {
        this.$message.error("【是否产生资产损失】与【是否造成不良影响】全部选择“否”不符合填报规则，请重新选择！");
        return false;
      }
      checkInvolve(this.problemId).then(
        response => {
          if (response.code == 200) {
            let companyString = ''; let // 单位列表
              deptString = ''// 部门列表
            if (response.data.resultCode == 'false') {
              this.$message.error('请选择单位！')
              final = false
            } else if (response.data.resultCode == 'company') {
              companyString = ''
              for (let i = 0; i < response.data.listCompany.length; i++) {
                companyString += '【' + response.data.listCompany[i].INVOL_COMPANY_NAME + '】'
                if (i + 1 != response.data.listCompany.length) {
                  companyString += '、'
                }
              }
              companyString = '' + companyString + '下未选择涉及部门！'
              this.$message.error(companyString)
              final = false
            } else if (response.data.resultCode == 'double') {
              companyString = ''
              deptString = ''
              for (let i = 0; i < response.data.listCompany.length; i++) {
                companyString += '【' + response.data.listCompany[i].INVOL_COMPANY_NAME + '】'
                if (i + 1 != response.data.listCompany.length) {
                  companyString += '、'
                }
              }
              for (let i = 0; i < response.data.listDept.length; i++) {
                deptString += '【' + response.data.listDept[i].INVOL_ORG_NAME + '】'
                if (i + 1 != response.data.listDept.length) {
                  deptString += '、'
                }
              }
              companyString = '1、' + companyString + '下未选择涉及部门！<br/>'
              deptString = '2、' + deptString + '下未选择涉及人员！'
              this.$message.error(companyString + deptString)
              final = false
            } else if (response.data.resultCode == 'dept') {
              deptString = ''
              for (let i = 0; i < response.data.listDept.length; i++) {
                deptString += '【' + response.data.listDept[i].INVOL_ORG_NAME + '】'
                if (i + 1 != response.data.listDept.length) {
                  deptString += '、'
                }
              }
              deptString = deptString + '下未选择涉及人员！'
              this.$message.error(deptString)
              final = false
            } else {
              generateInvolveItemModifyRecord({ problemId: this.problemId, businessId: this.relevantTableId }).then(response => {})
              final = true
            }
          }
        }
      )
      return final
    },
    /** 提交数据*/
    nextStep() {
      const volve = this.CheckInvolve()
      if (!volve) {
        return false
      }
      this.$refs['elForm'].validate(valid => {
        if (!valid) return
        // TODO 提交表单
        const array = []
        const specSelectedList = this.formData.specLists
        const specList = this.specList
        for (let i = 0, len = specSelectedList.length; i < len; i++) {
          for (let j = 0, leng = specList.length; j < leng; j++) {
            if (specList[j].dictValue == specSelectedList[i]) {
              specList[j].specCode = specList[j].dictValue
              specList[j].specName = specList[j].dictLabel
              array.push(specList[j])
            }
          }
        }
        this.formData.involveProfessionalLines = array
        if (!this.formData.isAdverseEffect) {
          this.formData.seriousAdverseEffectsFlag = 0;
        }
        saveViolationVerifyRecord(this.formData).then(
          response => {
            this.$modal.msgSuccess('保存成功')
            this.Modify()
          }
        )
      })
    },
    /** 保存数据*/
    publicSave() {
      // this.formData.specList=[];
      const array = []
      const specSelectedList = this.formData.specLists
      const specList = this.specList
      for (let i = 0, len = specSelectedList.length; i < len; i++) {
        for (let j = 0, leng = specList.length; j < leng; j++) {
          if (specList[j].dictValue == specSelectedList[i]) {
            specList[j].specCode = specList[j].dictValue
            specList[j].specName = specList[j].dictLabel
            array.push(specList[j])
          }
        }
      }
      this.formData.involveProfessionalLines = array
      saveViewVerrify(this.formData).then(
        response => {
          this.$modal.msgSuccess('保存成功')
        }
      )
    },
    resetForm() {
      this.$refs['elForm'].resetFields()
    },
    // 打开弹窗
    show() {
      this.visible = true
    },
    // 关闭弹窗
    closeTree() {
      this.$refs.persTree.CheckInvolve()// 校验
    },
    saveY() {
      this.visibleTree = false
      this.$refs.pers.DueryDepartmentSelectInfo()
    },
    // 选择人员
    treeOpen() {
      this.flag = !this.flag
      this.visibleTree = true
    },

    // 调用修改记录保存
    modifyClose() {
      this.$refs.modify.save()
    },
    // 修改记录保存后
    modifySave(type) {
      if (type) {
        this.visibleModify = false
        this.submitHisDataFun()
      } else {
        this.visibleModify = false
      }
    },
    // 修改记录
    Modify() {
      // 生成情形范围修改记录
      generateSituationRangeModifyRecord(this.problemId, this.relevantTableId)
      // 生成业务数据修改记录
      generateBusinessModifyRecord(this.problemId, this.relevantTableId, this.relevantTableName).then(
        response => {
          const isExistDifferenceField = response.data.isExistDifferenceField
          if (isExistDifferenceField) {
            this.visibleModify = true
          } else {
            this.submitHisDataFun()
          }
        }
      )
    },

    //最终提交
    submitHisDataFun(){
      this.$confirm('请确认填写内容，提交后不能再进行修改', '提示', {
        confirmButtonText: '继续提交',
        cancelButtonText: '返回确认',
        type: 'warning'
      }).then(() => {
        const loading = this.$loading({
          spinner: 'el-icon-loading', // 自定义加载图标类名
          text: '正在加载...', // 显示在加载图标下方的加载文案
          lock: false, // lock的修改符--默认是false
        });
        submitHisData(this.problemId).then(
          response => {
            loading.close();
            this.$modal.msgSuccess("提交成功");
            this.closeEmit();
          }
        ).catch(err => {
          loading.close();
        })
      })
    },
    //流程提交后推进到下一个环节
    closeEmit(){
      this.$emit('hisNext')
    }
  }
}

</script>
<style>
  .transfer-box {
  }

  .transfer-li {
    margin-bottom: 10px;
  }

  .transfer-li-info {
    height: 45px;
    padding: 0 10px;
    line-height: 45px;
    background-color: #ffffff;
    border-radius: 2px;
    border: solid 1px #d9d9d9;
  }

  .transfer-li-info span {
    font-size: 14px;
    color: #373d41;
  }

  .transfer-li-img {
    text-align: center;
    line-height: 45px;
  }

  .transfer-li-edit {
    line-height: 45px;
  }

  .transfer-li-edit .icon {
  }
  .tree-body-dialog ::v-deep.el-dialog__body{
    padding:0 20px;
  }
</style>
