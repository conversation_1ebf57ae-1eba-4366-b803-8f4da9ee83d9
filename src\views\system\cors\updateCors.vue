
<!-- 更新cors跨域资源白名单 -->

<template>
  <div class="wai-container" style="background-color: #fff">
    <div class="layui-row width height">
      <div class="width height">
        <div class="common-wai-box" style="height: 100%">
          <div class="common-in-box" style="height: auto; min-height: 100%">
            <div class="common-in-box-header">
              <div class="common-in-box-header-line" />
              更新cors跨域资源白名单
              <div class="flex-1" />
            </div>
          </div>
        </div>
      </div>
    </div>

    <el-form :inline="true" label-width="68px">
      <el-form-item>
      <el-button
        size="mini"
        @click="updateAllowedOrigins()"
      >更新cors跨域资源白名单
      </el-button>
    </el-form-item>
      （更新sys_config表的key=AUDIT_ALLOWED_ORIGINS对应的缓存后点击按钮进行更新）
    </el-form>

  </div>
</template>
<script>
  import {
    updateAllowedOrigins
  } from '@/api/system/cors'
  export default {
    components: { },
    props: {
    },
    data() {
      return {
      }
    },
    created() {

    },
    methods: {

      updateAllowedOrigins(){
        this.openLoading();
        updateAllowedOrigins().then((res)=>{
          this.closeLoading();
          this.$modal.msgSuccess("操作成功");
        })
      },

      openLoading(){//打开加载...
        this.$emit('openLoading');
      },
      closeLoading(){//关闭加载...
        this.$emit('closeLoading');
      },
    }
  }
</script>
