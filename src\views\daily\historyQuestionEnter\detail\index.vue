<template>
  <div class="height">
    <el-row class="height">
      <div class="process-left">
        <div class="processSeeBox float-left">
          <div id="processBox" class="process-box">
            <div
              v-for="(item,index) in processBox"
              :key="index"
              :class="
                processIndex==item.statusName?
                  problemStatus==item.statusName?
                    'active selects process-li':'green selects process-li':
                  problemStatus==item.statusName?
                    'active green process-li':problemStatus<item.statusName?
                      'process-li':'green process-li'"
              @click="iframeUrl(item.statusName)"
            >
              <span class="process-number float-left">{{ item.statusName }}</span>
              <div class="process-name float-right">
                <span class="span1">{{ item.problemStatus }}</span>
                <span v-if="index<problemStatus" class="span2 name">操作人：{{ item.userName }}</span>
                <span v-if="index<problemStatus" class="span2 time-title">通过时间：</span>
                <span v-if="item.userName&&index<problemStatus" class="span2 time">{{ item.endTime }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="process-content">
        <URL1
          v-if="processIndex==='1'"
          :key="selectValue.id"
          ref="accept"
          isShow="1"
          :edit="edit"
          :procInsId="procInsId"
          :problem-id="selectValue.id"
          @handle="handle"
        />
        <URL2
          v-if="processIndex==='2'"
          :key="selectValue.id"
          ref="accept"
          isShow="1"
          :edit="edit"
          :procInsId="procInsId"
          :problem-id="selectValue.id"
          @handle="handle"
        />
        <URL3
          v-if="processIndex==='3'"
          :key="selectValue.id"
          ref="accept"
          isShow="1"
          :edit="edit"
          :procInsId="procInsId"
          :problem-id="selectValue.id"
          @handle="handle"
        />
        <URL4
          v-if="processIndex==='4'"
          :key="selectValue.id"
          ref="accept"
          isShow="1"
          :edit="edit"
          :procInsId="procInsId"
          :problem-id="selectValue.id"
          @handle="handle"
        />
        <URL5
          v-if="processIndex==='5'"
          :key="selectValue.id"
          ref="accept"
          isShow="1"
          :edit="edit"
          :procInsId="procInsId"
          :problem-id="selectValue.id"
          @handle="handle"
        />
        <URL6
          v-if="processIndex==='6'"
          :key="selectValue.id"
          ref="accept"
          isShow="1"
          :edit="edit"
          :procInsId="procInsId"
          :problem-id="selectValue.id"
          @handle="handle"
        />
        <URL7
          v-if="processIndex==='7'"
          :key="selectValue.id"
          ref="accept"
          isShow="1"
          :edit="edit"
          :procInsId="procInsId"
          :problem-id="selectValue.id"
          @handle="handle"
        />
      </div>
    </el-row>
  </div>
</template>

<script>
import URL1 from './../process/taskTodoViewAcceptProess'
import URL2 from './../process/taskTodoViewVerifyProess'
import URL3 from './../process/taskTodoViewDisposalProess'
import URL4 from './../process/verificationRecordDetail'
import URL5 from './../process/handlingAppealRecordsDetail'
import URL6 from './../process/rectificationRecordProess'
import URL7 from './../process/taskTodoViewFileReviewCopy'
import { selectHisDailyFlowInfo } from '@/api/daily/historyQuestionEnter/index'

export default {
  name: 'Detail',
  components: {
    URL1, URL2, URL3, URL4, URL5, URL6, URL7
  },
  props: {
    selectValue: {
      type: Object
    },
    dailyProblemId: {
      type: String
    },
    dailyProblemStatus: {
      type: String
    },
    procInsId:{
      type: String
    }
  },
  data() {
    return {
      activeName: 1,
      processIndex: 1,
      edit: false,
      problemStatus: '',
      processBox: []
    }
  },
  mounted() {
    this.SelectStatusAndType()
  },
  methods: {
    iframeUrl(index) {
      if (index > this.problemStatus) {
        return false
      } else if (index == this.problemStatus) {
        this.processIndex = index
      } else {
        this.processIndex = index
      }
    },
    // 保存
    publicSave() {
      this.$refs.accept.publicSave()
    },
    // 下一步
    nextStep() {
      this.$refs.accept.nextStep()
    },
    // 下一步回调
    handle(type) {
      this.$emit('handle', type)
    },
    // 获取环节页面
    SelectStatusAndType() {
      // selectStatusAndType({ insId: this.selectValue.procInsId }).then(
      //   response => {
          this.problemStatus = this.dailyProblemStatus? this.dailyProblemStatus : '1'
          this.processIndex = this.dailyProblemStatus ?this.dailyProblemStatus : '1'
          this.SelectDailyFlowInfo()
        // }
      // )
    },
    // 环节名称
    SelectDailyFlowInfo() {
      selectHisDailyFlowInfo({ problemId: this.dailyProblemId,problemStatus: this. dailyProblemStatus}).then(
        response => {
          this.processBox = response.data
        }
      )
    }
  }
}
</script>

<style scoped lang="scss">
  .processSeeBox {
    width: 240px;
    margin-top: 10px;

  .process-box {
    height: auto;
    display: block;
    border-bottom: 0px;
    margin-top: 6px;
  }

  .process-box:before {
    position: absolute;
    content: '';
    width: 0;
    height: 0;
  }

  .process-li:before {
    position: absolute;
    content: "";
    left: 33px;
    top: 42px;
    width: 1px;
    z-index: 10;
    height: 47px;
    border-left: 1px solid rgb(217, 217, 217);
  }

  .process-li {
    width: 100%;
    min-height: 100px;
    box-sizing: border-box;
    position: relative;
    overflow: hidden;
    padding-left: 18px;

  .process-number {
    width: 32px;
    height: 32px;
    background-color: #fff;
    font-size: 16px;
    color: #f5222d;
    border: solid 1px #f5222d;
    position: relative;
    border-radius: 50%;
    text-align: center;
    line-height: 32px;
  }

  .process-name {
    width: 170px;

  span {
    display: block;
  }

  .span1 {
    line-height: 32px;
    color: #000000;
    opacity: 0.85;
    font-size: 16px;
    font-weight: bold;
  }

  }
  }
  .process-li.suspension:after {
    position: absolute;
    content: "";
    width: 70px;
    height: 100px;
    background-size: 70px;
    opacity: 0.7;
    right: 24px;
  }

  .process-li.complete:after {
    position: absolute;
    content: "";
    width: 100px;
    height: 100px;
    background-size: 70px;
    opacity: 0.7;
    right: 24px;
  }

  .process-li:last-child:before {
    position: absolute;
    content: "";
    left: 33px;
    top: 42px;
    width: 1px;
    z-index: 10;
    height: 0;
    border-width: 0;
  }

  .process-li.green {
    cursor: pointer;

  .process-number {
    background-color: #ffe2e4;
    color: #f5222d;
    border-color: #f5222d;
  }

  .process-name {
    color: #a9b0b4;
  }

  }
  .process-li.active {
    cursor: pointer;

  .process-number {
    background: #f5222d;
    color: #fff;
    border-color: #f5222d;
  }

  .process-name {
    color: #000;

  .span1 {
    font-weight: bold;
  }

  }
  }
  .process-li.selects {

  .process-number:before {
    position: absolute;
    content: '';
    bottom: -8px;
    width: 100%;
    left: 0;
    height: 4px;
    background-color: #f5222d;
  }

  }
  }

  .process-right {
    padding: 8px;
    float: right;
    width: calc(100% - 240px);
    box-shadow: 0px 0px 10px 0px rgb(0 0 0 / 10%);
    border-radius: 2px;
    border: solid 1px #d9d9d9;
    box-sizing: border-box;
  }

  @media screen and ( max-width: 1680px ) {
    .processSeeBox {

    .process-li {
      min-height: 90px;
    }

    .process-li:before {
      height: 40px;
    }
  }

  }

  @media screen and ( max-width: 1480px ) {
    .processSeeBox {

    .process-li {
      min-height: 78px;

    .process-number {
      width: 28px;
      height: 28px;
      line-height: 29px;
      font-size: 14px;
    }

    .process-name {
      font-size: 12px;

    .span1 {
      font-size: 14px;
    }
  }

  }
  .process-li:before {
    top: 38px;
    left: 31px;
    height: 30px;
  }

  }
  }

  @media screen and ( max-width: 1300px ) {
    .processSeeBox {

    .process-li {
      min-height: 58px;

    .process-name {

    .span1 {
      line-height: 20px;
      font-size: 12px;
    }
  }

  span.time-title {
    display: none;
  }

  }
  }
  }

  .position-top {
    position: fixed;
    top: 0;
  }

  .process-box:before {
    position: absolute;
    content: '';
    left: 0;
    top: 31px;
    width: 100%;
    z-index: 10;
    height: 1px;
    border-bottom: 1px solid #d9d9d9;
  }

  .verify-top-title {
    color: #a9b0b4;
    padding: 10px 0;
    text-align: center;
    border-bottom: 1px solid #d9d9d9;
  }

  .verify-bottom-title {
    color: #a9b0b4;
    padding: 10px 0;
    text-align: center;
    border-top: 1px solid #d9d9d9;
  }

  .process-box {
    height: 64px;
  }

  .process-left {
    float: left;
    width: 240px;
    height: 100%;
    overflow-x: hidden;
    overflow-y: auto;
  }

  .process-content {
    border: 1px solid #ddd;
    width: calc(100% - 240px);
    height: 100%;
    overflow: auto;
  }

</style>
