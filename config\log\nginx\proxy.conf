# proxy_redirect off;
# proxy_buffer_size 128k;
# proxy_buffers 32 32k;
# proxy_busy_buffers_size 128k;
# proxy_temp_file_write_size 128k;
proxy_intercept_errors on;
proxy_set_header Host $host;
proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
proxy_set_header X-Real-IP $remote_addr;
proxy_set_header X-Forwarded-Proto  $scheme;
# proxy_connect_timeout 5;
# proxy_send_timeout 30;
# proxy_read_timeout 10;
# proxy_next_upstream http_502 error timeout invalid_header;