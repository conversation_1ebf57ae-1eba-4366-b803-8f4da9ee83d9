
<!-- 初核报告--定时待阅 -->

<template>
  <div>
    <div style="padding:20px 10px"> 您好，请贵公司在收到{{infoData.projectName}}{{readInfo}}</div>
    <BlockCard title="项目信息">

      <div class="position">
        <div class="position-select">
          <div class="float-right">
            <el-button  type="primary" plain  size="mini" @click="handleDownload">专项初核报告模板</el-button>
          </div>
        </div>
      </div>
      <el-form r size="medium" label-width="150px">
        <el-row>
          <el-col :span="16">
            <el-form-item label="项目名称">
              <span>{{ infoData.projectName }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="项目编码">
              <span> {{infoData.projectNum}}</span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="项目类型">
              <span>{{infoData.projectTypeEnumId | fromatComon(dict.type.SPR_PROJECT_TYPE_ALL)}}</span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="审计对象">
              <span>  {{infoData.projectOrgName}}</span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="项目年度">
              <span> {{infoData.projectYear}}</span>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </BlockCard>
  </div>
</template>
<script>
import BlockCard from '@/components/BlockCard';
  import {
    queryProjectInfo
    ,queryReadInfo
  } from '@/api/special-report'
  export default {
    components: { BlockCard },
    props: {
      // 编辑内容
      rowData: {
        type: Object,
        default: () => {}
      },
      //流程参数
      centerVariable: {
        type: Object
      },
    },
    dicts: ['SPR_PROJECT_TYPE_ALL'],
    data() {
      return {
        infoData: {}, // 基本信息
        tableLoading: false, // 表格loading
        id : '',//主键
      }
    },
    created() {
      this.$emit('collocation',{
        refreshAssigneeUrl:'/spr/readflow',//业务url
      })
      this.id = this.centerVariable.busiKey;
      //查询项目信息
      this.queryProjectInfo();
    },
    methods: {
      //根据主键查询项目信息
      queryProjectInfo(){
        queryProjectInfo(this.id).then((res)=>{
          //项目信息
          this.infoData = res.data;
          this.$forceUpdate();
          this.queryReadInfoById();
        })
      },
      //根据主键查询业务信息
      queryReadInfoById(){
        queryReadInfo('special_report_audit_read').then((res)=>{
          this.readInfo = res.readInfo;
          this.$forceUpdate();
        })
      },
      //下载模板
      handleDownload(){
        this.download('/spr/attachment/downLoadSpReportAttachment/D_PRO_SP_PROJECT_INFO_SPR', {}, "专项初步核实报告（模板）.docx");
      },

      /** 下载模板*/
      fileDownload(obj) {
        this.download(
          '/sys/attachment/downloadSysAttachment/' + obj.attachmentId,
          {},
          obj.fileName
        )
      },
      fromatComon (value, list) {
        let lastLabel = '-'

        if (value && list.length > 0) {
          list.forEach(element => {
            if (element.value == value) {
              lastLabel = element.label
            }
          })
        }
        return lastLabel
      },
      openLoading(){//打开加载...
        this.$emit('openLoading');
      },
      closeLoading(){//关闭加载...
        this.$emit('closeLoading');
      },
      loadProcessData(){
        return ;
      },
      //校验
      passValidate(){
        return true;
      },
    }
  }
</script>
<style lang="scss" scoped>
  .position-select {
    position: absolute;
    width: 100%;
    text-align: right;
    top: -54px;
  }
</style>
