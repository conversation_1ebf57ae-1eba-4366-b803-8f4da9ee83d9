<template>
  <el-dialog
    :title="modalTitle"
    :visible.sync="dialogVisible"
    width="40%"
    append-to-body
    :before-close="handleClose">
    <el-row :gutter="20">
      <el-col :span="24">
        <el-card class="box-card"  shadow="never" style=" height:450px">
          <div slot="header" class="clearfix">
            <el-row :gutter="10">
              <el-col :span="18">
                <el-input
                  placeholder="输入关键字进行过滤"
                  v-model="filterText">
                </el-input>
              </el-col>
              <el-col :span="4">
                <el-button  type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
              </el-col>
            </el-row>
          </div>
          <radioTree v-on:selectNode="selectNode" :treeList="treeList" :onlyChild="onlyChild"></radioTree>
        </el-card>
      </el-col>
    </el-row>
    <span slot="footer" class="dialog-footer">
      <el-button size="mini" @click="handleClose()">取 消</el-button>
      <el-button size="mini" type="primary" @click="sub()">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script lang="ts">
  import radioTree from "../../common/radioTree";

  export default {
    name: "treeModal",
    components: { radioTree },
    props: {
      treeList: {
        type: Array,
        default: ()=>{ return []}
      },
      dialogVisible: {
        type: Boolean,
        default: true
      },
      modalTitle: {
        type: String,
        default: ''
      },
      nodeData: {
        type: Object,
        default: ()=>{return {}}
      },
      onlyChild: {
        type: Boolean,
        default: false
      },
    },
    data() {
      return {
        filterText: '',
        defaultProps: {
          children: 'children',
          label: 'label'
        },
        selectNodeData:{},
        // selectIds:[],
      };
    },
    created() {
      this.selectNodeData = this.nodeData;
    },
    methods: {
      /**关闭模态框*/
      handleClose() {
        this.$emit("closeModal");

      },
      /**选择节点*/
      selectNode(data) {
        this.selectNodeData = data
      },
      handleQuery(){
        this.$emit("getTreeData", this.filterText);
      },
      sub(){
        this.$emit("getOrg", this.selectNodeData);
        this.handleClose();
      }
    }
  };
</script>

<style>
  .text {
    font-size: 14px;
  }

  .item {
    margin-bottom: 18px;
  }

  .clearfix:before,
  .clearfix:after {
    display: table;
    content: "";
  }
  .clearfix:after {
    clear: both
  }
  .el-card__body{
    height:100%;
    overflow:auto;
    padding-top: 0px !important;
  }
</style>
