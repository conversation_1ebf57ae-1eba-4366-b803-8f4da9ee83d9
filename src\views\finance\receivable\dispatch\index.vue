<template>
  <div>
    <el-form ref="elForm" :model="formData" :rules="rules" size="medium" label-width="221px">
      <el-form-item label="对应《违规经营投资责任追究办法》" prop="field101">
        <el-button type="primary" icon="el-icon-search" size="medium"> 主要按钮 </el-button>
      </el-form-item>
      <el-row gutter="15">
      </el-row>
      <el-form-item size="large">
        <el-button size="mini" type="primary" @click="submitForm">提交</el-button>
        <el-button size="mini" @click="resetForm">重置</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
  export default {
    components: {},
    props: [],
    data() {
      return {
        formData: {
          field101: undefined,
        },
        rules: {},
      }
    },
    computed: {},
    watch: {},
    created() {},
    mounted() {},
    methods: {
      submitForm() {
        this.$refs['elForm'].validate(valid => {
          if (!valid) return
          // TODO 提交表单
        })
      },
      resetForm() {
        this.$refs['elForm'].resetFields()
      },
    }
  }

</script>
<style>
</style>
