import router from './router'
import store from './store'
import { Message } from 'element-ui'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'
import { setToken, getToken } from '@/utils/auth'
import { setWaterMark,clearWatermark }  from "@/utils/watermark.js";
import versionTood from '@/libs/versionUpdate'
NProgress.configure({ showSpinner: false })

const whiteList = ['/sign', '/system/webOffice','/login','/404', '/auth-redirect', '/bind', '/taskReadAllCloud','/taskToDoCloud','/taskReadCloud','/taskHasdoneCloud','/taskToReadCloud']

function getQueryVariable(variable)
{
  let query = window.location.search.substring(1);
  let vars = query.split("&");
  for (let i=0;i<vars.length;i++) {
    let pair = vars[i].split("=");
    if(pair[0] == variable){return pair[1];}
  }
  return(false);
}

function getQueryVariableA(variable)
{
  let query = window.location.search.substring(1);
  let vars = query.split("@");
  for (let i=0;i<vars.length;i++) {
    let pair = vars[i].split("=");
    if(pair[0] == variable){return pair[1];}
  }
  return(false);
}

router.beforeEach((to, from, next) => {

  NProgress.start()
  if (getToken()) {
    to.meta.title && store.dispatch('settings/setTitle', to.meta.title)
    /* has token*/
    if (to.path === '/login') {
      next({ path: '/' })
      NProgress.done()
    }else if(to.fullPath.indexOf("/jtauditLogin") !== -1){//审计系统跳转监督追责
      const userName = getQueryVariable('userName');
      const deptId = getQueryVariable('deptId');
      let url = process.env.VUE_APP_BASE_API+'/jtauditCloud/authentic?error=/404&userName='+userName+'&deptId='+deptId+'&returnUrl=/fromAudit?';
      window.location.href=url;
    }  else if(to.fullPath.indexOf("/taskDetailCloud") !== -1){//待办待阅
      const messageTrackId = getQueryVariable('message_track_id');
      const cloudPartyId = getQueryVariable('cloudPartyid');
      store.dispatch('AuthenticTask',{
        messageTrackId: messageTrackId,
        cloudPartyId: cloudPartyId,
        returnUrl:'/fromAuditToDo?messageTrackId='+messageTrackId+'@cloudPartyId='+cloudPartyId+'@token='
      }).then(response => {
        if(response.status=='406'){
          let url = response.data.url+'?messageTrackId='+response.data.messageTrackId+'&cloudPartyId='+response.data.cloudPartyId+'&success='+response.data.success+'&appid='+response.data.appid+'&return='+response.data.return+'&error='+response.data.error
          window.location.href=url;
        }
      }).catch(err => {
        next({ path: '/404' })
      })
    } else  if(to.fullPath.indexOf("/cloudLogin") !== -1){//门户登录
      store.dispatch('Authentic').then(response => {
        if(response.status=='406'){
          let url = response.data.url+'?success='+response.data.success+'&appid='+response.data.appid+'&return='+response.data.return+'&error='+response.data.error
          window.location.href=url;
        }
      }).catch(err => {
        next({ path: '/404' })
      })
    }else if(to.fullPath.indexOf("/fromAuditToDo") !== -1){//待办已办的跳转
      // 没有token
       const toDoToken2 = getQueryVariableA('token');
      const messageTrackId2 = getQueryVariableA('messageTrackId');
      const cloudPartyId2 = getQueryVariableA('cloudPartyId');
       setToken(toDoToken2);
      to.meta.title && store.dispatch('settings/setTitle', to.meta.title)
      // 判断当前用户是否已拉取完user_info信息
      store.dispatch('GetInfo').then(() => {
        store.dispatch('GenerateRoutes').then(accessRoutes => {
          // 根据roles权限生成可访问的路由表
          router.addRoutes(accessRoutes) // 动态添加可访问路由表
          store.dispatch('TaskToDoDetail',{
            messageTrackId: messageTrackId2,
            cloudPartyId: cloudPartyId2
          }).then(response => {
            if(response.data.doneStatus!=null){//待办已办
              if(response.data.doneStatus==='0'){//待办
                next({ path: '/taskToDoCloud',query: {
                    flowKey:response.data.flowKey,
                    linkKey:response.data.linkKey,
                    processInstanceId:response.data.processInstanceId,
                    readLinkId:response.data.readLinkId,
                    taskId:response.data.taskId,
                    typeId:response.data.typeId,
                  }})
              }else{//已办
                next({ path: '/taskHasdoneCloud',query: {
                    flowKey:response.data.flowKey,
                    linkKey:response.data.linkKey,
                    processInstanceId:response.data.processInstanceId,
                    readLinkId:response.data.readLinkId,
                    taskId:response.data.taskId,
                    typeId:response.data.typeId,
                  }})
              }
            }
          }).catch(err => {
            next({ path: '/404' })
          })

          //next({ path: '/' })
          NProgress.done()
        })
      }).catch(err => {
        store.dispatch('LogOut').then(() => {
          Message.error(err)
          next({ path: '/bind' })
        })
      })
    }else if(to.fullPath.indexOf("/fromAuditToRead") !== -1){//待阅已阅的跳转
      // 没有token
      const toDoToken3 = getQueryVariableA('token');
      const messageTrackId3 = getQueryVariableA('messageTrackId');
      const cloudPartyId3 = getQueryVariableA('cloudPartyId');
      setToken(toDoToken3);
      to.meta.title && store.dispatch('settings/setTitle', to.meta.title)
      // 判断当前用户是否已拉取完user_info信息
      store.dispatch('GetInfo').then(() => {
        store.dispatch('GenerateRoutes').then(accessRoutes => {
          // 根据roles权限生成可访问的路由表
          router.addRoutes(accessRoutes) // 动态添加可访问路由表
          store.dispatch('TaskReadDetail',{
            messageTrackId: messageTrackId3,
            cloudPartyId: cloudPartyId3
          }).then(response => {
            if(response.data.doneStatusRead!=null){//待阅已阅
              if(response.data.doneStatusRead==='0'){//待阅
                next({ path: '/taskToReadCloud',query: {
                    flowKey:response.data.flowKey,
                    linkKey:response.data.linkKey,
                    processInstanceId:response.data.processInstanceId,
                    readLinkId:response.data.readLinkId,
                    taskId:response.data.taskId,
                    typeId:response.data.typeId,
                    readerId:response.data.readerId,
                    busiId:response.data.busiId,
                  }})
              }else{//已阅
                next({ path: '/taskReadCloud',query: {
                    flowKey:response.data.flowKey,
                    linkKey:response.data.linkKey,
                    processInstanceId:response.data.processInstanceId,
                    readLinkId:response.data.readLinkId,
                    taskId:response.data.taskId,
                    typeId:response.data.typeId,
                    readerId:response.data.readerId,
                    busiId:response.data.busiId,
                  }})
              }
            }
          }).catch(err => {
            next({ path: '/404' })
          })

          //next({ path: '/' })
          NProgress.done()
        })
      }).catch(err => {
        store.dispatch('LogOut').then(() => {
          Message.error(err)
          next({ path: '/bind' })
        })
      })
    } else if(to.fullPath.indexOf("/taskReadDetailCloud") !== -1){//待阅已阅
      const messageTrackId = getQueryVariable('message_track_id');
      const cloudPartyId = getQueryVariable('cloudPartyid');
      store.dispatch('AuthenticTask',{
        messageTrackId: messageTrackId,
        cloudPartyId: cloudPartyId,
        returnUrl:'/fromAuditToRead?messageTrackId='+messageTrackId+'@cloudPartyId='+cloudPartyId+'@token='
      }).then(response => {
        if(response.status=='406'){
          let url = response.data.url+'?messageTrackId='+response.data.messageTrackId+'&cloudPartyId='+response.data.cloudPartyId+'&success='+response.data.success+'&appid='+response.data.appid+'&return='+response.data.return+'&error='+response.data.error
          window.location.href=url;
        }
      }).catch(err => {
        next({ path: '/404' })
      })
    }else if(to.fullPath.indexOf("/fromAudit") !== -1){
      // 没有token
      const url = to.fullPath ;
      const token = url.substr(url.indexOf("?") + 1);
      setToken(token);
      to.meta.title && store.dispatch('settings/setTitle', to.meta.title)
      // 判断当前用户是否已拉取完user_info信息
      store.dispatch('GetInfo').then(() => {
        store.dispatch('GenerateRoutes').then(accessRoutes => {
          // 根据roles权限生成可访问的路由表
          router.addRoutes(accessRoutes) // 动态添加可访问路由表
          next({ path: '/' })
          NProgress.done()
        })
      }).catch(err => {
        store.dispatch('LogOut').then(() => {
          Message.error(err)
          next({ path: '/bind' })
        })
      })
    } else {
      if (store.getters.roles.length === 0) {
        // 判断当前用户是否已拉取完user_info信息
        store.dispatch('GetInfo').then(() => {
          store.dispatch('GenerateRoutes').then(accessRoutes => {
            // 根据roles权限生成可访问的路由表
            router.addRoutes(accessRoutes) // 动态添加可访问路由表
            next({ ...to, replace: true }) // hack方法 确保addRoutes已完成
          })
        }).catch(err => {
            store.dispatch('LogOut').then(() => {
              Message.error(err)
              next({ path: '/' })
            })
          })
      } else {
        next()
      }
    }
  } else {
    if (whiteList.indexOf(to.path) !== -1) {
      // 在免登录白名单，直接进入
      next()
    } else if(to.fullPath.indexOf("/fromAuditToDo") !== -1){//待办已办的跳转
      // 没有token
      const toDoToken1 = getQueryVariableA('token');
      const messageTrackId1 = getQueryVariableA('messageTrackId');
      const cloudPartyId1 = getQueryVariableA('cloudPartyId');
       setToken(toDoToken1);
      to.meta.title && store.dispatch('settings/setTitle', to.meta.title)
      // 判断当前用户是否已拉取完user_info信息
      store.dispatch('GetInfo').then(() => {
        store.dispatch('GenerateRoutes').then(accessRoutes => {
          // 根据roles权限生成可访问的路由表
          router.addRoutes(accessRoutes) // 动态添加可访问路由表
          store.dispatch('TaskToDoDetail',{
            messageTrackId: messageTrackId1,
            cloudPartyId: cloudPartyId1
          }).then(response => {
            if(response.data.doneStatus!=null){//待办已办
              if(response.data.doneStatus==='0'){//待办
                next({ path: '/taskToDoCloud',query: {
                    flowKey:response.data.flowKey,
                    linkKey:response.data.linkKey,
                    processInstanceId:response.data.processInstanceId,
                    readLinkId:response.data.readLinkId,
                    taskId:response.data.taskId,
                    typeId:response.data.typeId,
                  }})
              }else{//已办
                next({ path: '/taskHasdoneCloud',query: {
                    flowKey:response.data.flowKey,
                    linkKey:response.data.linkKey,
                    processInstanceId:response.data.processInstanceId,
                    readLinkId:response.data.readLinkId,
                    taskId:response.data.taskId,
                    typeId:response.data.typeId,
                  }})
              }
            }
          }).catch(err => {
            next({ path: '/404' })
          })

          //next({ path: '/' })
          NProgress.done()
        })
      }).catch(err => {
        store.dispatch('LogOut').then(() => {
          Message.error(err)
          next({ path: '/bind' })
        })
      })
    } else if(to.fullPath.indexOf("/fromAuditToRead") !== -1){//待阅已阅的跳转
      // 没有token
      const toDoToken4 = getQueryVariableA('token');
      const messageTrackId4 = getQueryVariableA('messageTrackId');
      const cloudPartyId4 = getQueryVariableA('cloudPartyId');
      setToken(toDoToken4);
      to.meta.title && store.dispatch('settings/setTitle', to.meta.title)
      // 判断当前用户是否已拉取完user_info信息
      store.dispatch('GetInfo').then(() => {
        store.dispatch('GenerateRoutes').then(accessRoutes => {
          // 根据roles权限生成可访问的路由表
          router.addRoutes(accessRoutes) // 动态添加可访问路由表
          store.dispatch('TaskReadDetail',{
            messageTrackId: messageTrackId4,
            cloudPartyId: cloudPartyId4
          }).then(response => {
            if(response.data.doneStatusRead==null){//待阅已阅
              if(response.data.doneStatusRead=='0'){//待阅
                next({ path: '/taskToReadCloud',query: {
                    flowKey:response.data.flowKey,
                    linkKey:response.data.linkKey,
                    processInstanceId:response.data.processInstanceId,
                    readLinkId:response.data.readLinkId,
                    taskId:response.data.taskId,
                    typeId:response.data.typeId,
                    readerId:response.data.readerId,
                    busiId:response.data.busiId,
                  }})
              }else{//已阅
                next({ path: '/taskReadCloud',query: {
                    flowKey:response.data.flowKey,
                    linkKey:response.data.linkKey,
                    processInstanceId:response.data.processInstanceId,
                    readLinkId:response.data.readLinkId,
                    taskId:response.data.taskId,
                    typeId:response.data.typeId,
                    readerId:response.data.readerId,
                    busiId:response.data.busiId,
                  }})
              }
             }
          }).catch(err => {
            next({ path: '/404' })
          })

          //next({ path: '/' })
          NProgress.done()
        })
      }).catch(err => {
        store.dispatch('LogOut').then(() => {
          Message.error(err)
          next({ path: '/bind' })
        })
      })
    }else if(to.fullPath.indexOf("/fromAudit") !== -1){
      // 没有token
      const url = to.fullPath ;
      const token = url.substr(url.indexOf("?") + 1);
      setToken(token);
      to.meta.title && store.dispatch('settings/setTitle', to.meta.title)
      // 判断当前用户是否已拉取完user_info信息
      store.dispatch('GetInfo').then(() => {
        store.dispatch('GenerateRoutes').then(accessRoutes => {
          // 根据roles权限生成可访问的路由表
          router.addRoutes(accessRoutes) // 动态添加可访问路由表
          next({ path: '/' })
          NProgress.done()
        })
      }).catch(err => {
        store.dispatch('LogOut').then(() => {
          Message.error(err)
          next({ path: '/bind' })
        })
      })
    }else if(to.fullPath.indexOf("/taskDetailCloud") !== -1){//待办已办
      const messageTrackId = getQueryVariable('message_track_id');
      const cloudPartyId = getQueryVariable('cloudPartyid');
      store.dispatch('AuthenticTask',{
        messageTrackId: messageTrackId,
        cloudPartyId: cloudPartyId,
        returnUrl:'/fromAuditToDo?messageTrackId='+messageTrackId+'@cloudPartyId='+cloudPartyId+'@token='
      }).then(response => {
        if(response.status=='406'){
          let url = response.data.url+'?messageTrackId='+response.data.messageTrackId+'&cloudPartyId='+response.data.cloudPartyId+'&success='+response.data.success+'&appid='+response.data.appid+'&return='+response.data.return+'&error='+response.data.error
           window.location.href=url;
        }
      }).catch(err => {
        next({ path: '/404' })
      })
    } else if(to.fullPath.indexOf("/cloudLogin") !== -1){//门户登录
      store.dispatch('Authentic').then(response => {
        if(response.status=='406'){
          let url = response.data.url+'?success='+response.data.success+'&appid='+response.data.appid+'&return='+response.data.return+'&error='+response.data.error
          window.location.href=url;
        }
      }).catch(err => {
        next({ path: '/404' })
      })
    } else if(to.fullPath.indexOf("/taskReadDetailCloud") !== -1){//待阅已阅
      const messageTrackId = getQueryVariable('message_track_id');
      const cloudPartyId = getQueryVariable('cloudPartyid');
      store.dispatch('AuthenticTask',{
        messageTrackId: messageTrackId,
        cloudPartyId: cloudPartyId,
        returnUrl:'/fromAuditToRead?messageTrackId='+messageTrackId+'@cloudPartyId='+cloudPartyId+'@token='
      }).then(response => {
        if(response.status=='406'){
          let url = response.data.url+'?messageTrackId='+response.data.messageTrackId+'&cloudPartyId='+response.data.cloudPartyId+'&success='+response.data.success+'&appid='+response.data.appid+'&return='+response.data.return+'&error='+response.data.error
          window.location.href=url;
        }
      }).catch(err => {
        next({ path: '/404' })
      })
    }else if(to.fullPath.indexOf("/jtauditLogin") !== -1){//审计系统跳转监督追责
      const userName = getQueryVariable('userName');
      const deptId = getQueryVariable('deptId');
      let url = process.env.VUE_APP_BASE_API+'/jtauditCloud/authentic?error=/404&userName='+userName+'&deptId='+deptId+'&returnUrl=/fromAudit?';
      window.location.href=url;
    } else if(to.fullPath.indexOf("/taskReadAllCloud") !== -1){
      store.dispatch('Authentic').then(() => {
        next({ path: '/' })
      }).catch(err => {
        next({ path: '/' })
      })
    }else {
      next(`/sign?redirect=${to.fullPath}`) // 否则全部重定向到登录页
      NProgress.done()
    }
  }

    //判断当前代码版本是否与服务器中代码版本一致，如不一致则刷新页面获取最新
    versionTood.isNewVersion();
})

// 安全过滤函数
const sanitize = (str) => {
  return String(str).replace(/[&<>"'`=/]/g, (match) => {
    return {
      '&': '&amp;',
      '<': '&lt;',
      '>': '&gt;',
      '"': '&quot;',
      "'": '&#39;',
      '/': '&#x2F;',
      '`': '&#x60;',
      '=': '&#x3D;'
    }[match];
  });
};

router.afterEach((to, from) => {
  let watermark = store.getters.waterMark
  let loginInfo = {
    staff: store.getters.name,
    loginIP: store.getters.loginIP
  }
  if(watermark.wmId){
    // 存在有效水印配置
    if (watermark.isGlobal == '1'){
      // 全局水印
      setWaterMark(watermark.sysWatermarkAttrList, loginInfo);
    }else if (watermark.isGlobal == '0'){
      // 指定路径使用水印
      if (watermark.waterMarkPath && watermark.waterMarkPath.indexOf(";"+to.path+";") > -1) {
        setWaterMark(watermark.sysWatermarkAttrList, loginInfo);
      } else {
        clearWatermark()
      }
    }
  }
  //将接到的参数转义
  if(to.query){
    for (const key in to.query) {
      if (Object.prototype.hasOwnProperty.call(to.query, key)) {
        to.query[key] = sanitize(to.query[key]);
      }
    }
  }
  NProgress.done()
})
