<!--禁入限制人员-->
<template>
  <div class="padding_b10 app-limitPersonList">
    <SearchList :searchList="searchList" @on-search="handleQuery" @on-reset="resetQuery"></SearchList>
    <el-table :data="tableList" :header-cell-style="{background:'#F4F8FC',color:'#606266','text-align':'center'}" :cell-style="{'text-align':'center'}">
      <el-table-column label="序号" type="index" min-width="4%" align="center">
        <template slot-scope="scope">
          <table-index
          :index="scope.$index"
          :pageNum="queryParams.pageNum"
          :pageSize="queryParams.pageSize"
          />
        </template>
      </el-table-column>
      <el-table-column label="省分" prop="problemProvName" min-width="8%"/>
      <el-table-column label="地市" prop="problemAreaName"  min-width="10%"/>
      <el-table-column label="姓名" prop="userName"  min-width="8%"/>
      <el-table-column label="性别" prop="sex"  min-width="5%">
        <template slot-scope="scope">
          {{scope.row.sex=='1'?'男':'女'}}
        </template>
      </el-table-column>
      <el-table-column label="所在企业" prop="involAreaName" min-width="17%" show-overflow-tooltip/>
      <el-table-column label="企业层级" prop="orgGradeName" min-width="10%"  show-overflow-tooltip/>
      <el-table-column label="职位" prop="postName" min-width="10%"  show-overflow-tooltip/>
      <el-table-column label="禁入限制开始时间" :formatter="dateFormat" prop="limitStartTime" min-width="12%"/>
      <el-table-column label="禁入限制结束时间" :formatter="dateFormat" prop="limitEndTime" min-width="12%"/>
      <el-table-column label="操作" fixed="right" width="100" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            v-if="scope.row.dispatchStatus != 0"
            size="mini"
            type="text"
            icon="el-icon-search"
            title="查看"
            @click="handleDetails(scope.row)"
          ></el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="limitPersonBaseInfo"
    />
    <limitPersonDetail v-if="dialogVisible" v-on:closeModal="closeModal" :id="id"></limitPersonDetail>
  </div>
</template>

<script>
  import {getBaseInfo} from "@/api/base/limitPerson";
  import limitPersonDetail from "./limitPersonDetail";
  import moment from "moment"
  import SearchList from "../../common/SearchList";

  export default {
    name: "limitPersonList",
    components: { limitPersonDetail, SearchList },
    data() {
      return {
        // 总条数
        total: 0,
        // 表格数据
        tableList: [],
        // 是否显示弹出层
        dialogVisible: false,
        id:'',
        //新增主键
        //日常问题查询 参数
        queryParams: {
          pageNum: 1,
          pageSize: 10,
          problemProvName:'',
          problemAreaName:'',
          userName:'',
        },
        searchList: [
          {
            label: "省分",
            name: "problemProvName",
            value: null,
            type: "Input"
          },
          {
            label: "地市",
            name: "problemAreaName",
            value: null,
            type: "Input"
          },
          {
            label: "姓名",
            name: "userName",
            value: null,
            type: "Input"
          }
        ]
      };
    },
    created() {
      this.limitPersonBaseInfo();
    },
    methods: {
      /**查询禁入限制人员列表*/
      limitPersonBaseInfo() {
       //this.loading = true;
        getBaseInfo(this.queryParams).then(
          response => {
            this.tableList = response.rows;
            this.total = response.total;
            //this.loading = false;
          }
        );
      },
      /** 搜索按钮操作*/
      handleQuery(params) {
        this.queryParams={
          ...this.queryParams,
          ...params,
          pageNum: 1,
        }
        this.limitPersonBaseInfo();
      },
      /**重置按钮操作*/
      resetQuery() {
        this.queryParams = {
          pageNum: 1,
          pageSize: 10,
        };
        this.limitPersonBaseInfo();
      },
      /**查看按钮操作*/
      handleDetails(row) {
        this.dialogVisible=true;
        this.id = row.id;
      },
      /**关闭模态框*/
      closeModal(){
        this.dialogVisible = !this.dialogVisible;
      },
      /*日期处理*/
      dateFormat:function(row,column){
        var date = row[column.property];
        if(date === undefined){
          return ''
        }
        return moment(date).format("YYYY-MM-DD")
      },
    }
  };
</script>
<style rel="stylesheet/scss" lang="scss">
</style>







