<template>
    <div>
      <el-form-item label="对应《违规经营投资责任追究办法》">
        <el-button v-show="edit" type="primary" plain icon="el-icon-plus" size="mini" @click="addSituationRange">新增《违规经营投资责任追究办法》</el-button>
      </el-form-item>
      <el-row>
        <ScopeSituation :edit="edit" v-on:deleteScope="deleteScope" :scopeSituationData="scopeSituationData"></ScopeSituation>
      </el-row>
      <el-row>
        <actualSituationSelect :key="index" ref="select"  :actualProblemId="actualProblemId"
                              :relevantTableId="relevantTableId" :relevantTableName="relevantTableName" @queryRangeList="queryRangeList">
        </actualSituationSelect>
      </el-row>
    </div>
</template>

<script>
  import ScopeSituation from '@/components/ScopeSituation';
  import actualSituationSelect from '@/views/actual/common/actualSituationSelect';
  import {queryActualSituationRange, deleteSituationRangeData} from '@/api/actual/common/actualSituationRange';

  export default {
    name: "actualSituationRange",
    components: {ScopeSituation, actualSituationSelect},
    props: {
      edit: {type: Boolean},
      actualProblemId: {type: String},
      relevantTableId: {type: String},
      relevantTableName: {type: String}
    },
    data() {
      return {
        // status: '',
        index:0,
        scopeSituationData: [],
      };
    },
    created() { this.queryRangeList(); },
    methods: {
      queryRangeList() {
        queryActualSituationRange({actualProblemId: this.actualProblemId, relevantTableId: this.relevantTableId}).then(
          response => {
            this.scopeSituationData = response.data;
          }
        );
      },

      addSituationRange() {
        this.index++;
        this.$nextTick(() => {
          this.$refs.select.show();
        });
      },

      deleteScope(item) {
        let title = "";
        let data = {};
        if (2 === item.type) {
          title = "确认删除该范围情形吗？";
          data.aspectCode = item.id;
        } else {
          title = "确认删除该方面以及方面下涉及范围情形吗？";
          data.id = item.id;
        }

        data.actualProblemId = this.actualProblemId;
        data.relevantTableId = this.relevantTableId;
        this.$modal.confirm(title).then(function () {
          return deleteSituationRangeData(data);
        }).then(response => {
          if (200 === response.code) {
            this.$modal.msgSuccess(response.msg);
            this.queryRangeList();
          } else {
            this.$modal.alertError(response.msg);
          }
        }).catch(() => {});
      }
    }
  }
</script>

<style scoped>

</style>
