{"version": 3, "sources": ["webpack:///./src/api/system/dict/type.js"], "names": ["listType", "query", "request", "url", "method", "params", "getType", "dictId", "addType", "data", "updateType", "delType", "refreshCache", "optionselect"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAqC;;AAErC;AACO,SAASA,QAAQA,CAACC,KAAK,EAAE;EAC9B,OAAOC,8DAAO,CAAC;IACbC,GAAG,EAAE,wBAAwB;IAC7BC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,OAAOA,CAACC,MAAM,EAAE;EAC9B,OAAOL,8DAAO,CAAC;IACbC,GAAG,EAAE,oBAAoB,GAAGI,MAAM;IAClCH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,OAAOA,CAACC,IAAI,EAAE;EAC5B,OAAOP,8DAAO,CAAC;IACbC,GAAG,EAAE,mBAAmB;IACxBC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,UAAUA,CAACD,IAAI,EAAE;EAC/B,OAAOP,8DAAO,CAAC;IACbC,GAAG,EAAE,mBAAmB;IACxBC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,OAAOA,CAACJ,MAAM,EAAE;EAC9B,OAAOL,8DAAO,CAAC;IACbC,GAAG,EAAE,oBAAoB,GAAGI,MAAM;IAClCH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASQ,YAAYA,CAAA,EAAG;EAC7B,OAAOV,8DAAO,CAAC;IACbC,GAAG,EAAE,gCAAgC;IACrCC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASS,YAAYA,CAAA,EAAG;EAC7B,OAAOX,8DAAO,CAAC;IACbC,GAAG,EAAE,gCAAgC;IACrCC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ,C", "file": "js/33.1693388085916.js", "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 查询字典类型列表\r\nexport function listType(query) {\r\n  return request({\r\n    url: '/system/dict/type/list',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询字典类型详细\r\nexport function getType(dictId) {\r\n  return request({\r\n    url: '/system/dict/type/' + dictId,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 新增字典类型\r\nexport function addType(data) {\r\n  return request({\r\n    url: '/system/dict/type',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 修改字典类型\r\nexport function updateType(data) {\r\n  return request({\r\n    url: '/system/dict/type',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 删除字典类型\r\nexport function delType(dictId) {\r\n  return request({\r\n    url: '/system/dict/type/' + dictId,\r\n    method: 'delete'\r\n  })\r\n}\r\n\r\n// 刷新字典缓存\r\nexport function refreshCache() {\r\n  return request({\r\n    url: '/system/dict/type/refreshCache',\r\n    method: 'delete'\r\n  })\r\n}\r\n\r\n// 获取字典选择框列表\r\nexport function optionselect() {\r\n  return request({\r\n    url: '/system/dict/type/optionselect',\r\n    method: 'get'\r\n  })\r\n}"], "sourceRoot": ""}