<!--企业基本信息-->
<template>
  <div class="app-areaList padding_b10">
    <div class="search-top">
      <el-form ref="form" :model="queryParams" label-width="120px">
        <el-row>
          <el-col :span="5">
            <el-form-item label="年度" prop="problemYear">
              <el-date-picker
                style="width: 100%"
                v-model="queryParams.problemYear"
                type="year"
                size="small"
                value-format="yyyy"
                format="yyyy"
                placeholder="请选择年度"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item label="问题涉及单位" prop="problemProvinceCode">
              <el-select
                filterable
                v-model="queryParams.problemProvinceCode"
                placeholder="请选择问题涉及单位"
                clearable
                size="small"
              >
                 <el-option
                  v-for="province in provinces"
                  :key="province.deptId"
                  :label="province.deptName"
                  :value="province.deptId"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item label="问题描述" prop="problemDescription">
              <el-input
                size="small"
                placeholder="请输入问题描述"
                v-model="queryParams.problemDescription"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>

          <el-col :span="9">
            <div class="flex">
              <div class="flex-1"></div>
              <el-form-item>
                <el-button
                  type="primary"
                  icon="el-icon-search"
                  size="mini"
                  @click="handleQuery"
                  >搜索</el-button
                >
                <el-button
                  icon="el-icon-refresh"
                  size="mini"
                  class="el-refresh-btn"
                  @click="resetQuery"
                  >重置</el-button
                >
              </el-form-item>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <el-table
      v-loading="loading"
      :data="tableList"
      :header-cell-style="{
        background: '#F4F8FC',
        color: '#606266',
        'text-align': 'center',
      }"
      :cell-style="{ 'text-align': 'center' }"
    >
      <el-table-column prop="problemYear" label="年度" width="80">
      </el-table-column>
      <el-table-column prop="involveCompany" label="涉及企业名称" width="150" align="left" show-overflow-tooltip>
      </el-table-column>
      <el-table-column prop="companyLevel" label="企业层级" width="80">
      </el-table-column>
      <el-table-column label="违规问题线索有关情况">
        <el-table-column
          prop="problemSource"
          label="问题线索来源"
          width="200"
          align="left" show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          prop="acceptTime"
          label="问题受理时间"
          width="100"
        ></el-table-column>

        <el-table-column
          prop="isPreviousYear"
          label="是否为以前年度定期报告反映的问题"
          width="180"
        ></el-table-column>

        <el-table-column
          prop="problemDescription"
          label="问题描述"
          width="220"
          align="left" show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          prop="violationRegulation"
          label="违反具体规定"
          width="200"
          align="left" show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          prop="problemType"
          label="问题类别"
          width="150"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          prop="domesticOrForeign"
          label="境内（外）"
          width="100"
        >
          <template slot-scope="scope">
            <div>{{ scope.row.domesticOrForeign || "-" }}</div>
          </template>
        </el-table-column>

        <el-table-column
          prop="involveLossRisk"
          label="涉及损失及风险（万元）"
          width="100"
        >
          <template slot-scope="scope">
            <div style="text-align: right">
              {{ scope.row.involveLossRisk || "-" }}
            </div>
          </template>
        </el-table-column>

        <el-table-column
          prop="lossRiskType"
          label="损失风险类别"
          width="100"
          align="left" show-overflow-tooltip
        ></el-table-column>

        <el-table-column
          prop="lossCause"
          label="损失形成主要原因"
          width="220"
          align="left" show-overflow-tooltip
        ></el-table-column>
      </el-table-column>
      <el-table-column label="核查情况">
        <el-table-column
          prop="checkStatus"
          label="核查状态"
          width="100"
        ></el-table-column>

        <el-table-column
          prop="checkTime"
          label="核查时间"
          width="180"
        ></el-table-column>

        <el-table-column
          prop="checkSubject"
          label="核查主体"
          width="180"
        ></el-table-column>

        <el-table-column
          prop="checkIncompleteReason"
          label="未完成核查原因"
          width="220"
        ></el-table-column>
      </el-table-column>

      <el-table-column label="责任追究工作开展情况">
        <el-table-column
          prop="isAccountability"
          label="是否追责"
          width="100"
        ></el-table-column>

        <el-table-column
          prop="nonAccountabilityReason"
          label="未追责原因"
          width="100"
        ></el-table-column>

        <el-table-column
          prop="accountabilityTime"
          label="责任追究时间"
          width="100"
        ></el-table-column>

        <el-table-column
          prop="accountabilityTotalCount"
          label="追责总人数"
          width="100"
        ></el-table-column>

        <el-table-column
          prop="accountabilityTotalTimes"
          label="追责总人次"
          width="100"
        ></el-table-column>

        <el-table-column label="责任追究处理方式（人次）">
          <el-table-column
            prop="organizationProcessing"
            label="组织处理(人次)"
            width="100"
          ></el-table-column>

          <el-table-column label="扣减薪酬">
            <el-table-column
              prop="salaryDeductionTimes"
              label="人次"
              width="100"
            ></el-table-column>
            <el-table-column
              prop="salaryDeductionAmount"
              label="金额(万元)"
              width="120"
            >
              <template slot-scope="scope">
                <div style="text-align: right">
                  {{ scope.row.salaryDeductionAmount || "-" }}
                </div>
              </template>
            </el-table-column>
          </el-table-column>

          <el-table-column
            prop="partyPunishment"
            label="党纪处分(人次)"
            width="120"
          ></el-table-column>

          <el-table-column
            prop="administrativePunishment"
            label="政务处分(人次）"
            width="120"
          ></el-table-column>

          <el-table-column
            prop="restrictedTimes"
            label="禁入限制(人次)"
            width="120"
          ></el-table-column>

          <el-table-column
            prop="transferredTimes"
            label="移送国家监察机关或司法机(人次)"
            width="150"
          ></el-table-column>
        </el-table-column>
      </el-table-column>
      <el-table-column label="问题整改情况">
        <el-table-column
          prop="isReformCompleted"
          label="是否完成整改"
          width="100"
        ></el-table-column>

        <el-table-column label="完善制度情况">
          <el-table-column
            prop="improvedSystemCount"
            label="数量(项)"
            width="100"
          ></el-table-column>

          <el-table-column
            prop="systemName"
            label="制度名称、文号"
            width="180"
            align="left" show-overflow-tooltip
          ></el-table-column>
        </el-table-column>

        <el-table-column label="损失挽回情况">
          <el-table-column
            prop="recoveredAmount"
            label="金额(万元)"
            width="100"
          ></el-table-column>

          <el-table-column
            prop="mainMeasures"
            label="采取的主要措施"
            width="200"
            align="left" show-overflow-tooltip
          ></el-table-column>
        </el-table-column>
      </el-table-column>
      <el-table-column prop="remark" label="备注" width="180" align="left" show-overflow-tooltip></el-table-column>

      <el-table-column prop="accordLevel" label="符合情况" width="250" fixed="right">
        <template slot-scope="scope">
          <el-select
            v-model="scope.row.accordLevel"
            placeholder="请选择符合情况"
            clearable
            size="mini"
            @change="fullyCompliantChange($event, scope.row.id)"
          >
            <el-option
              v-for="dict in selectList"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="params.pageNum"
      :limit.sync="params.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import {
  dailyHistoryProblemAfter2019,
  saveDailyHistoryProblemAfter2019AccordLevel,
  allProvinces
} from "@/api/daily/historyQuestion/historyProblem";
export default {
  name: "Table",
  components: {},
  dicts: [],
  data() {
    return {
      provinces: [],
      // 总条数
      total: 0,
      // 表格数据
      tableList: [],
      //参数
      queryParams: {
        problemYear: "2020",
        problemProvinceCode: "",
        problemDescription: "",
      },
      // 表格页码
      params: {
        pageNum: 1,
        pageSize: 10,
      },
      loading: false,
      selectList: [
        {
          value: "1",
          label: "完全符合",
        },
        {
          value: "2",
          label: "部分符合",
        },
      ],
    };
  },
  created() {
    this.getProvince();
    this.getList();
  },
  methods: {
    getProvince() {
      allProvinces().then(res => {
        this.provinces = res.data;
      });
    },
    /**查询企業基本信息列表*/
    getList() {
      this.loading = true;
      dailyHistoryProblemAfter2019(this.queryParams, this.params).then(
        (response) => {
          this.tableList = response.rows;
          this.total = response.total;
          this.loading = false;
        }
      );
    },
    /** 搜索按钮操作*/
    handleQuery() {
      this.getList();
    },
    /**重置按钮操作*/
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        problemYear: "",
        problemProvinceCode: "",
        problemDescription: "",
      };
      this.getList();
    },
    fullyCompliantChange(val, id) {
      if (val) {
        saveDailyHistoryProblemAfter2019AccordLevel({id: id, accordLevel: val}).then(res => {
          if(res.code== 200){
            this.$message.success(res.msg)
          }
        })
      }
    },
  },
};
</script>
<style rel="stylesheet/scss" lang="scss">
.flex {
  display: flex;
}
.flex-1 {
  flex: 1;
}
</style>






