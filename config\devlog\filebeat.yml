filebeat.inputs:
  - type: log
    enabled: true
    close_inactive: -1
    paths:
      - /var/log/nginx/access.log
      - /var/log/nginx/error.log
    encoding: utf-8
filebeat.config:
  modules:
    path: ${path.config}/modules.d/*.yml
    reload.enabled: false

processors:
  - add_cloud_metadata: ~
  - add_docker_metadata: ~

output.elasticsearch:
  hosts: '************:34261'
  username: 'auditdevng'
  password: 'iCloud#20190128095728'
  index: "filebeatng-%{+yyyy.MM.dd}"
setup.template.name: "filebeatng"
setup.template.pattern: "filebeatng-*"
setup.ilm.enabled: false
