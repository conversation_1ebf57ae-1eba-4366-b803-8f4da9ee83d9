<!--4：核查记录-复核-->
<template>
  <div v-if="formData">
    <div>
      <ModifyrecordBtn
        :key="problemId||relevantTableId||relevantTableName"
        :problemId="problemId"
        :relevantTableId="relevantTableId"
        :relevantTableName="relevantTableName"
        :problemStatus="4"
      ></ModifyrecordBtn>
      <opinion
        :processInstanceId="procInsId"
        :isShow="isShow"
      />
      <BlockCard title="基本信息">
        <el-row>
          <el-form
            ref="elForm"
            :model="formData"
            :rules="rules"
            size="medium"
            label-width="150px"
          >
            <el-col :span="12">
              <el-form-item label="系统编号" prop="auditCode">

                <span>{{ formData.auditCode }}</span>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="问题编号" prop="problemCode">
                <span>{{ formData.problemCode }}</span>
              </el-form-item>
            </el-col>

            <el-col :span="24">
              <el-form-item label="违规事项" prop="problemTitle">

                <span>{{ formData.problemTitle }}</span>
              </el-form-item>
            </el-col>

            <el-col :span="24">
              <el-form-item label="问题线索描述" prop="problemDescribe">
                <span>{{ formData.problemDescribe }}</span>

              </el-form-item>
            </el-col>

            <el-col :span="24">
              <el-form-item label="涉及专业线" prop="specLists">
                <el-checkbox-group :key="formData.specLists" v-model="formData.specLists" size="medium">
                  <el-checkbox
                    v-for="item in specList"
                    :key="item.dictValue"
                    border
                    disabled
                    :label="item.dictValue"
                  >{{ item.dictLabel }}</el-checkbox>
                </el-checkbox-group>
              </el-form-item>
            </el-col>
            <el-col v-if="formData.id" :span="24">
              <ScopeSituation
                :key="problemId"
                ref="scope"
                :edit="edit"
                :problem-id="problemId"
                :relevant-table-id="formData.id"
                :relevant-table-name="formData.relevantTableName"
              />
            </el-col>
            <el-col :span="24">
              <el-form-item
                label="使用责任追究办法说明"
                prop="applicableInvestigateExplain"
              >
                <span>{{ formData.applicableInvestigateExplain }}</span>
              </el-form-item>
            </el-col>

                <el-col :span="24">
              <el-form-item
                label="移送纪检部门"
                prop="isHandoverInspectionDept"
              >
                <el-radio-group
                  disabled
                  v-model="formData.isHandoverInspectionDept"
                  @change="changeDel"
                  size="medium"
                >
                  <el-radio
                    v-for="(item, index) in YesOrNo"
                    :key="index"
                    :label="item.value"
                    >{{ item.label }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item>
                <div class="layui-form-item layui-form-item-sm">
                  <el-col class="layui-input-block">
                    <el-col class="transfer-box">
                      <el-row
                        class="transfer-li ry-row"
                        v-for="(
                          item, index
                        ) in formData.inspectionHandoverRecords"
                        :key="index"
                      >
                        <el-col :span="8" class="transfer-li-info">
                          <el-row>
                            <el-col :span="16" class="ovflowHidden text-left"
                              ><span>{{ item.fromUnitName }}</span></el-col
                            >
                            <el-col :span="8" class="text-right"
                              ><span>{{ item.fromUserName }}</span></el-col
                            >
                          </el-row>
                        </el-col>
                        <el-col :span="2" class="transfer-li-img">——</el-col>
                        <el-col
                          :span="8"
                          class="transfer-li-info ry-row cursor editSecondary"
                        >
                          <el-row>
                            <el-col :span="16" class="ovflowHidden text-left">
                              <span class="toUnitName">{{
                                item.toDeptName ||
                                "请选择被移送纪检部门及人员信息"
                              }}</span>
                            </el-col>
                            <el-col :span="8" class="text-right">
                              <span class="toUserName">{{
                                item.toUserName
                              }}</span>
                            </el-col>
                          </el-row>
                        </el-col>
                        <el-col :span="6" class="transfer-li-edit text-right">
                          <span
                            v-show="item.downloadLink"
                            @click="
                              downloadLink(item.downloadLink, item.fileName)
                            "
                            style="padding-left: 20px"
                            class="table-btn tip-edit float-right ovflowHidden text-red cursor"
                            :title="item.fileName"
                            >{{ item.fileName }}</span
                          >
                        </el-col>
                      </el-row>

                      <el-row
                        class="transfer-li ry-row"
                        v-if="
                          formData.initialInspectionHandoverObj &&
                          formData.isHandoverInspectionDept == '1'
                        "
                      >
                        <el-col :span="8" class="transfer-li-info">
                          <el-row>
                            <el-col :span="16" class="ovflowHidden text-left"
                              ><span>{{
                                formData.initialInspectionHandoverObj
                                  .fromUnitName
                              }}</span></el-col
                            >
                            <el-col :span="8" class="text-right"
                              ><span>{{
                                formData.initialInspectionHandoverObj
                                  .fromUserName
                              }}</span></el-col
                            >
                          </el-row>
                        </el-col>
                        <el-col :span="2" class="transfer-li-img">——</el-col>
                        <el-col
                          :span="8"
                          class="transfer-li-info ry-row cursor editSecondary"
                        >
                          <el-row>
                            <el-col :span="16" class="ovflowHidden text-left">
                              <span class="toUnitName">{{
                                formData.initialInspectionHandoverObj
                                  .toUnitName || "请选择被移送单位及人员信息"
                              }}</span>
                            </el-col>
                            <el-col :span="8" class="text-right">
                              <span class="toUserName">{{
                                formData.initialInspectionHandoverObj.toUserName
                              }}</span>
                            </el-col>
                          </el-row>
                        </el-col>
                      </el-row>
                    </el-col>
                  </el-col>
                </div>
              </el-form-item>
            </el-col>
          </el-form>
        </el-row>
      </BlockCard>
      <Remind
        :key="formData.actualFlag"
        :actualFlag="formData.actualFlag"
      ></Remind>
      <BlockCard title="核查方案">
        <el-row>
          <el-form
            ref="elForm"
            :model="formData"
            :rules="rules"
            size="medium"
            label-width="150px"
          >
            <el-col :span="12">
              <el-form-item label="核查组组长" prop="checkGroupLeaderList" class="input-btn">
                <div class="list1">
                  <div v-for="(item,index) of leaderList" :key="index" class="list1-one">
                    <span>{{ item.userName }}</span>
                  </div>
                </div>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="核查组成员" prop="checkGroupMemberList" class="input-btn">
                <div class="list1">
                  <div v-for="(item,index) of memberList" :key="index" class="list1-one">
                    <span>{{ item.userName }}</span>
                  </div>
                </div>
              </el-form-item>
            </el-col>

            <el-col :span="8">
              <el-form-item label="核查时间" prop="checkTime">
                <el-date-picker
                  v-model="formData.checkTime"
                  format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd"
                  :style="{ width: '100%' }"
                  placeholder="请选择核查时间"
                  clearable
                  readonly
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="核查单位" prop="" class="input-btn">

                <div class="list1">
                  <div v-for="(item,index) of subjectOrgDiv" :key="index" class="list1-one">
                    <span>{{ item.checkOrgName }}</span>
                  </div>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="核查主体" prop="checkSubject">
                <span>{{ formData.checkSubject }}</span>
              </el-form-item>
            </el-col>

            <el-col :span="8">
              <el-form-item
                label="需要进一步核查"
                prop="furtherVerificationFlag"
              >
                <el-radio-group
                  disabled
                  v-model="formData.furtherVerificationFlag"
                  size="medium"
                >
                  <el-radio
                    v-for="(item, index) in seriousAdverseEffectsFlagOptions"
                    :key="index"
                    :label="item.value"
                  >{{ item.label }}</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="需要外聘专业机构"
                prop="employMechanismFlag"
              >
                <el-radio-group
                  disabled
                  v-model="formData.employMechanismFlag"
                  size="medium"
                >
                  <el-radio
                    v-for="(item, index) in seriousAdverseEffectsFlagOptions"
                    :key="index"
                    :label="item.value"
                  >{{ item.label }}</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>

            <el-col :span="8">
              <el-form-item
                label="需要专业部门参与"
                prop="professionalDepartFlag"
              >
                <el-radio-group
                  disabled
                  v-model="formData.professionalDepartFlag"
                  size="medium"
                >
                  <el-radio
                    v-for="(item, index) in seriousAdverseEffectsFlagOptions"
                    :key="index"
                    :label="item.value"
                  >{{ item.label }}</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>

            <el-col v-if="formData.furtherVerificationFlag == '1'" :span="24">
              <el-form-item
                label="审计部组织或委托机构驻派审计部核查"
                prop="auditCheckSituation"
              >
                <span>{{ formData.auditCheckSituation }}</span>
              </el-form-item>
            </el-col>

            <el-col v-if="formData.employMechanismFlag=='1'" :span="24">
              <el-form-item label="外部机构参与情况" prop="employMechanismSituation">

                <span>{{ formData.employMechanismSituation }}</span>
              </el-form-item>
            </el-col>

            <el-col v-if="formData.professionalDepartFlag=='1'" :span="24">
              <el-form-item
                label="参与核查的专业部门及人员情况"
                prop="professionalDepartSituation"
              >
                <span>{{ formData.professionalDepartSituation }}</span>

              </el-form-item>
            </el-col>

            <el-col :span="24">
              <el-form-item label="核查结果" prop="checkResult">
                <span>{{ formData.checkResult }}</span>
              </el-form-item>
            </el-col>
          </el-form>
        </el-row>
      </BlockCard>

      <BlockCard title="损失及影响">
        <el-row>
          <el-form
            ref="elForm"
            :model="formData"
            :rules="rules"
            size="medium"
            label-width="160px"
          >

           <el-col :span="8">
              <el-form-item label="是否产生资产损失" prop="lossStateAssetsFlag">
                <el-radio-group v-model="formData.lossStateAssetsFlag" size="medium">
                  <el-radio
                     disabled
                    v-for="(item, index) in YesOrNo"
                    :key="index"
                    :label="item.value"
                    >{{ item.label }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>

            <el-col :span="8">
              <el-form-item label="预估损失金额（万元）" prop="lossAmount">
                 <span>{{ (formData.lossAmount).toFixed(2) }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="预估损失风险（万元）" prop="lossRisk">
           <span>{{ (formData.lossRisk).toFixed(2) }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="损失关联关系"
                prop="lossCategory"
              >
                <el-select
                  disabled
                  v-model="formData.lossCategory"
                  placeholder="请选择损失关联关系"
                  clearable
                  :style="{ width: '100%' }"
                >
                  <el-option
                    v-for="(item, index) in formData.lossCategoryList"
                    :key="index"
                    :label="item.dictLabel"
                    :value="item.dictValue"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="24" />
             <el-col :span="8">
              <el-form-item label="是否产生不良影响" prop="isAdverseEffect">
                <el-radio-group
                disabled
                  v-model="formData.isAdverseEffect"
                  size="medium"
                >
                  <el-radio
                    v-for="(item, index) in whetherEffectOptions"
                    :key="index"
                    :label="item.value"
                    :disabled="item.disabled"
                    >{{ item.label }}</el-radio
                  >
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="16">
              <el-form-item
                label="对应不良影响"
                prop="correspondingAdverseEffects"
                v-if="formData.isAdverseEffect"
              >
                <el-select
                disabled
                  v-model="formData.correspondingAdverseEffects"
                  :style="{ width: '100%' }"
                  clearable="clearable"
                  multiple="multiple"
                  value=""
                >
                  <el-option
                    v-for="(item, index) in dict.type
                      .corresponding_adverse_effect"
                    :key="index"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item
                label="造成的不良影响"
                prop="adverseEffects"
                v-if="formData.isAdverseEffect"
              >
                <span>{{formData.adverseEffects}}</span>
              </el-form-item>
            </el-col>

            <el-col :span="8">
              <el-form-item
                label="是否产生严重不良影响"
                prop="seriousAdverseEffectsFlag"
              >
                <el-radio-group
                disabled
                  v-model="formData.seriousAdverseEffectsFlag"
                  size="medium"
                >
                  <el-radio
                    v-for="(item, index) in YesOrNo"
                    :key="index"
                    :label="item.value"
                    :disabled="item.disabled"
                    >{{ item.label }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col v-show="formData.seriousAdverseEffectsFlag" :span="16">
              <el-form-item
                label="严重不良影响描述"
                prop="seriousAdverseEffectsDesc"
              >
                <el-select
                  v-model="formData.seriousAdverseEffectsDesc"
                  placeholder="请选择严重不良影响描述"
                  clearable
                  disabled
                  :style="{ width: '100%' }"
                  value="formData.seriousAdverseEffectsDesc"
                >
                  <el-option
                    v-for="(item, index) in dict.type.VIOLD_ADVER_EFFECT_DES"
                    :key="index"
                    :label="item.label"
                    :value="item.value"
                    :disabled="item.disabled"
                  />
                </el-select>
              </el-form-item>
            </el-col>

          </el-form>
        </el-row>
      </BlockCard>

      <BlockCard title="责任认定">
        <el-row>
          <el-form
            ref="elForm"
            :model="formData"
            :rules="rules"
            size="medium"
            label-width="110px"
          >
            <el-col :span="24">
              <el-form-item
                label="是否追究上一级有关人员责任"
                prop="investigateUpperlevelFlag"
              >
                <el-radio-group
                  disabled
                  v-model="formData.investigateUpperlevelFlag"
                  size="medium"
                >
                  <el-radio
                    v-for="(item, index) in seriousAdverseEffectsFlagOptions"
                    :key="index"
                    :label="item.value"
                    :disabled="item.disabled"
                  >{{ item.label }}</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>

            <el-col :span="24" v-if="formData.investigateUpperlevelFlag">
              <el-form-item
                label="追究上一级有关人员责任原因"
                prop="upperlevelResponsibility"
              >
                <span>{{ formData.upperlevelResponsibility }}</span>
              </el-form-item>
            </el-col>

            <!-- <el-col :span="24">
              <el-form-item
                label="涉及单位及人员的认定信息"
                prop="problemDescribe"
              >
                <el-button
                  type="primary"
                  class="float-right"
                  @click="department"
                >修改涉及/单位人员</el-button>
              </el-form-item>
            </el-col> -->

            <!--<el-col :span="24">-->
              <!--<el-form-item>-->
                <!--<PersList-->
                  <!--ref="pers"-->
                  <!--edit="true"-->
                  <!--:problem-id="problemId"-->
                  <!--:relevant-table-id="formData.id"-->
                  <!--:relevant-table-name="formData.relevantTableName"-->
                <!--/>-->
              <!--</el-form-item>-->
            <!--</el-col>-->

            <el-col :span="24">
              <el-form-item
                label="涉及单位及人员的认定信息"
              >
                <el-collapse v-model="activeNames" accordion>
                  <el-collapse-item
                    v-for="(item,index) of list"
                    :key="index"
                    :title="item.mainFlag == '1'?'涉及单位：' + item.involCompanyName+'（主责单位）':'涉及单位：' + item.involCompanyName"
                    :name="index"
                  >
                    <div class="invol-company-content el-row">
                      <el-col :span="12" class="invol-company-content-1">
                        <el-form-item
                          label="单位责任类型"
                          prop="dutyType"
                        >
                          <el-radio-group
                            v-model="item.dutyType"
                            size="medium"
                            disabled
                          >
                            <el-radio
                              v-for="(
                              item2, index2
                            ) in item.involCompanyDutyList"
                              :key="index2"
                              :label="item2.dictValue"
                              @change="involCompanyDutyListFun(item,item2.dictValue)"
                            >{{ item2.dictLabel }}
                            </el-radio>
                          </el-radio-group>
                        </el-form-item>
                      </el-col>
                      <el-col :span="24" class="invol-company-content-3">
                        <div class="invol-company-content-3-ul">
                          <div class="invol-company-content-3-li" v-for="(obj,i) in item.involPersonList">
                            <div class="invol-company-content-li-header">
                              <span class="invol-company-header-left">{{obj.userName}}</span>
                            </div>
                            <div class="invol-company-li-content el-row">
                              <div class="el-row invol-company-li-content-li">
                                <el-col :span="12" >
                                  <div class="invol-company-content-label vio-file-border">组织</div>
                                  <div class="invol-company-content-value vio-file-border vio-file-border-left">{{obj.involOrgName}}</div>
                                </el-col>
                                <el-col :span="12" >
                                  <div class="invol-company-content-label vio-file-border vio-file-border-left">职务</div>
                                  <div class="invol-company-content-value radio-duan-style vio-file-border-left" style="padding:0px 10px; display:flex;       align-items: center;   font-size: 12px; white-space: nowrap;">
                                 <span style="line-height:20px"> 职务： {{obj.postName }} <span v-if='obj.postName'>；</span>    <span style="padding-left:4px">干部类型：{{ fromatComon(obj.cadreType,dict.type
                                          .viold_cadre_type) }}</span> <span v-if='obj.cadreType'>；</span>


                                    <span style="padding-left:4px">
                                      <span>是否二级单位领导：</span>
                                      <span v-if="obj.isSecondUnitLeader=='1'">是</span>
                                       <span v-if="obj.isSecondUnitLeader=='0'">否</span>
                                   
                                  </span>
</span>
                                  </div>
                                </el-col>

                              </div>
                              <div class="el-row invol-company-li-content-li">
                                <el-col :span="12" >
                                  <div class="invol-company-content-label vio-file-border vio-file-border-right">责任类型</div>
                                  <div class="invol-company-content-value vio-file-border">
                                    <el-radio-group
                                      :key="obj.dutyType"
                                      v-model="obj.dutyType[0]"
                                      size="medium"
                                      disabled
                                    >
                                      <el-radio
                                        v-for="item4 in item.involvePersonDutyList"
                                        :key="item4.dictValue"
                                        :label="item4.dictValue"
                                        @change="checkbox(obj,item4.dictValue)"
                                      >{{ item4.dictLabel }}
                                      </el-radio>
                                    </el-radio-group>
                                  </div>
                                </el-col>
                                <el-col :span="12" >
                                  <div class="invol-company-content-label vio-file-border vio-file-border-left">责任认定原因标准</div>
                                  <div class="invol-company-content-value vio-file-border-left">
                                   {{obj.dutyReasonStandard}}
                                  </div>
                                </el-col>
                              </div>
                              <div class="el-row invol-company-li-content-li">
                                <el-col :span="12" >
                                  <div class="invol-company-content-label vio-file-border vio-file-border-right">是否适用从重处罚</div>
                                  <div class="invol-company-content-value vio-file-border ">
                                    <div style="display: flex; align-items: center">
                                      <div style="padding: 0px 12px 0 0">
                                        <el-radio-group
                                          disabled
                                          v-model="obj.heavierFlag"
                                          size="medium"
                                        >
                                          <el-radio
                                            v-for="(
                                      item5, index5
                                    ) in seriousAdverseEffectsFlagOptions2"
                                            :key="index5"
                                            :label="item5.value"
                                            @change="heavierFlagFun(obj)"
                                          >{{ item5.label }}
                                          </el-radio>
                                        </el-radio-group>
                                      </div>

                                      <div
                                        style="
                                    flex: 1;
                                  "
                                      >
                                       {{obj.heavierPunishReason}}
                                      </div>
                                    </div>
                                  </div>
                                </el-col>
                                <el-col :span="12" >
                                  <div class="invol-company-content-label vio-file-border vio-file-border-left">是否适用从轻处罚或免除处罚</div>
                                  <div class="invol-company-content-value vio-file-border-left">
                                    <div style="display: flex; align-items: center">
                                      <div style="padding: 0px 12px">
                                        <el-radio-group
                                          disabled
                                          v-model="obj.lighterImpunityFlag"
                                          size="medium"
                                        >
                                          <el-radio
                                            v-for="(
                                      item6, index6
                                    ) in seriousAdverseEffectsFlagOptions2"
                                            :key="index6"
                                            :label="item6.value"
                                            @change="lighterImpunityFlagFun(obj)"
                                          >{{ item6.label }}
                                          </el-radio>
                                        </el-radio-group>
                                      </div>
                                      <div
                                        style="flex: 1;"
                                        @dblclick="changeEnddate(i, 'tag4',item.involPersonList,index)"
                                      >
                                       {{obj.lighterImpunityReason}}
                                      </div>
                                    </div>
                                  </div>
                                </el-col>
                              </div>
                            </div>
                          </div>
                          <div>
                            <div  class="vio-file-box">
                              <div  class="vio-file-div el-row">
                                <div  class="vio-file-type flex vio-file-border el-col el-col-4">
                                  <span >上传被处罚单位及人员建议</span>
                                </div>
                                <div  class="vio-file-content el-col el-col-20">
                                  <ul  class="vio-file-list">
                                    <div  class="vio-file-li ry-row flex el-row" v-for="(obj,i) in item.fileList">
                                      <div  class="vio-file-name el-col el-col-13"><i   class="el-icon-tickets"></i><span
                                      >{{obj.fileName}}</span></div>
                                      <div  class="vio-file-user icon-grey el-col el-col-2"><span
                                      >{{obj.uploaderName}}</span></div>
                                      <div
                                        class="vio-file-time layui-col-md3 layui-col-sm3 icon-grey el-col el-col-5">
                                        {{obj.createTime}}
                                      </div>
                                      <div  class="vio-file-del layui-col-md2 layui-col-sm2 text-center el-col el-col-4">
                                        <a  href="javascript:void(0);" title="下载" class="table-btn tip-edit" @click="fileDownload(obj)">
                                          <i class="el-icon-bottom"></i>
                                        </a>
                                      </div>
                                    </div>
                                  </ul>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </el-col>
                    </div>
                  </el-collapse-item>
                </el-collapse>
              </el-form-item>

            </el-col>
          </el-form>
        </el-row>
      </BlockCard>

      <BlockCard
        title="附件列表"
      >
        <File
          :key="problemId||formData.id||formData.relevantTableName"
          ref="file"
          :edit="edit"
          :problem-id="problemId"
          :relevant-table-id="formData.id"
          :relevant-table-name="formData.relevantTableName"
          flow-type="VIOL_DAILY"
          problem-status="4"
          flowKey = "SupervisionDailyReport"
        />
      </blockcard></div>
    <el-dialog :visible.sync="visibleTree" width="90%" class="tree-body-dialog" append-to-body title="人员选择">
      <Tree v-if="visibleTree"
            :key="problemId||formData.id||formData.relevantTableName"
            :problemId="problemId"
            :relevantTableId="formData.id"
            :relevantTableName="formData.relevantTableName"
      ></Tree>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="closeTree">保存</el-button>
      </div>
    </el-dialog>
    <el-dialog :visible.sync="fourTreeVisible" width="90%" append-to-body title="人员选择">
      <FourTree
        ref="fourTreeFun"
        :key="problemId||formData.id||formData.relevantTableName"
        :problemId="problemId"
        :relevantTableId="formData.id"
        :relevantTableName="formData.relevantTableName"
      ></FourTree>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="closeTree">保存</el-button>
      </div>
    </el-dialog>
    <el-dialog :visible.sync="VisibleCheckTree" width="60%" append-to-body :title="title">
      <CheckTree
        v-if="VisibleCheckTree"
        :key="index"
        ref="checkTree"
        :url="url"
        :selectTree="selectTree"
        :params="params"
        @list="persList"
      />
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="savePers">保存</el-button>
      </div>
    </el-dialog>
    <el-dialog :visible.sync="VisibleRadioTree" width="60%" append-to-body title="核查单位">
      <RadioTree
        v-if="VisibleRadioTree"
        :key="problemId"
        ref="radioTree"
        url="/colligate/violDailyCheck/getOrgTree"
        :select-tree="[]"
        :params="params"
        @accept="orgList"
      />
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="getOrgTree">保存</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
  import BlockCard from '@/components/BlockCard'
  import Remind from './../../components/remind';
  import ScopeSituation from './../scopeSituation/scopeSituationData' // 范围情形展示
  import { queryDailyCheckInfo, queryInvolveCompanyAndPerson, savePersonPostName, saveCompanyDuty,delCheckGroupMember, delPersonInvolved, checkAndSaveCheckInfo,saveCheckGroupMember,queryRelevpersonByType } from '@/api/daily/process/verificationRecord'
  import File from './../../components/fileUpload'// 附件
  import Tree from './../tree'// tree
  import FourTree from './../tree/fourIndex'
  import { saveViewVerrify } from '@/api/daily/process/taskTodoViewVerify'
  import PersList from './../tree/persList'// tree
  import CheckTree from './../tree/checkTree'// checkTree
  import RadioTree from './../tree/radioTree';//tree
  import {deleteViolFile} from "@/api/components/index";
  import ModifyrecordBtn from '../modifyRecord/btn';
  import opinion from '../modifyRecord/opinion';
  export default {
    dicts: ['VIOLD_DAILY_SPEC','VIOLD_ADVER_EFFECT_DES','corresponding_adverse_effect', 'viold_cadre_type'],
    components: {
      Remind,
      Tree,
      FourTree,
      BlockCard,
      ScopeSituation,
      File,
      PersList,
      CheckTree,
      RadioTree,ModifyrecordBtn,
      opinion
    },
    props: {
      isShow:{
        type: String,
        default: '0'
      },
      procInsId:{
        type: String
      },
      problemId: {
        type: String,
        default: () => {}
      },
      key: {
        type: String,
        default: () => {}
      }

    },
    data() {
      return {
        url:'/colligate/violDailyRelevperson/checkStaffOrgTree',
        selectTree:[],
        title:'',

        edit: false,
        showTip:false,
        index:1,
        params:{
          name:'',
          problemId:'',
          relevantTableId:'',
          relevantTableName:'',
          relevorgType:'CHECK_GROUP_LEADER'
        },
        rowData: '',
        involCompanyDuty: '', // 单位责任类型
        flag: false,
        fourTreeVisible:false,
        visible: false,
        visibleTree: false,
        VisibleCheckTree:false,
        VisibleRadioTree:false,
        formData: {
          actualFlag: '',
          adverseEffects: '',
          adverseEffectsDescList: [],
          applicableInvestigateExplain: '',
          auditCheckSituation: '',
          auditCode: '',
          checkGroupLeaderList: [],
          checkGroupMemberList: [],
          checkOrgId: '',
          checkOrgLevel: '',
          checkOrgName: '',
          checkResult: '',
          checkSubject: '',
          checkTime: '',
          employMechanismFlag: '',
          employMechanismSituation: '',
          expandFileTypeOptions: [],
          furtherVerificationFlag: '',
          id: '',
          investigateUpperlevelFlag: '',
          lossAmount: '',
          lossCategory: '',
          lossCategoryList: [],
          lossRisk: '',
          problemCode: '',
          problemDescribe: '',
          problemId: '',
          problemTitle: '',
          professionalDepartFlag: '',
          professionalDepartSituation: '',
          relevantTableName: '',
          seriousAdverseEffectsDesc: '',
          seriousAdverseEffectsFlag: '',
          specLists: [],
          specList: [],
          specSelectedList: [],
          upperlevelResponsibility: ''
        },
        list: [], // 涉嫌单位数据
        specList: [

        ],
        rules: {
        },
        seriousAdverseEffectsFlagOptions: [
          {
            label: '是',
            value: 1
          },
          {
            label: '否',
            value: 0
          }
        ],
        seriousAdverseEffectsFlagOptions2: [
          {
            label: '是',
            value:"1"
          },
          {
            label: '否',
            value: "0"
          }
        ],
            YesOrNo: [
        {
          label: "是",
          value: 1,
        },
        {
          label: "否",
          value: 0,
        },
      ],
          whetherEffectOptions: [
        {
          label: "是",
          value: 1,
        },
        {
          label: "否",
          value: 0,
        },
      ],
        problemSourceList: [],
        activeNames: [],
        leaderList:[],
        memberList:[],
        subjectOrgDiv:[],
        relevantTableId:'',
        relevantTableName:''
      }
    },
    computed: {},
    watch: {},
    created() {
      this.queryDailyCheckInfo()
    },
    mounted() {},
    methods: {
      fromatComon(value, list) {
        let lastLabel = "-";
        if (value && list.length > 0) {
          list.forEach((element) => {
            if (element.value === value) {
              lastLabel = element.label;
            }
          });
        }
        return lastLabel;
      },
         //下载
    downloadLink(url, fileName) {
      this.download(url, {}, fileName);
    },
      /**下载文件*/
      fileDownload(obj){
        this.download('/sys/attachment/downloadSysAttachment/'+obj.attachmentId, {
        },obj.fileName)
      },
      //返回数据
      persList(data){
        let list=[];
        this.index++;
        if(!data.length)
          return false;
        for (let i = 0; i < data.length; i++) {
          list.push(Number(data[i].id));
        }
        this.formData.checkGroupLeaderList = data;
        let query = { postIds: list };
        this.params.personType = this.params.relevorgType;
        saveCheckGroupMember({...query,...this.params}).then(
          response => {
            const { code, data } = response;
            if (code === 200) {
              this.QueryRelevpersonByType();
              this.VisibleCheckTree = false;
            }
          }
        )
      },
      //查询组长与组员
      QueryRelevpersonByType(){
        //组长
        queryRelevpersonByType({
          problemId:this.problemId,
          relevantTableId:this.formData.id,
          relevantTableName:this.formData.relevantTableName,
          personType:'CHECK_GROUP_LEADER'
        }).then(
          response => {
            const { code, data } = response;
            if (code === 200) {
              this.leaderList=data;
            }
          }
        );
        //组员
        queryRelevpersonByType({
          problemId:this.problemId,
          relevantTableId:this.formData.id,
          relevantTableName:this.formData.relevantTableName,
          personType:'CHECK_GROUP_MEMBER'
        }).then(
          response => {
            const { code, data } = response;
            if (code === 200) {
              this.memberList=data;
            }
          }
        );
      },
      // 请求数据详情
      queryDailyCheckInfo() {
        queryDailyCheckInfo({ problemId: this.problemId }).then(
          response => {
            const { code, data } = response
            if (code === 200) {
              var list = []
              list = Object.assign(this.formData, data)
              const array = []
              const specSelectedList = data.specSelectedList
              for (let i = 0, len = specSelectedList.length; i < len; i++) {
                if (specSelectedList[i].specCode) {
                  array.push(specSelectedList[i].specCode)
                }
              }
              this.params.problemId = this.problemId
              this.params.relevantTableId = data.id
              this.params.relevantTableName = data.relevantTableName
              this.formData.specLists = array
              this.formData = list
              this.relevantTableId = data.id
              this.relevantTableName = data.relevantTableName
              this.specList = data.specList
              this.QueryRelevpersonByType();
              this.$forceUpdate()
              this.queryInvolveCompanyAndPerson()
              if(this.formData.checkOrgName){
                this.subjectOrgDiv=[
                  {
                    checkOrgName:this.formData.checkOrgName,
                  }
                ];
              }else{
                this.subjectOrgDiv=[];
              }
              this.$nextTick(() => {
                //   this.$refs.pers.DueryDepartmentSelectInfo()
                this.$refs.file.ViolationFileItems()
              })
              this.$emit('closeLoading');
            }
          }
        )
      },
      // 涉嫌单位查询
      queryInvolveCompanyAndPerson() {
        queryInvolveCompanyAndPerson({ problemId: this.problemId, relevantTableId: this.formData.id }).then(
          response => {
            const { code, data } = response
            if (code === 200) {
              data.forEach((ele1, index) => {
                this.activeNames.push(index)
                ele1.involPersonList.forEach(ele2 => {
                  if (ele2.dutyType === null) {
                    ele2.dutyType = []
                  } else {
                    ele2.dutyType = [ele2.dutyType]
                  }
                })
              })

              this.list = data
            }
          }
        )
      },
      addTdClass({ row, column }) {
        console.log(column)
        if (
          column.label === '职务' ||
          column.label === '责任认定原因标准' ||
          column.label === '是否适用从重处罚' ||
          column.label === '是否适用从轻处罚或免除处罚'
        ) {
          return 'editStyle'
        }
      },
      //关闭
      closeTree(){
        this.visibleTree = false;
        this.fourTreeVisible = false;
        this.queryInvolveCompanyAndPerson();
      },

      /** 提交数据*/
      nextStep() {
        this.$emit('handle', 1)
      },
      /** 保存数据*/
      publicSave() {

      },
      resetForm() {
        this.$refs['elForm'].resetFields()
      },
      // 打开弹窗
      show() {
        this.visible = true
      },

      // 添加核查组组长
      addCheckGroupLeaderList(type) {
        if(type===1){
          this.title='核查组组长';
          this.params.relevorgType='CHECK_GROUP_LEADER';
        }else if(type===2){
          this.title='核查组成员';
          this.params.relevorgType='CHECK_GROUP_MEMBER';
        }
        this.VisibleCheckTree = true;
      },
      // 删除核查组成员
      deleteCheckGroupMemberList(item, index) {
        this.formData.checkGroupMemberList.splice(index, 1)
        this.$forceUpdate()
      },
      // 删除核查单位
      deleteSubjectOrgDiv(item, index) {
        this.formData.subjectOrgDiv.splice(index, 1)
        this.$forceUpdate()
      },
      // 添加核查单位
      addSubjectOrgDiv() {
        this.VisibleRadioTree = true;
      },
      //保存核查单位
      getOrgTree(){
        this.$refs.radioTree.save();
      },
      //返回核查单位
      orgList(data){
        let obj = data[0];
        if(data){
          this.formData.subjectOrgDiv = [
            {
              checkOrgId:obj.id,
              checkOrgName:obj.name,
              checkOrgLevel:obj.orgGrade,
              checkSubject:obj.checkSubject
            }
          ];
          this.formData.checkOrgId = obj.checkOrgId;
          if (obj.checkSubject === '' || obj.checkSubject == null) {
            if (obj.orgGrade === 'P' || obj.orgGrade === 'G') {
              this.formData.checkSubject = obj.name + '违规责任办公室';
            } else if (obj.orgGrade === 'A') {
              this.formData.checkSubject = obj.name + '核查小组';
            }
          } else {
            this.formData.checkSubject = obj.checkSubject;
          }
          this.formData.checkOrgName = obj.name;
          this.formData.checkOrgLevel = obj.orgGrade;
        }
        this.VisibleRadioTree = false;
      },

    }
  }
</script>
<style scoped lang="scss">
  .file-label{
    display: inline-block;
    padding:10px 0px;
  }
  .input-btn {
    ::v-deep .el-form-item__content {
      display: flex;
      button {
      }
    }
  }
  .float-right {
    float: right;
  }
  .edit-span {
    white-space: normal;
    overflow-y: auto;
    overflow-wrap: break-word;
    word-break: normal;
    height: 61px;
    line-height: 61px;
    text-align: left;
    padding: 0px 10px;
    display: block;
  }
  ::v-deep .editStyle {
    padding: 0px !important;
  }
  ::v-deep .editStyle div.cell {
    padding: 0px !important;
  }

  ::v-deep .el-collapse-item__header {
    height: 40px;
    background: #E6F7FF;
    border-radius: 1px;
    color: #4E98FF;
    padding: 0 16px;
    box-sizing: border-box;
  }

  ::v-deep .editStyle .el-input--mini .el-input__inner {
    height: 56px;
    line-height: 56px;
    border: 0px;
  }
  .list1 {
    overflow: hidden;
    .list1-one {
      background-color: #e6f7ff;
      color: #40a9ff;
      margin: 0 10px 2px 10px;
      float: left;
      height: 30px;
      line-height: 30px;
      padding: 0 12px 0 12px;
      border-radius: 2px;
      .close {
        padding: 8px;
        cursor: pointer;
      }
    }
  }
  .bottom-line {
    padding: 10px 0;
    text-align: center;
    border-top: 1px solid #d9d9d9;
    color: #f5222d !important;
  }

  .invol-company-content {
    margin-top: 6px;
    background: #F6F7F8;
    padding: 7px 14px;
    box-sizing: border-box;
    ::v-deep .el-form-item__label {
      text-align: left !important;
      width: 97px !important;
    }
    ::v-deep .el-form-item__content {
      margin-left: 97px !important;
    }
    .invol-company-content-2 {
      margin: 4px 0;
      ::v-deep .el-button--primary {
        color: #F5222D;
        background: #fee9ea;
        border-color: #fba7ab;
      }
      ::v-deep .el-button--primary:hover {
        background: #f74e57;
        border-color: #f74e57;
        color: #FFF;
      }
    }
    .invol-company-content-3 {
      background: #fff;
      padding: 12px 22px;
      box-sizing: border-box;
      .invol-company-content-3-ul {
        .invol-company-content-3-li {
          margin-bottom: 16px;
          .invol-company-content-li-header {
            position: relative;
            .invol-company-header-left {
              position: relative;
              display: inline-block;
              font-size: 15px;
              font-weight: bold;
              color: #333333;
              padding: 0 12px;
              background: #fff;
              z-index: 99;
            }
            .invol-company-header-left::before {
              content: " ";
              position: absolute;
              left: 0;
              right: 0;
              top: 5px;
              z-index: 2;
              width: 4px;
              height: 16px;
              background: #F5222D;
              opacity: 1;
            }
            .invol-company-header-right {
            }
          }
          .invol-company-content-li-header::before {
            content: " ";
            position: absolute;
            left: 0;
            right: 0;
            width: calc(100% - 26px);
            top: 13px;
            z-index: 2;
            height: 1px;
            background: #CED3D8;
          }
          .invol-company-li-content {
            border: 1px solid #d9d9d9;
            margin-top: 10px;
            .invol-company-li-content-li:last-child {
              border-bottom: 0 solid #d9d9d9;
            }
            .invol-company-li-content-li {
              border-bottom: 1px solid #d9d9d9;
              .vio-file-border {
                // border-right: 1px solid #E2E7F1;
              }
              .vio-file-border-left {
                border-left: 1px solid #E2E7F1;
              }
              .vio-file-border-right {
                border-right: 1px solid #E2E7F1;
              }
              .invol-company-content-label {
                float: left;
                width: 150px;
                min-height: 46px;
                line-height: 18px;
                background: #F4F8FC;
                opacity: 1;
                padding: 0 10px 0 20px;
                box-sizing: border-box;
                font-size: 14px;
                font-weight: 400;
                color: #333333;
                display: flex;
                align-items: center;
                position: absolute;
    top: 0;
    bottom: 0;
              }
              .invol-company-content-value {
                float: left;
                width: calc(100% - 150px);
                min-height: 46px;
                line-height: 46px;
                padding: 0 22px;
                box-sizing: border-box;
                margin-left: 150px;
              }
            }
          }
        }
      }
      .flex{
        display: flex;
        align-items: center;
      }
      .vio-file-box{
        border: 1px solid #d9d9d9;
        .vio-file-div{
          display: flex;
          width: 100%;
          border-bottom: 1px solid #d9d9d9;
          .vio-file-border{
            // border-right: 1px solid #d9d9d9;
          }
          .vio-file-type{
            background-color: #F4F8FC;
            color: #73777a;
            min-height: 48px;
            padding: 0 10px;
            box-sizing: border-box;
            .text-red{
              color: #f5222d !important;
            }
          }
          .vio-file-download{
            justify-content: center;
            .vio-file-down{
              padding: 0 4px;
              border-right: 1px solid #d9d9d9;
            }
            i{
              color: #f5222d;
            }
            .vio-file-down:last-child{
              border-right-width: 0;
            }
          }

          .vio-file-content{
            min-height: 48px;
            .vio-file-list{
              padding:0;
              margin:0;
              .vio-file-li{
                padding-left: 10px;
                box-sizing: border-box;
                border-bottom: 1px solid #d9d9d9;
                min-height: 48px;
                .vio-file-name{
                  i{
                    margin-right:6px;
                  }
                }
                .vio-file-user,.vio-file-time{
                  height: 48px;
                  display: flex;
                  align-items: center;
                  color: #a9b0b4;
                }
                .vio-file-del{
                  text-align: center;
                  i{
                    color:#f5222d;
                    margin:0 6px;
                  }
                }
              }
              .vio-file-li:last-child {
                border-bottom-width: 0;
              }
            }
          }
        }
        .vio-file-div:last-child {
          border-bottom-width: 0;
        }
        ::v-deep.upload-file-uploader{
          margin-bottom: 0;
        }
      }
    }

  }


.radio-duan-style .el-radio {margin-right:12px}
::v-deep .el-radio-group{
  flex:1
}
  .tree-body-dialog ::v-deep.el-dialog__body{
    padding:0 20px;
  }
</style>
