import request from '@/utils/request'


//生成情形范围修改记录
export function generateSituationRangeModifyRecord(problemId,relevantTableId){
  console.info("生成情形范围修改记录");
  return request({
    url: '/colligate/violDailyModifyRecord/generateSituationRangeModifyRecord',
    method: 'post',
    data:JSON.stringify({
      problemId:problemId
      ,businessId:relevantTableId
    })
  })
}

//生成业务数据修改记录
export function generateBusinessModifyRecord(problemId,relevantTableId,relevantTableName){
  console.info("生成业务数据修改记录");
  return request({
    url: '/colligate/violDailyModifyRecord/generateBusinessModifyRecord',
    method: 'post',
    data:JSON.stringify({
      problemId:problemId
      ,businessId:relevantTableId
      ,businessTable:relevantTableName
    })
  })
}



