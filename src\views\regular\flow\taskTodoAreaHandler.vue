<!--定期待办页-->
<template>
   <div>
     <BlockCard
       title="上报说明"
     >
       <div class="position">
         <div class="position-select">
           <div class="float-right " style="margin-left:15px;" v-if="formData.startGrade === 'G' && formData.regularReportStatus !== 'other'">
             <el-button  type="primary" plain icon="el-icon-plus" size="mini" @click="violDailyAccept">{{exportBtn}}</el-button>
           </div>
           <div class="float-right " style="margin-left:15px;"  v-if="formData.backNum > 0">
             <el-button  type="primary" plain icon="el-icon-search" size="mini" @click="showHistoryInfo">{{historyBtn}}</el-button>
           </div>
         </div>

       </div>
       <el-form ref="elForm"  size="medium" label-width="118px">
         <el-row>
           <el-col :span="6">
             <el-form-item label="报告类型" >
               <span>{{formData.regularReportStatus=='other'?'其他报告':'定期报告'}}</span>
             </el-form-item>
           </el-col>
         <el-col :span="6">
           <el-form-item label="上报年度" >
             <span>{{formData.reportYear}}</span>
           </el-form-item>
         </el-col>
         <el-col :span="6">
           <el-form-item label="上报区间" >
             <span>{{formData.reportStartTime}} - {{formData.reportEndTime}}</span>
           </el-form-item>
         </el-col>
         <el-col :span="6">
           <el-form-item label="上报截止日期" >
             <span>{{formData.reportCloseTime}}</span>
           </el-form-item>
         </el-col>
         <el-col :span="24">
           <el-form-item label="标题">
             <span>{{formData.reportTitle}}</span>
           </el-form-item>
         </el-col>
         <el-col :span="24">
           <el-form-item label="上报要求">
             <span>{{formData.reportRequire}}</span>
           </el-form-item>
         </el-col>
         </el-row>
       </el-form>
     </BlockCard>
     <!--<BlockCard title="附件列表">
       <FileUpload
         :edit='edit'
         :problemId="formData.reportUnitId"
         :relevantTableId="formData.reportUnitId"
         :relevantTableName="formData.businessTable"
         flowType="VIOL_REGULAR"
         :problemStatus="formData.regularReportStatus"
         :isNoReport = "formData.isNoReport"
         :problemsIds = "formData.problemsIds"
         linkKey="a003"
         ref="file"
         :flowKey = "centerVariable.flowKey"
       ></FileUpload>
     </BlockCard>-->
     <BlockCard title="附件列表">
       <RegularTemplateFileUpload :edit='edit'
                                  :problemId="formData.reportUnitId"
                                  :relevantTableId="formData.reportUnitId"
                                  :relevantTableName="formData.businessTable"
                                  flowType="VIOL_REGULAR"
                                  :problemStatus="formData.regularReportStatus"
                                  :isNoReport="formData.isNoReport"
                                  :problemsIds="formData.problemsIds"
                                  linkKey="a003"
                                  ref="flowTemplate"
                                  :flowKey="centerVariable.flowKey">
       </RegularTemplateFileUpload>
     </BlockCard>
     <BlockCard
       v-if="false"
       title="定期报告附表"
     >
       <el-form ref="elForm" size="medium" label-width="118px">
       <el-row class="problem_list margin-b10">
         <el-col :span="24">
           <el-form-item label="是否0报告">
             <span>{{formData.isNoReport == '1' ? '是' : '否'}}</span>
           </el-form-item>
         </el-col>
       </el-row>
       </el-form>
       <el-table
       :class="formData.regularProblems.length>6?'el-table-change':''"
         border
         :data="formData.regularProblems"
         @selection-change="handleSelectionChange"
         ref="table"
         :height="formData.regularProblems.length>6?'700':''"
       >
         <el-table-column
           fixed
           align="center"
           label="序号"
           type="index"
           width="50">
         </el-table-column>
         <el-table-column  align="center" label="上报单位" show-overflow-tooltip prop="reportUnitName" width="200"/>
         <el-table-column  align="center" label="系统编号" show-overflow-tooltip prop="auditCode" width="200"/>
         <el-table-column align="center" label="涉及企业名称" show-overflow-tooltip prop="involveEnterprise" width="200"/>
         <el-table-column align="center" label="企业层级" show-overflow-tooltip prop="enterpriseGrade" width="200"/>

         <el-table-column align="center" label="违规问题线索有关详情" show-overflow-tooltip prop="prov1" width="2000">
           <el-table-column align="center" label="问题线索来源" show-overflow-tooltip prop="problemSource" width="200"/>
           <el-table-column align="center" label="问题受理时间" show-overflow-tooltip prop="acceptTime" width="200"/>
           <el-table-column align="center" label="是否为以前年度定期报告反映的问题" show-overflow-tooltip prop="isPreviousYear" width="200"/>
           <el-table-column align="center" label="问题描述" prop="problemDescribe" show-overflow-tooltip width="200"/>
           <el-table-column align="center" label="问题类别" prop="problemAspect" show-overflow-tooltip width="200"/>
           <el-table-column align="center" label="违反具体规定" prop="problemSituation" show-overflow-tooltip width="200"/>
           <el-table-column align="center" label="境内（外）" prop="domesticOrForeign" show-overflow-tooltip width="200">
             <template slot-scope="scope">
               <div>
                 <el-radio-group v-model="scope.row.domesticOrForeign" @change="replacingProvincialData(1,scope.row.dailyProblemId,scope.row.domesticOrForeign)">
                   <el-radio label="境外" value="境外"></el-radio>
                   <el-radio label="境内" value="境内"></el-radio>
                 </el-radio-group>
               </div>
             </template>
           </el-table-column>
           <el-table-column align="center" label="涉及损失及风险（万元）" show-overflow-tooltip prop="lossAmountRisk" width="200"/>
           <el-table-column align="center" label="损失风险类别（一般/较大/重大资产损失）" show-overflow-tooltip prop="lossRiskType" width="200"/>
           <el-table-column align="center" label="损失形成主要原因" show-overflow-tooltip prop="lossReason" width="200"/>
         </el-table-column>

         <el-table-column align="center" label="核查情况" show-overflow-tooltip prop="prov2" width="800">
           <el-table-column align="center" label="核查状态" show-overflow-tooltip prop="checkStatus" width="200"/>
           <el-table-column align="center" label="核查时间" show-overflow-tooltip prop="checkTime" width="200"/>
           <el-table-column align="center" label="核查主体" show-overflow-tooltip prop="checkSubject" width="200"/>
           <el-table-column align="center" label="未完成核查原因" show-overflow-tooltip prop="notCheckedReason" width="200">
             <template slot-scope="scope">
               <div v-if="scope.row.needFillNotCheckReason">
                 <el-autocomplete
                   v-if="formData.startGrade === 'G' && formData.regularReportStatus !== 'other'"
                   type="textarea"
                   v-model="scope.row.notCheckedReason"
                   :fetch-suggestions="querySearchAsync"
                   @focus="subordinateUnitRelatedData(scope.row,'notCheckedReason')"
                   placeholder="请输入内容"
                   @blur="replacingProvincialData(2,scope.row.dailyProblemId,scope.row.notCheckedReason)"
                 ></el-autocomplete>
                 <el-autocomplete
                   v-else
                   type="textarea"
                   v-model="scope.row.notCheckedReason"
                   placeholder="请输入内容"
                   @blur="replacingProvincialData(2,scope.row.dailyProblemId,scope.row.notCheckedReason)"
                 ></el-autocomplete>
               </div>
               <span v-else>{{scope.row.notCheckedReason}}</span>
             </template>
           </el-table-column>
         </el-table-column>
         <el-table-column align="center" label="责任追究工作开展情况" show-overflow-tooltip prop="prov3" width="2400">
           <el-table-column align="center" label="是否追责" show-overflow-tooltip prop="isAccountability" width="200"/>
           <el-table-column align="center" label="未追责原因" show-overflow-tooltip prop="notAccountabilityReason" width="200"/>
           <el-table-column align="center" label="责任追究时间" show-overflow-tooltip prop="accountabilityTime" width="200"/>
           <el-table-column align="center" label="追责总人数" show-overflow-tooltip prop="accountabilityPersonNumber" width="200"/>
           <el-table-column align="center" label="追责总人次" show-overflow-tooltip prop="accountabilityPersonItem" width="200"/>
           <el-table-column align="center" label="责任追究处理方式（人次）" show-overflow-tooltip prop="prov24" width="1400">
             <el-table-column align="center" label="组织处理（人次）" show-overflow-tooltip prop="orgHandleItem" width="200"/>
             <el-table-column align="center" label="扣减薪酬" show-overflow-tooltip prop="a2" width="400">
               <el-table-column align="center" label="人次" show-overflow-tooltip prop="deductionPayItem" width="200"/>
               <el-table-column align="center" label="金额（万元）" show-overflow-tooltip prop="deductionAmount" width="200"/>
             </el-table-column>
             <el-table-column align="center" label="党纪处分（人次）" show-overflow-tooltip prop="partyPunishmentItem" width="200"/>
             <el-table-column align="center" label="政务处分（人次）" show-overflow-tooltip prop="governmentPunishmentItem" width="200"/>
             <el-table-column align="center" label="禁入限制（人次）" show-overflow-tooltip prop="debarPersonItem" width="200"/>
             <el-table-column align="center" label="移送国家检察机关或司法机关（人次）" show-overflow-tooltip prop="transferPersonItem" width="200"/>
             <el-table-column align="center" label="其他（人次）" show-overflow-tooltip prop="processingOtherItem" width="150"/>
           </el-table-column>
         </el-table-column>
         <el-table-column align="center" label="问题整改情况" show-overflow-tooltip prop="prov4" width="1000">
           <el-table-column  align="center" label="是否完成整改" show-overflow-tooltip prop="isCompleteReform" width="200"/>
           <el-table-column align="center" label="完善制度情况" show-overflow-tooltip prop="prov44" width="400">
             <el-table-column align="center" label="数量（项）" show-overflow-tooltip prop="perfectSystemNumber" width="200"/>
             <el-table-column align="center" label="制度名称、文号（无文号请注明出台时间）" show-overflow-tooltip prop="perfectSystemName" width="200"/>
           </el-table-column>
           <el-table-column align="center" label="损失挽回情况" show-overflow-tooltip prop="prov44" width="400">
             <el-table-column align="center" label="金额（万元）" show-overflow-tooltip prop="retrieveLossAmount" width="200"/>
             <el-table-column align="center" label="采取的主要措施" show-overflow-tooltip prop="takeMainStep" width="200"/>
           </el-table-column>
         </el-table-column>
         <el-table-column align="center" label="备注" prop="remark" width="200">
           <template slot-scope="scope">
             <el-autocomplete
               v-if="formData.startGrade === 'G' && formData.regularReportStatus !== 'other'"
               type="textarea"
               v-model="scope.row.remark"
               :fetch-suggestions="querySearchAsync"
               @focus="toSubordinateUnitRelatedData(scope.row,'remark')"
               placeholder="请输入内容"
               @blur="replacingProvincialData(3,scope.row.dailyProblemId,scope.row.remark)"
             ></el-autocomplete>
             <el-autocomplete
               v-else
               type="textarea"
               v-model="scope.row.remark"
               placeholder="请输入内容"
               @blur="replacingProvincialData(3,scope.row.dailyProblemId,scope.row.remark)"
             ></el-autocomplete>
           </template>
         </el-table-column>
       </el-table>
     </BlockCard>
     <!--新增下级-->
     <addRegularB
       :key="index"
       ref="add"
       v-on:ViolRegularList="refreshList"
       :parentId="formData.regularReportId"
       title="新增上报"
     ></addRegularB>
     <!--编辑-->
     <editRegularB
       :key="index"
       ref="edit"
       v-on:ViolRegularList="refreshList"
       :regularReportId="newCode"
       title="编辑上报"
     ></editRegularB>
     <!--查看-->
     <showRegular
       :key="index"
       ref="showInfo"
       v-on:ViolRegularList="refreshList"
       :regularReportId="newCode"
       title="上报详情"
     ></showRegular>
     <showHistory
       :key="reportUnitId"
       ref="showHistory"
       :reportUnitCode="reportUnitCode"
       :regularReportId="regularReportId"
       :orgGrade="orgGrade"
     >
     </showHistory>
   </div>
</template>

<script>
  import BlockCard from '@/components/BlockCard';
  import FileUpload from './../../components/fileUpload/regularIndex';//附件
  import RegularTemplateFileUpload from '@/views/regular/common/regularTemplateFileUpload';
  import {
    regularHandlerFillData
    ,savedHandlerFillData
    ,replacingProvincialData
    ,subordinateUnitRelatedData//保存表格
    ,checkReportTimeForReload
    ,saveHandlerFillData
  } from '@/api/regular/flow/taskTodoAreaHandler'
  import showRegular from './../details/regularDetail';
  import addRegularB from './../add/addRegularB';
  import editRegularB from './../add/editRegularB';
  import showHistory from './../details/showHistoryList';//显示历史数据
    export default {
        name: "taskTodoAreaHandler",
        components:{
          BlockCard
          ,FileUpload
          ,showRegular
          ,addRegularB
          ,editRegularB
          ,RegularTemplateFileUpload
          ,showHistory//显示历史版本数据
        },
        props: {
          selectValue: {
            type: Object
          },
          centerVariable: {
            type: Object
          },
        },
      data(){
        return{
          exportBtn:'新增下级单位上报',
          historyBtn:'查看历史版本',
          formData:{},
          edit:true,
          queryList:[],
          newCode:'',
          index:0,
          reportIntervals:[],
          reportIntervalsCode:'',
          reportData:{unitStatisticsData:[]},
          multipleSelection:[],//选中的值
          reportUnitCode:''
          ,regularReportId:''
          ,reportUnitId:''
          ,orgGrade:''
        }
      },
      created(){
        this.RegularHandlerFillData();
      },
      methods:{
        //初始数据获取
          RegularHandlerFillData(){
           if(this.selectValue.linkKey=="a001" ){
             regularHandlerFillData(this.centerVariable.busiKey).then(
               response => {
                 if (response.code === 200){
                   this.formData = response.data;
                   this.reportUnitCode = this.formData.reportUnitCode;
                   this.regularReportId = this.formData.regularReportId;
                   this.reportUnitId = this.formData.reportUnitId;
                   this.orgGrade = this.formData.startGrade;
                   this.$nextTick(() => {
                     /*this.$refs.file.ViolationFileItems();*/
                     this.$refs.flowTemplate.regularReportTemplateItems();
                   })
                 }else{
                   this.$modal.msgError(response.msg);
                 }
               }
             )
           }else{
             savedHandlerFillData(this.centerVariable.busiKey).then(
               response => {
                 if (response.code === 200){
                   this.formData = response.data;
                   this.reportUnitCode = this.formData.reportUnitCode;
                   this.regularReportId = this.formData.regularReportId;
                   this.reportUnitId = this.formData.reportUnitId;
                   this.orgGrade = this.formData.startGrade;
                   this.$nextTick(() => {
                     // this.$refs.file.ViolationFileItems();
                     this.$refs.flowTemplate.regularReportTemplateItems();
                   })
                 }else{
                   this.$modal.msgError(response.msg);
                 }
               }
             )
           }
          },
        //保存表格数据
        replacingProvincialData(type, id, value) {
          let data = {};
          if (type === 1) {//修改境内（外）
            data = {
              reportUnitId: this.formData.reportUnitId,
              dailyProblemId: id,
              isSaveDomesticOrForeign: true,
              domesticOrForeign: value,
              needFillNotCheckReason: false,
              isSaveRemark: false
            }
          } else if (type === 2) {//未核查原因
            if(value && value.length>1024){
              this.$message.error('未完成核查原因字符数不能超过1024!');
              return false;
            }
            data = {
              reportUnitId: this.formData.reportUnitId,
              dailyProblemId: id,
              isSaveDomesticOrForeign: false,
              needFillNotCheckReason: true,
              notCheckedReason: value,
              isSaveRemark: false
            }
          } else if (type === 3) {//备注
            data = {
              reportUnitId: this.formData.reportUnitId,
              dailyProblemId: id,
              isSaveDomesticOrForeign: false,
              needFillNotCheckReason: false,
              isSaveRemark: true,
              remark: value
            }
          }
          replacingProvincialData(data).then(
            response =>{
              if (response.code === 200) {
              } else {
                this.$modal.msgError(response.msg);
              }
            }
          )
        },
        //获取意见
        toSubordinateUnitRelatedData(row,type){
          let params = {
             reportStartTime: this.formData.reportStartTime,
             reportEndTime: this.formData.reportEndTime,
             problemProvCode: row.reportUnitCode,
             problemAreaCode: row.acceptUnitCode,
             dailyProblemId: row.dailyProblemId
           }
          subordinateUnitRelatedData(params).then(response => {
            if (response.data && response.data[type]) {
              this.queryList = [{'value':'意见'}];
            } else {
             this.queryList = []
            }
          });

        },
        //查询快捷意见
        querySearchAsync(queryString, cb) {
          clearTimeout(this.timeout);
          this.timeout = setTimeout(() => {
            cb(this.queryList)
          }, 2000 * Math.random());
        },
        //新增下级
        violDailyAccept(){
          checkReportTimeForReload(this.formData.regularReportId).then(response => {
            if (response.code === 200) {
              var data = response.data;
              if(data.codeFlag==true){//校验通过
                this.exportBtn = '新增下级单位上报';
                this.index++;
                this.$nextTick(()=>{
                    this.$refs.add.show();
                  }
                )
              }else{
                if(data.codeType === "0"){
                  this.exportBtn = '新增下级单位上报';
                  this.newCode = data.code;
                  this.index++;
                  this.$nextTick(()=>{
                      this.$refs.edit.show();
                    }
                  )
                }else{
                  this.exportBtn = '查看下级单位上报';
                  this.newCode = data.code;
                  this.index++;
                  this.$nextTick(()=>{
                      this.$refs.showInfo.show();
                    }
                  )
                }
              }
            }
           });
        },
        //显示历史数据+本条数据
        showHistoryInfo(){
          this.$refs.showHistory.show();
        },
        //流程提交
        nextStep() {
          let data={linkKey:this.selectValue.linkKey,
            problemId : this.formData.regularReportId,
            busiTable : this.formData.businessTable,
            busiTableId : this.formData.reportUnitId
          };
          if(this.formData.regularProblems && this.formData.regularProblems.problemDescribe && this.formData.regularProblems.problemDescribe.lenth>1024){
            this.$message.error('问题描述的字符数应小于1024!');
            return false;
          }
          if(this.formData.regularProblems && this.formData.regularProblems.problemAspect && this.formData.regularProblems.problemAspect.lenth>64){
            this.$message.error('问题类别的字符数应小于64!');
            return false;
          }
          if(this.formData.regularProblems && this.formData.regularProblems.problemSituation && this.formData.regularProblems.problemSituation.lenth>255){
            this.$message.error('违反具体规定的字符数应小于255!');
            return false;
          }
          if(this.formData.regularProblems && this.formData.regularProblems.lossReason && this.formData.regularProblems.lossReason.lenth>1024){
            this.$message.error('损失形成主要原因的字符数应小于1024!');
            return false;
          }
          if(this.formData.regularProblems && this.formData.regularProblems.checkSubject && this.formData.regularProblems.checkSubject.lenth>64){
            this.$message.error('核查主体的字符数应小于64!');
            return false;
          }
          if(this.formData.regularProblems && this.formData.regularProblems.notCheckedReason && this.formData.regularProblems.notCheckedReason.lenth>1024){
            this.$message.error('未完成核查原因的字符数应小于1024!');
            return false;
          }
          if(this.formData.regularProblems && this.formData.regularProblems.notAccountabilityReason && this.formData.regularProblems.notAccountabilityReason.lenth>2048){
            this.$message.error('未追责原因的字符数应小于2048!');
            return false;
          }
          if(this.formData.regularProblems && this.formData.regularProblems.perfectSystemName && this.formData.regularProblems.perfectSystemName.lenth>255){
            this.$message.error('制度名称、文号（无文号请注明出台时间）的字符数应小于255!');
            return false;
          }
          if(this.formData.regularProblems && this.formData.regularProblems.takeMainStep && this.formData.regularProblems.takeMainStep.lenth>2048){
            this.$message.error('采取的主要措施的字符数应小于2048!');
            return false;
          }
          if(this.formData.regularProblems && this.formData.regularProblems.remark && this.formData.regularProblems.remark.lenth>512){
            this.$message.error('备注的字符数应小于512!');
            return false;
          }

          saveHandlerFillData({
            ...data,...this.formData
          }).then(
              response => {
                  if (response.code===200) {
                    this.$emit('handle',1,{provinceId:this.formData.problemProvCode,areaId:this.formData.problemAreaCode});
                  }else{
                    this.$modal(response.msg);
                  }

              }
            );
         },
        //保存
        save(){
          this.$modal.msgSuccess('保存成功！')
        },
      }
    }
</script>

<style scoped>
  .position-select {
    position: absolute;
    width: 100%;
    text-align: right;
    top: -54px;
  }
  .el-dialog__body{
    background: #fff !important;
  }
  .el-table-change{
    height: 700px!important;
  }
</style>
