<template>
  <div>
    <el-tabs v-model="activeName">
      <el-tab-pane label="日常报送" name="0">
        <div class="process-x">
          <dailyDetail
            v-if="dailyProblemId||dailyProblemStatus"
            :dailyProblemId="dailyProblemId"
            :dailyProblemStatus="dailyProblemStatus"
            :selectValue="formData"
          ></dailyDetail>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
  // import dailyDetail from './detail';
  import {dailyProblemInfo} from '@/api/actual/index';

  export default {
    name: "details",
    props: {
      selectValue: {
        type:Object
      },
      activeName:{
        type:String
      }
    },
    components: {
    dailyDetail: (d) => import("./detail")
    },
    data() {
      return {
        dailyProblemId:'',
        dailyProblemStatus:'',
        actualProblemId:'',
        actualProblemStatus:'',
        formData:{}
      }
    },
    mounted() {
      this.ProblemStatus();
    },
    methods: {
      //获取流程环节
      ProblemStatus(){
        dailyProblemInfo({actualProblemId: this.selectValue.actualProblemId}).then(response => {
          this.formData = response.data;
          this.dailyProblemId = response.data.id;
          this.dailyProblemStatus = response.data.status;
        });
        //接口：/colligate/violActual/dailyProblemInfo
        //dailyProblemInfo({dailyProblemId: this.selectValue.dailyProblemId}).then(response => {
        /*const MIS_DATA={"data":{"actualFlag":1,"adverseEffects":"\u8FDD\u89C4\u8FFD\u8D23\u6D4B\u8BD5\u6807\u98981108","auditCode":"SD20211227003","cancelReason":"","cancelTime":"","createBy":228657,"createByOrg":"00370000053","createLoginName":"liuyi59","createPostId":819154,"createTime":"2021-10-11 17:50:07","createUserName":"\u5218\u6BC5","dealPostId":"","deletedFlag":"0","fillTime":"20211227","handoverType":"","id":"d52a142eac8a42b3a510279c32395603","isCancel":"0","lossAmount":0.0,"lossCategory":"2","lossReason":"\u8FDD\u89C4\u8FFD\u8D23\u6D4B\u8BD5\u6807\u98981108","lossRisk":2000.0,"orgGrade":"P","orgGradePre":"P","problemAreaCode":"003700","problemAreaCodePre":"003700","problemAreaName":"\u7701\u672C\u90E8","problemAreaNamePre":"\u7701\u672C\u90E8","problemCode":"202100119","problemDescribe":"\u8FDD\u89C4\u8FFD\u8D23\u6D4B\u8BD5\u6807\u98981108","problemFlag":"1","problemNumberCode":119,"problemProvCode":"0037","problemProvCodePre":"0037","problemProvName":"\u5C71\u4E1C","problemProvNamePre":"\u5C71\u4E1C","problemYear":2021,"problemYearNum":2021,"procEndTime":"","procInsId":"2243ae3e281e4718816e4eef101c8f7d","procStartTime":"2021-11-08","seriousAdverseEffectsDesc":"2","seriousAdverseEffectsFlag":0,"status":"6","stepDealFlag":0,"stepDealLinkKey":"","stepDealTime":"","updateBy":228657,"updateByOrg":"00370000053","updateLoginName":"liuyi59","updatePostId":819154,"updateTime":"2021-12-29 10:48:09"},"httpCode":200,"msg":"\u8BF7\u6C42\u6210\u529F","timestamp":1642866200895};
        this.formData=MIS_DATA.data;
         //this.formData=response.data;
          problemStatus({dailyProblemId: this.selectValue.dailyProblemId}).then(response => {
            this.dailyProblemId = response.data.dailyProblemId;
            this.dailyProblemStatus = response.data.dailyProblemStatus;
            this.actualProblemId = response.data.actualProblemId;
            this.actualProblemStatus = response.data.actualProblemStatus;
         // });
        });*/
      }
    }
  }
</script>

<style scoped lang="scss">
  .process-x {
    overflow: hidden;
    height: calc(80vh - 165px);
  }
</style>
