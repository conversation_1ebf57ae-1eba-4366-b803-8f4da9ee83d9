{"version": 3, "sources": ["webpack:///src/views/workflow/tasklist/gateway/taskToDo.vue", "webpack:///./src/views/workflow/tasklist/gateway/taskToDo.vue?dd42", "webpack:///./src/views/workflow/tasklist/gateway/taskToDo.vue?ab30", "webpack:///./src/views/workflow/tasklist/gateway/taskToDo.vue?53e2", "webpack:///./src/views/workflow/tasklist/gateway/taskToDo.vue", "webpack:///./src/views/workflow/tasklist/gateway/taskToDo.vue?05e1", "webpack:///./src/views/workflow/tasklist/gateway/taskToDo.vue?0149", "webpack:///./src/views/workflow/tasklist/gateway/taskToDo.vue?5755"], "names": ["components", "Opinion", "Process", "Daily", "Regular", "DailyProcess", "<PERSON><PERSON><PERSON>", "History", "RegularProcess", "Actual", "ActualProcess", "inheritAttrs", "props", "tabFlag", "type", "String", "data", "index", "saveBtnType", "centerVariable", "flowCfgLink", "visible", "tabPosition", "processType", "activities", "loadingInstance", "computed", "watch", "created", "selectValue", "linkKey", "$route", "query", "processInstanceId", "readLinkId", "taskId", "typeId", "show", "mounted", "methods", "openLoading", "$store", "dispatch", "Loading", "service", "target", "document", "querySelector", "background", "spinner", "text", "lock", "closeLoading", "close", "Tasktodopath", "Histoicflow", "saveBtn", "publicSave", "$refs", "todo", "nextStep", "handle", "object", "process", "window", "opener", "open", "_this", "tasktodopath", "then", "response", "dataRows", "url", "<PERSON><PERSON><PERSON>", "_this2", "histoicflow", "taburls", "flowKey"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4HA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEe;EACfA,UAAA;IACAC,OAAA,EAAAA,uDAAA;IACAC,OAAA,EAAAA,2DAAA;IACAC,KAAA,EAAAA,6DAAA;IACAC,OAAA,EAAAA,2DAAA;IACAC,YAAA,EAAAA,iEAAA;IACAC,SAAA,EAAAA,yDAAA;IACAC,OAAA,EAAAA,uDAAA;IACAC,cAAA,EAAAA,mEAAA;IACAC,MAAA,EAAAA,0DAAA;IACAC,aAAA,EAAAA;EACA;EACAC,YAAA;EACAC,KAAA;IACAC,OAAA;MACAC,IAAA,EAAAC;IACA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,KAAA;MACAC,WAAA;MACAC,cAAA;MACAC,WAAA;MACAC,OAAA;MAAA;MACAC,WAAA;MACAC,WAAA;MACAC,UAAA;MACAV,IAAA;MACAW,eAAA;IACA;EACA;EACAC,QAAA;EACAC,KAAA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,WAAA;MACAC,OAAA,OAAAC,MAAA,CAAAC,KAAA,CAAAF,OAAA;MACAG,iBAAA,OAAAF,MAAA,CAAAC,KAAA,CAAAC,iBAAA;MACAC,UAAA,OAAAH,MAAA,CAAAC,KAAA,CAAAE,UAAA;MACAC,MAAA,OAAAJ,MAAA,CAAAC,KAAA,CAAAG,MAAA;MACAC,MAAA,OAAAL,MAAA,CAAAC,KAAA,CAAAI;IACA;IACA,KAAAC,IAAA;EACA;EACAC,OAAA,WAAAA,QAAA;EACAC,OAAA;IACA;IACAC,WAAA,WAAAA,YAAA;MACA,KAAAC,MAAA,CAAAC,QAAA;MACA,KAAAjB,eAAA,GAAAkB,OAAA,CAAAC,OAAA;QACAC,MAAA,EAAAC,QAAA,CAAAC,aAAA;QACAC,UAAA;QACAC,OAAA;QAAA;QACAC,IAAA;QAAA;QACAC,IAAA;MACA;;MACA,YAAA1B,eAAA;IACA;IACA;IACA2B,YAAA,WAAAA,aAAA;MACA,KAAA3B,eAAA,CAAA4B,KAAA;IACA;IACA,WACAhB,IAAA,WAAAA,KAAA;MACA,KAAAiB,YAAA;MACA,KAAAC,WAAA;IACA;IACA,aACAC,OAAA,WAAAA,QAAA1C,IAAA;MACA,KAAAI,WAAA,GAAAJ,IAAA;IACA;IACA,SACA2C,UAAA,WAAAA,WAAA;MACA,KAAAC,KAAA,CAAAC,IAAA,CAAAF,UAAA;IACA;IACA,UACAG,QAAA,WAAAA,SAAA;MACA,KAAAF,KAAA,CAAAC,IAAA,CAAAC,QAAA;IACA;IACA,YACAC,MAAA,WAAAA,OAAA/C,IAAA,EAAAgD,MAAA;MACA,KAAAJ,KAAA,CAAAK,OAAA,CAAAF,MAAA,CAAA/C,IAAA,EAAAgD,MAAA;IACA;IACA,WACAT,KAAA,WAAAA,MAAA;MACAW,MAAA,CAAAC,MAAA;MACAD,MAAA,CAAAE,IAAA;MACAF,MAAA,CAAAX,KAAA;MACA;IACA;IACA,UACAC,YAAA,WAAAA,aAAA;MAAA,IAAAa,KAAA;MACAC,6EAAA,MAAAvC,WAAA,EAAAwC,IAAA,CACA,UAAAC,QAAA;QACAH,KAAA,CAAAhD,cAAA,GAAAmD,QAAA,CAAAtD,IAAA,CAAAuD,QAAA,IAAApD,cAAA;QACAgD,KAAA,CAAA/C,WAAA,GAAAkD,QAAA,CAAAtD,IAAA,CAAAuD,QAAA,IAAAnD,WAAA;QACA+C,KAAA,CAAArD,IAAA,GAAAwD,QAAA,CAAAtD,IAAA,CAAAuD,QAAA,IAAAC,GAAA;QACAL,KAAA,CAAAM,OAAA;QACAN,KAAA,CAAAlD,KAAA;QACAkD,KAAA,CAAA9C,OAAA;MACA,CACA;IACA;IACA,UACAkC,WAAA,WAAAA,YAAA;MAAA,IAAAmB,MAAA;MACAC,4EAAA,MAAA9C,WAAA,CAAAI,iBAAA,EAAAoC,IAAA,CACA,UAAAC,QAAA;QACAI,MAAA,CAAAlD,UAAA,GAAA8C,QAAA;MACA,CACA;IACA;IACA,uBACAG,OAAA,WAAAA,QAAA;MACAG,wEAAA,MAAAzD,cAAA,CAAA0D,OAAA,OAAAhD,WAAA,CAAAC,OAAA,OAAAjB,OAAA,EAAAwD,IAAA,CACA,UAAAC,QAAA,GAEA,CACA;IACA;EACA;AACA,CAAC,E;;;;;;;;;;;;ACjQD;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK,sBAAsB;AAC3B;AACA,iBAAiB,8BAA8B;AAC/C;AACA;AACA,WAAW,6BAA6B;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB;AACnB;AACA,iBAAiB;AACjB,eAAe;AACf;AACA,uCAAuC,SAAS,aAAa,EAAE;AAC/D;AACA;AACA,uCAAuC,SAAS,aAAa,EAAE;AAC/D;AACA;AACA,uCAAuC,SAAS,aAAa,EAAE;AAC/D;AACA;AACA;AACA;AACA;AACA,2BAA2B,SAAS,6BAA6B,EAAE;AACnE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,uDAAuD;AAC/E,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB,mBAAmB;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB,mBAAmB;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB,mBAAmB;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA,aAAa;AACb,4BAA4B,SAAS,6BAA6B,EAAE;AACpE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA,aAAa;AACb;AACA;AACA;AACA,wBAAwB,kCAAkC;AAC1D,eAAe;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,eAAe,2CAA2C,EAAE;AACrE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA,eAAe;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA,eAAe;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA,eAAe;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB,qBAAqB,mBAAmB;AACxC;AACA,eAAe;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;AC9OA;AACA,kCAAkC,mBAAO,CAAC,iHAA4D;AACtG;AACA;AACA,cAAc,QAAS,gEAAgE,gCAAgC,qCAAqC,mDAAmD,mDAAmD,iBAAiB,GAAG,wCAAwC,wBAAwB,GAAG,qCAAqC,qBAAqB,oBAAoB,mBAAmB,8BAA8B,GAAG,8CAA8C,8BAA8B,GAAG,8CAA8C,kCAAkC,GAAG,2CAA2C,kCAAkC,yBAAyB,wBAAwB,iBAAiB,mBAAmB,GAAG;AAClyB;AACA;;;;;;;;;;;;ACNA;;AAEA;AACA,cAAc,mBAAO,CAAC,o1BAA8e;AACpgB;AACA,4CAA4C,QAAS;AACrD;AACA;AACA,UAAU,mBAAO,CAAC,mIAAsE;AACxF,8CAA8C,qCAAqC;AACnF;AACA,GAAG,KAAU,EAAE,E;;;;;;;;;;;;ACXf;AAAA;AAAA;AAAA;AAAA;AAAmG;AACvC;AACL;AACsC;;;AAG7F;AACmG;AACnG,gBAAgB,2GAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,+FAAM;AACR,EAAE,wGAAe;AACjB;AACA;AACA;AACA;;AAEA;;AAEA;AACA,IAAI,KAAU,EAAE,YAiBf;AACD;AACe,gF;;;;;;;;;;;;ACvCf;AAAA;AAAA,wCAAyT,CAAgB,yUAAG,EAAC,C;;;;;;;;;;;;ACA7U;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA", "file": "js/17.1693388085916.js", "sourcesContent": ["<template>\r\n  <div class=\"todo\">\r\n    <div class=\"todo-content\">\r\n      <div class=\"todo-header\">\r\n        <el-radio-group v-model=\"tabPosition\">\r\n          <el-radio-button label=\"1\">业务信息</el-radio-button>\r\n          <el-radio-button label=\"2\">流程历史</el-radio-button>\r\n          <el-radio-button label=\"3\">流程图</el-radio-button>\r\n        </el-radio-group>\r\n        <Opinion\r\n          :activities=\"activities\"\r\n        />\r\n      </div>\r\n    </div>\r\n    <el-scrollbar style=\"height:calc(100vh - 70px);overflow-x: hidden\">\r\n      <div v-show=\"tabPosition==='1'\" class=\"todo-data\">\r\n        <Daily\r\n          v-if=\"type==='daily'\"\r\n          :key=\"index\"\r\n          ref=\"todo\"\r\n          :select-value=\"selectValue\"\r\n          :center-variable=\"centerVariable\"\r\n          @handle=\"handle\"\r\n          @saveBtn=\"saveBtn\"\r\n          @openLoading=\"openLoading\"\r\n          @closeLoading=\"closeLoading\"\r\n        />\r\n        <Regular\r\n          v-if=\"type==='regular'\"\r\n          :key=\"index\"\r\n          ref=\"todo\"\r\n          :select-value=\"selectValue\"\r\n          :center-variable=\"centerVariable\"\r\n          @handle=\"handle\"\r\n          @saveBtn=\"saveBtn\"\r\n          @openLoading=\"openLoading\"\r\n          @closeLoading=\"closeLoading\"\r\n        />\r\n        <Actual\r\n          v-if=\"type==='actual'||type==='actualEdit'\"\r\n          :key=\"index\"\r\n          ref=\"todo\"\r\n          :type=\"type\"\r\n          :select-value=\"selectValue\"\r\n          :center-variable=\"centerVariable\"\r\n          @handle=\"handle\"\r\n          @saveBtn=\"saveBtn\"\r\n          @openLoading=\"openLoading\"\r\n          @closeLoading=\"closeLoading\"\r\n        />\r\n      </div>\r\n      <div v-show=\"tabPosition==='2'\" class=\"todo-data\">\r\n        <History\r\n          :activities=\"activities\"\r\n        />\r\n      </div>\r\n      <div v-show=\"tabPosition==='3'\" class=\"todo-data\">\r\n        <FlowChart\r\n          :key=\"selectValue\"\r\n          :select-value=\"selectValue\"\r\n        />\r\n      </div>\r\n    </el-scrollbar>\r\n    <div style=\"text-align: right;padding:0 10px\">\r\n      <RegularProcess\r\n        v-if=\"type==='regular'\"\r\n        slot=\"footer\"\r\n        :key=\"centerVariable\"\r\n        ref=\"process\"\r\n        type=\"parent\"\r\n        :tab-flag=\"tabFlag\"\r\n        :select-value=\"selectValue\"\r\n        :center-variable=\"centerVariable\"\r\n        :flow-cfg-link=\"flowCfgLink\"\r\n        flow-params-url=\"\"\r\n        @close=\"close\"\r\n        @nextStep=\"nextStep\"\r\n        @publicSave=\"publicSave\"\r\n      />\r\n      <DailyProcess\r\n        v-else-if=\"type==='daily'\"\r\n        slot=\"footer\"\r\n        :key=\"centerVariable||saveBtnType\"\r\n        ref=\"process\"\r\n        :save-btn-type=\"saveBtnType\"\r\n        type=\"parent\"\r\n        :tab-flag=\"tabFlag\"\r\n        :select-value=\"selectValue\"\r\n        :center-variable=\"centerVariable\"\r\n        :flow-cfg-link=\"flowCfgLink\"\r\n        @close=\"close\"\r\n        @nextStep=\"nextStep\"\r\n        @publicSave=\"publicSave\"\r\n      />\r\n      <ActualProcess\r\n        v-else-if=\"type==='actual'||type==='actualEdit'\"\r\n        slot=\"footer\"\r\n        :key=\"centerVariable\"\r\n        ref=\"process\"\r\n        type=\"parent\"\r\n        :tab-flag=\"tabFlag\"\r\n        :select-value=\"selectValue\"\r\n        :center-variable=\"centerVariable\"\r\n        :flow-cfg-link=\"flowCfgLink\"\r\n        flow-params-url=\"/colligate/violActual/flowParams\"\r\n        @close=\"close\"\r\n        @nextStep=\"nextStep\"\r\n        @publicSave=\"publicSave\"\r\n      />\r\n      <Process\r\n        v-else\r\n        slot=\"footer\"\r\n        :key=\"centerVariable\"\r\n        ref=\"process\"\r\n        :tab-flag=\"tabFlag\"\r\n        :select-value=\"selectValue\"\r\n        :center-variable=\"centerVariable\"\r\n        :flow-cfg-link=\"flowCfgLink\"\r\n        @close=\"close\"\r\n      />\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nimport Opinion from './../common/opinion'\r\nimport Process from '@/components/Process'\r\nimport RegularProcess from '@/components/Process/regular'\r\nimport DailyProcess from '@/components/Process/daily'\r\nimport ActualProcess from '@/components/Process/actual'\r\nimport FlowChart from './../common/flowChart'\r\nimport Daily from '@/views/daily/dailyBox'// 日常\r\nimport Regular from '@/views/regular/flow'// 定期\r\nimport Actual from '@/views/actual/flow'// 实时\r\nimport History from './../common/history'\r\nimport { tasktodopath, taburls, histoicflow } from '@/api/components/process'\r\n\r\nexport default {\r\n  components: {\r\n    Opinion,\r\n    Process,\r\n    Daily,\r\n    Regular,\r\n    DailyProcess,\r\n    FlowChart,\r\n    History,\r\n    RegularProcess,\r\n    Actual,\r\n    ActualProcess\r\n  },\r\n  inheritAttrs: false,\r\n  props: {\r\n    tabFlag: {\r\n      type: String\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      index: 1,\r\n      saveBtnType: true,\r\n      centerVariable: {},\r\n      flowCfgLink: {},\r\n      visible: false, // 弹框\r\n      tabPosition: '1',\r\n      processType: 1,\r\n      activities: [],\r\n      type: '',\r\n      loadingInstance: ''\r\n    }\r\n  },\r\n  computed: {},\r\n  watch: {},\r\n  created() {\r\n    this.selectValue = {\r\n      linkKey: this.$route.query.linkKey,\r\n      processInstanceId: this.$route.query.processInstanceId,\r\n      readLinkId: this.$route.query.readLinkId,\r\n      taskId: this.$route.query.taskId,\r\n      typeId: this.$route.query.typeId\r\n    }\r\n    this.show()\r\n  },\r\n  mounted() {},\r\n  methods: {\r\n    // 打开这招\r\n    openLoading() {\r\n      this.$store.dispatch('app/setSpyj', '1')\r\n      this.loadingInstance = Loading.service({\r\n        target: document.querySelector('#todo'),\r\n        background: 'rgba(255,255,255,0)',\r\n        spinner: 'el-icon-loading', // 自定义加载图标类名\r\n        text: '正在加载...', // 显示在加载图标下方的加载文案\r\n        lock: false // lock的修改符--默认是false\r\n      })\r\n      return this.loadingInstance\r\n    },\r\n    // 关闭这招\r\n    closeLoading() {\r\n      this.loadingInstance.close()\r\n    },\r\n    /** 点开弹窗 */\r\n    show() {\r\n      this.Tasktodopath()\r\n      this.Histoicflow()\r\n    },\r\n    /** 保存按钮展示 */\r\n    saveBtn(type) {\r\n      this.saveBtnType = type\r\n    },\r\n    /** 保存 */\r\n    publicSave() {\r\n      this.$refs.todo.publicSave()\r\n    },\r\n    /** 下一步 */\r\n    nextStep() {\r\n      this.$refs.todo.nextStep()\r\n    },\r\n    /** 下一步回调 */\r\n    handle(type, object) {\r\n      this.$refs.process.handle(type, object)\r\n    },\r\n    /** 关闭弹窗 */\r\n    close() {\r\n      window.opener = null\r\n      window.open('', '_self')\r\n      window.close()\r\n      // this.$emit('refresh');\r\n    },\r\n    /** 主要数据*/\r\n    Tasktodopath() {\r\n      tasktodopath(this.selectValue).then(\r\n        response => {\r\n          this.centerVariable = response.data.dataRows[0].centerVariable\r\n          this.flowCfgLink = response.data.dataRows[0].flowCfgLink\r\n          this.type = response.data.dataRows[0].url\r\n          this.Taburls()\r\n          this.index++\r\n          this.visible = true\r\n        }\r\n      )\r\n    },\r\n    /** 主要数据*/\r\n    Histoicflow() {\r\n      histoicflow(this.selectValue.processInstanceId).then(\r\n        response => {\r\n          this.activities = response\r\n        }\r\n      )\r\n    },\r\n    /** 根据所在环节查询需展现的自定义标签*/\r\n    Taburls() {\r\n      taburls(this.centerVariable.flowKey, this.selectValue.linkKey, this.tabFlag).then(\r\n        response => {\r\n\r\n        }\r\n      )\r\n    }\r\n  }\r\n}\r\n\r\n</script>\r\n<style scoped lang=\"scss\">\r\n  .todo{\r\n    .todo-header{\r\n      ::v-deep.el-radio-button__inner{\r\n        border-radius: 0 !important;\r\n        border-color: #f4f4f4 !important;\r\n        box-shadow:0 0 0 0 #f5222d !important;\r\n        width: 120px;\r\n      }\r\n    }\r\n    .todo-content{\r\n    background: #F4F4F4;\r\n     }\r\n    .todo-data{\r\n      background: #fff;\r\n      margin-top:8px;\r\n      overflow: auto;\r\n      height: calc(100% - 10px);\r\n    }\r\n    ::v-deep.el-scrollbar__view{\r\n      height: calc(100% - 10px);\r\n    }\r\n    ::v-deep.el-scrollbar__wrap {\r\n      overflow-x: hidden !important;\r\n    }\r\n    ::v-deep.el-dialog__body{\r\n      border-top: 2px solid #E9E8E8;\r\n      padding:0 20px 10px;\r\n      background: #F4F4F4;\r\n      height: 70vh;\r\n      overflow: auto;\r\n    }\r\n  }\r\n\r\n</style>\r\n\r\n", "var render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return _c(\n    \"div\",\n    { staticClass: \"todo\" },\n    [\n      _c(\"div\", { staticClass: \"todo-content\" }, [\n        _c(\n          \"div\",\n          { staticClass: \"todo-header\" },\n          [\n            _c(\n              \"el-radio-group\",\n              {\n                model: {\n                  value: _vm.tabPosition,\n                  callback: function ($$v) {\n                    _vm.tabPosition = $$v\n                  },\n                  expression: \"tabPosition\",\n                },\n              },\n              [\n                _c(\"el-radio-button\", { attrs: { label: \"1\" } }, [\n                  _vm._v(\"业务信息\"),\n                ]),\n                _c(\"el-radio-button\", { attrs: { label: \"2\" } }, [\n                  _vm._v(\"流程历史\"),\n                ]),\n                _c(\"el-radio-button\", { attrs: { label: \"3\" } }, [\n                  _vm._v(\"流程图\"),\n                ]),\n              ],\n              1\n            ),\n            _c(\"Opinion\", { attrs: { activities: _vm.activities } }),\n          ],\n          1\n        ),\n      ]),\n      _c(\n        \"el-scrollbar\",\n        {\n          staticStyle: { height: \"calc(100vh - 70px)\", \"overflow-x\": \"hidden\" },\n        },\n        [\n          _c(\n            \"div\",\n            {\n              directives: [\n                {\n                  name: \"show\",\n                  rawName: \"v-show\",\n                  value: _vm.tabPosition === \"1\",\n                  expression: \"tabPosition==='1'\",\n                },\n              ],\n              staticClass: \"todo-data\",\n            },\n            [\n              _vm.type === \"daily\"\n                ? _c(\"Daily\", {\n                    key: _vm.index,\n                    ref: \"todo\",\n                    attrs: {\n                      \"select-value\": _vm.selectValue,\n                      \"center-variable\": _vm.centerVariable,\n                    },\n                    on: {\n                      handle: _vm.handle,\n                      saveBtn: _vm.saveBtn,\n                      openLoading: _vm.openLoading,\n                      closeLoading: _vm.closeLoading,\n                    },\n                  })\n                : _vm._e(),\n              _vm.type === \"regular\"\n                ? _c(\"Regular\", {\n                    key: _vm.index,\n                    ref: \"todo\",\n                    attrs: {\n                      \"select-value\": _vm.selectValue,\n                      \"center-variable\": _vm.centerVariable,\n                    },\n                    on: {\n                      handle: _vm.handle,\n                      saveBtn: _vm.saveBtn,\n                      openLoading: _vm.openLoading,\n                      closeLoading: _vm.closeLoading,\n                    },\n                  })\n                : _vm._e(),\n              _vm.type === \"actual\" || _vm.type === \"actualEdit\"\n                ? _c(\"Actual\", {\n                    key: _vm.index,\n                    ref: \"todo\",\n                    attrs: {\n                      type: _vm.type,\n                      \"select-value\": _vm.selectValue,\n                      \"center-variable\": _vm.centerVariable,\n                    },\n                    on: {\n                      handle: _vm.handle,\n                      saveBtn: _vm.saveBtn,\n                      openLoading: _vm.openLoading,\n                      closeLoading: _vm.closeLoading,\n                    },\n                  })\n                : _vm._e(),\n            ],\n            1\n          ),\n          _c(\n            \"div\",\n            {\n              directives: [\n                {\n                  name: \"show\",\n                  rawName: \"v-show\",\n                  value: _vm.tabPosition === \"2\",\n                  expression: \"tabPosition==='2'\",\n                },\n              ],\n              staticClass: \"todo-data\",\n            },\n            [_c(\"History\", { attrs: { activities: _vm.activities } })],\n            1\n          ),\n          _c(\n            \"div\",\n            {\n              directives: [\n                {\n                  name: \"show\",\n                  rawName: \"v-show\",\n                  value: _vm.tabPosition === \"3\",\n                  expression: \"tabPosition==='3'\",\n                },\n              ],\n              staticClass: \"todo-data\",\n            },\n            [\n              _c(\"FlowChart\", {\n                key: _vm.selectValue,\n                attrs: { \"select-value\": _vm.selectValue },\n              }),\n            ],\n            1\n          ),\n        ]\n      ),\n      _c(\n        \"div\",\n        { staticStyle: { \"text-align\": \"right\", padding: \"0 10px\" } },\n        [\n          _vm.type === \"regular\"\n            ? _c(\"RegularProcess\", {\n                key: _vm.centerVariable,\n                ref: \"process\",\n                attrs: {\n                  slot: \"footer\",\n                  type: \"parent\",\n                  \"tab-flag\": _vm.tabFlag,\n                  \"select-value\": _vm.selectValue,\n                  \"center-variable\": _vm.centerVariable,\n                  \"flow-cfg-link\": _vm.flowCfgLink,\n                  \"flow-params-url\": \"\",\n                },\n                on: {\n                  close: _vm.close,\n                  nextStep: _vm.nextStep,\n                  publicSave: _vm.publicSave,\n                },\n                slot: \"footer\",\n              })\n            : _vm.type === \"daily\"\n            ? _c(\"DailyProcess\", {\n                key: _vm.centerVariable || _vm.saveBtnType,\n                ref: \"process\",\n                attrs: {\n                  slot: \"footer\",\n                  \"save-btn-type\": _vm.saveBtnType,\n                  type: \"parent\",\n                  \"tab-flag\": _vm.tabFlag,\n                  \"select-value\": _vm.selectValue,\n                  \"center-variable\": _vm.centerVariable,\n                  \"flow-cfg-link\": _vm.flowCfgLink,\n                },\n                on: {\n                  close: _vm.close,\n                  nextStep: _vm.nextStep,\n                  publicSave: _vm.publicSave,\n                },\n                slot: \"footer\",\n              })\n            : _vm.type === \"actual\" || _vm.type === \"actualEdit\"\n            ? _c(\"ActualProcess\", {\n                key: _vm.centerVariable,\n                ref: \"process\",\n                attrs: {\n                  slot: \"footer\",\n                  type: \"parent\",\n                  \"tab-flag\": _vm.tabFlag,\n                  \"select-value\": _vm.selectValue,\n                  \"center-variable\": _vm.centerVariable,\n                  \"flow-cfg-link\": _vm.flowCfgLink,\n                  \"flow-params-url\": \"/colligate/violActual/flowParams\",\n                },\n                on: {\n                  close: _vm.close,\n                  nextStep: _vm.nextStep,\n                  publicSave: _vm.publicSave,\n                },\n                slot: \"footer\",\n              })\n            : _c(\"Process\", {\n                key: _vm.centerVariable,\n                ref: \"process\",\n                attrs: {\n                  slot: \"footer\",\n                  \"tab-flag\": _vm.tabFlag,\n                  \"select-value\": _vm.selectValue,\n                  \"center-variable\": _vm.centerVariable,\n                  \"flow-cfg-link\": _vm.flowCfgLink,\n                },\n                on: { close: _vm.close },\n                slot: \"footer\",\n              }),\n        ],\n        1\n      ),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../../../../../node_modules/css-loader/dist/runtime/api.js\");\nexports = ___CSS_LOADER_API_IMPORT___(false);\n// Module\nexports.push([module.id, \".todo .todo-header[data-v-18d7ec2c] .el-radio-button__inner {\\n  border-radius: 0 !important;\\n  border-color: #f4f4f4 !important;\\n  -webkit-box-shadow: 0 0 0 0 #f5222d !important;\\n          box-shadow: 0 0 0 0 #f5222d !important;\\n  width: 120px;\\n}\\n.todo .todo-content[data-v-18d7ec2c] {\\n  background: #F4F4F4;\\n}\\n.todo .todo-data[data-v-18d7ec2c] {\\n  background: #fff;\\n  margin-top: 8px;\\n  overflow: auto;\\n  height: calc(100% - 10px);\\n}\\n.todo[data-v-18d7ec2c] .el-scrollbar__view {\\n  height: calc(100% - 10px);\\n}\\n.todo[data-v-18d7ec2c] .el-scrollbar__wrap {\\n  overflow-x: hidden !important;\\n}\\n.todo[data-v-18d7ec2c] .el-dialog__body {\\n  border-top: 2px solid #E9E8E8;\\n  padding: 0 20px 10px;\\n  background: #F4F4F4;\\n  height: 70vh;\\n  overflow: auto;\\n}\", \"\"]);\n// Exports\nmodule.exports = exports;\n", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../../../../node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../../node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-2!../../../../../node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!../../../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./taskToDo.vue?vue&type=style&index=0&id=18d7ec2c&scoped=true&lang=scss&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add the styles to the DOM\nvar add = require(\"!../../../../../node_modules/vue-style-loader/lib/addStylesClient.js\").default\nvar update = add(\"2fe93c6b\", content, false, {\"sourceMap\":false,\"shadowMode\":false});\n// Hot Module Replacement\nif(module.hot) {\n // When the styles change, update the <style> tags\n if(!content.locals) {\n   module.hot.accept(\"!!../../../../../node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../../node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-2!../../../../../node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!../../../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./taskToDo.vue?vue&type=style&index=0&id=18d7ec2c&scoped=true&lang=scss&\", function() {\n     var newContent = require(\"!!../../../../../node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../../node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-2!../../../../../node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!../../../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./taskToDo.vue?vue&type=style&index=0&id=18d7ec2c&scoped=true&lang=scss&\");\n     if(newContent.__esModule) newContent = newContent.default;\n     if(typeof newContent === 'string') newContent = [[module.id, newContent, '']];\n     update(newContent);\n   });\n }\n // When the module is disposed, remove the <style> tags\n module.hot.dispose(function() { update(); });\n}", "import { render, staticRenderFns } from \"./taskToDo.vue?vue&type=template&id=18d7ec2c&scoped=true&\"\nimport script from \"./taskToDo.vue?vue&type=script&lang=js&\"\nexport * from \"./taskToDo.vue?vue&type=script&lang=js&\"\nimport style0 from \"./taskToDo.vue?vue&type=style&index=0&id=18d7ec2c&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"18d7ec2c\",\n  null\n  \n)\n\n/* hot reload */\nif (module.hot) {\n  var api = require(\"E:\\\\workspace\\\\info\\\\trigram-ui\\\\node_modules\\\\vue-hot-reload-api\\\\dist\\\\index.js\")\n  api.install(require('vue'))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!api.isRecorded('18d7ec2c')) {\n      api.createRecord('18d7ec2c', component.options)\n    } else {\n      api.reload('18d7ec2c', component.options)\n    }\n    module.hot.accept(\"./taskToDo.vue?vue&type=template&id=18d7ec2c&scoped=true&\", function () {\n      api.rerender('18d7ec2c', {\n        render: render,\n        staticRenderFns: staticRenderFns\n      })\n    })\n  }\n}\ncomponent.options.__file = \"src/views/workflow/tasklist/gateway/taskToDo.vue\"\nexport default component.exports", "import mod from \"-!../../../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../../../node_modules/babel-loader/lib/index.js!../../../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./taskToDo.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../../../node_modules/babel-loader/lib/index.js!../../../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./taskToDo.vue?vue&type=script&lang=js&\"", "export * from \"-!../../../../../node_modules/vue-style-loader/index.js??ref--8-oneOf-1-0!../../../../../node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../../node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-2!../../../../../node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!../../../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./taskToDo.vue?vue&type=style&index=0&id=18d7ec2c&scoped=true&lang=scss&\"", "export * from \"-!../../../../../node_modules/cache-loader/dist/cjs.js?{\\\"cacheDirectory\\\":\\\"node_modules/.cache/vue-loader\\\",\\\"cacheIdentifier\\\":\\\"f1aaaf00-vue-loader-template\\\"}!../../../../../node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./taskToDo.vue?vue&type=template&id=18d7ec2c&scoped=true&\""], "sourceRoot": ""}