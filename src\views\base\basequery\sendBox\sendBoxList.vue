<!--规章制度-->
<template>
  <div class="padding_b10 app-sendBoxList">
    <SearchList :searchList="searchList" @on-search="handleQuery" @on-reset="resetQuery"></SearchList>
    <el-table :data="tableList" :header-cell-style="{background:'#F4F8FC',color:'#606266','text-align':'center'}" :cell-style="{'text-align':'center'}">
      <el-table-column label="序号" type="index" min-width="4%" align="center">
        <template slot-scope="scope">
          <table-index
          :index="scope.$index"
          :pageNum="queryParams.pageNum"
          :pageSize="queryParams.pageSize"
          />
        </template>
      </el-table-column>
      <el-table-column label="省分" prop="involProvName" min-width="8%"  show-overflow-tooltip/>
      <el-table-column label="地市" prop="involAreaName"  min-width="15%"  show-overflow-tooltip/>
      <el-table-column label="收件人" prop="receivePerson"  min-width="15%"  show-overflow-tooltip/>
      <el-table-column label="发件人" prop="sendPerson" min-width="15%"  show-overflow-tooltip/>
      <el-table-column label="标题" prop="title" min-width="33%" align="left" show-overflow-tooltip/>
      <el-table-column label="发送内容" prop="mailContent" width="350" align="left" show-overflow-tooltip/>
      <el-table-column label="操作" fixed="right" width="100" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            v-if="scope.row.dispatchStatus != 0"
            size="mini"
            type="text"
            icon="el-icon-search"
            title="查看"
            @click="handleDetails(scope.row)"
          ></el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="sendBoxBaseInfo"
    />
    <sendBoxDetail v-if="dialogVisible" v-on:closeModal="closeModal" :id="id"></sendBoxDetail>
  </div>
</template>

<script>
  import {getSendBoxList} from "@/api/base/sendBox";
  import sendBoxDetail from "./sendBoxDetail";
  import SearchList from "../../common/SearchList";

  export default {
    name: "sendBoxList",
    components: { sendBoxDetail, SearchList  },
    data() {
      return {
        // 总条数
        total: 0,
        // 表格数据
        tableList: [],
        // 是否显示弹出层
        dialogVisible: false,
        id:'',
        //新增主键
        //日常问题查询 参数
        queryParams: {
          pageNum: 1,
          pageSize: 10,
          involProvName:'',
          title:'',
        },
        searchList: [
          {
            label: "省分",
            name: "involProvName",
            value: null,
            type: "Input"
          },
          {
            label: "发件人",
            name: "sendPerson",
            value: null,
            type: "Input"
          },
          {
            label: "收件人",
            name: "receivePerson",
            value: null,
            type: "Input"
          }
        ]
      };
    },
    created() {
      this.sendBoxBaseInfo();
    },
    methods: {
      /**查询企業基本信息列表*/
      sendBoxBaseInfo() {
       //this.loading = true;
        getSendBoxList(this.queryParams).then(
          response => {
            this.tableList = response.rows;
            this.total = response.total;
            //this.loading = false;
          }
        );
      },
      /** 搜索按钮操作*/
      handleQuery(params) {
        this.queryParams={
          ...this.queryParams,
          ...params,
          pageNum: 1,
        }
        this.sendBoxBaseInfo();
      },
      /**重置按钮操作*/
      resetQuery() {
        this.queryParams = {
          pageNum: 1,
          pageSize: 10,
        };
        this.sendBoxBaseInfo();
      },
      /**查看按钮操作*/
      handleDetails(row) {
        this.dialogVisible=true;
        this.id = row.id;
      },
      /**关闭模态框*/
      closeModal(){
        this.dialogVisible = !this.dialogVisible;
      },
    }
  };
</script>
<style rel="stylesheet/scss" lang="scss">
</style>







