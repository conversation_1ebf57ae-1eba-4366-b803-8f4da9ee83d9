<!--新增上报单位-补录-->
<template>
  <div>
    <el-form-item   label="上报单位">
      <el-button
        v-show="edit"
        type="primary"
        plain
        icon="el-icon-plus"
        size="mini"
        @click="addReportDepart"
      >添加上报单位
      </el-button>
    </el-form-item>
    <el-form>
      <el-table v-loading="loading" :data="tableList">
        <el-table-column label="" type="index" min-width="4%" align="center"/>
        <el-table-column label="上报单位" prop="reportUnitName" align="center" min-width="30%"/>
        <el-table-column label="接口人" prop="interfaceUserName" align="center" min-width="20%"/>
        <el-table-column label="邮箱" prop="interfaceUserMail"  align="center" min-width="20%"/>
        <el-table-column label="联系电话" prop="interfaceUserPhone" align="center" min-width="20%"/>
        <el-table-column label="操作" fixed="right" width="200" align="center"
                         class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              title="编辑"
              @click="clickPostName(scope.row)"
            >
            </el-button>
            <el-button
              size="mini"
              type="text"
              title="删除"
              icon="el-icon-delete"
              @click="toDelReportData(scope.row)"
            >
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-form>
    <el-form-item class="margin0"  label="已发起上报单位">
    </el-form-item>
    <el-form>
      <el-table v-loading="loading" :data="tableList1">
        <el-table-column label="" type="index" min-width="4%" align="center"/>
        <el-table-column label="上报单位" prop="reportUnitName" align="center" min-width="30%"/>
        <el-table-column label="接口人" prop="interfaceUserName" align="center" min-width="20%"/>
        <el-table-column label="邮箱" prop="interfaceUserMail"  align="center" min-width="20%"/>
        <el-table-column label="联系电话" prop="interfaceUserPhone" align="center" min-width="26%"/>
      </el-table>
    </el-form>

    <el-dialog :visible.sync="departmentFlag" width="60%"  class="treeOpen" :modal-append-to-body="false" append-to-body title="上报单位">
      <RegularTree
        v-if="departmentFlag"
        :key="selectTree"
        ref="regularTree"
        :supplementalAdmission="supplementalAdmission"
        :url="regularTreeUrl"
        :selectTree="selectTree"
        @list="departLists"
      />
<!--      <div slot="footer" class="dialog-footer">-->
<!--        <el-button size="mini" type="primary" @click="toSaveReportUnitByEdit">保存</el-button>-->
<!--      </div>-->
    </el-dialog>

    <el-dialog :visible.sync="regularPersonFlag" width="60%" :modal-append-to-body="false" append-to-body title="上报单位接口人">
      <RegularPersonTree
        v-if="regularPersonFlag"
        :key="selectTree"
        ref="regularPersonTree"
        :url="regularPersonTreeUrl"
        :selectTree="selectTree"
        :params = "params"
        @list="personLists"
      />
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="toSavePersonById">保存</el-button>
      </div>
    </el-dialog>


    <!-- 人员选择 -->
    <el-dialog
      v-if="memberOpen"
      :visible.sync="memberOpen"
      width="80%"
      class="changePeoples"
      title="上报单位接口人"
      append-to-body
    >
      <changePeoples :close-btn="cancelPeople" :rowData = "params" />
    </el-dialog>
  </div>
</template>

<script>
  import {
     getNotUnitList
    ,delReportData
    ,saveAddReportUnitByEdit//保存上报单位
    ,savePersonById//保存上报单位接口人
  } from "@/api/regular/tree/regularTree"
  import {
    selectReportDetailList//查询数据列表
  } from '@/api/regular/details/regularDetail';
  import RegularTree from '../tree/regularDepartmentTree'// 上报单位
  import RegularPersonTree from './../tree/regularPersonTree'// 上报单位接口人
  import changePeoples from '@/views/regular/add/changePeoples'
  export default {
    name: "reportDepartment"
    ,components: {
      RegularTree
      ,RegularPersonTree
      ,changePeoples
    }
    ,props:{
      regularReportId: {
        type: String
      },
      edit:{
        type: Boolean
      }
    },
    data(){
      return{
        supplementalAdmission:true,//补录
        // 表格数据
        tableList: [],//上报单位
        tableList1:[]//已发起上报单位
        ,loading:false//遮罩
        ,selectTree:[]
        ,departmentFlag:false//添加上报单位树
        ,regularTreeUrl:'/colligate/violRegular/report/getAddReportUnitList/'+this.regularReportId
        ,regularPersonFlag:false//上报单位接口人树
        ,regularPersonTreeUrl:"/colligate/violRegular/report/getStaffOrgTree"
        ,params:{}
        ,memberOpen: false // 选择人员
      }
    }
    ,created(){
      //初始化上报单位
      this.GetunitList();
      //已上报的数据
      this.SelectReportDetailList();
    }
    ,methods:{
      //初始化上报单位
      GetunitList(){
        this.loading = true;
        getNotUnitList(this.regularReportId).then(
          response=>{
            this.tableList = response.data;
            this.$emit('toSetUnitList',response.data);
            this.loading = false;
          }
        );
      },
      //初始化已上报单位
      SelectReportDetailList(){
        selectReportDetailList(this.regularReportId,0).then(
          response=>{
            this.tableList1 = response.data;
          }
        );
      },
      //删除上报单位
      toDelReportData(data){
        this.$modal.confirm('是否确定删除该单位？').then(()=> {
          delReportData(this.regularReportId,data.id).then(
            response => {
              if (response.code === 200) {
                this.$modal.msgSuccess("删除成功！");
                this.GetunitList();
              } else {
                this.$modal.msgError(response.msg);
              }
            }
          )
        }).catch(() => {});
      },
      //添加上报单位
      addReportDepart(){
        this.selectTree = [];
        for(let i = 0;i<this.tableList.length;i++){
          let departStr = {};
          departStr.id = this.tableList[i].reportUnitCode+"";
          departStr.name = this.tableList[i].reportUnitName;
          this.selectTree.push(departStr);
        }
        this.departmentFlag = true;
      },
      //保存上报单位
      toSaveReportUnitByEdit(){
        //获取选中信息
        this.$refs.regularTree.list();
      },
      //保存上报单位(回调函数)
      departLists(data){
        let list=[];
        if(!data.length)
          return false;
        for (let i = 0; i < data.length; i++) {
          let item = data[i];
          list.push({reportUnitCode: item.id, reportUnitName: item.name})
        }
        //保存上报单位
          saveAddReportUnitByEdit(this.regularReportId,list).then(
          response => {
            const { code, data } = response;
            if (code === 200) {
              //关闭上报单位窗口
              this.departmentFlag = false;
              //刷新上报单位列表
              this.GetunitList();
            }
          }
        )
      },
      //选择上报单位接口人
      clickPostName(data){
        // this.selectTree = [];
        // if(data.interfaceUserId){
        //   let departStr = {};
        //   departStr.id = data.interfaceUserId;
        //   departStr.name = data.interfaceUserName;
        //   this.selectTree.push(departStr);
        // }
        // this.params.id = data.id;
        // this.regularPersonFlag = true;
        this.memberOpen = true;
        this.params.id = data.id;
        this.params.reportUnitCode = data.reportUnitCode;
      },
      //保存上报单位接口人
      toSavePersonById(){
        //获取选中信息
        this.$refs.regularPersonTree.list();
      },
      //保存接口人（树回调函数）
      personLists(data){
        if(data.length == 0){
          this.$modal.msgError("请选择一位人员！");
        }else{
          savePersonById(this.regularReportId,this.params.id,data[0].id).then(
            response => {
              if (response.code === 200) {
                this.$modal.msgSuccess(response.msg);
                this.regularPersonFlag = false;
                //刷新上报单位列表
                this.GetunitList();
              } else {
                this.$modal.msgError(response.msg);
              }
            }
          )
        }

      },
      // 人员选择完成
      cancelPeople(detail) {
        this.memberOpen = false;
        savePersonById(this.regularReportId,this.params.id,detail[0].user_id).then(
          response => {
            if (response.code === 200) {
              this.$modal.msgSuccess(response.msg);
              //刷新上报单位列表
              this.GetunitList();
            } else {
              this.$modal.msgError(response.msg);
            }
          }
        )
      },
    }
  }
</script>

<style scoped>
.margin0{
  margin:0;
}
.treeOpen ::v-deep.el-dialog__body{
  padding:0 20px;
}
.changePeoples ::v-deep.el-dialog__body{
  padding:20px;
}
</style>
