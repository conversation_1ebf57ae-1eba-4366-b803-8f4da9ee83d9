import request from '@/utils/request'

// 查询页面信息
export function queryDailyCheckInfo(data) {
  return request({
    url: '/colligate/violDailyCheck/queryDailyCheckInfo',
    method: 'post',
    data: data
  })
}

// 涉嫌单位查询

export function queryInvolveCompanyAndPerson(data) {
  return request({
    url: '/colligate/supervisionInvolve/queryInvolveCompanyAndPerson',
    method: 'post',
    data: data
  })
}

// 保存数据

export function saveCheckInfo(data) {
  return request({
    url: '/colligate/violDailyCheck/saveCheckInfo',
    method: 'post',
    data: data
  })
}

// 保存表格数据

export function savePersonPostName(data) {
  return request({
    url: '/colligate/supervisionInvolve/savePersonPostName',
    method: 'post',
    data: data
  })
}

// 保存单位责任类型

export function saveCompanyDuty(data) {
  return request({
    url: '/colligate/supervisionInvolve/saveCompanyDuty',
    method: 'post',
    data: data
  })
}

// 删除该涉及人员

export function delPersonInvolved(data) {
  return request({
    url: '/colligate/supervisionInvolve/delPersonInvolved',
    method: 'post',
    data: data
  })
}

// 下一步加验证

export function checkAndSaveCheckInfo(data) {
  return request({
    url: '/colligate/violDailyCheck/checkAndSaveCheckInfo',
    method: 'post',
    data: data
  })
}

// 保存人员
export function saveCheckGroupMember(data) {
  return request({
    url: '/colligate/violDailyRelevperson/saveCheckGroupMember',
    method: 'post',
    data: data
  })
}

// 查询组长人员
export function queryRelevpersonByType(data) {
  return request({
    url: '/colligate/violDailyRelevperson/queryRelevpersonByType',
    method: 'post',
    data: data
  })
}

// 删除组长人员
export function delCheckGroupMember(data) {
  return request({
    url: '/colligate/violDailyRelevperson/delCheckGroupMember',
    method: 'post',
    data: data
  })
}

/**
 * 保存是否二级单位领导
 * @param data
 */
export function saveSecondUnitLeaderOption(data) {
  return request({
    url: '/colligate/supervisionInvolve/saveSecondUnitLeaderOption',
    method: 'post',
    data: data
  });
}

/**
 * 更改干部类型
 * @param data
 */
export function changeCadreType(data) {
  return request({
    url: '/colligate/supervisionInvolve/changeCadreType',
    method: 'post',
    data: data
  });
}

/**
 * 保存主责单位
 */
export function saveMainCompany(data){
  return request({
    url: '/colligate/supervisionInvolve/saveMainCompany',
    method: 'post',
    data: data
  });
}

/**
 * 取消主责单位
 * @param data
 */
export function cancelMainCompany(data){
  return request({
    url: '/colligate/supervisionInvolve/cancelMainCompany',
    method: 'post',
    data: data
  });
}
