<!--规章制度-->
<template>
  <div class="padding_b10 app-limitPersonList">
    <SearchList :searchList="searchList" @on-search="handleQuery" @on-reset="resetQuery"></SearchList>
    <el-table :data="tableList"  :header-cell-style="{background:'#F4F8FC',color:'#606266','text-align':'center'}" :cell-style="{'text-align':'center'}">
      <el-table-column label="序号" type="index" min-width="4%" align="center">
        <template slot-scope="scope">
          <table-index
          :index="scope.$index"
          :pageNum="queryParams.pageNum"
          :pageSize="queryParams.pageSize"
          />
        </template>
      </el-table-column>
      <el-table-column label="省分" prop="problemProvName" min-width="8%"/>
      <el-table-column label="地市" prop="problemAreaName"  min-width="10%"/>
      <el-table-column label="姓名" prop="userName"  min-width="8%"/>
      <el-table-column label="性别" prop="sex" :formatter="textFormat" min-width="5%"/>
      <el-table-column label="所在企业" prop="involAreaName" min-width="17%" show-overflow-tooltip/>
      <el-table-column label="企业层级" prop="orgGradeName" min-width="10%" show-overflow-tooltip/>
      <el-table-column label="职位" prop="postName" min-width="10%" show-overflow-tooltip/>
      <el-table-column label="禁入限制开始时间" :formatter="textFormat" prop="limitStartTime" min-width="12%"/>
      <el-table-column label="禁入限制结束时间" :formatter="textFormat" prop="limitEndTime" min-width="12%"/>
      <el-table-column label="状态" prop="commitFlag" :formatter="textFormat" min-width="8%"/>
      <el-table-column label="操作" fixed="right" width="200" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            title="编辑"
            @click="openAdd(scope.row)"
          ></el-button>
          <el-button
            v-if="scope.row.commitFlag == 0"
            size="mini"
            type="text"
            icon="el-icon-finished"
            title="提交"
            @click="subRow(scope.row)"
          ></el-button>
          <el-button
            v-if="scope.row.reportNum <= 0"
            size="mini"
            type="text"
            icon="el-icon-delete"
            title="删除"
            @click="delRow(scope.row)"
          ></el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="limitPersonBaseInfo"
    />
    <limitPersonAdd v-if="dialogVisible" v-on:closeModal="closeModal" :id="id"></limitPersonAdd>
  </div>
</template>

<script>
  import {getLimitedPersonPage, delLimitedPersonInfo, saveLimitPerson} from "@/api/base/limitPerson";
  import limitPersonAdd from "./limitPersonAdd";
  import moment from "moment"
  import SearchList from "../../common/SearchList";

  export default {
    name: "limitPersonList",
    components: { limitPersonAdd, SearchList },
    data() {
      return {
        // 总条数
        total: 0,
        // 表格数据
        tableList: [],
        // 是否显示弹出层
        dialogVisible: false,
        id:'',
        //新增主键
        //日常问题查询 参数
        queryParams: {
          pageNum: 1,
          pageSize: 10,
          involProvName:'',
          title:'',
        },
        searchList: [
          {
            label: "省分",
            name: "problemProvName",
            value: null,
            type: "Input"
          },
          {
            label: "地市",
            name: "problemAreaName",
            value: null,
            type: "Input"
          },
          {
            label: "姓名",
            name: "userName",
            value: null,
            type: "Input"
          }
        ]
      };
    },
    created() {
      this.limitPersonBaseInfo();
    },
    methods: {
      /**查询企業基本信息列表*/
      limitPersonBaseInfo() {
       //this.loading = true;
        getLimitedPersonPage(this.queryParams).then(
          response => {
            this.tableList = response.rows;
            this.total = response.total;
            //this.loading = false;
          }
        );
      },
      /** 搜索按钮操作*/
      handleQuery(params) {
        this.queryParams={
          ...this.queryParams,
          ...params,
          pageNum: 1,
        }
        this.limitPersonBaseInfo();
      },
      /**重置按钮操作*/
      resetQuery() {
        this.queryParams = {
          pageNum: 1,
          pageSize: 10,
        };
        this.limitPersonBaseInfo();
      },
      /**打开新增编辑框*/
      openAdd(row) {
        this.dialogVisible = !this.dialogVisible;
        if(row){
          this.id = row.id;
        }
      },
      /**删除行*/
      delRow(row){

        this.$confirm('确定删除该条禁入限制人员?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          delLimitedPersonInfo({
            id: row.id,
            uniqueCode: row.uniqueCode
          }).then(
            response => {
              if(response.code === 200){
                this.limitPersonBaseInfo();
              }else{
                this.$message.error(response.msg);
              }
            }
          );
        });
      },
      /**提交*/
      subRow(row){
        this.$confirm('确定提交该条禁入限制人员?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          saveLimitPerson({
            ...row,
            commitFlag: 1
          }).then(
            response => {
              if(response.code === 200){
                this.limitPersonBaseInfo();
              }else{
                this.$message.error(response.msg);
              }
            }
          );
        });
      },
      /**关闭模态框*/
      closeModal(){
        this.dialogVisible = !this.dialogVisible;
        this.limitPersonBaseInfo();
      },
      /*日期处理*/
      textFormat:function(row,column){
        var text = row[column.property];
        if(text === undefined){
          return ''
        }
        if(column.property === 'sex'){
          return text=='1'?'男':'女'
        }else if(column.property === 'commitFlag'){
          return text == 1 ? "已提交" : "未提交";
        }
        return moment(text).format("YYYY-MM-DD")
      },
    }
  };
</script>
<style rel="stylesheet/scss" lang="scss">
</style>







