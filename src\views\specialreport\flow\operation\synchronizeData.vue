<!--同步数据存在重复信息，请重新核查手工录入的问题信息。-->
<template>
  <div class="scope">
    <el-dialog v-bind="$attrs" :visible.sync="visible" width="80%" v-on="$listeners" @open="onOpen" @close="onClose" append-to-body :title="title">
<!--      <Jscrollbar height="68vh">-->
        <el-form style="height:68vh">
          <el-table border v-loading="tableLoading" :data="ledgerTable" height="100%">
            <el-table-column label="" type="index" min-width="8%" align="center"label="序号" >
              <template slot-scope="scope">
                <table-index
                  :index="scope.$index"
                />
              </template>
            </el-table-column>
            <el-table-column label="问题编号" prop="problemNum" min-width="15%" align="center" show-overflow-tooltip/>
            <el-table-column label="发现问题业务类型" prop="problemTypeEnumName" min-width="20%" align="center" show-overflow-tooltip/>
            <el-table-column label="审计发现问题" prop="problemAudit" min-width="30%" align="center" show-overflow-tooltip>
              <template slot-scope="scope">
                <div class="table-text-left ovflowHidden">{{ scope.row.problemAudit }}</div>
              </template>
            </el-table-column>
            <el-table-column label="具体问题描述" prop="problemDescription" min-width="30%" align="center" show-overflow-tooltip>
              <template slot-scope="scope">
                <div class="table-text-left ovflowHidden">{{ scope.row.problemDescription }}</div>
              </template>
            </el-table-column>
            <el-table-column label="是否上报告" prop="reportFlag" min-width="10%" align="center" show-overflow-tooltip>
              <template slot-scope="scope" class="text-center">
                {{ scope.row.reportFlag==1?'是':scope.row.reportFlag==0?'否':'' }}
              </template>
            </el-table-column>
            <el-table-column label="是否追责" prop="ifDuty" min-width="10%" align="center" show-overflow-tooltip>
              <template slot-scope="scope">
                {{ scope.row.ifDuty==1?'是':scope.row.ifDuty==0?'否':'' }}
              </template>
            </el-table-column>
            <el-table-column label="是否移交纪检" prop="transferFlag" min-width="10%" align="center" show-overflow-tooltip>
              <template slot-scope="scope">
                {{ scope.row.transferFlag==1?'是':scope.row.transferFlag==0?'否':'' }}
              </template>
            </el-table-column>
            <el-table-column
              label="操作"
              fixed="right"
              min-width="10%"
              align="center"
              class-name="small-padding fixed-width"
            >
              <template slot-scope="scope">
                <el-button
                  v-if="scope.row.updateMethod === '1'"
                  size="mini"
                  type="text"
                  icon="el-icon-edit"
                  @click="delLedgers(scope.row)"
                >删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-form>
<!--      </Jscrollbar>-->
      <div slot="footer">
        <el-button size="mini" @click="close" type="primary" >确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  queryLedgersList,
  delLedgers
} from '@/api/special-report'
export default {
  name: "synchronizeData",
  props: {
    id: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      loading:false,
      title:'同步数据存在重复信息，请重新核查手工录入的问题信息。',
      visible:false,//弹框
      ledgerTable:[]
    };
  },
  created() {
  },
  filters: {
  },
  methods: {
    // 显示弹框
    show() {
      this.visible = true;
      //台账信息
      this.queryLedgersList();
    },
    close() {
      this.visible = false;
      this.$emit('close');
    },
    delLedgers(obj){
      this.$modal.confirm("确认删除此条数据").then(function() {
        return delLedgers({id:obj.id,projectId:obj.projectId})
      }).then((response) => {
        this.$modal.msgSuccess("删除成功");
        //台账信息
        this.queryLedgersList();
      }).catch(() => {});
    },
    queryLedgersList() {
      this.loading = true;
      //刷新台账列表
        queryLedgersList(this.id).then((res)=>{
          //台账信息
          this.ledgerTable = res.data.ledgerReturnList;
        })
      },

  }
};
</script>

<style lang="scss" scoped>
.height{
  height: 100%;
}
::v-deep .el-dialog__body {
  padding: 0px 20px;
  color: #606266;
  font-size: 14px;
  word-break: break-all;
}
</style>




