import request from '@/utils/request'

// 单位树查询
export function queryCompanyInvolved(data) {
  return request({
    url: '/colligate/supervisionInvolve/queryCompanyInvolved',
    method: 'post',
    data:data
  })
}

// 单位保存
export function saveCompanyInvolved(data) {
  return request({
    url: '/colligate/supervisionInvolve/saveCompanyInvolved',
    method: 'post',
    data:data
  })
}

// 单位删除
export function delCompanyInvolved(data) {
  return request({
    url: '/colligate/supervisionInvolve/delCompanyInvolved',
    method: 'post',
    data:data
  })
}

// 部门树查询
export function queryDepartmentInvolved(data) {
  return request({
    url: '/colligate/supervisionInvolve/queryDepartmentInvolved',
    method: 'post',
    data:data
  })
}

// 部门保存
export function saveDepartmentInvolved(data) {
  return request({
    url: '/colligate/supervisionInvolve/saveDepartmentInvolved',
    method: 'post',
    data:data
  })
}


// 部门删除
export function delDepartmentInvolved(data) {
  return request({
    url: '/colligate/supervisionInvolve/delDepartmentInvolved',
    method: 'post',
    data:data
  })
}

// 人员列表查询
export function queryPersonInvolved(data) {
  return request({
    url: '/colligate/supervisionInvolve/queryPersonInvolved',
    method: 'post',
    data:data
  })
}

// 人员保存
export function savePersonInvolved(data) {
  return request({
    url: '/colligate/supervisionInvolve/savePersonInvolved',
    method: 'post',
    data:data
  })
}

// 人员删除
export function delPersonInvolved(data) {
  return request({
    url: '/colligate/supervisionInvolve/delPersonInvolved',
    method: 'post',
    data:data
  })
}

// 人员树
export function queryDepartmentSelectInfo(data) {
  return request({
    url: '/colligate/supervisionInvolve/queryDepartmentSelectInfo',
    method: 'post',
    data:data
  })
}

// 人员职务保存
export function savePersonPostName(data) {
  return request({
    url: '/colligate/supervisionInvolve/savePersonPostName',
    method: 'post',
    data:data
  })
}

/**
 * 保存不在岗人员
 * @param data
 */
export function saveNotOnDutyPerson(data) {
  return request({
    url: '/colligate/supervisionInvolve/saveNotOnDutyPerson',
    method: 'post',
    data: data
  });
}

/**
 * 保存涉及人员干部类型
 * @param data
 */
export function savePersonCadreType(data) {
  return request({
    url: '/colligate/supervisionInvolve/savePersonCadreType',
    method: 'post',
    data: data
  });
}

/**
 * 删除人员
 * @param id
 */
export function deleteUnitById(id) {
  return request({
    url: '/colligate/supervisionInvolve/deleteUnitById/'+id,
    method: 'post',
  });
}

/**
 * 查询不在岗人员涉及的部门
 * @param data
 */
export function notPostInvolveDepartments(data) {
  return request({
    url: '/colligate/supervisionInvolve/notPostInvolveDepartments',
    method: 'post',
    data: data
  });
}
