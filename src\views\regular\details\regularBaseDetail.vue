<!--上报基本信息-->
<template>
  <div>
    <el-col :span="6">
      <el-form-item label="报告类型" prop="reportType">
        <span>{{formData.reportType==='1'?'定期报告':'其他报告'}}</span>
      </el-form-item>
    </el-col>
    <el-col :span="6">
      <el-form-item label="上报年度" prop="reportYear">
        <span>{{formData.reportYear}}</span>
      </el-form-item>
    </el-col>
    <el-col :span="6">
      <el-form-item label="上报区间" prop="reportTime">
        <span>{{formData.reportStartTime}}</span>
        <span>-</span>
        <span>{{formData.reportEndTime}}</span>
      </el-form-item>
    </el-col>
    <el-col :span="6">
      <el-form-item label="上报截止日期" prop="reportCloseTime">
        <span>{{formData.reportCloseTime}}</span>
      </el-form-item>
    </el-col>
    <el-col :span="24">
      <el-form-item label="标题" prop="reportTitle">
        <span>{{formData.reportTitle}}</span>
      </el-form-item>
    </el-col>
    <el-col :span="24">
      <el-form-item label="上报要求" prop="reportRequire">
        <span>{{formData.reportRequire}}</span>
      </el-form-item>
    </el-col>
    <el-col :span="24">
      <RegularGlobalTemplate :key="regularReportId" :edit="false" :regularReportId="regularReportId"></RegularGlobalTemplate>
    </el-col>
  </div>
</template>

<script>
  import RegularGlobalTemplate from "@/views/regular/common/regularGlobalTemplate";
  export default {
    name: "regularBaseDetail",
    components: {RegularGlobalTemplate},
    props:{
      regularReportId:{
        type:String,
        default:''
      }
    },
    data(){
      return{
        formData:{
          id:''
          ,reportType:undefined//报告类型 1:定期  2：其他
          ,reportYear:undefined//上报年度
          ,reportTime:undefined//上报区间
          ,reportStartTime:undefined//上报区间-开始
          ,reportEndTime:undefined//上报区间-结束
          ,reportCloseTime:undefined//上报截止日期
          ,reportTitle:undefined//标题
          ,reportRequire:undefined//上报要求
          ,reportProvCode:undefined
          ,reportProvName:undefined
          ,orgGrade:undefined
        }
      }
    }
    ,created(){
    }
    ,methods:{
      showInfo(data){
        this.formData = data;
      },
    }
  }
</script>

<style scoped>

</style>
