<!-- 初核报告--首环节待办 -->
<template>
  <div>
   <BlockCard title="项目信息">
      <div class="position" v-if="projectUpdateMethod === '0'">
        <div class="position-select">
          <div class="float-right " style="margin-left:15px;">
            <el-button  type="primary" plain icon="el-icon-plus" size="mini"  @click="syncData">接口信息同步</el-button>
          </div>
        </div>
      </div>
     <el-form r size="medium" label-width="150px">
       <el-row>
         <el-col :span="16">
           <el-form-item label="项目名称">
             <span v-if="projectUpdateMethod === '0'">{{ infoData.projectName }}</span>
             <el-input v-if="projectUpdateMethod === '1'" v-model="infoData.projectName" placeholder="项目名称" clearable maxlength = '120' show-word-limit></el-input>
           </el-form-item>
         </el-col>
         <el-col :span="8">
           <el-form-item label="项目编码">
             <span v-if="projectUpdateMethod === '0'"> {{infoData.projectNum}}</span>
             <el-input v-if="projectUpdateMethod === '1'" v-model="infoData.projectNum" placeholder="项目编号" clearable maxlength = '100' ></el-input>
           </el-form-item>
         </el-col>
         </el-row>
         <el-row>
         <el-col :span="8">
           <el-form-item label="项目类型">
             <span v-if="projectUpdateMethod === '0'"> {{infoData.projectTypeEnumId | fromatComon(dict.type.SPR_PROJECT_TYPE_ALL)}}</span>
             <el-select
               v-if="projectUpdateMethod === '1'"
               v-model="infoData.projectTypeEnumId"
               placeholder="项目类型"
               clearable
               size="medium"
             >
               <el-option
                 v-for="dict in dict.type.SPR_PROJECT_TYPE"
                 :key="dict.value"
                 :label="dict.label"
                 :value="dict.value"
               />
             </el-select>
           </el-form-item>
         </el-col>
         <el-col :span="8">
           <el-form-item label="审计对象">
             <span  v-if="projectUpdateMethod === '0'"> {{infoData.projectOrgName}}</span>
             <el-input v-model="infoData.projectOrgId" placeholder="审计对象"
                       size="medium"v-show="false"></el-input>
             <el-input  v-if="projectUpdateMethod === '1' && grade != 'P'" v-model="infoData.projectOrgName" placeholder="审计对象" @focus="showAuditOrg()"
                       size="medium"></el-input>
             <el-input v-model="infoData.projectOrgName" v-if="projectUpdateMethod === '1' && grade == 'P'"  placeholder="审计对象" readonly
                       size="medium"></el-input>
           </el-form-item>
         </el-col>
         <el-col :span="8">
           <el-form-item label="项目年度">
             <span  v-if="projectUpdateMethod === '0'"> {{infoData.projectYear}}</span>
             <el-date-picker  v-if="projectUpdateMethod === '1'" v-model="infoData.projectYear"   format="yyyy" value-format="yyyy" type="year"
                             :style="{width: '100%'}" placeholder="项目年度" clearable></el-date-picker>
           </el-form-item>
         </el-col>



       </el-row>
     </el-form>
    </BlockCard>
    <BlockCard title="审计报告信息">
      <!--<el-form v-if="projectUpdateMethod === '0'">-->
        <!--<el-table v-loading="tableLoading" :data="auditAttachment"-->
                  <!--border-->
                  <!--ref="table">-->
          <!--<el-table-column  type="index" min-width="8%" align="center" label="序号" >-->
            <!--<template slot-scope="scope">-->
              <!--<table-index-->
                <!--:index="scope.$index"-->
              <!--/>-->
            <!--</template>-->
          <!--</el-table-column>-->
          <!--<el-table-column label="附件名称" prop="fileName" min-width="30%" />-->
          <!--<el-table-column label="附件类型" prop="attachmentTypeName" min-width="20%" />-->
          <!--<el-table-column label="上传人" prop="uploadNickName" min-width="10%"/>-->
          <!--<el-table-column label="上传时间" prop="createTime" min-width="12%" />-->
          <!--<el-table-column-->
            <!--label="操作"-->
            <!--fixed="right"-->
            <!--min-width="10%"-->
            <!--align="center"-->
            <!--class-name="small-padding fixed-width"-->
          <!--&gt;-->
            <!--<template slot-scope="scope">-->
              <!--<el-button-->
                <!--size="mini"-->
                <!--type="text"-->
                <!--title="下载"-->
                <!--icon="el-icon-download"-->
                <!--@click="downLoadAuditFile(scope.row.enAttachmentUrl)"-->
              <!--&gt;-->
              <!--</el-button>-->
            <!--</template>-->
          <!--</el-table-column>-->
        <!--</el-table>-->
      <!--</el-form>-->
      <AuditReportInformationUpload
        v-if="projectUpdateMethod === '0'"
        :key="id"
        :id="id"
        ref="file"
      ></AuditReportInformationUpload>
      <AuditReportInformationUpload
        v-if="projectUpdateMethod === '1'"
        :edit='edit'
        :key="id"
        :id="id"
        ref="file"
      ></AuditReportInformationUpload>
    </BlockCard>
    <BlockCard title="项目台账信息">
      <div class="position">
        <div class="position-select">
          <div class="float-right " style="margin-left:15px;">
            <el-button  type="primary" plain icon="el-icon-plus" size="mini" @click="addLedgerFun">新增台账</el-button>
            <el-button  type="primary" plain  size="mini" @click="handleDownload">模板下载</el-button>

            <FileUpload
              style="display: inline-block;margin-left: 10px;"
              :fileUrl="uploadUrl"
              btnTitle="导入台账"
              uploadPrompt="问题台账导入为增量导入，请注意处理重复数据"
              :isShowTip=showTip
              icon="el-icon-download"
              :param="{projectId:id}"
              @handleUploadSuccess="handleUploadSuccess"
            >
            </FileUpload>
          </div>
        </div>
      </div>
      <el-form>
        <el-table border v-loading="tableLoading" :data="ledgerTable" :key="ledgerTable">
          <el-table-column  type="index" min-width="8%" align="center" label="序号" >
            <template slot-scope="scope">
              <table-index
                :index="scope.$index"
              />
            </template>
          </el-table-column>
          <el-table-column label="问题编号" prop="problemNum" min-width="15%" align="center" show-overflow-tooltip/>
          <el-table-column label="发现问题业务类型" prop="problemTypeEnumName"  min-width="25%" align="center" show-overflow-tooltip/>
          <el-table-column label="审计发现问题" prop="problemAudit"  min-width="25%" align="center" show-overflow-tooltip>
            <template slot-scope="scope">
              <div class="table-text-left ovflowHidden">{{ scope.row.problemAudit }}</div>
            </template>
          </el-table-column>
          <el-table-column label="具体问题描述" prop="problemDescription"  min-width="25%" show-overflow-tooltip align="center">
            <template slot-scope="scope">
              <div class="table-text-left ovflowHidden">{{ scope.row.problemDescription }}</div>
            </template>
          </el-table-column>
          <el-table-column label="是否上报告" prop="reportFlag"  min-width="15%" align="center">
            <template slot-scope="scope" class="text-center">
              {{ scope.row.reportFlag==1?'是':scope.row.reportFlag==0?'否':'' }}
            </template>
          </el-table-column>
          <el-table-column label="是否追责" prop="ifDuty"  min-width="15%" align="center">
            <template slot-scope="scope">
              <div v-if="scope.row.updateMethod === '0'">
                <el-radio-group v-model="scope.row.ifDuty" @change="toSaveLedgersList">
                  <el-radio label="1">是</el-radio>
                  <el-radio label="0">否</el-radio>
                </el-radio-group>
              </div>
              <div v-if="scope.row.updateMethod === '1'">
                {{ scope.row.ifDuty==1?'是':scope.row.ifDuty==0?'否':'' }}
              </div>
            </template>
          </el-table-column>
          <el-table-column label="是否移交纪检" prop="transferFlag"  min-width="20%" align="center">
            <template slot-scope="scope">
              <div>
                <el-radio-group v-model="scope.row.transferFlag">
                  <el-radio label="1">是</el-radio>
                  <el-radio label="0">否</el-radio>
                </el-radio-group>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="日常问题" prop="problemNum"  min-width="13%" align="center" >
            <template slot-scope="scope" class="text-center">
              <span v-if="scope.row.ifDuty !='1'"></span>
              <a @click="showProblemInfo(scope.row.id)"class="table-btn"  style='color: #c20000;'  v-if="scope.row.ifDuty =='1'">
                {{ scope.row.problemCount }}
              </a>
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            min-width="25%"
            align="center"
            class-name="small-padding fixed-width"
          >
            <template slot-scope="scope">
              <el-button
                v-if="scope.row.updateMethod === '1'"
                size="mini"
                type="text"
                icon="el-icon-edit"
                title="编辑"
                @click="updateLedgerFun(scope.row.id)"
              >
              </el-button>
              <el-button
                v-if="scope.row.updateMethod === '1'"
                size="mini"
                type="text"
                icon="el-icon-delete"
                title="删除"
                @click="delLedgers(scope.row)"
              >
              </el-button>
              <el-button
                size="mini"
                type="text"
                icon="el-icon-connection"
                @click="relation(scope.row)"
                title="关联"
                v-if="scope.row.ifDuty == '1'"
              >
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
    </BlockCard>
    <BlockCard
      title="初核专项报告"
    >
      <PreliminaryFileUpload
        :edit='edit'
        :key="id"
        :projectId = "id"
        ref="file"
      ></PreliminaryFileUpload>

    </BlockCard>
    <SynchronizeData
      ref="synchronizeData"
      :key="index"
      :id="id"
      @close="queryFlowInfo"
    ></SynchronizeData>

    <AddLedger
      ref="addLedger"
      :key="index1"
      :projectId="id"
      :ledgerId="ledgerId"
      :ledgerType="ledgerType"
      :projectUpdateMethod="projectUpdateMethod"
      @close="queryLedgersList"
    ></AddLedger>

    <el-dialog :visible.sync="auditOrgFlag" width="60%" append-to-body title="上报单位">
      <AuditOrgTree
        v-if="auditOrgFlag"
        :key="auditOrgIndex"
        ref="auditOrgTree"
        :url="auditOrgTreeUrl"
        :projectOrgId="infoData.projectOrgId"
        @list="departLists"
      />
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="sureAuditOrg" >确认</el-button>
      </div>
    </el-dialog>


    <!--台账关联日常问题列表页面-->
    <RelationDailyProblem
      ref="relation"
      :key="relationIndex"
      :pbInId="ledgerId"
      :projectId="id"
      :projectOrgId="infoData.projectOrgId"
      @onClose="queryLedgersList"
    ></RelationDailyProblem>

    <!--已关联日常问题列表页面-->
    <AssociatedDailyProblem
      ref="associated"
      :key="associatedIndex"
      :pbInId="ledgerId"
      :projectOrgId="infoData.projectOrgId"
      :edit="edit"
      @onClose="queryLedgersList"
    ></AssociatedDailyProblem>
  </div>
</template>
<script>
import BlockCard from '@/components/BlockCard';
import AddLedger from './operation/addLedger';
import SynchronizeData from './operation/synchronizeData';//同步数据存在重复信息，请重新核查手工录入的问题信息。
import PreliminaryFileUpload from './operation/fileUpload';//附件
  import {
    queryProjectInfo
    ,queryAuditFile
    ,queryLedgersList
    ,queryReportFileList
    ,delLedgers
    ,syncData
    ,saveLedgersList
    ,validateInfo
    ,saveSpecialReportInfo
  } from '@/api/special-report'
import AuditOrgTree from '@/views/specialreport/add/operation/auditOrgTree'// 审计对象
import AuditReportInformationUpload from '@/views/specialreport/add/operation/auditReportInformationUpload';//审计报告信息
import RelationDailyProblem from '@/views/specialreport/spledger/relationDailyProblem';//台账关联问题
import AssociatedDailyProblem from '@/views/specialreport/spledger/associatedDailyProblem';//已关联问题
  export default {
    components: { BlockCard,SynchronizeData,AddLedger,PreliminaryFileUpload
    ,AuditOrgTree
    ,AuditReportInformationUpload
    ,RelationDailyProblem
    ,AssociatedDailyProblem},
    props: {
      // 编辑内容
      rowData: {
        type: Object,
        default: () => {}
      },
      //流程参数
      centerVariable: {
        type: Object
      },
    },
    dicts: ['SPR_PROJECT_TYPE_ALL','SPR_PROJECT_TYPE'],
    data() {
      return {
        edit:true,
        index:0,
        index1:0,
        infoData: {
          projectName: ''
          , projectNum: ''
          , projectTypeEnumId: ''
          , projectYear: ''
          , projectOrgId: ''
          , projectOrgName: ''
        }, // 项目信息
        tableLoading: false, // 表格loading
        id : '',//主键
        auditAttachment:[],//审计报告
        ledgerTable:[],//台账信息
        showTip:false,
        attachmentTable:[],//附件信息
        handleAddFlag:'',//否有手动新增的台账信息 0:否；1：是（同步信息后需要弹窗提示删除重复数据）
        ledgerId:'',//台账表主键
        ledgerType:'',//台账修改方式 add:新增；edit:编辑
        auditFileFlag:'1',//审计报告是否校验 0：否；1：是
        projectUpdateMethod:'',//项目问题新增方式
        auditOrgIndex:0,
        auditOrgFlag:false,//上报单位是否加载
        auditOrgTreeUrl:'/spr/getAuditOrgList',
        loading:{},
        uploadUrl:'/spr/attachment/importSprLedger',//导入台账
        relationIndex:0,//关联问题
        associatedIndex:0,//已关联问题
        edit:'edit',//已关联问题可操作
        relationFlag:'1',//台账关联关系是否校验 0：否；1：是
        ledgerFlag:'1',//校验台账必填
        grade:'',//当前登录人层级
        saveFlag:true,//专项是否保存
      }
    },
    created() {
      this.$emit('collocation',{
        refreshAssigneeUrl:'/spr/flow',//业务url
        saveBtn:true,//保存按钮
      })
      this.id = this.centerVariable.busiKey;
      //查询项目信息
      this.queryFlowInfo();
    },
    methods: {
      queryFlowInfo(){
        //查询项目信息
        this.queryProjectInfo();
        //审计报告信息
        this.queryAuditFile();
        //台账信息
        this.queryLedgersList();
      },
      //根据主键查询项目信息
      queryProjectInfo(){
        queryProjectInfo(this.id).then((res)=>{
          //项目信息
          this.infoData = res.data;
          this.projectUpdateMethod = res.data.updateMethod
          this.grade = res.data.grade;
          this.$forceUpdate();
       })
      },
      //根据主键查询项目信息
      queryAuditFile(){
        queryAuditFile(this.id).then((res)=>{
          //审计报告
          this.auditAttachment = res.data;
          this.$forceUpdate();
        })
      },
      //根据主键查询项目信息
      queryLedgersList(){
        //清空台账表主键
        this.ledgerId = '';
        queryLedgersList(this.id).then((res)=>{
          //台账信息
          this.ledgerTable = res.data.ledgerReturnList;
          this.handleAddFlag = res.data.handleAddFlag;
          this.$forceUpdate();
        })
      },
      //审计报告附件下载
      downLoadAuditFile(url){
        window.location.href = url
      },
      delLedgers(obj){
        this.$modal.confirm("确认删除此条数据").then(function() {
          return delLedgers({id:obj.id,projectId:obj.projectId})
        }).then((response) => {
          this.$modal.msgSuccess("删除成功");
          //刷新台账列表
          queryLedgersList(this.id).then((res)=>{
            this.ledgerTable = res.data.ledgerReturnList;
            this.handleAddFlag = res.data.handleAddFlag;
            this.$forceUpdate();
          })
        }).catch(() => {});

      },
      //新增台账
      addLedgerFun(){
        this.ledgerType = "add"
        this.index1++;
        this.$nextTick(()=> {
          this.$refs.addLedger.show();
        });
      },
      //编辑台账
      updateLedgerFun(id){
        this.ledgerType = "edit"
        this.ledgerId = id;
        this.index1++;
        this.$nextTick(()=> {
          this.$refs.addLedger.show();
        });
      },

      //接口同步
      syncData(){
        this.openLoading();
        syncData(this.id).then((res)=>{
          this.closeLoading();
          //审计报告信息
          this.queryAuditFile();
          //台账信息
          this.queryLedgersList();

          //刷新业务信息
          if(this.handleAddFlag == 1 && res.data.flag){//弹框
            this.$nextTick(()=> {
              this.$refs.synchronizeData.show();
            });
          }else{
            this.$modal.msgSuccess('同步成功');
          }
          this.$refs.file.fieldListFun()
          this.queryFlowInfo();
        })
      },
      getLoading(){
        this.loading = this.$loading({
          lock: true,//lock的修改符--默认是false
          text: '保存中',//显示在加载图标下方的加载文案
          spinner: 'el-icon-loading',//自定义加载图标类名
          background: 'rgba(0, 0, 0, 0.7)',//遮罩层颜色
          target: document.querySelector('#table')//loadin覆盖的dom元素节点
        });
      },
      //保存
      publicSave(){
        if(this.projectUpdateMethod == '1'){
          saveSpecialReportInfo({...this.infoData, ...{id: this.id}}).then((res)=>{
            saveLedgersList(this.ledgerTable).then(()=>{
              this.$modal.msgSuccess('保存成功');
            })
          })
        }else{
          saveLedgersList(this.ledgerTable).then(()=>{
            this.$modal.msgSuccess('保存成功！')
          })
        }
      },
      //保存台账
      toSaveLedgersList(){
        saveLedgersList(this.ledgerTable).then(()=>{
          // this.queryLedgersList();
        })
      },
      loadProcessData(){
        return {}
         ;
      },
      //校验
      passValidate(){
        if(this.projectUpdateMethod == '1'){
          saveSpecialReportInfo({...this.infoData, ...{id: this.id}}).then((res)=>{
            saveLedgersList(this.ledgerTable).then(()=>{
              //提交
              this.submitFlow();
            })
          })
        }else{
          saveLedgersList(this.ledgerTable).then(()=>{
            //提交
            this.submitFlow();
          })
        }
      },
      //提交
      submitFlow(){
        let params = {
          projectId:this.id
          ,auditFileFlag:this.auditFileFlag
          ,relationFlag:this.relationFlag
          ,ledgerFlag:this.ledgerFlag
        }
        validateInfo(params).then((res)=>{
          if(res.data.flag){
            // this.$modal.msgSuccess('校验通过！')
            this.$emit('nextStep',true)
          }else{
            let validateType = res.data.validateType;
            if(validateType == 'SJBG'){
              this.$confirm(res.data.msg, '提示', {
                confirmButtonText: '继续提交',
                cancelButtonText: '返回',
                type: 'warning'
              }).then(()=> {
                //继续提交，不再校验审计报告
                this.auditFileFlag = '0';
                this.passValidate();
              }).catch(() => {});
            }if(validateType == 'sp'){
              this.$confirm(res.data.msg, '提示', {
                confirmButtonText: '直接提交',
                cancelButtonText: '返回',
                type: 'warning'
              }).then(()=> {
                //继续提交，不再校验台账关联日常问题
                this.relationFlag = '0';
                this.passValidate();
              }).catch(() => {});
            }if(validateType == 'ledger'){
              this.$confirm(res.data.msg, '提示', {
                confirmButtonText: '直接提交',
                cancelButtonText: '返回',
                type: 'warning'
              }).then(()=> {
                //继续提交，不再校验台账必填
                this.ledgerFlag = '0';
                this.passValidate();
              }).catch(() => {});
            }else{
              this.$message.error(res.data.msg);
            }
          }
        })
      },

      //审计对象
      showAuditOrg(){
        this.auditOrgIndex ++;
        this.auditOrgFlag = true;
      },
      //保存上报单位(回调函数)
      departLists(data){
        this.infoData.projectOrgName =data[0].name
        this.infoData.projectOrgId =data[0].id
        this.auditOrgFlag = false;
      },
      //确认审计对象
      sureAuditOrg(){
        //获取选中信息
        this.$refs.auditOrgTree.list();
        this.saveFlag = false
      },

      //下载台账模板
      handleDownload(){
        this.download('/spr/attachment/downLoadSpReportAttachment/D_PRO_SP_PROJECT_INFO_LEDGER', {}, "初核专项报告问题台账模板.xlsx");
      },
      // 导入台账
      handleUploadSuccess(res, file) {
        this.$modal.msgSuccess('上传成功');
        this.queryLedgersList();
      },

      openLoading(){//打开加载...
        this.$emit('openLoading');
      },
      closeLoading(){//关闭加载...
        this.$emit('closeLoading');
      },
      //关联日常问题、新增台账之前先保存专项报告
      problemSaveSpInfo(){
        saveSpecialReportInfo({...this.infoData, ...{id: this.id}}).then((res)=>{
          saveLedgersList(this.ledgerTable).then(()=>{
            this.saveFlag = true;
          })
        })
      },
      //专项台账关联问题
      relation(obj){
        if(!this.infoData.projectOrgId){
          this.$message.warning("请先选择审计对象");
          return;
        }
        if(!this.saveFlag){
          //调用保存方法
          this.problemSaveSpInfo();
        }
        this.ledgerId = obj.id;
        this.relationIndex++;
        this.$nextTick(()=> {
          this.$refs.relation.show();
        });
      },
      //攥取已关联问题列表
      showProblemInfo(id){
        this.ledgerId = id;
        this.associatedIndex++;
        this.$nextTick(()=> {
          this.$refs.associated.show();
        });
      },
    }
  }
</script>
<style scoped>
  .position-select {
    position: absolute;
    width: 100%;
    text-align: right;
    top: -54px;
  }
</style>
