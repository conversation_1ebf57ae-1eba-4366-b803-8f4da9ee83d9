<!--7:审核页面-->
<template>
  <div>
    <div>
      <ModifyrecordBtn
        :key="problemId||relevantTableId||relevantTableName"
        :problem-id="problemId"
        :relevant-table-id="relevantTableId"
        :relevant-table-name="relevantTableName"
        :problem-status="7"
      />
      <opinion
        :process-instance-id="procInsId"
        :is-show="isShow"
      />
      <BlockCard title="违规问题线索有关情况">
        <el-row>
          <el-form
            ref="elForm"
            :model="formData"
            :rules="rules"
            size="medium"
            label-width="168px"
          >
            <el-col :span="8">
              <el-form-item label="系统编号" prop="auditCode">
                <span>{{ formData.auditCode }}</span>
              </el-form-item>
            </el-col>

            <el-col :span="16">
              <el-form-item label="问题编号" prop="problemCode">
                <span>{{ formData.problemCode }}</span>
              </el-form-item>
            </el-col>

            <el-col :span="24">
              <el-form-item label="涉及企业名称" prop="">
                <span v-for="(item, index) in formData.involveCompanies">{{ item }}</span>
              </el-form-item>
            </el-col>

              <el-col :span="8">
              <el-form-item label="企业层级" prop="companyLevel">
                <span>{{ formData.companyLevel }}</span>
              </el-form-item>
            </el-col>

            <el-col :span="8">
              <el-form-item label="问题受理时间" prop="acceptTime">
                <span>{{ formData.acceptTime }}</span>
              </el-form-item>
            </el-col>

            <el-col :span="8">
              <el-form-item label="问题线索来源" prop="problemSource">
                <el-select v-model="formData.problemSource" :style="{width: '100%'}" value="" :disabled="!edit">
                  <el-option v-for="(item, index) in dict.type.VIOLD_PROBLEM_SOURCE_SASAC" :key="index" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>

            <el-col :span="8">
              <el-form-item label="涉及损失及风险（万元）" prop="involveLossRisk">
                <span>{{ formData.involveLossRisk }}</span>
              </el-form-item>
            </el-col>

            <el-col :span="8">
              <el-form-item label="损失风险类别" prop="lossRiskType">
                <el-select v-model="formData.lossRiskType" :style="{width: '100%'}" value="" :disabled="!edit">
                  <el-option v-for="(item, index) in dict.type.VIOLD_LOSS_RISK_TYPE" :key="index" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8" v-if="spLedgerShow">
              <SpLedger
                :key="problemId"
                ref="scope"
                :edit="edit"
                :problem-id="problemId"
              />
            </el-col>

            <el-col :span="24">
              <el-form-item label="问题描述" prop="problemDescribe">
                <span>{{ formData.problemDescribe }}</span>
              </el-form-item>
            </el-col>

            <el-col :span="24">
              <el-form-item label="问题类别" prop="problemAspects">
                <span v-for="item in formData.problemAspects">{{ item }}</span>
              </el-form-item>
            </el-col>

               <el-col :span="24">
              <el-form-item label="违反具体规定" prop="problemSituations">
                <span v-for="item in formData.problemSituations">{{ item }}</span>
              </el-form-item>
            </el-col>

               <el-col :span="24">
              <el-form-item label="损失形成主要原因" prop="lossReason">
                <span>{{ formData.lossReason }}</span>
              </el-form-item>
            </el-col>
          </el-form>
        </el-row>
      </BlockCard>
      <Remind
        :key="formData.actualFlag"
        :actual-flag="formData.actualFlag"
      />

  <BlockCard title="核查情况">
        <el-row>
          <el-form
            ref="elForm"
            :model="formData"
            :rules="rules"
            size="medium"
            label-width="138px"
          >
            <el-col :span="8">
              <el-form-item label="核查时间" prop="checkTime">
                <span>{{ formData.checkTime }}</span>
              </el-form-item>
            </el-col>
             <el-col :span="8">
              <el-form-item label="核查主体" prop="checkSubject">
                <span>{{ formData.checkSubject }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="核查结果" prop="checkResult">
                <span>{{ formData.checkResult }}</span>
              </el-form-item>
            </el-col>
          </el-form>
        </el-row>
      </BlockCard>

      <BlockCard title="责任追究工作开展情况">
        <el-row>
          <el-form
            ref="elForm"
            :model="formData"
            :rules="rules"
            size="medium"
            label-width="160px"
          >

            <el-col :span="8">
              <el-form-item label="是否追责" prop="accountabilityFlag">
                <span>{{ formData.accountabilityFlag ? "是" : "否" }}</span>
              </el-form-item>
            </el-col>

            <el-col :span="16">
              <el-form-item label="未追责原因" prop="unInvestigateReason">
                <span>{{ formData.unInvestigateReason }}</span>
              </el-form-item>
            </el-col>

             <el-col :span="8">
              <el-form-item label="责任追究时间" prop="investigateTime">
                <span>{{ formData.investigateTime }}</span>
              </el-form-item>
            </el-col>

              <el-col :span="8">
              <el-form-item label="责任总人数" prop="accountabilityNum">
                <span>{{ formData.accountabilityNum }}</span>
              </el-form-item>
            </el-col>

            <el-col :span="8">
              <el-form-item label="追责总人次" prop="accountabilitySum">
                <span>{{ formData.accountabilitySum }}</span>
              </el-form-item>
            </el-col>

            <el-col :span="8">
              <el-form-item label="组织处理人次" prop="orgHandleTimes">
                <span>{{ formData.orgHandleTimes }}</span>
              </el-form-item>
            </el-col>

            <el-col :span="8">
              <el-form-item label="扣减薪酬人次" prop="deductionSalaryTimes">
                <span>{{ formData.deductionSalaryTimes }}</span>
              </el-form-item>
            </el-col>

            <el-col :span="8">
              <el-form-item label="扣减薪酬金额（万元）" prop="deductionSalaryAmount">
                <span>{{ formData.deductionSalaryAmount }}</span>
              </el-form-item>
            </el-col>

             <el-col :span="8">
              <el-form-item label="党纪处分人次" prop="partyPunishmentTimes">
                <span>{{ formData.partyPunishmentTimes }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="政务处分人次" prop="governmentPunishmentTimes">
                <span>{{ formData.governmentPunishmentTimes }}</span>
              </el-form-item>
            </el-col>

             <el-col :span="8">
              <el-form-item label="禁入限制人次" prop="prohibitTimes">
                <span>{{ formData.prohibitTimes }}</span>
              </el-form-item>
            </el-col>

            <el-col :span="8" class="long_text">
              <el-form-item label="移送国家监察机关或司法机关人次" prop="transferAuthorityTimes">
                <span>{{ formData.transferAuthorityTimes }}</span>
              </el-form-item>
            </el-col>

            <el-col :span="8">
              <el-form-item label="其他人次" prop="processingOtherTimes">
                <span>{{ formData.processingOtherTimes }}</span>
              </el-form-item>
            </el-col>
          </el-form>
        </el-row>
      </BlockCard>

        <BlockCard title="问题整改情况">
        <el-row>
          <el-form
            ref="elForm"
            :model="formData"
            :rules="rules"
            size="medium"
            label-width="155px"
          >

            <el-col :span="8">
              <el-form-item label="完成制度数量" prop="perfectSystemNumber">
            <span>{{ formData.perfectSystemNumber }}</span>
              </el-form-item>
            </el-col>

            <el-col :span="8">
              <el-form-item label="挽回损失金额（万元）" prop="retrieveLossAmount">
            <span>{{ formData.retrieveLossAmount }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="降低损失风险(万元)" prop="reduceLossRisk">
                <span>{{ formData.reduceLossRisk }}</span>
              </el-form-item>
            </el-col>
              <el-col :span="24">
              <el-form-item label="完善制度名称" prop="perfectSystemName">
                <span>{{ formData.perfectSystemName }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="采取的主要措施" prop="reformMeasure">
                <span>{{ formData.reformMeasure }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="整改效果" prop="reformEffect">
                <span>{{ formData.reformEffect }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="整改复核结果" prop="reformReview">
                <span>{{ formData.reformReview }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="其他工作成效" prop="otherAchievement">
                <span>{{ formData.otherAchievement }}</span>
              </el-form-item>
            </el-col>
          </el-form>
        </el-row>
      </BlockCard>

      <BlockCard
        title="附件列表"
        class="files_Style"
      >
      <el-col :span="24" class="invol-company-content-2">
                        <div class="float-right" style="margin: 0 0 0 10px">
                          <FileUpload
                            :isShowTip="showTip"
                            fileUrl="/colligate/violDailyFiled/uploadFiledStageFile"
                            btnTitle="上传附件"
                            :param="{problemId: problemId, businessId: formData.filedStageId}"
                            @handleUploadSuccess="dailyFiledAttachments"
                          >
                            {{ '上传附件' }}
                          </FileUpload>
                        </div>
                      </el-col>
        <el-table v-loading="loading" :data="attachmentData">
          <el-table-column label="序号" type="index" :index="table_index" min-width="5%" align="center" />
          <el-table-column label="附件名称" prop="fileName" min-width="40%" align="center"/>
          <el-table-column label="附件类型" prop="fileType"  min-width="30%" align="center"/>
          <el-table-column label="附件大小" prop="fileSize"  min-width="15%" align="center"/>
          <el-table-column label="操作" fixed="right" min-width="10%" align="center" class-name="small-padding fixed-width">
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                title="下载"
                icon="el-icon-bottom"
                @click="handleDetail(scope.row)"
              ></el-button>
              <el-button
                size="mini"
                type="text"
                icon="el-icon-delete"
                title="删除"
                @click="handleDel(scope.row)"
                v-if="scope.row.stageId === formData.filedStageId"
              ></el-button>
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="total>0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="dailyFiledAttachments"
        />

      </BlockCard>

    </div>
  </div>
</template>
<script>
import BlockCard from '@/components/BlockCard'
import Remind from './../../components/remind'
import File from "./../../components/fileUpload"; // 附件
import Check2Tree from './../tree/check2Tree'// checkTree
import CheckTree from './../tree/check2Tree'// checkTree
import PersList from './../tree/persList'// tree
import opinion from '../modifyRecord/opinion'
import { initDailyFiledData, dailyFiledAttachments } from '@/api/daily/process/taskTodoViewFiled'
import { deleteViolFile } from '@/api/components/index'
import { queryDailyCheckInfo, temporarySaveViolationReformRecord, saveReformResponsibleUnit, saveReformResponsiblePerson, deleteReformResponsibleUnit, deleteReformResponsiblePerson } from '@/api/daily/process/rectificationRecord'
import ModifyrecordBtn from '../modifyRecord/btn'
import SpLedger from '@/views/daily/spledger/spLedgerData.vue';//专项报告台账
export default {
  components: {
    BlockCard,
    Remind,
    Check2Tree,
    CheckTree, File, ModifyrecordBtn, PersList,
    opinion
    ,SpLedger
  },
  dicts: ['VIOLD_PROBLEM_SOURCE_SASAC', 'VIOLD_LOSS_RISK_TYPE'],
  props: {
    isShow: {
      type: String,
      default: '0'
    },
    procInsId: {
      type: String
    },
    problemId: {
      type: String
    }
  },
  data() {
    return {
      relevantTableId: '',
      relevantTableName: '',
      rules: {},
      edit: false,
      url1: '/colligate/violationDailyReform/responsibleUnitCheckedTree',
      url2: '/colligate/violationDailyReform/nationalOrganizationPersonTree',
      flag: false,
      visible: false,
      visibleTree: false,
      VisibleCheckTree: false,
      VisibleCheckTree2: false,
      loading: false,
      formData: {
        filedStageId: "",
        auditCode: "",
        problemCode: "",
        involveCompanies: [],
        companyLevel: "",
        acceptTime: "",
        problemSource: "",
        involveLossRisk: null,
        lossRiskType: "",
        problemDescribe: "",
        problemAspects: [],
        problemSituations: [],
        lossReason: "",
        checkTime: "",
        checkSubject: "",
        checkResult: "",
        accountabilityFlag: null,
        unInvestigateReason: "",
        investigateTime: "",
        accountabilityNum: null,
        accountabilitySum: null,
        orgHandleTimes: null,
        deductionSalaryTimes: null,
        deductionSalaryAmount: null,
        partyPunishmentTimes: null,
        governmentPunishmentTimes: null,
        prohibitTimes: null,
        transferAuthorityTimes: null,
        perfectSystemNumber: null,
        retrieveLossAmount: null,
        reduceLossRisk: null,
        perfectSystemName: null,
        reformMeasure: "",
        reformEffect: "",
        reformReview: "",
        otherAchievement: ""
      },
      total: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      attachmentData: [],
      showTip:false,
      problemSourceList: [],
      spLedgerShow:false,//关联追责台账是否显示
    }
  },
  computed: {},
  watch: {},
  created() {
    this.queryDailyCheckInfo();
  },
  mounted() {},
  methods: {
    handleDetail(row) {
      this.download('/sys/attachment/downloadSysAttachment/'+row.attachmentId, {
      },row.fileName)
    },
    handleDel(row) {
      deleteViolFile(row.businessId).then(res => {
        if (200 === res.code) {
          this.$message.success("删除附件成功！");
          this.dailyFiledAttachments();
        }
      });
    },
    table_index(index) {
      return (this.queryParams.pageNum - 1) * this.queryParams.pageSize + index + 1;
    },
    // 保存数据
    savePers() {
      this.$refs.checkTree2.list()
    },
    // 数据整改责任单位
    persList(data) {
      const list = []
      if (!data.length) { return false }
      for (let i = 0; i < data.length; i++) {
        list.push(data[i].id)
      }
      const query = {
        id: this.formData.id,
        problemId: this.problemId,
        waitSaveReformResponsibleCodes: list
      }
      saveReformResponsibleUnit(query).then(
        response => {
          const { code, data } = response
          if (code === 200) {
            this.VisibleCheckTree = false
            this.queryDailyCheckInfoPer()
          }
        }
      )
    },
    // 保存数据
    savePer() {
      this.$refs.checkTree.list()
    },
    // 数据整改责任人
    persLists(data) {
      const list = []
      if (!data.length) { return false }
      for (let i = 0; i < data.length; i++) {
        list.push(data[i].id)
      }
      const query = {
        id: this.formData.id,
        problemId: this.problemId,
        waitSaveReformResponsibleCodes: list
      }
      saveReformResponsiblePerson(query).then(
        response => {
          const { code, data } = response
          if (code === 200) {
            this.VisibleCheckTree2 = false
            this.queryDailyCheckInfoPer()
          }
        }
      )
    },
    //问题线索来源切换事件
    problemSourceChanged(problemSource){
      //循环数组，取对应值的remark
      let remark = this.problemSourceList[this.problemSourceList.findIndex(item => item.dictValue === problemSource)].remark;
      if(remark.indexOf("LEDGER")>0){
        this.spLedgerShow = true;
      }else{
        this.spLedgerShow = false;
      }
    },
    /** 初始化数据*/
    queryDailyCheckInfo() {
      this.loading = true
      initDailyFiledData(this.problemId).then(
        response => {
          this.formData = { ...this.formData, ...response.data };
          this.actualFlag = response.data.actualFlag;
          this.lossRiskTypeOptions = response.data.lossRiskTypeOptions;
          this.relevantTableId = response.data.reformId;
          this.relevantTableName = response.data.businessTableName
          this.dailyFiledAttachments()
          this.problemSourceList = response.data.problemSourceList
          //判断是否显示关联台账
          if(this.formData.problemSource){
            this.problemSourceChanged(this.formData.problemSource)
          }
          this.loading = false
          this.$emit('closeLoading')
        }
      );

    },
    dailyFiledAttachments() {
      let parameter = {problemId: this.problemId}
      dailyFiledAttachments(parameter, this.queryParams).then(res => {
        this.attachmentData = res.rows;
        console.log(this.attachmentData);
        this.total = res.total;
        this.loading = false
      });
    },
    /** 初始化整改责任单位与整改责任人*/
    queryDailyCheckInfoPer() {
      queryDailyCheckInfo(this.problemId).then(
        response => {
          this.formData.reformResponsibleUnits = response.data.reformResponsibleUnits
          this.formData.reformResponsiblePersons = response.data.reformResponsiblePersons
        }
      )
    },
    /** 保存数据*/
    publicSave() {

    },
    /** 提交数据*/
    nextStep() {
      this.$emit('handle', 1, {
        orgGrade: this.formData.orgGrade,
        isJTHandover: this.formData.isGroupHandover
      })
    },
    resetForm() {
      this.$refs['elForm'].resetFields()
    },
    // 打开弹窗
    show() {
      this.visible = true
    },
    addReformResponsibleUnits() {
      this.VisibleCheckTree = true
    },
    addReformResponsiblePersons() {
      this.VisibleCheckTree2 = true
    },
    // 删除单位
    deletetReformResponsibleUnits(item, index) {
      deleteReformResponsibleUnit(item.id).then((response) => {
        this.queryDailyCheckInfoPer()
      })
    },
    // 删除责任人人
    deletetReformResponsiblePersons(item, index) {
      deleteReformResponsiblePerson(item.id).then((response) => {
        this.queryDailyCheckInfoPer()
      })
    }

  }
}
</script>
<style scoped lang="scss">
  .input-btn {
    ::v-deep .el-form-item__content {
      display: flex;
      button {
        margin-left: 8px;
        height: 35px;
      }
    }
  }
  .float-right {
    float: right;
  }
  .edit-span {
    white-space: normal;
    overflow-y: auto;
    overflow-wrap: break-word;
    word-break: normal;
    height: 61px;
    line-height: 30px;
    text-align: left;
    padding: 0px 10px;
    display: block;
  }
  ::v-deep .editStyle {
    padding: 0px !important;
  }
  ::v-deep .editStyle div.cell {
    padding: 0px !important;
  }

  ::v-deep .editStyle .el-input--mini .el-input__inner {
    height: 56px;
    line-height: 56px;
    border: 0px;
  }
  .list1 {
    overflow: hidden;
    .list1-one {
      background-color: #e6f7ff;
      color: #40a9ff;
      margin: 0 10px 10px 10px;
      float: left;
      height: 30px;
      line-height: 30px;
      padding: 0 12px 0 12px;
      border-radius: 2px;
      .close {
        padding: 8px;
        cursor: pointer;
      }
    }
  }
  .list2 {
    display: flex;
    align-items: center;
    .list2-one {
      width: 250px;
      height: 30px;
      line-height: 30px;
      margin: 0 0 12px 0;
      display: inline-block;
      padding: 0 6px 0 12px;
      border-radius: 2px;
      box-sizing: border-box;
      position: relative;
      margin-right: 35px;
      span {
        text-align: left;
        display: inline-block;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        word-break: break-all;
        max-width: 100%;
        padding-right: 6px;
        box-sizing: border-box;
      }
    }
    .list2-one-bg {
      background: #f4f4f4;
      border-radius: 4px;
      color: #000;
      width: 250px;
      height: 30px;
      line-height: 30px;
      margin: 0 0 12px 0;
      display: inline-block;
      padding: 0 6px 0 12px;
      border-radius: 2px;
      box-sizing: border-box;
      position: relative;
      margin-right: 35px;

      span {
        text-align: left;
        display: inline-block;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        word-break: break-all;
        max-width: 100%;
        padding-right: 6px;
        box-sizing: border-box;
      }
      &::before {
        position: absolute;
        content: "";
        right: -30px;
        top: 15px;
        width: 25px;
        height: 1px;
        background: #d9d9d9;
      }
    }
  }
  .bottom-line {
    padding: 10px 0;
    text-align: center;
    border-top: 1px solid #d9d9d9;
    color: #f5222d !important;
  }
  ::v-deep .long_text .el-form-item__label{
    width: 240px!important;
  }
  ::v-deep .long_text .el-form-item__content{
    margin-left: 240px!important;
  }
  .files_Style{
    position: relative;
  }
  .right-value{
    // position: absolute;
    // top: 10px;
    // right: 25px;
  }


    .invol-company-content-2 {
    margin: 4px 0;
    ::v-deep .el-button--primary {
      color: #f5222d;
      background: #fee9ea;
      border-color: #fba7ab;
    }
    ::v-deep .el-button--primary:hover {
      background: #f74e57;
      border-color: #f74e57;
      color: #fff;
    }
  }
</style>
