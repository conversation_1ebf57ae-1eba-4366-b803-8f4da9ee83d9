import request from '@/utils/request'
// 查询五日报告信息
export function queryFiveReportInfo(actualProblemId) {
    return request({
        url: '/colligate/violActualFiveReport/waitHandleFiveReport/' + actualProblemId,
        method: 'post'
    })
}

/**
 * 保存五个工作日报告
 * @param data
 */
export function saveFiveReport(data) {
  return request({
    url: '/colligate/violActualFiveReport/saveFiveReport',
    method: 'post',
    data: data
  });
}

/**
 * 违规追责实时报送五日报告与日常报送问题比较
 * @param data
 */
export function fiveReportCompareWithDailyProblem(data) {
  return request({
    url: '/colligate/violActualCompareResult/fiveReportCompareWithDailyProblem',
    method: 'post',
    data: data
  });
}

/**
 * 提交五个工作日报告
 * @param data
 */
export function submitFiveReport(data) {
  return request({
    url: '/colligate/violActualFiveReport/submitFiveReport',
    method: 'post',
    data: data
  });
}
