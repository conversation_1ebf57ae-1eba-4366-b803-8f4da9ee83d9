<!--还原默认数据-->
<template>
  <div class="app-container app-report">
    <el-form  ref="queryForm" id="queryParams"  label-width="120px">
      <el-row>
        <el-col :span="8">
          <el-form-item label="业务ID">
            <el-input v-model="reportProvId" placeholder="业务ID"  :style="{width: '100%'}">
            </el-input>
          </el-form-item>
        </el-col>
        <div class="float-left">
            <el-button class="" plain type="primary" icon="el-icon-refresh-right" size="mini" @click="restoreInitDataClick">还原默认数据</el-button>
        </div>
      </el-row>
    </el-form>
  </div>
</template>

<script>
import {restoreInitData} from '@/api/quarterly-report'
export default {
  name: "restoreInitData",
  components: {},
  data() {
    return {
      reportProvId:''
    };
  },
  created() {
  },
  filters: {
  },
  methods: {
    //还原默认数据
    restoreInitDataClick(){
      this.$confirm('确认还原数据？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(()=>{
        this.restoreInitform()
      }).catch(()=>{});
    },
    //调用还原接口
    restoreInitform(){
      restoreInitData(this.reportProvId).then((response)=>{
        if (response.code == 200) {
          this.$message({message: '操作成功',type: 'success'});
        }
      }).catch(()=>{
      })
    }
  }
};
</script>
<style rel="stylesheet/scss" lang="scss">
</style>







