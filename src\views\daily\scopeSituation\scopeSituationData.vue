<template>
  <div>
    <el-form-item   label="对应《违规经营投资责任追究办法》">
      <el-button
        v-show="edit"
        type="primary"
        plain
        icon="el-icon-plus"
        size="mini"
        @click="scopeAdd"
      >新增《违规经营投资责任追究办法》
      </el-button>
    </el-form-item>
    <el-row>
    <ScopeSituation
      :edit="edit"
      v-on:deleteScope="deleteScope"
      :scopeSituationData="scopeSituationData"
    ></ScopeSituation>
    </el-row>
    <el-row>
      <scopeSituationSelect
        ref="select"
        :key="index"
        :problemId="problemId"
        :relevantTableId = "relevantTableId"
        :relevantTableName = "relevantTableName"
        @queryRangeList="queryRangeList"
      ></scopeSituationSelect>
    </el-row>
  </div>
</template>

<script>
  import ScopeSituation from '@/components/ScopeSituation';
  import scopeSituationSelect from './scopeSituationSelect';//范围情形选择页面
  import {queryRangeList,delRangeById} from "@/api/daily/scopeSituation/index";

    export default {
      name: "scopeSituationData",
      components: {
        ScopeSituation,
        scopeSituationSelect
      },
      props: {
      edit: {
        type: Boolean,
        default:false
      },
        problemId:{
          type: String
        },
        relevantTableId:{
          type: String
        },
        relevantTableName:{
          type: String
        },
      },
      data(){
        return{
          status:'',
          index:0,
          scopeSituationData:[]
        }
      },
      created() {
        this.queryRangeList();
      },
      methods:{
        /** 获取数据*/
        queryRangeList() {
          queryRangeList({problemId:this.problemId,relevantTableId:this.relevantTableId,relevantTableName:this.relevantTableName}).then(
            response => {
             this.scopeSituationData = response.data;
             this.$emit('scopeSituation',response.data)
            }
          );
        },
        /** 新增范围情形*/
        scopeAdd() {
          this.index++;
          this.$nextTick(()=> {
            this.$refs.select.show();
          });
        },
        /** 删除操作 */
        deleteScope(item) {
          let  title = '确认删除该范围情形吗？';
          let data={};
          if(item.type==2){
            title = '确认删除该范围情形吗？';
            data.aspectCode = item.id;
            data.id=undefined;
          }else{
            title = '确认删除该方面以及方面下涉及范围情形吗？';
            data.id = item.id;
            data.aspectCode=undefined;
          }
          data.problemId = this.problemId;
          data.relevantTableId = this.relevantTableId;
          data.situationName = this.relevantTableName;
            this.$modal.confirm(title).then(function() {
              return delRangeById(data)
            }).then((response) => {
                  this.$modal.msgSuccess("删除成功");
                  this.queryRangeList();
            }).catch(() => {});
        },
      }
    }
</script>

<style scoped>

</style>
