<!--专项报告台账关联问题列表-->
<template>
  <div class="scope">
    <el-dialog v-bind="$attrs"
               :visible.sync="visible"
               width="90%"
               v-on="$listeners"
               @open="onOpen"
               @close="close"
               append-to-body :title="title">
         <el-form :model="queryParams" ref="queryForm" id="queryParams"  :inline="true" label-width="125px">
         <el-row>
           <el-col :span="8">
              <el-form-item label="违规事项">
                <el-input
                  v-model="queryParams.problemTitle"
                  placeholder="违规事项"
                  :style="{width: '100%'}"
                  clearable
                />
              </el-form-item>
           </el-col>
           <el-col :span="8">
             <el-form-item label="问题编号">
               <el-input
                 v-model="queryParams.problemCode"
                 placeholder="问题编号"
                 :style="{width: '100%'}"
                 clearable
               />
             </el-form-item>
           </el-col>
           <!--<el-col :span="8" v-if="areaList.length>0">-->
             <!--<el-form-item label="受理地市" prop="problemAreaCode">-->
               <!--<el-select v-model="queryParams.problemAreaCode" :style="{width: '100%'}">-->
                 <!--<el-option label="请选择" value="">&#45;&#45;请选择&#45;&#45;</el-option>-->
                 <!--<el-option-->
                   <!--v-for="(item,index) in areaList"-->
                   <!--:key="index"-->
                   <!--:label="item.areaName"-->
                   <!--:value="item.areaCode"-->
                 <!--/>-->
               <!--</el-select>-->
             <!--</el-form-item>-->
           <!--</el-col>-->
           <el-col :span="8">
             <el-form-item label="问题状态">
               <el-select
                 v-model="queryParams.inclusionPhases"
                 :style="{width: '100%'}"
                 clearable
                 multiple
               >
                 <el-option label="请选择" value="">--请选择--</el-option>
                 <el-option
                   v-for="(item,index) in dailyStatusList"
                   :key="index"
                   :label="item.dictLabel"
                   :value="item.dictValue"
                 />
               </el-select>
             </el-form-item>
           </el-col>
         </el-row>
           <el-row>
           <el-col :span="8">
             <el-form-item label="问题涉及单位">
               <el-input
                 v-model="queryParams.involCompany"
                 placeholder="问题涉及单位"
                 :style="{width: '100%'}"
                 clearable
               />
             </el-form-item>
           </el-col>
           <el-col :span="8">
             <el-form-item label="问题线索来源">
               <el-select v-model="queryParams.problemSource" :style="{width: '100%'}">
                 <el-option label="请选择" value="">--请选择--</el-option>
                 <el-option
                   v-for="(item,index) in sourceSasacList"
                   :key="index"
                   :label="item.dictLabel"
                   :value="item.dictValue"
                 />
               </el-select>
             </el-form-item>
           </el-col>
           <el-col :span="12">
             <el-form-item label="受理日期" prop="acceptTimes">
               <el-date-picker
                 v-model="queryParams.acceptTimes"
                 type="daterange"
                 unlink-panels
                 format="yyyy-MM-dd"
                 value-format="yyyy-MM-dd"
                 :style="{width: '90%'}"
                 range-separator="至"
                 start-placeholder="开始日期"
                 end-placeholder="结束日期"
                 clearable
               />
             </el-form-item>
           </el-col>
           <el-form-item style="float: right">
             <el-button type="primary" plain  size="mini" @click="batchRelation" v-if="tableList.length>0">批量关联</el-button>
             <el-button type="primary" plain size="mini" @click="showAssociatedProblemInfo">已关联问题</el-button>
             <el-button type="primary" icon="el-icon-search" size="mini" @click="queryUnRelationProblemList">搜索</el-button>
             <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
             <el-button  plain size="mini" @click="close">返回</el-button>
           </el-form-item>
         </el-row>
      </el-form>
      <el-form  >
      <el-table border v-loading="loading" :data="tableList" @selection-change="handleSelectionChange"  ref="table"  height="calc(70vh - 290px)">
        <el-table-column type="selection" min-width="10%" fixed="left"/>
        <el-table-column label="序号" type="index" min-width="10%" align="center"  show-overflow-tooltip>
          <template slot-scope="scope">
            <table-index
              :index="scope.$index"
              :page-num="queryParams.pageNum"
              :page-size="queryParams.pageSize"
            />
          </template>
        </el-table-column>
        <el-table-column label="问题受理地市" align="center" prop="problemAreaName" min-width="15%"  show-overflow-tooltip/>
        <el-table-column label="系统编号" align="center" prop="auditCode" min-width="15%"  show-overflow-tooltip/>
        <el-table-column label="问题编号" align="center" min-width="15%"  show-overflow-tooltip>
          <template slot-scope="scope">
            <a @click="showProblemInfo(scope.row)" class="table-btn" style='color: #c20000;'>{{scope.row.problemCode}}</a>
          </template>
        </el-table-column>
        <el-table-column
          prop="tag"
          label="违规事项"
          show-overflow-tooltip
          min-width="30%"
        >
          <template slot-scope="scope">
            {{ scope.row.problemTitle }}
          </template>
        </el-table-column>
        <el-table-column label="问题涉及单位" prop="involCompanyNames" min-width="20%" show-overflow-tooltip />
        <el-table-column label="问题线索来源" prop="problemSourceName" min-width="20%"  show-overflow-tooltip/>
        <el-table-column label="受理日期" align="center" prop="acceptTime" min-width="15%"  show-overflow-tooltip/>
        <el-table-column label="问题状态" align="center" prop="statusName" min-width="15%"  show-overflow-tooltip/>
        <el-table-column
          label="操作"
          min-width="15%"
          align="center"
          fixed="right"
          class-name="small-padding fixed-width"
        >
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-connection"
              title="关联"
              @click="sigleRelation(scope.row)"
            >
            </el-button>
          </template>
        </el-table-column>
      </el-table>
        <pagination
          v-show="total>0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="queryUnRelationProblemList"
        />
    </el-form>
  </el-dialog>
    <!--问题查看-->
    <el-dialog v-bind="$attrs" :visible.sync="showDetail" width="90%" :title="'日常问题-'+rows.problemTitle" append-to-body>
      <Details
        v-if="showDetail"
        :key="rows.problemId"
        :select-value="rows"
        :active-name="activeName"
        :proc-ins-id="rows.procInsId"
      />
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="closeProblem">确定</el-button>
      </div>
    </el-dialog>
    <!--   历史问题查看-->
    <el-dialog v-bind="$attrs" :visible.sync="hisVisible" width="90%" :title="'日常问题-'+rows.problemTitle" append-to-body>
      <HisDetails
        v-if="hisVisible"
        :key="rows.id"
        :select-value="rows"
        :active-name="activeName"
        proc-ins-id=""
      />
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="close">确定</el-button>
      </div>
    </el-dialog>

    <!--已关联日常问题列表页面-->
    <AssociatedDailyProblem
      ref="associated"
      :key="associatedIndex"
      :pbInId="pbInId"
      :projectOrgId="projectOrgId"
      :edit="edit"
      @onClose="queryUnRelationProblemList"
    ></AssociatedDailyProblem>
  </div>
</template>

<script>
  import {
    queryUnRelationProblemList,
    ledgerRelationDaily,
    queryProblemSource
  } from "@/api/daily/spledger/index";
  import {
    dailyStatus,
    queryAreaList
  } from '@/api/daily/index'
  import AssociatedDailyProblem from '@/views/specialreport/spledger/associatedDailyProblem';//已关联问题
  import Details from '@/views/daily/detail/details';// 问题详情

  import HisDetails from '@/views/daily/historyQuestionEnter/details'

  export default {
    name: "relationDaily",
    components: {
      AssociatedDailyProblem
      ,Details
      ,HisDetails
    },

    props: {
      pbInId:'',
      projectOrgId:'',
      projectId:'',
    },
    dicts: [],
    data() {
      return {
        loading:false,
        title:'关联日常问题',
        visible:false,//弹框
        // 总条数
        total: 0,
        // 表格数据
        tableList: [],
        //已选中problemId
        hasSelectList:[],
        // 查看专项报告
        showProjectKey:0,
        //问题查询 参数
        queryParams: {
          pageNum: 1,
          pageSize: 10,
          pbInId:this.pbInId,
          projectOrgId: this.projectOrgId,
          problemAreaCode: '',
          problemCode: '',
          status: '',
          involCompany: '',
          acceptTimes: [],
          acceptTimeStart: '',
          acceptTimeEnd: '',
          problemTitle: '',
          problemSource: '',
          inclusionPhases: [],
        },
        dailyStatusList: [],//问题状态
        sourceSasacList: [], // 问题线索来源下拉
        areaList: [], // 地市
        associatedIndex:0,//已关联问题
        edit:'edit',//已关联问题可操作
        rows: {},
        activeName: '0',
        showDetail:false,//是否显示详情页面
        hisVisible: false,
      };
    },
    created() {
    },
    filters: {
    },
    methods: {
      // 显示弹框
      show() {
        this.DailyStatus()
        this.SourceSasacStatus()
        // this.QueryAreaList();
        this.visible = true;
        this.queryUnRelationProblemList();
      },
      // 问题状态
      DailyStatus() {
        dailyStatus({}).then(
          response => {
            this.dailyStatusList = response.data
          }
        )
      },
      // 问题线索来源下拉
      SourceSasacStatus() {
        queryProblemSource().then(
          response => {
            this.sourceSasacList = response.data
          }
        )
      },
      // 地市
      QueryAreaList() {
        queryAreaList({ provCode: this.projectOrgId }).then(
          response => {
            this.queryParams.problemAreaCode = ''
            this.areaList = response.data
          }
        )
      },
      /**查询未关联问题列表*/
      queryUnRelationProblemList() {
        this.loading = true;
        if (this.queryParams.acceptTimes && this.queryParams.acceptTimes.length) {
          this.queryParams.acceptTimeStart = this.queryParams.acceptTimes[0]
          this.queryParams.acceptTimeEnd = this.queryParams.acceptTimes[1]
        }
        queryUnRelationProblemList(this.queryParams).then(
          response => {
            this.tableList = response.rows;
            this.total = response.total;
            this.loading = false;
          }
        );
      },
      //攥取问题编号，显示问题查看页面
      showProblemInfo(row){
        this.rows = row;
        this.rows.id = row.problemId
        if(row.hisDataFlag == '1'){
          this.$nextTick(()=>{
            this.hisVisible = true
          })
        }else{
          this.$nextTick(()=>{
            this.showDetail = true
          })
        }
      },
      /**重置按钮操作*/
      resetQuery() {
        this.queryParams = {
          pageNum: 1,
            pageSize: 10,
            pbInId:this.pbInId,
            projectOrgId: this.projectOrgId,
            problemAreaCode: '',
            problemCode: '',
            status: '',
            involCompany: '',
            acceptTimes: [],
            acceptTimeStart: '',
            acceptTimeEnd: '',
            problemTitle: '',
            problemSource: '',
          inclusionPhases: [],
        },
        this.queryUnRelationProblemList();
      },
      //台账单个关联问题
      sigleRelation(obj){
        let problemIds = []
        problemIds[0] = obj.problemId;
        var relationParams = {
          projectId:this.projectId,
          pbInId:this.pbInId,
          problemIds:problemIds
        }
        this.ledgerRelationDaily(relationParams);
      },
      //批量关联
      batchRelation(){
        if(this.hasSelectList.length == 0){
          this.$modal.msgWarning("暂无需要关联的日常问题");
          return;
        }
        var relationParams = {
          projectId:this.projectId,
          pbInId:this.pbInId,
          problemIds:this.hasSelectList
        }
        this.ledgerRelationDaily(relationParams);
      },
      //关联日常问题操作
      ledgerRelationDaily(relationParams){
        ledgerRelationDaily(relationParams).then(response=>{
          if(response.code == 200){
            this.$modal.msgSuccess("关联成功");
            //刷新列表
            this.queryUnRelationProblemList();
          }else if (response.code == 400) {
            this.$modal.msgWarning(response.msg);
          }else{
            this.$modal.msgError(response.msg);
          }
        })
      },
      close() {
        this.visible = false;
        this.$emit('onClose');
      },
      closeProblem(){
        this.showDetail = false;
      },
      //选中的值
      handleSelectionChange(val){
        let selectList = [];
        val.forEach(function (item) {
          selectList.push(item.problemId)
        });
        this.hasSelectList = selectList;
      },
      //关闭
      onOpen(){},

      //攥取已关联问题列表
      showAssociatedProblemInfo(){
        this.associatedIndex++;
        this.$nextTick(()=> {
          this.$refs.associated.show();
        });
      },
    }
  };
</script>

<style lang="scss">
  .height{
    height: 100%;
  }
</style>




