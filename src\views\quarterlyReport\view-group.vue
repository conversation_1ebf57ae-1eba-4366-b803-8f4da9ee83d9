<!-- 季度报告--查看页面--集团 -->

<template>
  <div>
    <div class="wai-container" style="background-color: #fff">
      <div class="layui-row width height">
        <div class="width height">
          <div class="common-wai-box" style="height: 100%">
            <div class="common-in-box" style="height: auto; min-height: 100%">
              <div class="common-in-box-header">
                <div class="common-in-box-header-line"></div>
                <div class="common-in-box-header-text">基本信息</div>
                <div class="flex-1"></div>
                <!-- <el-button type="primary" size="mini" @click="syncData()">同步数据</el-button> -->
                <el-button v-if="!isAllData" type="primary" size="mini" @click="detailedExport()">明细导出</el-button>
                <el-button v-if="isAllData" type="primary" size="mini" @click="detailedExportAll()">明细导出</el-button>
                <el-button v-if="isAllData"  size="mini" @click="summaryExportAll()">汇总导出</el-button>


              </div>

              <div class="common-in-box-content">
                <div class="top-search">
                  <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left">上报年度</div>

                      <div class="layui-form-value">
                        {{ infoData.reportYear }}
                      </div>
                    </div>
                  </el-col>

                  <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left">上报季度</div>
                      <div class="layui-form-value">
                        {{
                          infoData.reportQuarter
                            | fromatComon(dict.type.REPORT_QUARTER)
                        }}
                      </div>
                    </div>
                  </el-col>

                  <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left">上报截止日期</div>

                      <div class="layui-form-value">
                        <el-date-picker
                          format="yyyy-MM-dd"
                          value-format="yyyy-MM-dd"
                          v-model="infoData.reportCloseTime"
                          type="datetime"
                          placeholder=""
                          readonly
                        >
                        </el-date-picker>
                      </div>
                    </div>
                  </el-col>
                </div>

                <div class="top-search">
                  <div class="layui-col-md12 layui-col-sm12 layui-col-lg12">
                    <div class="layui-form">
                      <div class="layui-form-left">上报标题</div>

                      <div class="layui-form-value">
                        {{ infoData.reportTitle }}
                      </div>
                    </div>
                  </div>
                </div>

                <div class="top-search">
                  <div class="layui-col-md12 layui-col-sm12 layui-col-lg12">
                    <div class="layui-form">
                      <div class="layui-form-left">上报要求</div>
                      <div class="layui-form-value" id="uploadAsk">
                        {{ infoData.reportRequire }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div class="common-in-box-header" style="margin-top: 10px"      v-if="isAllData == 'isAllData'">
                <div class="common-in-box-header-line"></div>
                <div class="common-in-box-header-text">各上报单位上报情况</div>
              </div>

              <div
                class="common-in-box-content"
                v-if="isAllData == 'isAllData'"
              >
                <div class="all-province">
                  <div class="one-row-province">
                    <div class="province-left-status">
                      <span
                        class="province-left-status-text-1"
                        style="color: #2882ff"
                        >已完成：</span
                      >
                      <span class="province-mid-num" style="color: #2882ff">{{
                        ywcCount||0
                      }}</span>
                    </div>

                    <div class="province-lists" id="ywc-province">
                      <div
                        class="one-province"
                        v-for="(item, index) in ywcList"
                        :key="index"
                        :title="item"
                      >
                        {{ item }}
                      </div>
                      <div
                        class="one-province-more"
                        @click="openYwcProvince()"
                        v-if="ywcList.length > 0"
                      >
                        更多
                        <el-tooltip
                          class="item"
                          effect="dark"
                          :content="tipsText"
                          placement="top-end"
                        >
                          <el-button>
                            <span class="el-icon-question"></span>
                          </el-button>
                        </el-tooltip>

                      </div>
                    </div>
                  </div>

                  <div class="one-row-province">
                    <div class="province-left-status">
                      <span class="province-left-status-text-1">进行中：</span>
                      <span class="province-mid-num">{{ jxzCount||0 }}</span>
                    </div>

                    <div class="province-lists" id="jxz-province">
                      <div
                        class="one-province"
                        v-for="(item, index) of jxzList"
                        :key="index"
                        :title="item"
                      >
                        {{ item }}
                      </div>
                      <div
                        class="one-province-more"
                        @click="openJxzProvince()"
                        v-if="jxzList.length > 0"
                      >
                        更多
                      </div>
                    </div>
                  </div>

                  <div class="one-row-province">
                    <div class="province-left-status">
                      <span
                        class="province-left-status-text-1"
                        style="color: #000"
                        >未提交：</span
                      >
                      <span class="province-mid-num" style="color: #000">{{
                        wtjCount||0
                      }}</span>
                    </div>

                    <div class="province-lists" id="wtj-province">
                      <div
                        class="one-province"
                        v-for="(item, index) of wtjList"
                        :key="index"
                        :title="item"
                      >
                        {{ item }}
                      </div>
                      <div
                        class="one-province-more"
                        @click="openWtjProvince()"
                        v-if="wtjList.length > 0"
                      >
                        更多
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div
                class="common-in-box-header"
                style="margin-top: 10px"
                v-if="!isAllData"
              >
                <div class="common-in-box-header-line"></div>
                <div class="common-in-box-header-text">附件列表</div>
              </div>

              <div class="tables tables_1" v-if="!isAllData">
                <el-table
                  :data="filesData"
                  border
                  v-loading="tableLoading"
                  style="width: 100%"
                >
                  <el-table-column
                    label="序号"
                    type="index"
                    min-width="5%"
                    align="center"
                  />
                  <el-table-column
                    label="文件名"
                    prop="fileName"
                    min-width="50%"
                  >
                    <template slot-scope="scope">
                      <div
                        style="text-align: left"
                        class="overflowHidden-1"
                        :title="scope.row.fileName"
                      >
                        {{ scope.row.fileName || "" }}
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column
                    label="上传人"
                    prop="createUserName"
                    align="center"
                    min-width="10%"
                  />
                  <el-table-column
                    label="上传时间"
                    prop="createTime"
                    align="center"
                    min-width="20%"
                  />

                  <el-table-column
                    label="操作"
                    min-width="15%"
                    align="center"
                    class-name="small-padding fixed-width"
                  >
                    <template slot-scope="scope">
                      <el-button
                        size="mini"
                        type="text"
                        title="下载"
                        icon="el-icon-bottom"
                        @click="fileDownload(scope.row)"
                      ></el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>

              <div class="common-in-box-header" style="margin-top:20px">
                <div class="common-in-box-header-line"></div>
                <div class="common-in-box-header-text">汇总信息</div>
              </div>

              <div
                class="common-in-box-header"
                style="margin-top: 10px; border: 0px; padding-left: 10px"
              >
                <div class="common-in-box-header-text">工作部署情况</div>
              </div>

              <div class="common-in-box-content">
                <div class="top-search">
                  <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left width-label-2">
                        本季度召开领导小组会议（次）
                      </div>
                      <div class="layui-form-value">
                        {{ infoData.quarterTeamMeetingTime || "0" }}
                      </div>
                    </div>
                  </el-col>

                  <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left width-label-2">
                        本季度召开领导小组办公室会议（次）
                      </div>
                      <div class="layui-form-value">
                        {{ infoData.quarterTeamOfficeMeetingTime || "0" }}
                      </div>
                    </div>
                  </el-col>

                  <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left width-label-2">
                        本季度召开专题会议 （次）
                      </div>
                      <div class="layui-form-value">
                        {{ infoData.quarterSpecialMeetingTime || "0" }}
                      </div>
                    </div>
                  </el-col>
                </div>

                <div class="top-search">
                  <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left width-label-2">
                        当年累计召开领导小组会议（次）
                      </div>
                      <div class="layui-form-value">
                        {{ infoData.totalLeaderTeamMeetingTime || "0" }}
                      </div>
                    </div>
                  </el-col>

                  <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left width-label-2">
                        当年累计召开领导小组办公室会议（次）
                      </div>
                      <div class="layui-form-value">
                        {{ infoData.totalTeamOfficeMeetingTime || "0" }}
                      </div>
                    </div>
                  </el-col>

                  <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left width-label-2">
                        当年累计召开专题会议（次）
                      </div>
                      <div class="layui-form-value">
                        {{ infoData.totalSpecialMeetingTime || "0" }}
                      </div>
                    </div>
                  </el-col>
                </div>
              </div>

              <div
                class="common-in-box-header"
                style="margin-top: 10px; border: 0px; padding-left: 10px"
              >
                <div class="common-in-box-header-text">体系建设情况</div>
              </div>

              <div class="common-in-box-content">
                <div class="top-search">
                  <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left width-label-2">
                        2019年至今累计印发责任追究相关制度数量（项）
                      </div>
                      <div class="layui-form-value">
                        {{ infoData.totalAccountabilitySystemNumber || "0" }}
                      </div>
                    </div>
                  </el-col>

                  <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left width-label-2">
                        2019年至今累计专职人员数量 (人)
                      </div>
                      <div class="layui-form-value">
                        {{ infoData.totalProfessionalNumber || "0" }}
                      </div>
                    </div>
                  </el-col>
                </div>

                <div class="top-search">
                  <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left width-label-2">
                        当年累计新增配套制度（项）
                      </div>
                      <div class="layui-form-value">
                        {{ infoData.totalNewSupportingSystem || "0" }}
                      </div>
                    </div>
                  </el-col>

                  <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left width-label-2">
                        当年累计新增工作机制（项）
                      </div>
                      <div class="layui-form-value">
                        {{ infoData.totalNewWorkSystem || "0" }}
                      </div>
                    </div>
                  </el-col>

                  <el-col :span="8" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left width-label-2">
                        当年累计新增专职人员数量（人）
                      </div>
                      <div class="layui-form-value">
                        {{ infoData.totalNewSpecialPersonNumber || "0" }}
                      </div>
                    </div>
                  </el-col>
                </div>

                <div class="top-search">
                  <el-col :span="24" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left width-label-2">
                        新增配套制度名称
                      </div>
                      <div class="layui-form-value">
                        {{ infoData.newSupportingName || "" }}
                      </div>
                    </div>
                  </el-col>
                </div>

                <div class="top-search">
                  <el-col :span="24" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left width-label-2">
                        新增工作机制名称
                      </div>
                      <div class="layui-form-value">
                        {{ infoData.newWorkName || "" }}
                      </div>
                    </div>
                  </el-col>
                </div>

                <div class="top-search">
                  <el-col :span="24" class="height">
                    <div class="layui-form">
                      <div class="layui-form-left width-label-2">
                        集团主责部门
                      </div>
                      <div class="layui-form-value">
                        {{ infoData.groupMainDept || "" }}
                      </div>
                    </div>
                  </el-col>
                </div>

                <div
                  class="common-in-box-header"
                  style="margin-top: 10px; border: 0px; padding-left: 10px"
                >
                  <div class="common-in-box-header-text">
                    违规问题线索查办情况
                  </div>
                </div>

                <div class="common-in-box-content">
                  <div class="top-search">
                    <el-col :span="8" class="height">
                      <div class="layui-form">
                        <div class="layui-form-left width-label-2">
                          本季度新受理问题线索数量（件）
                        </div>
                        <div class="layui-form-value">
                          {{ infoData.quarterNewProblemNumber || "0" }}
                        </div>
                      </div>
                    </el-col>
                    <el-col :span="8" class="height">
                      <div class="layui-form">
                        <div class="layui-form-left width-label-2">
                          本季度涉及资产损失（万元）
                        </div>
                        <div class="layui-form-value">
                          {{ infoData.lossAmount || "0.00" }}
                        </div>
                      </div>
                    </el-col>

                    <el-col :span="8" class="height">
                      <div class="layui-form">
                        <div class="layui-form-left width-label-2">
                          本季度涉及资产损失风险（万元）
                        </div>
                        <div class="layui-form-value">
                          {{ infoData.lossRisk || "0.00" }}
                        </div>
                      </div>
                    </el-col>
                  </div>

                  <div class="top-search">
                    <el-col :span="8" class="height">
                      <div class="layui-form">
                        <div class="layui-form-left width-label-2">
                          当年累计受理问题线索数量（件）
                        </div>
                        <div class="layui-form-value">
                          {{ infoData.totalProblemSourceNumber || "0" }}
                        </div>
                      </div>
                    </el-col>

                    <el-col :span="8" class="height">
                      <div class="layui-form">
                        <div class="layui-form-left width-label-2">
                          上年结转问题线索数量（件）
                        </div>
                        <div class="layui-form-value">
                          {{ infoData.lastYearProblemSourceNumber || "0" }}
                        </div>
                      </div>
                    </el-col>
                  </div>

                  <div class="top-search">
                    <el-col :span="8" class="height">
                      <div class="layui-form">
                        <div class="layui-form-left width-label-2">
                          其中:未启动核查（件）
                        </div>
                        <div class="layui-form-value">
                          {{ infoData.checkNoStartedNumber || "0" }}
                        </div>
                      </div>
                    </el-col>

                    <el-col :span="8" class="height">
                      <div class="layui-form">
                        <div class="layui-form-left width-label-2">
                          其中: 正在核查(件)
                        </div>
                        <div class="layui-form-value">
                          {{ infoData.checkInProcessNumber || "0" }}
                        </div>
                      </div>
                    </el-col>

                    <el-col :span="8" class="height">
                      <div class="layui-form">
                        <div class="layui-form-left width-label-2">
                          其中: 完成核查(件)
                        </div>
                        <div class="layui-form-value">
                          {{ infoData.checkCompletedNumber || "0" }}
                        </div>
                      </div>
                    </el-col>
                  </div>
                </div>

                <div
                  class="common-in-box-header"
                  style="margin-top: 10px; border: 0px; padding-left: 10px"
                >
                  <div class="common-in-box-header-text">追责整改工作成效</div>
                </div>

                <div class="common-in-box-content">
                  <div class="top-search new-change-height-bottom-view">
                    <el-col :span="8" class="height">
                      <div class="layui-form">
                        <div class="layui-form-left width-label-2">
                          当年累计完成追责问题数量（件）
                        </div>
                        <div class="layui-form-value">
                          {{ infoData.totalCompletedProblemNumber || "0" }}
                        </div>
                      </div>
                    </el-col>




                  </div>
                  <div class="top-search">
                    <el-col :span="8" class="height">
                      <div class="layui-form">
                        <div class="layui-form-left width-label-2">
                          当年累计追责总人数（人）
                        </div>
                        <div class="layui-form-value">
                          {{ infoData.totalAccountabilityPersonNumber || "0" }}
                        </div>
                      </div>
                    </el-col>

                    <el-col :span="8" class="height">
                      <div class="layui-form">
                        <div class="layui-form-left width-label-2">
                          其中: 中央企业负责人（人）
                        </div>
                        <div class="layui-form-value">
                          {{ infoData.enterpriseManagementNumber || "0" }}
                        </div>
                      </div>
                    </el-col>
                    <el-col :span="8" class="height">
                      <div class="layui-form">
                        <div class="layui-form-left width-label-2">
                          其中：集团管理干部（人）
                        </div>
                        <div class="layui-form-value">
                          {{ infoData.groupManagementNumber || "0" }}
                        </div>
                      </div>
                    </el-col>



                  </div>
                  <div class="top-search new-change-height-bottom-view">

                    <el-col :span="8" class="height">
                      <div class="layui-form">
                        <div class="layui-form-left width-label-2">
                          其中: 子企业管理干部（人）
                        </div>
                        <div class="layui-form-value">
                          {{ infoData.subManagementNumber || "0" }}
                        </div>
                      </div>
                    </el-col>
                  </div>
                  <div class="top-search">
                    <el-col :span="8" class="height">
                      <div class="layui-form">
                        <div class="layui-form-left width-label-2">
                          当年累计追责总人次（人次）
                        </div>
                        <div class="layui-form-value">
                          {{ infoData.totalAccountabilityPersonTime || "0" }}
                        </div>
                      </div>
                    </el-col>

                    <el-col :span="8" class="height">
                      <div class="layui-form">
                        <div class="layui-form-left width-label-2">
                          其中: 组织处理 （人次）
                        </div>
                        <div class="layui-form-value">
                          {{ infoData.orgHandleTime || "0" }}
                        </div>
                      </div>
                    </el-col>

                    <el-col :span="8" class="height">
                      <div class="layui-form">
                        <div class="layui-form-left width-label-2">
                          其中: 扣减薪酬 （人次）
                        </div>
                        <div class="layui-form-value">
                          {{ infoData.deductionSalaryTime || "0" }}
                        </div>
                      </div>
                    </el-col>
                  </div>
                  <div class="top-search">




                    <el-col :span="8" class="height">
                      <div class="layui-form">
                        <div class="layui-form-left width-label-2">
                          其中: 党纪处分（人次）
                        </div>
                        <div class="layui-form-value">
                          {{ infoData.partyPunishmentTime || "0" }}
                        </div>
                      </div>
                    </el-col>

                    <el-col :span="8" class="height">
                      <div class="layui-form">
                        <div class="layui-form-left width-label-2">
                          其中: 政务处分（人次）
                        </div>
                        <div class="layui-form-value">
                          {{ infoData.governmentPunishmentTime || "0" }}
                        </div>
                      </div>
                    </el-col>

                    <el-col :span="8" class="height">
                      <div class="layui-form">
                        <div class="layui-form-left width-label-2">
                          其中: 禁入限制（人次）
                        </div>
                        <div class="layui-form-value">
                          {{ infoData.prohibitTime || "0" }}
                        </div>
                      </div>
                    </el-col>
                  </div>

                  <div class="top-search new-change-height-bottom-view">



                    <el-col :span="8" class="height">
                      <div class="layui-form">
                        <div class="layui-form-left width-label-2">
                          其中:移送监察机关或司法机关（人次）
                        </div>
                        <div class="layui-form-value">
                          {{ infoData.transferAuthorityTime || "0" }}
                        </div>
                      </div>
                    </el-col>

                    <el-col :span="8" class="height">
                      <div class="layui-form">
                        <div class="layui-form-left width-label-2">
                          其中: 其他 （人次）
                        </div>
                        <div class="layui-form-value">
                          {{ infoData.processingOtherItem || "0" }}
                        </div>
                      </div>
                    </el-col>
                  </div>
                  <div class="top-search new-change-height-bottom-view">


                    <el-col :span="8" class="height">
                      <div class="layui-form">
                        <div class="layui-form-left width-label-2">
                          当年累计扣减薪酬金额（万元）
                        </div>
                        <div class="layui-form-value">
                          {{ infoData.totalDeductionSalary || "0.00" }}
                        </div>
                      </div>
                    </el-col>
                  </div>

                  <div class="top-search new-change-height-bottom-view">
                    <el-col :span="8" class="height">
                      <div class="layui-form">
                        <div class="layui-form-left width-label-2">
                          责任约谈-当年累计责任约谈次数（次）
                        </div>
                        <div class="layui-form-value">
                          {{ infoData.dutyInterviewNumber || "0" }}
                        </div>
                      </div>
                    </el-col>

                    <el-col :span="8" class="height">
                      <div class="layui-form">
                        <div class="layui-form-left width-label-2">
                          责任约谈-当年累计责任约谈总人次（人次）
                        </div>
                        <div class="layui-form-value">
                          {{ infoData.dutyInterviewPersonTime || "0" }}
                        </div>
                      </div>
                    </el-col>
                  </div>

                  <div class="top-search">
                    <el-col :span="8" class="height">
                      <div class="layui-form">
                        <div class="layui-form-left width-label-2">
                          当年累计挽回资产损失（万元）
                        </div>
                        <div class="layui-form-value">
                          {{ infoData.totalRetrieveLossAmount || "0.00" }}
                        </div>
                      </div>
                    </el-col>

                    <el-col :span="8" class="height">
                      <div class="layui-form">
                        <div class="layui-form-left width-label-2">
                          当年累计降低损失风险（万元）
                        </div>
                        <div class="layui-form-value">
                          {{ infoData.totalReduceLossRisk || "0.00" }}
                        </div>
                      </div>
                    </el-col>

                    <el-col :span="8" class="height">
                      <div class="layui-form">
                        <div class="layui-form-left width-label-2">
                          当年累计制修订管理制度（项）
                        </div>
                        <div class="layui-form-value">
                          {{ infoData.totalPerfectSystemNumber || "0" }}
                        </div>
                      </div>
                    </el-col>
                  </div>

                  <div class="top-search row">
                    <el-col :span="24" class="height">
                      <div class="layui-form">
                        <div class="layui-form-left width-label-2">
                          其他工作成效
                        </div>
                        <div class="layui-form-value">
                          {{ infoData.otherAchievement || "" }}
                        </div>
                      </div>
                    </el-col>
                  </div>

                  <div
                    class="common-in-box-header"
                    v-if="!isAllData"
                    style="margin-top: 10px; border: 0px; padding-left: 10px"
                  >
                    <div class="common-in-box-header-text">其他</div>
                  </div>

                  <div class="top-search" v-if="!isAllData">
                    <el-col :span="24" class="height">
                      <div class="layui-form">
                        <div class="layui-form-left">备注</div>
                        <div class="layui-form-value">
                          {{ infoData.remark || "" }}
                        </div>
                      </div>
                    </el-col>
                  </div>

                  <div class="top-search" v-if="!isAllData">
                    <el-col :span="8" class="height">
                      <div class="layui-form">
                        <div class="layui-form-left">追责部门填报人</div>
                        <div class="layui-form-value">
                          {{ infoData.informantName || "" }}
                        </div>
                      </div>
                    </el-col>

                    <el-col :span="8" class="height">
                      <div class="layui-form">
                        <div class="layui-form-left">联系电话</div>
                        <div class="layui-form-value">
                          {{ infoData.informantPhone || "" }}
                        </div>
                      </div>
                    </el-col>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 各上报单位上报情况（已完成） -->
      <el-dialog
        class="commons_popup"
        v-bind="$attrs"
        :visible.sync="ywcvisible"
        width="90%"
        title="新各上报单位上报情况（已完成）"
        v-if="ywcvisible"
        append-to-body
      >
        <div slot="title" class="el-popup-header-title">
          <svg-icon icon-class="edit_file" />
          <span class="el-dialog-header-name">{{
            "各上报单位上报情况（已完成）"
          }}</span>
        </div>
        <ywcTable :rowData="rowData" />
      </el-dialog>

      <!-- 各上报单位上报情况（进行中） -->
      <el-dialog
        class="commons_popup"
        v-bind="$attrs"
        :visible.sync="jxzvisible"
        width="90%"
        title="新各上报单位上报情况（进行中）"
        v-if="jxzvisible"
        append-to-body
      >
        <div slot="title" class="el-popup-header-title">
          <svg-icon icon-class="edit_file" />
          <span class="el-dialog-header-name">{{
            "各上报单位上报情况（进行中）"
          }}</span>
        </div>
        <jxzTable :rowData="rowData" />
      </el-dialog>

      <!-- 各上报单位上报情况（未提交） -->
      <el-dialog
        class="commons_popup"
        v-bind="$attrs"
        :visible.sync="wwcvisible"
        width="90%"
        title="新各上报单位上报情况（未提交）"
        v-if="wwcvisible"
        append-to-body
      >
        <div slot="title" class="el-popup-header-title">
          <svg-icon icon-class="edit_file" />
          <span class="el-dialog-header-name">{{
            "各上报单位上报情况（未提交）"
          }}</span>
        </div>
        <wwcTable :rowData="rowData" />
      </el-dialog>
    </div>
  </div>
</template>
    <script>
// import {} from '@/api/views/quarterly-report'
import ywcTable from "@/views/quarterlyReport/ywcTable";
import jxzTable from "@/views/quarterlyReport/jxzTable";
import wwcTable from "@/views/quarterlyReport/wwcTable";
import {
  getSumQuarterReportProvData,
  queryQuarterReportProv,
  queryProvList,
} from "@/api/quarterly-report/view";
import {queryQuarterReportFileList} from "@/api/quarterly-report";
export default {
  components: { ywcTable, jxzTable, wwcTable },
  props: {
    //编辑内容
    rowData: {
      type: Object,
      default: () => {},
    },
    isAllData: {
      type: String,
      default: "",
    },
  },
  dicts: ['REPORT_QUARTER'],
  data() {
    return {
      infoData: {}, //基本信息
      tableLoading: false, //表格loading
      filesData: [], //附件列表
      //附件上传
      files: {
        busiTableId: "",
        busiTableName: "",
      },
      //本单位汇总信息
      formData: {},
      //已完成弹窗
      ywcvisible: false,
      //进行中弹窗
      jxzvisible: false,
      //未完成弹窗
      wwcvisible: false,
      //已完成
      ywcList: [],
      // 进行中
      jxzList: [],
      //未提交
      wtjList: [],
      wtjCount: "",
      jxzCount: "",
      ywcCount: "",
      tipsText:'可在已完成列表中进行 “驳回”'
    };
  },
  created() {
    this.formData = this.rowData;
    if (this.isAllData){
      this.getSumQuarterReportProvData(this.formData);
      this.queryProvList(this.formData);
    }else {
      this.queryQuarterReportProv(this.formData);
      //查询附件列表
      this.queryFileList();
    }
  },
  methods: {
    queryFileList(){
      this.tableLoading = true
      queryQuarterReportFileList(this.formData.quarterReportProvId).then((res)=>{
        this.filesData = res.data;
        this.tableLoading = false
      })
    },
    queryQuarterReportProv(obj) {
      queryQuarterReportProv(obj).then((response) => {

        if( response.data){
          this.infoData = response.data;
        }else{
          this.$message.error('未获取到上报单位信息')
        }
      });
    },
    getSumQuarterReportProvData(obj) {
      getSumQuarterReportProvData(obj).then((response) => {
        if(response.data){
          this.infoData = response.data;
        }/*else{
          this.$message.error('未获取到上报单位信息')
        }*/
      });
    },
    queryProvList(obj) {
      queryProvList(obj).then((response) => {
        const sumProvData = response.data;
        sumProvData.forEach((ele) => {
          if (ele.procStatus === "0") {
            this.wtjList = ele.unitNames.split(",");
            this.wtjCount = ele.provCount;
          }
          if (ele.procStatus === "1") {
            this.jxzList = ele.unitNames.split(",");
            this.jxzCount = ele.provCount;
          }
          if (ele.procStatus === "2") {
            this.ywcList = ele.unitNames.split(",");
            this.ywcCount = ele.provCount;
          }
        });

      });
    },
      //已完成
      openYwcProvince(){
          this.ywcvisible = true
          this.rowData = {
            quarterReportId:this.infoData.quarterReportId,
            procStatus:'2'
          }
      },
      //进行中
      openJxzProvince(){
        this.jxzvisible = true
        this.rowData = {
          quarterReportId:this.infoData.quarterReportId,
          procStatus:'1'
        }
      },

       //未提交
       openWtjProvince(row){
         this.wwcvisible = true
         this.rowData = {
           quarterReportId:this.infoData.quarterReportId,
           procStatus:'0'
         }
      },
      /**下载文件*/
      fileDownload(obj) {
        console.info(obj);
        this.download(
          "/sys/attachment/downloadSysAttachment/" + obj.attachmentId,
          {},
          obj.fileName
        );
      },
    // //未提交
    // openWtjProvince(row) {
    //   this.wwcvisible = true;
    //   this.rowData = row;
    // },
    // /**下载文件*/
    // fileDownload(obj) {
    //   this.download(
    //     "/sys/attachment/downloadSysAttachment/" + obj.attachmentId,
    //     {},
    //     obj.fileName
    //   );
    // },
    //
    // 同步数据
    syncData() {
      this.$confirm('确定要同步数据吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 重新获取数据
        if (this.isAllData) {
          this.getSumQuarterReportProvData(this.formData);
          this.queryProvList(this.formData);
          this.$message.success('同步成功');
        } else {
          this.queryQuarterReportProv(this.formData);
          this.queryFileList();
          this.$message.success('同步成功');
        }
      }).catch(() => {
        // 用户取消操作
      });
    },
    detailedExport() {
      this.download(
        "/quarterReport/getDataDetailExport",
        { quarterReportId: this.formData.quarterReportId },
        this.getFileName()
      );
    },

    //06查看汇总-明细导出
    detailedExportAll() {
      this.download(
        "/quarterReport/getSumDataDetailExport",
        { quarterReportId: this.formData.quarterReportId },
        this.getFileName()
      );
    },
    //07查看汇总-汇总导出
    summaryExportAll() {
      this.download(
        "/quarterReport/summaryExport",
        { quarterReportId: this.formData.quarterReportId },
        this.getFileName()
      );
    },
    fromatComon (value, list) {
      let lastLabel = '-'

      if (value && list.length > 0) {
        list.forEach(element => {
          if (element.value == value) {
            lastLabel = element.label
          }
        })
      }
      return lastLabel
    },
    getFileName(){
      const date =  this.formData.reportYear+'年'+this.fromatComon(this.formData.reportQuarter,this.dict.type.REPORT_QUARTER)
      const  filename = date+'中央企业违规经营投资责任追究工作情况表.xlsx'
      return filename;
    }
  },
};
</script>
    <style lang="scss" scoped>
@import "~@/assets/styles/quarterly-report/index.css";

.el-icon-question {
  color: #fff;
  font-size: 18px;
  margin-left: 8px;
  cursor: pointer;
}
.one-province-more .el-button{
  padding: 0px;
    background: transparent;
    border: 0;
}
</style>
