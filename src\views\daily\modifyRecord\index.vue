<template>
  <div>
    <el-tabs v-model="activeName">
      <el-tab-pane label="本次修改" name="first">
        <el-form ref="elForm" size="medium" label-width="138px">
          <el-row>
            <el-col :span="24">
              <el-form-item label="修改原因" prop="modifyReason">
                <el-input v-model="modifyReason"
                          :readonly="!edit"
                          type="textarea"
                          :autosize="{minRows: 4, maxRows: 4}"
                          :style="{width: '100%'}" clearable></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="变更内容">
                <!--业务数据-->
                <ul v-if="modifyDataList.isBusinessDataModify">
                  <li class="modify-record-li" v-for="(item,index) in modifyDataList.modifyDetails">
                    <div class="modify-record-label">
                      <span class="question-li-ranking2 question-li-ranking">{{index+1}}</span>
                      <span class="modify-record-label-name">{{item.modifyFieldCn}}</span>
                    </div>
                    <div class="modify-record-content">
                      <div class="modify-record-content-li ry-row">
                        <span class="modify-record-content-span float-left">修改前</span>
                        <p class="modify-record-content-p float-right">{{item.initialValue}}</p>
                      </div>
                      <div class="modify-record-content-li ry-row">
                        <span class="modify-record-content-span float-left">修改后</span>
                        <p class="modify-record-content-p float-right">{{item.modifyValue}}</p>
                      </div>
                    </div>
                  </li>
                </ul>
                <!--涉及单位-->
                <ul v-if="modifyDataList.isInvolveUnitModify">
                  <li class="modify-record-li">
                    <div class="modify-record-label">
                      <span class="question-li-ranking2 question-li-ranking">{{modifyDataList.unitModifyIndex}}</span>
                      <span class="modify-record-label-name">涉及单位</span>
                    </div>
                    <div class="modify-record-content">
                      <div class="modify-record-content-li ry-row">
                        <span class="modify-record-content-span float-left">修改前</span>
                        <p class="modify-record-content-p float-right">
                        <span v-for="(item,index) in modifyDataList.involveUnitInitialData">
                          {{ item.mainFlag == '1'?'（主责单位）'+item.involCompanyName:item.involCompanyName}}{{(index+1)==modifyDataList.involveUnitInitialData.length?'':'、'}}
                        </span>
                        </p>
                      </div>
                      <div class="modify-record-content-li ry-row">
                        <span class="modify-record-content-span float-left">修改后</span>
                        <p class="modify-record-content-p float-right">
                        <span v-for="(item,index) in modifyDataList.involveUnitModifyData">
                          {{item.mainFlag == '1'?'（主责单位）'+item.involCompanyName:item.involCompanyName}}{{(index+1)==modifyDataList.involveUnitModifyData.length?'':'、'}}
                        </span>
                        </p>
                      </div>
                    </div>
                  </li>
                </ul>
                <!--涉及部门-->
                <ul v-if="modifyDataList.isInvolveDeptModify">
                  <li class="modify-record-li">
                    <div class="modify-record-label">
                      <span class="question-li-ranking2 question-li-ranking">{{modifyDataList.deptInitialIndex}}</span>
                      <span class="modify-record-label-name">涉及部门</span>
                    </div>
                    <div class="modify-record-content">
                      <div class="modify-record-content-li ry-row">
                        <span class="modify-record-content-span float-left">修改前</span>
                        <p class="modify-record-content-p float-right">
                        <span v-for="(item,index) in modifyDataList.involveDeptInitialData">
                          {{item.involOrgName}}{{(index+1)==modifyDataList.involveDeptInitialData.length?'':'、'}}
                        </span>
                        </p>
                      </div>
                      <div class="modify-record-content-li ry-row">
                        <span class="modify-record-content-span float-left">修改后</span>
                        <p class="modify-record-content-p float-right">
                        <span v-for="(item,index) in modifyDataList.involveDeptModifyData">
                          {{item.involOrgName}}{{(index+1)==modifyDataList.involveDeptModifyData.length?'':'、'}}
                        </span>
                        </p>
                      </div>
                    </div>
                  </li>
                </ul>
                <!--涉及人员-->
                <ul v-if="modifyDataList.isInvolvePersonModify">
                  <li class="modify-record-li">
                    <div class="modify-record-label">
                      <span class="question-li-ranking2 question-li-ranking">{{modifyDataList.involvePersonInitialIndex}}</span>
                      <span class="modify-record-label-name">涉及人员</span>
                    </div>
                    <div class="modify-record-content" v-if="problemStatus!='4'">
                      <div class="modify-record-content-li ry-row">
                        <span class="modify-record-content-span float-left">修改前</span>
                        <p class="modify-record-content-p float-right">
                        <span v-for="(item,index) in modifyDataList.involvePersonInitialData">
                          {{item.userName}}{{(index+1)==modifyDataList.involvePersonInitialData.length?'':'、'}}
                        </span>
                        </p>
                      </div>
                      <div class="modify-record-content-li ry-row">
                        <span class="modify-record-content-span float-left">修改后</span>
                        <p class="modify-record-content-p float-right">
                        <span v-for="(item,index) in modifyDataList.involvePersonModifyData">
                          {{item.userName}}{{(index+1)==modifyDataList.involvePersonModifyData.length?'':'、'}}
                        </span>
                        </p>
                      </div>
                    </div>
                    <div class="modify-record-content new-style"
                         v-if="problemStatus=='4'">
                      <div class="modify-record-content-li ry-row">
                        <span class="modify-record-content-span float-left">修改前</span>

                        <el-table
                          :data="modifyDataList.involvePersonInitialData"
                          border
                          stripe
                          style="width: 100%;"
                          fixed="right"
                        >
                          <el-table-column
                            label="序号"
                            type="index"
                            width="80"
                            align="center"
                          />
                          <el-table-column
                            label="姓名"
                            prop="userName"
                            width="100"
                            align="center"
                          />
                          <el-table-column
                            label="组织"
                            prop="involAreaName"
                            width="180"
                            align="center"
                          />
                          <el-table-column
                            :show-overflow-tooltip="true"
                            align="center"
                            prop="postName"
                            label="职务"
                            width="250"
                          >
                          </el-table-column>

                          <el-table-column
                            :show-overflow-tooltip="true"
                            align="center"
                            prop="dutyType"
                            label="责任类型"
                            min-width="500"
                          >
                            <template slot-scope="scope">
                              {{scope.row.dutyType}}
                              <el-checkbox-group
                                :key="scope.row.dutyType"
                                v-model="scope.row.dutyType"
                                size="medium"
                              >
                                <el-checkbox
                                  disabled
                                  v-for="item4 in involCompanyDutyList"
                                  :key="item4.dictValue"
                                  border
                                  :true-label="item4.dictValue"
                                  :label="item4.dictLabel"
                                >{{ item4.dictLabel }}
                                </el-checkbox>
                              </el-checkbox-group>
                            </template>
                          </el-table-column>

                          <el-table-column
                            :show-overflow-tooltip="true"
                            align="center"
                            prop="dutyReasonStandard"
                            label="责任认定原因标准"
                            width="250"
                          >
                          </el-table-column>

                          <el-table-column
                            :show-overflow-tooltip="true"
                            align="center"
                            prop="heavierPunishReason"
                            label="是否适用从重处罚"
                            width="350"
                          >
                            <template slot-scope="scope">
                              <div style="display: flex; align-items: center">
                                <span class="edit-span">{{scope.row.heavierFlag=='1'?'是':'否'}}：{{ scope.row.heavierPunishReason }}</span>
                              </div>
                            </template>
                          </el-table-column>

                          <el-table-column
                            :show-overflow-tooltip="true"
                            align="center"
                            prop="lighterImpunityReason"
                            label="是否适用从轻处罚或免除处罚"
                            width="350"
                          >
                            <template slot-scope="scope">
                              <div style="display: flex; align-items: center">
                                <span
                                  v-show="!scope.row.is_show_tag4"
                                  class="edit-span"
                                >
                                  {{scope.row.lighterImpunityFlag=='1'?'是':'否'}}：
                                  {{ scope.row.lighterImpunityReason }}</span>
                              </div>
                            </template>
                          </el-table-column>
                        </el-table>
                      </div>
                      <div class="modify-record-content-li ry-row">
                        <span class="modify-record-content-span float-left">修改后</span>
                        <el-table
                          :data="modifyDataList.involvePersonModifyData"
                          border
                          stripe
                          style="width: 100%;"
                          fixed="right"
                        >
                          <el-table-column
                            label="序号"
                            type="index"
                            width="80"
                            align="center"
                          />
                          <el-table-column
                            label="姓名"
                            prop="userName"
                            width="100"
                            align="center"
                          />
                          <el-table-column
                            label="组织"
                            prop="involAreaName"
                            width="180"
                            align="center"
                          />
                          <el-table-column
                            :show-overflow-tooltip="true"
                            align="center"
                            prop="postName"
                            label="职务"
                            width="250"
                          >
                          </el-table-column>

                          <el-table-column
                            :show-overflow-tooltip="true"
                            align="center"
                            prop="dutyType"
                            label="责任类型"
                            min-width="500"
                          >
                            <template slot-scope="scope">
                              <el-checkbox-group
                                v-model="scope.row.dutyType"
                                size="medium"
                              >
                                <el-checkbox
                                  disabled
                                  v-for="item4 in involCompanyDutyList"
                                  border
                                  :true-label="item4.dictValue"
                                  :label="item4.dictLabel"
                                />
                              </el-checkbox-group>
                            </template>
                          </el-table-column>

                          <el-table-column
                            :show-overflow-tooltip="true"
                            align="center"
                            prop="dutyReasonStandard"
                            label="责任认定原因标准"
                            width="250"
                          >
                          </el-table-column>

                          <el-table-column
                            :show-overflow-tooltip="true"
                            align="center"
                            prop="heavierPunishReason"
                            label="是否适用从重处罚"
                            width="350"
                          >
                            <template slot-scope="scope">
                              <div style="display: flex; align-items: center">
                                <span class="edit-span">{{scope.row.heavierFlag=='1'?'是':'否'}}：{{ scope.row.heavierPunishReason }}</span>
                              </div>
                            </template>
                          </el-table-column>

                          <el-table-column
                            :show-overflow-tooltip="true"
                            align="center"
                            prop="lighterImpunityReason"
                            label="是否适用从轻处罚或免除处罚"
                            width="350"
                          >
                            <template slot-scope="scope">
                              <div style="display: flex; align-items: center">
                                <span
                                  v-show="!scope.row.is_show_tag4"
                                  class="edit-span"
                                >
                                  {{scope.row.lighterImpunityFlag=='1'?'是':'否'}}：
                                  {{ scope.row.lighterImpunityReason }}</span>
                              </div>
                            </template>
                          </el-table-column>
                        </el-table>
                      </div>
                    </div>
                    <div class="modify-record-content new-style"
                         v-if="problemStatus=='5'">
                      <div class="modify-record-content-li ry-row">
                        <span class="modify-record-content-span float-left">修改前</span>

                        <el-table :data="modifyDataList.involvePersonInitialData" border stripe style="width: 100%">
                          <el-table-column
                            label="序号"
                            type="index"
                            width="80"
                            align="center"
                            fixed
                          />
                          <el-table-column
                            label="涉及人员"
                            prop="userName"
                            width="180"
                            align="center"
                            fixed
                          />
                          <el-table-column
                            label="部门"
                            prop="involOrgName"
                            width="180"
                            align="center"
                            fixed
                          />

                          <el-table-column
                            label="职务"
                            prop="postName"
                            width="180"
                            align="center"
                            fixed
                          />

                          <el-table-column
                            label="追责总人次"
                            prop="accountabilitySum"
                            width="180"
                            align="center"
                            fixed
                          />

                          <el-table-column
                            :show-overflow-tooltip="true"
                            align="center"
                            prop="enddate"
                            label="处理方式"
                            min-width="1100"
                          >
                            <template slot-scope="scope">
                              <el-checkbox-group
                                class="checkbox-group-specLists"
                                v-if="scope.row.userName !== '合计'"
                                :key="scope.row.specLists"
                                v-model="scope.row.specLists"
                                size="medium"
                                disabled="true"
                              >
                                <el-checkbox
                                  v-for="item in scope.row.detailWidth"
                                  :key="item.dictValue"
                                  border
                                  :label="item.dictValue"
                                >{{ item.dictLabel }}</el-checkbox>
                              </el-checkbox-group>

                              <el-checkbox-group
                                class="checkbox-group-specLists checkbox-group-processingOther"
                                v-if="scope.row.userName !== '合计'"
                                :key="scope.row.processingOther"
                                v-model="scope.row.processingOther"
                                size="medium"
                                disabled="true"
                              >
                                <el-checkbox
                                  v-for="dict in dict.type.other_handler_way"
                                  :key="dict.value"
                                  border
                                  :label="dict.value"
                                >{{ dict.label }}</el-checkbox>
                              </el-checkbox-group>
                            </template>
                          </el-table-column>

                          <el-table-column
                            :show-overflow-tooltip="true"
                            align="center"
                            prop="deductionSalary"
                            label="扣减金额"
                            width="250"
                          />

                          <el-table-column
                            label="组织处理+扣减薪酬"
                            type="handleDeductionCount"
                            width="180"
                            align="center"
                          >
                            <template
                              v-if="scope.row.userName !== '合计'"
                              slot-scope="scope"
                            >
                              <span v-if="scope.row.handleDeductionCount == '1'">是</span>
                              <span v-if="scope.row.handleDeductionCount != '1'">否</span>
                            </template>

                            <template
                              v-if="scope.row.userName === '合计'"
                              slot-scope="scope"
                            >
                              <span>{{ scope.row.handleDeductionCount }}</span>
                            </template>
                          </el-table-column>

                          <el-table-column
                            label="政务处分+扣减薪酬"
                            type="governmentDeductionCount"
                            width="180"
                            align="center"
                          >
                            <template
                              v-if="scope.row.userName !== '合计'"
                              slot-scope="scope"
                            >
                    <span
                      v-if="scope.row.governmentDeductionCount == '1'"
                    >是</span>
                              <span
                                v-if="scope.row.governmentDeductionCount != '1'"
                              >否</span>
                            </template>

                            <template
                              v-if="scope.row.userName === '合计'"
                              slot-scope="scope"
                            >
                              <span>{{ scope.row.governmentDeductionCount }}</span>
                            </template>
                          </el-table-column>

                          <el-table-column
                            label="党纪处分+扣减薪酬"
                            type="partyDeductionCount"
                            width="180"
                            align="center"
                          >
                            <template
                              v-if="scope.row.userName !== '合计'"
                              slot-scope="scope"
                            >
                              <span v-if="scope.row.partyDeductionCount == '1'">是</span>
                              <span v-if="scope.row.partyDeductionCount != '1'">否</span>
                            </template>

                            <template
                              v-if="scope.row.userName === '合计'"
                              slot-scope="scope"
                            >
                              <span>{{ scope.row.partyDeductionCount }}</span>
                            </template>
                          </el-table-column>
                        </el-table>
                      </div>
                      <div class="modify-record-content-li ry-row">
                        <span class="modify-record-content-span float-left">修改后</span>
                        <el-table :data="modifyDataList.involvePersonModifyData" border stripe style="width: 100%">
                          <el-table-column
                            label="序号"
                            type="index"
                            width="80"
                            align="center"
                            fixed
                          />
                          <el-table-column
                            label="涉及人员"
                            prop="userName"
                            width="180"
                            align="center"
                            fixed
                          />
                          <el-table-column
                            label="部门"
                            prop="involOrgName"
                            width="180"
                            align="center"
                            fixed
                          />

                          <el-table-column
                            label="职务"
                            prop="postName"
                            width="180"
                            align="center"
                            fixed
                          />

                          <el-table-column
                            label="追责总人次"
                            prop="accountabilitySum"
                            width="180"
                            align="center"
                            fixed
                          />

                          <el-table-column
                            :show-overflow-tooltip="true"
                            align="center"
                            prop="enddate"
                            label="处理方式"
                            min-width="1100"
                          >
                            <template slot-scope="scope">
                              <el-checkbox-group
                                class="checkbox-group-specLists"
                                v-if="scope.row.userName !== '合计'"
                                :key="scope.row.specLists"
                                v-model="scope.row.specLists"
                                size="medium"
                                disabled="true"
                              >
                                <el-checkbox
                                  v-for="item in scope.row.detailWidth"
                                  :key="item.dictValue"
                                  border
                                  :label="item.dictValue"
                                >{{ item.dictLabel }}</el-checkbox>
                              </el-checkbox-group>

                              <el-checkbox-group
                                class="checkbox-group-specLists checkbox-group-processingOther"
                                v-if="scope.row.userName !== '合计'"
                                :key="scope.row.processingOther"
                                v-model="scope.row.processingOther"
                                size="medium"
                                disabled="true"
                              >
                                <el-checkbox
                                  v-for="dict in dict.type.other_handler_way"
                                  :key="dict.value"
                                  border
                                  :label="dict.value"
                                >{{ dict.label }}</el-checkbox>
                              </el-checkbox-group>
                            </template>
                          </el-table-column>

                          <el-table-column
                            :show-overflow-tooltip="true"
                            align="center"
                            prop="deductionSalary"
                            label="扣减金额"
                            width="250"
                          />

                          <el-table-column
                            label="组织处理+扣减薪酬"
                            type="handleDeductionCount"
                            width="180"
                            align="center"
                          >
                            <template
                              v-if="scope.row.userName !== '合计'"
                              slot-scope="scope"
                            >
                              <span v-if="scope.row.handleDeductionCount == '1'">是</span>
                              <span v-if="scope.row.handleDeductionCount != '1'">否</span>
                            </template>

                            <template
                              v-if="scope.row.userName === '合计'"
                              slot-scope="scope"
                            >
                              <span>{{ scope.row.handleDeductionCount }}</span>
                            </template>
                          </el-table-column>

                          <el-table-column
                            label="政务处分+扣减薪酬"
                            type="governmentDeductionCount"
                            width="180"
                            align="center"
                          >
                            <template
                              v-if="scope.row.userName !== '合计'"
                              slot-scope="scope"
                            >
                    <span
                      v-if="scope.row.governmentDeductionCount == '1'"
                    >是</span>
                              <span
                                v-if="scope.row.governmentDeductionCount != '1'"
                              >否</span>
                            </template>

                            <template
                              v-if="scope.row.userName === '合计'"
                              slot-scope="scope"
                            >
                              <span>{{ scope.row.governmentDeductionCount }}</span>
                            </template>
                          </el-table-column>

                          <el-table-column
                            label="党纪处分+扣减薪酬"
                            type="partyDeductionCount"
                            width="180"
                            align="center"
                          >
                            <template
                              v-if="scope.row.userName !== '合计'"
                              slot-scope="scope"
                            >
                              <span v-if="scope.row.partyDeductionCount == '1'">是</span>
                              <span v-if="scope.row.partyDeductionCount != '1'">否</span>
                            </template>

                            <template
                              v-if="scope.row.userName === '合计'"
                              slot-scope="scope"
                            >
                              <span>{{ scope.row.partyDeductionCount }}</span>
                            </template>
                          </el-table-column>
                        </el-table>
                      </div>
                    </div>
                  </li>
                </ul>
                <!--情形范围-->
                <ul v-if="modifyDataList.isSituationRangeModify">
                  <li class="modify-record-li">
                    <div class="modify-record-label">
                      <span class="question-li-ranking2 question-li-ranking">{{modifyDataList.situationRangeModifyIndex}}</span>
                      <span class="modify-record-label-name">情形范围</span>
                    </div>
                    <div class="modify-record-content">
                      <div class="modify-record-content-li ry-row">
                        <span class="modify-record-content-span float-left">修改前</span>
                        <p class="modify-record-content-p float-right">
                        <span v-for="(item,index) in modifyDataList.situationRangeInitialData">
                        {{item.aspectName}} - {{item.situationName}}{{(index+1)==modifyDataList.situationRangeInitialData.length?'':'、'}}
                        </span>
                        </p>
                      </div>
                      <div class="modify-record-content-li ry-row">
                        <span class="modify-record-content-span float-left">修改后</span>
                        <p class="modify-record-content-p float-right">
                        <span v-for="(item,index) in modifyDataList.situationRangeModifyData">
                        {{item.aspectName}} - {{item.situationName}}{{(index+1)==modifyDataList.situationRangeModifyData.length?'':'、'}}
                        </span>
                        </p>
                      </div>
                    </div>
                  </li>
                </ul>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-tab-pane>
      <el-tab-pane label="修改历史" name="second">
        <div v-if="historyData.length">
          <ul  v-for="(item,index) in historyData">
            <li class="modify-history-li ry-row">
              <div class="history-li-left float-left">
                <p class="history-li-left-name">{{item.modifyStage}}</p>
                <p class="history-li-left-time">{{item.modifyTime}}</p>
              </div>
              <div class="history-li-right float-right">
                <div class="history-li-title ry-row">
                  <span class="history-li-user float-left">{{item.modifyUser}}</span>
                  <div class="history-li-text float-left">
                    <span>修改原因：</span>
                    {{item.modifyReason}}
                  </div>
                </div>
                <div class="history-li-content">
                  <ul class="modify-record-ul">
                    <!--业务数据-->
                    <li class="modify-record-li" v-if="item.isBusinessDataModify" v-for="(obj,indexs) in item.businessModifyDetails">
                      <div class="modify-record-label">
                        <span class="modify-record-label-name">{{obj.modifyFieldCn}}</span>
                      </div>
                      <div class="modify-record-content">
                        <div class="modify-record-content-li ry-row">
                          <span class="modify-record-content-span float-left">修改前</span>
                          <p class="modify-record-content-p float-right">{{obj.initialValue}}</p>
                        </div>
                        <div class="modify-record-content-li ry-row">
                          <span class="modify-record-content-span float-left">修改后</span>
                          <p class="modify-record-content-p float-right">{{obj.modifyValue}}</p>
                        </div>
                      </div>
                    </li>
                    <!--涉及单位-->
                    <li class="modify-record-li" v-if="item.isInvolveUnitModify">
                      <div class="modify-record-label">
                        <span class="modify-record-label-name">涉及单位</span>
                      </div>
                      <div class="modify-record-content">
                        <div class="modify-record-content-li ry-row">
                          <span class="modify-record-content-span float-left">修改前</span>
                          <p class="modify-record-content-p float-right">
                        <span v-for="(obj,indexs) in item.involveUnitInitialData">
                          {{obj.mainFlag == '1'?'（主责单位）'+obj.involCompanyName:obj.involCompanyName}}{{(indexs+1)==item.involveUnitInitialData.length?'':'、'}}
                        </span>
                          </p>
                        </div>
                        <div class="modify-record-content-li ry-row">
                          <span class="modify-record-content-span float-left">修改后</span>
                          <p class="modify-record-content-p float-right">
                        <span v-for="(obj,indexs) in item.involveUnitModifyData">
                          {{obj.mainFlag == '1'?'（主责单位）'+obj.involCompanyName:obj.involCompanyName}}{{(indexs+1)==item.involveUnitModifyData.length?'':'、'}}
                        </span>
                          </p>
                        </div>
                      </div>
                    </li>
                    <!--涉及部门-->
                    <li class="modify-record-li" v-if="item.isInvolveDeptModify">
                      <div class="modify-record-label">
                        <span class="modify-record-label-name">涉及部门</span>
                      </div>
                      <div class="modify-record-content">
                        <div class="modify-record-content-li ry-row">
                          <span class="modify-record-content-span float-left">修改前</span>
                          <p class="modify-record-content-p float-right">
                        <span v-for="(obj,indexs) in item.involveDeptInitialData">
                          {{obj.involOrgName}}{{(indexs+1)==item.involveDeptInitialData.length?'':'、'}}
                        </span>
                          </p>
                        </div>
                        <div class="modify-record-content-li ry-row">
                          <span class="modify-record-content-span float-left">修改后</span>
                          <p class="modify-record-content-p float-right">
                        <span v-for="(obj,indexs) in item.involveDeptModifyData">
                          {{obj.involOrgName}}{{(indexs+1)==item.involveDeptModifyData.length?'':'、'}}
                        </span>
                          </p>
                        </div>
                      </div>
                    </li>
                    <!--涉及人员-->
                    <li class="modify-record-li" v-if="item.isInvolvePersonModify">
                      <div class="modify-record-label">
                        <span class="modify-record-label-name">涉及人员</span>
                      </div>
                      <div class="modify-record-content">
                        <div class="modify-record-content-li ry-row">
                          <span class="modify-record-content-span float-left">修改前</span>
                          <p class="modify-record-content-p float-right">
                        <span v-for="(obj,indexs) in item.involvePersonInitialData">
                          {{obj.userName}}{{(indexs+1)==item.involvePersonInitialData.length?'':'、'}}
                        </span>
                          </p>
                        </div>
                        <div class="modify-record-content-li ry-row">
                          <span class="modify-record-content-span float-left">修改后</span>
                          <p class="modify-record-content-p float-right">
                        <span v-for="(obj,indexs) in item.involvePersonModifyData">
                          {{obj.userName}}{{(indexs+1)==item.involvePersonModifyData.length?'':'、'}}
                        </span>
                          </p>
                        </div>
                      </div>
                      <div class="modify-record-content new-style"
                           v-if="item.modifyStage=='核查记录'">
                        <div class="modify-record-content-li ry-row">
                          <span class="modify-record-content-span float-left">修改前</span>

                          <el-table
                            :data="modifyDataList.involvePersonInitialData"
                            border
                            stripe
                            style="width: 100%;"
                            fixed="right"
                          >
                            <el-table-column
                              label="序号"
                              type="index"
                              width="80"
                              align="center"
                            />
                            <el-table-column
                              label="姓名"
                              prop="userName"
                              width="100"
                              align="center"
                            />
                            <el-table-column
                              label="组织"
                              prop="involAreaName"
                              width="180"
                              align="center"
                            />
                            <el-table-column
                              :show-overflow-tooltip="true"
                              align="center"
                              prop="postName"
                              label="职务"
                              width="250"
                            >
                            </el-table-column>

                            <el-table-column
                              :show-overflow-tooltip="true"
                              align="center"
                              prop="dutyType"
                              label="责任类型"
                              min-width="500"
                            >
                              <template slot-scope="scope">
                                <el-checkbox-group
                                  :key="scope.row.dutyType"
                                  v-model="scope.row.dutyType"
                                  size="medium"
                                >
                                  <el-checkbox
                                    disabled
                                    v-for="item4 in involCompanyDutyList"
                                    :key="item4.dictValue"
                                    border
                                    :true-label="item4.dictValue"
                                    :label="item4.dictLabel"
                                  >{{ item4.dictLabel }}
                                  </el-checkbox>
                                </el-checkbox-group>
                              </template>
                            </el-table-column>

                            <el-table-column
                              :show-overflow-tooltip="true"
                              align="center"
                              prop="dutyReasonStandard"
                              label="责任认定原因标准"
                              width="250"
                            >
                            </el-table-column>

                            <el-table-column
                              :show-overflow-tooltip="true"
                              align="center"
                              prop="heavierPunishReason"
                              label="是否适用从重处罚"
                              width="350"
                            >
                              <template slot-scope="scope">
                                <div style="display: flex; align-items: center">
                                  <span class="edit-span">{{scope.row.heavierFlag=='1'?'是':'否'}}：{{ scope.row.heavierPunishReason }}</span>
                                </div>
                              </template>
                            </el-table-column>

                            <el-table-column
                              :show-overflow-tooltip="true"
                              align="center"
                              prop="lighterImpunityReason"
                              label="是否适用从轻处罚或免除处罚"
                              width="350"
                            >
                              <template slot-scope="scope">
                                <div style="display: flex; align-items: center">
                                <span
                                  v-show="!scope.row.is_show_tag4"
                                  class="edit-span"
                                >
                                  {{scope.row.lighterImpunityFlag=='1'?'是':'否'}}：
                                  {{ scope.row.lighterImpunityReason }}</span>
                                </div>
                              </template>
                            </el-table-column>
                          </el-table>
                        </div>
                        <div class="modify-record-content-li ry-row">
                          <span class="modify-record-content-span float-left">修改后</span>
                          <el-table
                            :data="modifyDataList.involvePersonModifyData"
                            border
                            stripe
                            style="width: 100%;"
                            fixed="right"
                          >
                            <el-table-column
                              label="序号"
                              type="index"
                              width="80"
                              align="center"
                            />
                            <el-table-column
                              label="姓名"
                              prop="userName"
                              width="100"
                              align="center"
                            />
                            <el-table-column
                              label="组织"
                              prop="involAreaName"
                              width="180"
                              align="center"
                            />
                            <el-table-column
                              :show-overflow-tooltip="true"
                              align="center"
                              prop="postName"
                              label="职务"
                              width="250"
                            >
                            </el-table-column>

                            <el-table-column
                              :show-overflow-tooltip="true"
                              align="center"
                              prop="dutyType"
                              label="责任类型"
                              min-width="500"
                            >
                              <template slot-scope="scope">
                                <el-checkbox-group
                                  :key="scope.row.dutyType"
                                  v-model="scope.row.dutyType"
                                  size="medium"
                                >
                                  <el-checkbox
                                    disabled
                                    v-for="item4 in involCompanyDutyList"
                                    :key="item4.dictValue"
                                    border
                                    :true-label="item4.dictValue"
                                    :label="item4.dictLabel"
                                  >{{ item4.dictLabel }}
                                  </el-checkbox>
                                </el-checkbox-group>
                              </template>
                            </el-table-column>

                            <el-table-column
                              :show-overflow-tooltip="true"
                              align="center"
                              prop="dutyReasonStandard"
                              label="责任认定原因标准"
                              width="250"
                            >
                            </el-table-column>

                            <el-table-column
                              :show-overflow-tooltip="true"
                              align="center"
                              prop="heavierPunishReason"
                              label="是否适用从重处罚"
                              width="350"
                            >
                              <template slot-scope="scope">
                                <div style="display: flex; align-items: center">
                                  <span class="edit-span">{{scope.row.heavierFlag=='1'?'是':'否'}}：{{ scope.row.heavierPunishReason }}</span>
                                </div>
                              </template>
                            </el-table-column>

                            <el-table-column
                              :show-overflow-tooltip="true"
                              align="center"
                              prop="lighterImpunityReason"
                              label="是否适用从轻处罚或免除处罚"
                              width="350"
                            >
                              <template slot-scope="scope">
                                <div style="display: flex; align-items: center">
                                <span
                                  v-show="!scope.row.is_show_tag4"
                                  class="edit-span"
                                >
                                  {{scope.row.lighterImpunityFlag=='1'?'是':'否'}}：
                                  {{ scope.row.lighterImpunityReason }}</span>
                                </div>
                              </template>
                            </el-table-column>
                          </el-table>
                        </div>
                      </div>
                      <div class="modify-record-content new-style"
                           v-if="item.modifyStage=='处理及申诉记录'">
                        <div class="modify-record-content-li ry-row">
                          <span class="modify-record-content-span float-left">修改前</span>

                          <el-table :data="modifyDataList.involvePersonInitialData" border stripe style="width: 100%">
                            <el-table-column
                              label="序号"
                              type="index"
                              width="80"
                              align="center"
                              fixed
                            />
                            <el-table-column
                              label="涉及人员"
                              prop="userName"
                              width="180"
                              align="center"
                              fixed
                            />
                            <el-table-column
                              label="部门"
                              prop="involOrgName"
                              width="180"
                              align="center"
                              fixed
                            />

                            <el-table-column
                              label="职务"
                              prop="postName"
                              width="180"
                              align="center"
                              fixed
                            />

                            <el-table-column
                              label="追责总人次"
                              prop="accountabilitySum"
                              width="180"
                              align="center"
                              fixed
                            />

                            <el-table-column
                              :show-overflow-tooltip="true"
                              align="center"
                              prop="enddate"
                              label="处理方式"
                              min-width="1100"
                            >
                              <template slot-scope="scope">
                                <el-checkbox-group
                                  class="checkbox-group-specLists"
                                  v-if="scope.row.userName !== '合计'"
                                  :key="scope.row.specLists"
                                  v-model="scope.row.specLists"
                                  size="medium"
                                  disabled="true"
                                >
                                  <el-checkbox
                                    v-for="item in scope.row.detailWidth"
                                    :key="item.dictValue"
                                    border
                                    :label="item.dictValue"
                                  >{{ item.dictLabel }}</el-checkbox>
                                </el-checkbox-group>

                                <el-checkbox-group
                                  class="checkbox-group-specLists checkbox-group-processingOther"
                                  v-if="scope.row.userName !== '合计'"
                                  :key="scope.row.processingOther"
                                  v-model="scope.row.processingOther"
                                  size="medium"
                                  disabled="true"
                                >
                                  <el-checkbox
                                    v-for="dict in dict.type.other_handler_way"
                                    :key="dict.value"
                                    border
                                    :label="dict.value"
                                  >{{ dict.label }}</el-checkbox>
                                </el-checkbox-group>
                              </template>
                            </el-table-column>

                            <el-table-column
                              :show-overflow-tooltip="true"
                              align="center"
                              prop="deductionSalary"
                              label="扣减金额"
                              width="250"
                            />

                            <el-table-column
                              label="组织处理+扣减薪酬"
                              type="handleDeductionCount"
                              width="180"
                              align="center"
                            >
                              <template
                                v-if="scope.row.userName !== '合计'"
                                slot-scope="scope"
                              >
                                <span v-if="scope.row.handleDeductionCount == '1'">是</span>
                                <span v-if="scope.row.handleDeductionCount != '1'">否</span>
                              </template>

                              <template
                                v-if="scope.row.userName === '合计'"
                                slot-scope="scope"
                              >
                                <span>{{ scope.row.handleDeductionCount }}</span>
                              </template>
                            </el-table-column>

                            <el-table-column
                              label="政务处分+扣减薪酬"
                              type="governmentDeductionCount"
                              width="180"
                              align="center"
                            >
                              <template
                                v-if="scope.row.userName !== '合计'"
                                slot-scope="scope"
                              >
                    <span
                      v-if="scope.row.governmentDeductionCount == '1'"
                    >是</span>
                                <span
                                  v-if="scope.row.governmentDeductionCount != '1'"
                                >否</span>
                              </template>

                              <template
                                v-if="scope.row.userName === '合计'"
                                slot-scope="scope"
                              >
                                <span>{{ scope.row.governmentDeductionCount }}</span>
                              </template>
                            </el-table-column>

                            <el-table-column
                              label="党纪处分+扣减薪酬"
                              type="partyDeductionCount"
                              width="180"
                              align="center"
                            >
                              <template
                                v-if="scope.row.userName !== '合计'"
                                slot-scope="scope"
                              >
                                <span v-if="scope.row.partyDeductionCount == '1'">是</span>
                                <span v-if="scope.row.partyDeductionCount != '1'">否</span>
                              </template>

                              <template
                                v-if="scope.row.userName === '合计'"
                                slot-scope="scope"
                              >
                                <span>{{ scope.row.partyDeductionCount }}</span>
                              </template>
                            </el-table-column>
                          </el-table>
                        </div>
                        <div class="modify-record-content-li ry-row">
                          <span class="modify-record-content-span float-left">修改后</span>
                          <el-table :data="modifyDataList.involvePersonModifyData" border stripe style="width: 100%">
                            <el-table-column
                              label="序号"
                              type="index"
                              width="80"
                              align="center"
                              fixed
                            />
                            <el-table-column
                              label="涉及人员"
                              prop="userName"
                              width="180"
                              align="center"
                              fixed
                            />
                            <el-table-column
                              label="部门"
                              prop="involOrgName"
                              width="180"
                              align="center"
                              fixed
                            />

                            <el-table-column
                              label="职务"
                              prop="postName"
                              width="180"
                              align="center"
                              fixed
                            />

                            <el-table-column
                              label="追责总人次"
                              prop="accountabilitySum"
                              width="180"
                              align="center"
                              fixed
                            />

                            <el-table-column
                              :show-overflow-tooltip="true"
                              align="center"
                              prop="enddate"
                              label="处理方式"
                              min-width="1100"
                            >
                              <template slot-scope="scope">
                                <el-checkbox-group
                                  class="checkbox-group-specLists"
                                  v-if="scope.row.userName !== '合计'"
                                  :key="scope.row.specLists"
                                  v-model="scope.row.specLists"
                                  size="medium"
                                  disabled="true"
                                >
                                  <el-checkbox
                                    v-for="item in scope.row.detailWidth"
                                    :key="item.dictValue"
                                    border
                                    :label="item.dictValue"
                                  >{{ item.dictLabel }}</el-checkbox>
                                </el-checkbox-group>

                                <el-checkbox-group
                                  class="checkbox-group-specLists checkbox-group-processingOther"
                                  v-if="scope.row.userName !== '合计'"
                                  :key="scope.row.processingOther"
                                  v-model="scope.row.processingOther"
                                  size="medium"
                                  disabled="true"
                                >
                                  <el-checkbox
                                    v-for="dict in dict.type.other_handler_way"
                                    :key="dict.value"
                                    border
                                    :label="dict.value"
                                  >{{ dict.label }}</el-checkbox>
                                </el-checkbox-group>
                              </template>
                            </el-table-column>

                            <el-table-column
                              :show-overflow-tooltip="true"
                              align="center"
                              prop="deductionSalary"
                              label="扣减金额"
                              width="250"
                            />

                            <el-table-column
                              label="组织处理+扣减薪酬"
                              type="handleDeductionCount"
                              width="180"
                              align="center"
                            >
                              <template
                                v-if="scope.row.userName !== '合计'"
                                slot-scope="scope"
                              >
                                <span v-if="scope.row.handleDeductionCount == '1'">是</span>
                                <span v-if="scope.row.handleDeductionCount != '1'">否</span>
                              </template>

                              <template
                                v-if="scope.row.userName === '合计'"
                                slot-scope="scope"
                              >
                                <span>{{ scope.row.handleDeductionCount }}</span>
                              </template>
                            </el-table-column>

                            <el-table-column
                              label="政务处分+扣减薪酬"
                              type="governmentDeductionCount"
                              width="180"
                              align="center"
                            >
                              <template
                                v-if="scope.row.userName !== '合计'"
                                slot-scope="scope"
                              >
                    <span
                      v-if="scope.row.governmentDeductionCount == '1'"
                    >是</span>
                                <span
                                  v-if="scope.row.governmentDeductionCount != '1'"
                                >否</span>
                              </template>

                              <template
                                v-if="scope.row.userName === '合计'"
                                slot-scope="scope"
                              >
                                <span>{{ scope.row.governmentDeductionCount }}</span>
                              </template>
                            </el-table-column>

                            <el-table-column
                              label="党纪处分+扣减薪酬"
                              type="partyDeductionCount"
                              width="180"
                              align="center"
                            >
                              <template
                                v-if="scope.row.userName !== '合计'"
                                slot-scope="scope"
                              >
                                <span v-if="scope.row.partyDeductionCount == '1'">是</span>
                                <span v-if="scope.row.partyDeductionCount != '1'">否</span>
                              </template>

                              <template
                                v-if="scope.row.userName === '合计'"
                                slot-scope="scope"
                              >
                                <span>{{ scope.row.partyDeductionCount }}</span>
                              </template>
                            </el-table-column>
                          </el-table>
                        </div>
                      </div>
                    </li>
                    <!--情形范围-->
                    <li class="modify-record-li" v-if="item.isSituationRangeModify">
                      <div class="modify-record-label">
                        <span class="modify-record-label-name">对应《违规经营投资责任追究办法》</span>
                      </div>
                      <div class="modify-record-content">
                        <div class="modify-record-content-li ry-row">
                          <span class="modify-record-content-span float-left">修改前</span>
                          <p class="modify-record-content-p float-right">
                        <span v-for="(obj,indexs) in item.situationRangeInitialData">
                          {{obj.aspectName}} -
                          {{obj.situationName}}{{(indexs+1)==item.situationRangeInitialData.length?'':'；'}}
                        </span>
                          </p>
                        </div>
                        <div class="modify-record-content-li ry-row">
                          <span class="modify-record-content-span float-left">修改后</span>
                          <p class="modify-record-content-p float-right">
                        <span v-for="(obj,indexs) in item.situationRangeModifyData">
                         {{obj.aspectName}} -
                         {{obj.situationName}}{{(indexs+1)==item.situationRangeModifyData.length?'':'；'}}
                        </span>
                          </p>
                        </div>
                      </div>
                    </li>
                  </ul>
                </div>
              </div>
            </li>
          </ul>
        </div>
        <div v-else style="width:100%;height: 380px">
          <NoData/>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
  import {
    //查询本次修改
    thisStageModifyRecord
    //查询涉及单位、涉及人员
    ,queryInvolveCompanyAndPerson
    //查询修改历史
    ,violDailyModifyHistory
    //保存数据
    ,saveModifyReason} from "@/api/daily/modifyRecord/index";
  export default {
    dicts: ['other_handler_way'],
    name: "index",
    props: {
      edit: {
        type: Boolean,
        default: false
      },
      problemId: {
        type: String,
        default: () => {
        }
      },
      relevantTableId: {
        type: String,
        default: () => {
        }
      },
      relevantTableName: {
        type: String,
        default: () => {
        }
      }, problemStatus: {
        type: String,
        default: () => {
        }
      },
    },
    data() {
      return {
        activeName: 'first',
        modifyReason: '',//填写修改原因
        modifyDataList: [],
        involCompanyDutyList: [],
        involvePersonDutyList: [],
        historyData:[],
      }
    },
    created() {
      this.modifyData();
      this.history();
    },
    methods: {
      /**本次修改*/
      modifyData() {
        //查询本次修改
        thisStageModifyRecord(this.problemId,this.relevantTableId,this.relevantTableName).then(
          response => {
            this.modifyDataList = response.data;
            this.modifyDataList.unitModifyIndex = this.modifyDataList.modifyDetails.length + 1;
            this.modifyDataList.deptInitialIndex = this.modifyDataList.unitModifyIndex + (this.modifyDataList.isInvolveUnitModify ? 1 : 0);
            this.modifyDataList.involvePersonInitialIndex = this.modifyDataList.deptInitialIndex + (this.modifyDataList.isInvolveDeptModify ? 1 : 0);
            this.modifyDataList.situationRangeModifyIndex = this.modifyDataList.involvePersonInitialIndex + (this.modifyDataList.isInvolvePersonModify ? 1 : 0);
            this.modifyReason = this.modifyDataList.modifyReason;
          }
        )
        //查询涉及单位、涉及人员
        queryInvolveCompanyAndPerson(this.problemId,this.relevantTableId).then(
          response => {
            this.involCompanyDutyList = response.data[0].involCompanyDutyList;
            this.involvePersonDutyList = response.data[0].involvePersonDutyList;
          }
        )
      },
      /**修改历史*/
      history(){
        //查询修改历史
        violDailyModifyHistory(this.problemId).then(
          response => {
            this.historyData = response.data;
          }
        )
      },
      /**保存数据*/
      save(){
        if(!this.modifyReason){
          this.$message.error('【填写修改原因】不能为空！');
        }else{
          saveModifyReason(this.problemId,this.relevantTableId,this.relevantTableName,this.modifyReason,this.modifyDataList.initialBusinessId).then(
            response => {
              //成功后
              if(this.relevantTableName == 'T_COL_VIOL_DAILY_CHECK' && this.modifyDataList.isInvolveUnitModify){
                //第四环节修改了涉及单位，判断是否修改了主责单位
                var oldMainDept = '';
                var newMainDept = '';
                for(var i = 0;i<this.modifyDataList.involveUnitInitialData.length;i++){
                  var data = this.modifyDataList.involveUnitInitialData[i];
                  if(data.mainFlag == '1'){
                    oldMainDept = data.involCompanyName;
                  }
                }
                for(var i = 0;i<this.modifyDataList.involveUnitModifyData.length;i++){
                  var data = this.modifyDataList.involveUnitModifyData[i];
                  if(data.mainFlag == '1'){
                    newMainDept = data.involCompanyName;
                  }
                }
                if(oldMainDept != newMainDept){
                  this.$emit('modifySaveMainDept',true)
                }else{
                  this.$emit('modifySave',true)
                }
              }else{
                this.$emit('modifySave',true)
              }
            }
          )
        }

      }
    }
  }
</script>

<style scoped>
  ::v-deep .el-tabs__content {
    height: 62vh;
    overflow-y: auto;
  }

  .modify-label {
    height: 48px;
    font-size: 16px;
    line-height: 48px;
    color: #373d41;
  }

  .modify-record {
    width: 100%;
    overflow: auto;
    padding-right: 10px;
    box-sizing: border-box;
    height: calc(100% - 166px);
  }

  .modify-record-ul {
  }

  .modify-record-li {
  }

  .modify-record-label {
    line-height: 40px;
  }

  .modify-record-label-name {
    color: #A8ACB0;
  }

  .question-li-ranking {
    margin-right: 10px;
  }

  .modify-record-content {
    padding: 0 0 8px 0;
  }

  .modify-record-content-p {
    background: #F4F4F4;
    border-radius: 2px;
    padding: 10px;
    width: calc(100% - 48px);
    box-sizing: border-box;
    min-height: 37px;
  }

  .modify-record-content-li {
    margin-bottom: 20px;
  }

  .modify-record-content-span {
    width: 44px;
    padding: 8px 0;
  }

  .modify-record-content-direction {
    width: 100%;
    text-align: center;
    height: 32px;
    line-height: 32px;
  }

  .modify-record-content-direction img {
    transform: rotate(
      90deg
    );
  }

  .modify-history {
    height: calc(100% - 45px);
    padding: 20px 0;
    box-sizing: border-box;
    overflow: auto;
  }

  .modify-history-li {
    position: relative;
    width: 100%;
  }

  .modify-history-li:before {
    position: absolute;
    content: '';
    width: 1px;
    height: 100%;
    border-left: 1px dashed #d9d9d9;
    left: 158px;
    top: 18px;
  }

  .history-li-left {
    width: 140px;
    position: relative;
  }

  .history-li-left-name {
    font-size: 14px;
    line-height: 24px;
    text-align: right;
  }

  .history-li-left-time {
    font-size: 14px;
    line-height: 24px;
    color: #a9b0b4;
    text-align: right;
  }

  .history-li-left:before {
    position: absolute;
    content: '';
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: #ffffff;
    border: solid 2px #f5222d;
    right: -25px;
    top: 4px;
  }

  .history-li-right {
    width: calc(100% - 180px);
  }

  .history-li-title {
    font-size: 14px;
    line-height: 24px;
  }

  .history-li-user {
    width: 80px;
  }

  .history-li-text {
    padding-left: 10px;
    box-sizing: border-box;
    width: calc(100% - 80px);
  }

  .history-li-text span {
    color: #a9b0b4;
  }

  .history-li-content {
    border-radius: 2px;
    padding: 10px;
    box-sizing: border-box;
    margin: 0 10px 30px 0;
  }

  .question-li-ranking {
    width: 20px;
    background-color: #a9b0b4;
    height: 20px;
    line-height: 22px;
    color: #fff;
    display: inline-block;
    border-radius: 50%;
    text-align: center;
  }
  .checkbox-group-specLists{
    display: inline-block;
  }
  .checkbox-group-processingOther{
    margin-left:30px;
  }
</style>
