import request from '@/utils/request'



// 政策文章
export function getArticleListCollection(params,data) {
  return request({
    url: '/learnGarden/getArticleListCollection',
    method: 'post',
    data: data,
    params: params
  })
}

// 制度机制
export function institutionalMechanism(params,data) {
  return request({
    url: '/learnGarden/getBaseLawPage',
    method: 'post',
    data: data,
    params: params
  })
}
// 发布信息
export function getInformationListCollection(params,data) {
  return request({
    url: '/learnGarden/getInformationListCollection',
    method: 'post',
    data: data,
    params: params
  })
}


// 收藏
export function contextCollection(data) {
  return request({
    url: '/learnGarden/contextCollection',
    method: 'post',
    data: data
  })
}

// 取消收藏
export function cancelContextCollection(data) {
  return request({
    url: '/learnGarden/cancelContextCollection',
    method: 'post',
    data: data
  })
}

// 我的收藏
export function getContextCollection(params,data) {
  return request({
    url: '/learnGarden/getContextCollection',
    method: 'post',
    data: data,
    params: params
  })
}

// 查看详情 时 调用阅读记录
export function readLog(data) {
  return request({
    url: '/learnGarden/readLog',
    method: 'post',
    data: data
  })
}




// 阅读记录
export function getLastReadLog(params,data) {
  return request({
    url: '/learnGarden/getLastReadLog',
    method: 'post',
    data: data,
    params: params
  })
}
