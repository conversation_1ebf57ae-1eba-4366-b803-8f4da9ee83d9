<!--禁入限制人员-->
<template>
  <div  style="height:calc(70vh - 220px);">
    <el-table
      border
      :data="tableList"
      ref="table"
      height="100%"
    >
      <el-table-column
        align="center"
        type="index"
        fixed
        width="50">
      </el-table-column>
      <el-table-column label="央企集团名称" prop="groupName" width="200" show-overflow-tooltip/>
      <el-table-column label="集团简称" prop="groupSortName" width="200" show-overflow-tooltip/>
      <el-table-column label="统一社会信用代码" prop="uscCode" width="200" show-overflow-tooltip/>
      <el-table-column label="行业名称" prop="industryName" width="200" show-overflow-tooltip/>
      <el-table-column label="行业代码" prop="industryCode" width="200" show-overflow-tooltip align="center"/>
      <el-table-column label="上报时间" prop="reportDate" width="200" show-overflow-tooltip align="center"/>
      <el-table-column label="禁入人姓名" prop="userName" width="200" show-overflow-tooltip align="center"/>
      <el-table-column label="禁入编码" prop="uniqueCode" width="200" show-overflow-tooltip align="center"/>
      <el-table-column label="性别" prop="classifyText" width="200" align="center">
        <template slot-scope="scope">
          {{ scope.row.sex==1?'男':'女'}}
        </template>
      </el-table-column>
      <el-table-column label="身份证件号" prop="idCard"  width="200" show-overflow-tooltip align="center"/>

      <el-table-column label="处理前职务" prop="postName"  width="200" show-overflow-tooltip/>
      <el-table-column label="干部类别" prop="cadreCategory"  width="200" show-overflow-tooltip align="center"/>
      <el-table-column label="现任企业" prop="orgGradeName"  width="200" show-overflow-tooltip/>
      <el-table-column label="企业层级" prop="involAreaName"  width="200" show-overflow-tooltip/>
      <el-table-column label="工作简历" prop="workResume"  width="200" show-overflow-tooltip/>
      <el-table-column label="违规问题" prop="violationsProblem"  width="200" show-overflow-tooltip/>
      <el-table-column label="不良后果" prop="adverseConsequences"  width="200" show-overflow-tooltip/>
      <el-table-column label="责任认定情况" prop="responIdenty"  width="200" show-overflow-tooltip/>
      <el-table-column label="责任追究处理情况" prop="accountabHandle"  width="200" show-overflow-tooltip/>
      <el-table-column label="禁入限制期间开始" prop="limitStartTime"  :formatter="dateFormat" width="200" show-overflow-tooltip align="center"/>
      <el-table-column label="禁入限制期间结束" prop="limitEndTime"  :formatter="dateFormat" width="200" show-overflow-tooltip align="center"/>
      <el-table-column label="责任追究处理部门" prop="accountabDepartment"  width="200" show-overflow-tooltip/>
      <el-table-column label="责任处理联系人" prop="contactsName"  width="200" show-overflow-tooltip align="center"/>
      <el-table-column label="责任处理联系方式" prop="contactInformation"  width="200" show-overflow-tooltip align="center"/>
      <el-table-column label="文件" prop="personPostalCode" align="center" width="100" show-overflow-tooltip>
        <template slot-scope="scope">
          <span class="text-red cursor underline" @click="fileDialog( scope.row.files)">
            {{ scope.row.files.length?scope.row.files.length:0}}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="备注" prop="remark"  width="200" show-overflow-tooltip/>

      <el-table-column label="状态" prop="del" width="60" align="center">
        <template slot-scope="scope">
          {{ scope.row.del==1?'已删除': scope.row.del==2?'新增':'编辑'}}
        </template>
      </el-table-column>
    </el-table>
    <el-dialog  :visible.sync="visible" width="800px"  append-to-body  @close="close" title="附件列表">
      <el-table
        border
        :data="fileList"
        ref="table2"
      >
        <el-table-column
          fixed
          align="center"
          label="序号"
          type="index"
          min-width="50">
        </el-table-column>
        <el-table-column label="文档名称" prop="fileName" min-width="225"/>
        <el-table-column label="上传时间" prop="createTime" min-width="225" align="center"/>
        <el-table-column label="文档类型" prop="fileDocumentType" min-width="100"/>
        <el-table-column label="操作" prop="del" min-width="150"
                         align="center"
        >
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-bottom"
              title="下载"
              @click="downloadFile(scope.row)"
            >
            </el-button>
            <!-- <el-button
              size="mini"
              type="text"
              icon="el-icon-search"
            >预览
            </el-button> -->
          </template>
        </el-table-column>
      </el-table>
      <div slot="footer">
        <el-button size="mini" @click="close()">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import {getReportLimitPerson} from "@/api/sasac/reportManagement/edit/detail/index";
  import moment from "moment"

  export default {
    name: "restrictedPersonnel",
    props: {
      problemId: {
        type: String
      },
      height:{
        type: String
      }
    },
    data() {
      return {
        tableList: [],
        total: 0,
        visible:false,
        fileList:[],//附件列表
        hasSelectList:[],//选中的值
        params: {
          pageNum: 1,
          pageSize: 10,
        }
      }
    },
    created() {
      this.GetReportLimitPerson();
    },
    mounted() {
    },
    methods: {
      // 获取查询页数据
      GetReportLimitPerson() {
        //接口：/colligate/baseInfo/report/getReportLimitPerson
        getReportLimitPerson({ reportId:this.problemId,...this.params}).then(response => {
          this.tableList = response.data;
        });
      },
      // 获取刷新数据
      onRefresh() {
        this.GetReportLimitPerson();
      },
      //弹出附件
      fileDialog(files){
        this.fileList = files;
        this.$nextTick(()=>{
          this.visible=true;
        })
      },
      /** 下载附件 */
      downloadFile(row) {
        this.download('/sys/documentTemplate/downloadByAttachmentId', { id: row.id }, row.fileName)
      },
      //附件列表关闭
      close(){
        this.visible=false;
      },
      /*日期处理*/
      dateFormat(row,column){
        var date = row[column.property];
        if(date === undefined){
          return ''
        }
        return moment(date).format("YYYY-MM-DD")
      },
    }
  }
</script>

<style scoped>

</style>
