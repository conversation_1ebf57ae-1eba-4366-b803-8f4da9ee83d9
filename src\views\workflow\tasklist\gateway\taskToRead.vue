<template>
  <div class="todo">
      <div class="todo-content">
        <div class="todo-header">
          <el-radio-group  v-model="tabPosition">
            <el-radio-button label="1">业务信息</el-radio-button>
          </el-radio-group>
        </div>
      </div>
    <el-scrollbar style="height:calc(100vh - 70px);overflow-x: hidden">
      <div class="todo-data">
        <Daily
          v-if="type==='daily'"
          :key="index"
          ref="todo"
          :selectValue="selectValue"
          :centerVariable="centerVariable"
          @handle="handle"
        ></Daily>
        <Regular
          v-if="type==='regular'"
          :key="index"
          ref="todo"
          :selectValue="selectValue"
          :centerVariable="centerVariable"
          @handle="handle"
        ></Regular>
        <Actual
          v-if="type==='actual1'||type==='actual2'"
          :key="index"
          ref="todo"
          :type="type"
          :selectValue="selectValue"
          :centerVariable="centerVariable"
          @handle="handle"
        ></Actual>
        <component
          v-if="type!=='actual'&&type!=='actualEdit'&&type!='regular'&&type!='daily'"
          :is="businessUrl"
          :key="index"
          ref="todo"
          :selectValue="selectValue"
          :centerVariable="centerVariable"
          @handle="handle"
        />
      </div>
    </el-scrollbar>
    <div  style="text-align: right;padding:0 10px">
      <Read :params="centerVariable"
            slot="footer"
            :key="centerVariable"
            ref="process"
            :tabFlag="tabFlag"
            :selectValue="selectValue"
            :centerVariable="centerVariable"
            :flowCfgLink="flowCfgLink"
            :edit="edit"
            @close="close">

      </Read>
    </div>
  </div>
</template>
<script>
  import Opinion from "./../common/opinion";
  import Read from "@/components/Process/read";
  import { findRecordPath } from "@/api/components/process";
  import Daily from "@/views/daily/dailyHasdone";//日常
  import Actual from "@/views/actual/flow/toRead";//实时
  import Regular from "@/views/regular/flow/taskHastonAreaHandler";//定期

  export default {
    inheritAttrs: false,
    components: {
      Opinion,
      Read,
      Daily,
      Actual,
      Regular
    },
    props: {
      selectValue: {
        type: Object
      },
      tabFlag: {
        type: String
      },
    },
    data() {
      return {
        businessUrl: "",
        edit:true,
        refreshAssigneeUrl: "",
        targetComponent:'Read',
        currentTabComponent:false,
        tabPosition:"1",
        centerVariable:{},
        flowCfgLink:{},
        type:'',
        index:0,
        visible:false,//弹框
      }
    },
    watch: {},
    created() {
      this.selectValue={
        linkKey:this.$route.query.linkKey,
        processInstanceId:this.$route.query.processInstanceId,
        readLinkId:this.$route.query.readLinkId,
        taskId:this.$route.query.taskId,
        typeId:this.$route.query.typeId,
        flowKey:this.$route.query.flowKey,
        readerId:this.$route.query.readerId,
        busiId:this.$route.query.busiId,
      };
      this.show();
    },
    mounted() {},
    computed:{
      NextTickName: function (){
        let map = window.componentsConfig;
        if(this.targetComponent){
          let k = this.targetComponent;       // 组件映射关系key值
          // let p = map[k];        // 通知k值读取到路径信息
          // let c = () => import(`${p}`);          // 动态组件
          return k
        }
      }
    },
    methods: {
      /** 点开弹窗 */
      show(){
        this.visible = true;
        this.findRecordPath();
      },
      /** 关闭弹窗 */
      close() {
        window.opener=null;
        window.open('','_self');
        window.close();
      },
      /**主要数据*/
      findRecordPath(){
        findRecordPath(this.selectValue).then(
          response => {
            this.centerVariable = {
              busiKey:this.selectValue.busiId
            };
            this.flowCfgLink = response.data.dataRows[0].flowCfgLink;
            this.type=response.data.dataRows[0].url;
            this.businessUrl = (resolve) =>
              require([`@/views/${response.data.dataRows[0].url}`], resolve);
            this.index++;
            this.$nextTick(()=>{
              this.visible = true;
            })
          }
        );
      },
    }
  }

</script>
<style scoped lang="scss">
  .todo{
    .todo-header{
      ::v-deep.el-radio-button__inner{
        border-radius: 0 !important;
        border-color: #f4f4f4 !important;
        box-shadow:0 0 0 0 #f5222d !important;
        width: 120px;
      }
    }
    .todo-content{
      background: #F4F4F4;
    }
    .todo-data{
      background: #fff;
      margin-top:8px;
      overflow: auto;
      height: calc(100% - 10px);
    }
    ::v-deep.el-scrollbar__view{
      height: calc(100% - 10px);
    }
    ::v-deep.el-scrollbar__wrap {
      overflow-x: hidden !important;
    }
    ::v-deep.el-dialog__body{
      border-top: 2px solid #E9E8E8;
      padding:0 20px 10px;
      background: #F4F4F4;
      height: 70vh;
      overflow: auto;
    }
  }
</style>

