<template>
  <div class="height100">
    <el-form class="common-card padding10_0" :model="reportData" size="medium" ref="elForm"
             label-width="80px">
      <el-row>
        <el-col :span="8">
          <el-form-item label="上报年度"><span>{{reportData.reportYear}}</span></el-form-item>
        </el-col>
        <el-col :span="16">
          <el-form-item label="上报标题">{{reportData.reportTitle}}</el-form-item>
        </el-col>
        <el-col :span="23">
          <el-form-item label="上报内容">
            <span v-for="(item,i) in reportData.reportContentDictionaries">{{item.reportTypeName}}{{reportData.reportContentDictionaries.length==(i+1)?'':'、'}}</span>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div class=" bottom-content">
      <el-row class="height100">
        <el-col  class="height100" :span="24">
          <div class="float-left left-report-detail height100">
            <ul class="report-detail-ul">
              <li v-for="(item,i) in reportContentDictionaries" v-show="reportContents.indexOf(item.reportTypeCode)!='-1'"
                  :class="index==i?'report-detail-li cursor text-red':'report-detail-li cursor'" @click="report(i)">
                <span class="report-detail-title">{{item.reportTypeName}}</span>
                <i v-if="item.isConfirm==1" class="el-icon-success"></i>
              </li>
            </ul>
          </div>
          <div class="float-right right-report-detail position height100" v-if="reportContentDictionaries.length">
            <div class="right-report-detail-title">
              <span class="right-report-detail-span">{{reportContentDictionaries[index].reportTypeName}}</span>
            </div>
            <div class="report-detail-contents" ref="main">
              <!--企业基本信息-->
              <areaInfo ref="info" :key="height" :height="height" v-if="reportContentDictionaries[index].reportTypeCode=='COMPANY_BASIC_INFO'" :problemId="centerVariable.busiKey"></areaInfo>
              <!--企业联系人-->
              <personInfo ref="info" :key="height" :height="height" v-if="reportContentDictionaries&&reportContentDictionaries[index].reportTypeCode=='COMPANY_CONTACT_PERSON'"  :problemId="centerVariable.busiKey"></personInfo>
              <!--规章制度-->
              <rulesInfo ref="info" :key="height" :height="height" v-if="reportContentDictionaries&&reportContentDictionaries[index].reportTypeCode=='RULES_AND_REGULATIONS'" :problemId="centerVariable.busiKey"></rulesInfo>
              <!--禁入限制人员-->
              <restrictedInfo ref="info" :key="height" :height="height" v-if="reportContentDictionaries&&reportContentDictionaries[index].reportTypeCode=='RESTRICTED_PERSONNEL'" :problemId="centerVariable.busiKey"></restrictedInfo>
              <!--发件箱-->
              <outboxInfo ref="info" :key="height" :height="height" v-if="reportContentDictionaries&&reportContentDictionaries[index].reportTypeCode=='SASAC_OUTBOX'" :problemId="centerVariable.busiKey"></outboxInfo>
              <!--实时报告-->
              <reportSummary ref="info" :key="height" :height="height" v-if="reportContentDictionaries&&reportContentDictionaries[index].reportTypeCode=='ACTUAL_REPORT'" @cancel="cancel" @actualStage="actualStage"  :problemId="centerVariable.busiKey" :edit="edit"></reportSummary>
              <!--定期报告-->
              <periodicInfo  ref="info" :key="height" :height="height"  v-if="reportContentDictionaries&&reportContentDictionaries[index].reportTypeCode=='REGULAR_REPORT'" :problemId="centerVariable.busiKey"></periodicInfo>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>
<script>
import {
  reportDetailBasicData,
  reportContentConfirm,
  validEachContentConfirmedStatus,
  deleteReportContent,
  reportStructuredDataPackage
} from "@/api/sasac/reportManagement/edit/index";

import areaInfo from './../edit/details/enterprisBasicInformation';//企业基本信息-详情页
import areaEdit from './../edit/edit/area';//企业基本信息-编辑
import personInfo from './../edit/details/enterpriseContact';//企业联系人-详情页
import personEdit from './../edit/edit/person';//企业联系人-编辑
import rulesInfo from './../edit/details/enterpriseRulesSystems';//规章制度-详情页
import rulesEdit from './../edit/edit/rules';//规章制度-编辑
import restrictedInfo from './../edit/details/restrictedPersonnel';//禁入限制人员-详情页
import restrictedEdit from './../edit/edit/restricted';//禁入限制人员-编辑
import outboxInfo from './../edit/details/outbox';//发件箱-详情页
import outboxEdit from './../edit/edit/outbox';//发件箱-编辑
import reportSummary from './../edit/detail/reportSummary';//实时报告-编辑
import periodicInfo from './../edit/detail/periodicReport';//定期-详情页
import periodicEdit from './../edit/edit/periodicReport';//定期-编辑


export default {
  inheritAttrs: false,
  components: {areaInfo,areaEdit,personInfo,personEdit,rulesInfo,rulesEdit,restrictedInfo,restrictedEdit,outboxInfo,outboxEdit,reportSummary,periodicInfo,periodicEdit},

  props: {

    closeBtn: {
      type: Function,
      default: null,
    },
    // add 为新增 edit 为编辑
    editType: {
      type: String,
      default: "",
    },
    //编辑内容
    rowData: {
      type: Object,
      default: () => {},
    },
    //流程参数
    centerVariable: {
      type: Object
    },
  },
  data() {
    return {
      index: 0,
      reportContents: [],//随时变更的数据
      originalData:[],//未变更的数据
      reportData: {},
      reportContentDictionaries: [],
      actualStageItems:[],
      edit:false,
      height:'',
    }
  },
  computed: {},
  watch: {},
  created() {
    this.$emit('collocation',{
      refreshAssigneeUrl:'/sasac/flow',//业务url
    })
    this.ReportDetailBasicData()
  },
  mounted() {
  },
  methods: {
    //刷新
    onRefresh(){
      this.$refs.info.onRefresh();
    },
    //点击编辑
    editReport(){
      this.$refs.edit.onShow();
    },
    //编辑关闭
    editClose(commitFlag){
      //更新状态
      this.cancel();
      if(commitFlag && commitFlag == 1){//提交操作，则刷新展示数据
        // 关闭了弹出层
        this.onRefresh();
      }
    },
    //修改actualStage
    actualStage(data){
      this.actualStageItems=data;
    },
    // 根据problemId获取页面初始数据
    ReportDetailBasicData() {
      reportDetailBasicData( this.centerVariable.busiKey).then(response => {
        this.reportData = response.data;
        this.reportContentDictionaries = response.data.reportContentDictionaries;
        for (var i = 0; i < this.reportContentDictionaries.length; i++) {
          this.reportContentDictionaries[i].isShow = true;
        }
        this.reportContentDictionaries.forEach(row => {
          this.reportContents.push(row.reportTypeCode);
          this.originalData.push(row.reportTypeCode);
        });
        this.$nextTick(()=>{
          this.height=this.$refs.main.offsetHeight;
        })
      });
    },
    //点击左侧
    report(i) {
      this.index = i;
    },
    //点击确认
    ReportContent(){
      this.$modal.confirm('确认【' + this.reportContentDictionaries[this.index].reportTypeName + '】数据正确？').then(() => {
        this.ReportContentConfirm();
      }).catch(() => {
      });
    },
    //确认
    ReportContentConfirm(){

      let reportContentObj = {
        id:  this.centerVariable.busiKey,
        reportContent: this.reportContentDictionaries[this.index].reportTypeCode,
        actualStageItems: this.actualStageItems
      };
      reportContentConfirm(reportContentObj).then(response => {
        if (200 === response.code) {
          this.reportContentDictionaries[this.index].isConfirm = '1';
          this.$modal.msgSuccess("确认成功！");
        } else {
          this.$modal.alertError(response.msg);
        }
      });
    },
    //刷新状态确认
    cancel(){
      reportDetailBasicData( this.centerVariable.busiKey).then(response => {
        let report = response.data.reportContentDictionaries;
        for (var i = 0; i < this.reportContentDictionaries.length; i++) {
          for (var j = 0; j< report.length; j++) {
            if(this.reportContentDictionaries[i].reportTypeCode==report[j].reportTypeCode){
              this.reportContentDictionaries[i].isConfirm = report[j].isConfirm;
            }
          }
        }
      });
    },
    //点击提交
    //第一步删除
    submitReport(){
      let result = this.originalData.filter(item => !this.reportContents.includes(item));
      if(result.length==0){//无删除
        this.validEachContentConfirmedStatus();//校验
      }else{
        deleteReportContent({id:  this.centerVariable.busiKey, reportContents: result}).then(response => {
          if (200 === response.code) {
            this.validEachContentConfirmedStatus();
          } else {
            this.$modal.alertError(response.msg);
          }
        });
      }
    },
    //第二步提交前的校验
    validEachContentConfirmedStatus() {
      validEachContentConfirmedStatus( this.centerVariable.busiKey).then(response => {
        if (200 === response.code) {
          this.passValidateJup();
        } else {
          this.$modal.alertError(response.msg);
        }
      });
    },
    //第三步点击提交
    reportStructuredDataPackage(){
      reportStructuredDataPackage( this.centerVariable.busiKey).then(response => {
        if (200 === response.code) {
          this.$modal.msgSuccess('提交成功！');
          this.$emit('closeEdit');
        } else {
          this.$modal.alertError('提交失败！');
        }
      });
    },
    //额外参数
    loadProcessData(){
      return {}
    },
    //校验
    passValidate(){
      this.submitReport();
      return false;
    },
    //接口校验异步校验时
    passValidateJup(){
      this.$emit('nextStep',true)
    },
  }

}

</script>
<style lang="scss" scoped>
.common-card {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 0 10px 0 rgb(155 11 9 / 10%);
}

.bottom-content {
  padding-top: 16px;
  height: calc(100% - 130px);
  min-height: 400px;
}

.left-report-detail {
  width: 200px;
  background: #FCFAFB;
  opacity: 1;
}

.report-detail-ul {
  width: 100%;
  .report-detail-li {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 32px;
    height: 48px;
    line-height: 48px;
    border-bottom: 1px solid #D5DCE2;
  }
}

.report-detail-title {
  font-size: 16px;
}

.report-detail-li.icon-red {
  .iconfont {
    color: #f5222d;
  }
}

.right-report-detail {
  box-sizing: border-box;
  border: 8px solid #F4F4F4;
  padding: 8px;
  width: calc(100% - 214px);
  .right-report-detail-title {
    width: 100%;
    height: 48px;
    line-height: 48px;
    padding: 0 12px;
    box-sizing: border-box;
    background: #F9F9F9;
  }
  .right-report-detail-span {
    font-size: 16px;
    font-weight: bold;
    color: #333333;
  }
}

.report-detail-contents {
  overflow: auto;
  width: 100%;
  height: calc(100% - 72px);
}

.el-form-item {
  margin-bottom: 10px;
}
::v-deep.el-form-item--medium .el-form-item__content{
  line-height: 28px;
}
::v-deep.el-form-item--medium .el-form-item__label{
  line-height: 28px;
}
</style>
