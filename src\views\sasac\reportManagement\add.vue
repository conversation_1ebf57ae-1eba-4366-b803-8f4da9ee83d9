<template>
  <div>
    <el-dialog  :visible.sync="visible"  width="800px" :modal-append-to-body="false" title="新增上报">
      <el-form ref="elForm"   size="medium" label-width="80px">
        <el-row>
          <el-col :span="24">
        <el-form-item label="上报年度">
          <el-date-picker
            :style="{width: '100%'}"
            v-model="queryParams.reportYear"
            type="year"
            value-format="yyyy"
            placeholder="上报年度">
          </el-date-picker>
        </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="上报标题">
              <el-input v-model="queryParams.reportTitle" placeholder="上报标题"   :style="{width: '100%'}">
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="上报内容">
              <el-table
                border
                :data="list"
                ref="multipleTable"
                @selection-change="handleSelectionChange"
              >
                <el-table-column type="selection" width="40"></el-table-column>
                <el-table-column label="上报内容" prop="codeText" min-width="90%"/>
              </el-table>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer">
        <el-button size="mini" @click="close">取消</el-button>
        <el-button size="mini" type="primary" @click="handelConfirm">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
  import { saveSasacReportRecord} from "@/api/sasac/reportManagement/add";
  import { Loading } from 'element-ui';

  export default {
    inheritAttrs: false,
    components: {
    },
    props: {
      list:{
        type:Array
      },
      length:'',
    },
    data() {
      return {
        visible:false,
        reportContents:[],
        queryParams:{
          reportYear:'',
          reportTitle:'',
          reportContents:[]
        },
        loadingInstance:''
      }
    },
    computed: {},
    watch: {},
    created() {},
    mounted() {

    },
    methods: {
      //弹出
      show() {
        this.visible = true;
        this.$nextTick(()=>{
          this.lengthFun();
        })
      },
      //判断是否第一次上报
      lengthFun(){
        if(this.length=='0'){
          this.list.forEach(row => {
            if(row.code=='COMPANY_BASIC_INFO'){
              this.$refs.multipleTable.toggleRowSelection(row,true);
            }
          });
        }else{
          this.reportContents.forEach(item => {
            if (item.code=='COMPANY_CONTACT_PERSON') {
              this.list.forEach(row => {
                if (row.code=='COMPANY_BASIC_INFO') {
                  this.$refs.multipleTable.toggleRowSelection(row, true);
                }
              });
            }
          });
        }
      },
      //关闭
      close(){
        this.visible = false;
      },
      //确认提交
      handelConfirm(){
        this.queryParams.reportContents=[];
        this.reportContents.forEach(row => {
         this.queryParams.reportContents.push(row.code);
        });
        if(!this.reportContents.length){
          this.$message.error('请选择上报类型！');
        }else if(!this.queryParams.reportYear){
          this.$message.error('【上报年度】不能为空！');
        }else if(!this.queryParams.reportTitle){
          this.$message.error('【上报标题】不能为空！');
        }else{
          this.openLoading();
          saveSasacReportRecord(this.queryParams).then(response => {
            this.closeLoading();
            if (200 === response.code) {
              this.$modal.msgSuccess('保存成功！');
              this.$emit('addReport',response.msg);
              this.visible = false;
            } else {
              this.$modal.alertError(response.msg);
            }
          });
        }

      },

      //打开遮罩
      openLoading() {
        this.loadingInstance = Loading.service({
          target: document.querySelector('#todo'),
          background:'rgba(255,255,255,0)',
          spinner: 'el-icon-loading', // 自定义加载图标类名
          text: '正在加载...', // 显示在加载图标下方的加载文案
          lock: false // lock的修改符--默认是false
        })
        return this.loadingInstance
      },
      //关闭这招
      closeLoading(){
        this.loadingInstance.close()
      },
      handleSelectionChange(val) {
        this.reportContents = val;
        this.$nextTick(()=>{
          this.lengthFun();
        });
      }
    }
  }

</script>
<style lang="scss">
  .el-dialog__body{
    height: 70vh;
    overflow: auto;
  }
</style>
