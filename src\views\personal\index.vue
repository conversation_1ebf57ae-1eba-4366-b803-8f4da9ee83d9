<template>
  <div class="home-container">
    <el-row style="padding-bottom: 4px">
      <el-col :span="6" class="height-420-px" style="padding-right: 4px">
        <BlockCard title="超时提醒" class="height">
          <div class="el-title-right-num">({{ ordinaryTimeoutBlockData.untreatedTimeout||'0' }}）</div>
          <div
            class="
              el-common-card
              row-echars-block
              margin-top-8-px
              height-95-px
              flex
              align-items-center
            "
          >
            <div class="el-row-one-num el-after-line">
              <div class="el-bottom-nums pointer" @click="goOverTimeList">
                {{ ordinaryTimeoutBlockData.cumulativeTimeout || "0" }}
              </div>
              <div class="el-top-text">累计超时</div>
            </div>

            <div class="el-row-one-num">
              <div class="el-bottom-nums pointer" @click="goAllList('all')">
                {{ ordinaryTimeoutBlockData.allQuestionNumber || "0" }}
              </div>
              <div class="el-top-text">全部问题</div>
            </div>

            <div class="flex-1 height">
              <div id="echars-huan">
                <ehcarsHuan
                  id="echars-huan-1"
                  :chars-data="
                    ordinaryTimeoutBlockData.timeoutQuestionRate || '0'
                  "
                />
              </div>
            </div>
          </div>

          <div class="height-surplus-107-px width margin-top-12-px">
            <div class="el-alignment-table height width">
              <el-scrollbar class="width" style="height: 98%">
                <div
                  v-for="(
                    item, index
                  ) of ordinaryTimeoutBlockData.timeoutNotProcesses"
                  :key="index"
                  style="height:37px; lineheight:37px"
                  class="el-alignment-row flex align-items-center"
                >
                  <div class="el-left-point" />
                  <div
                    class="el-mid-content ovflowHidden flex-1"
                    :title="item.problemTitle"
                  >
                    {{ item.problemTitle }}
                  </div>
                  <div class="el-last-content">
                    <span class="icon iconfont" />
                    <span class="el-last-content-text">{{
                      item.warnSendDay
                    }}</span>
                  </div>
                </div>
              </el-scrollbar>
            </div>
          </div>
        </BlockCard>
      </el-col>

      <el-col
        :span="12"
        class="height-420-px"
        style="padding-left: 4px; padding-right: 4px"
      >
        <div class="el-common-card height">
          <div class="el-common-header">
            <div class="el-common-card-headers">
              <div
                class="el-card-header-select"
                :class="changeTabsIndex == '0' ? 'active' : ''"
                @click="changeTabs('0')"
              >
                我的待办 (<span class="mapName">{{ taskToTotal }}</span>)
              </div>

              <div
                class="el-card-header-select"
                :class="changeTabsIndex == '1' ? 'active' : ''"
                @click="changeTabs('1')"
              >
                我的已办 (<span class="mapName">{{ taskHasDoneTotal }}</span>)
              </div>

              <div
                class="el-card-header-select"
                :class="changeTabsIndex == '2' ? 'active' : ''"
                @click="changeTabs('2')"
              >
                我的待阅 (<span class="mapName">{{ taskToReadTotal }}</span>)
              </div>

              <div
                class="el-card-header-select"
                :class="changeTabsIndex == '3' ? 'active' : ''"
                @click="changeTabs('3')"
              >
                我的已阅 (<span class="mapName">{{ taskHasReadTotal }}</span>)
              </div>
            </div>
          </div>

          <div
            v-if="changeTabsIndex == '0'"
            id="tableDataDom"
            class="el-common-card-content relactive"
          >
            <div v-if="taskToDoData.length === 0" class="text-center">
              暂无数据
            </div>
            <div class="el-alignment-table height-surplus-50-px width">
              <el-scrollbar class="width" style="height: 100%">
                <div
                  v-for="(item, index) of taskToDoData"
                  :key="index"
                  style="height:37px; lineheight:37px"
                  class="el-alignment-row flex align-items-center"
                  @click="checkTaskorg(item)"
                >
                  <div
                    class="el-mid-content ovflowHidden flex-1"
                    :title="item.title"
                  >
                    <TrigTag
                      :bg-color="handleColor[item.handleType]"
                    >
                      {{ item.handleTypeName }}
                    </TrigTag>
                    {{ item.title }}
                  </div>
                  <div class="el-last-2-content">
                    <span class="icon iconfont" />
                    <span class="el-last-2-content-text">{{
                      item.senderPerson
                    }}</span>
                  </div>

                  <div class="el-last-content">
                    <span class="icon iconfont" />
                    <span class="el-last-content-text">{{
                      item.startTime
                    }}</span>
                  </div>
                </div>
              </el-scrollbar>
            </div>

            <pagination
              v-show="taskToTotal > 0"
              :total="taskToTotal"
              :page.sync="params.pageNum"
              :limit.sync="params.pageSize"
              layout="total, prev, pager, next,jumper"
              @pagination="getList"
            />
          </div>

          <div
            v-if="changeTabsIndex == '1'"
            id="tableDataDom"
            class="el-common-card-content relactive"
          >
            <div v-if="taskHasDoneData.length === 0" class="text-center">
              暂无数据
            </div>
            <div class="el-alignment-table height-surplus-50-px width">
              <el-scrollbar class="width" style="height: 100%">
                <div
                  v-for="(item, index) of taskHasDoneData"
                  :key="index"
                  style="height:37px; lineheight:37px"
                  class="el-alignment-row flex align-items-center"
                  @click="checkTaskorg2(item)"
                >
                  <div
                    class="el-mid-content ovflowHidden flex-1"
                    :title="item.title"
                  >
                    <TrigTag
                      :bg-color="handleColor[item.handleType]"
                    >
                      {{ item.handleTypeName }}
                    </TrigTag>
                    {{ item.title }}
                  </div>
                  <div class="el-last-2-content">
                    <span class="icon iconfont" />
                    <span class="el-last-2-content-text">{{
                      item.performerName
                    }}</span>
                  </div>

                  <div class="el-last-content">
                    <span class="icon iconfont" />
                    <span class="el-last-content-text">{{
                      item.startTime
                    }}</span>
                  </div>
                </div>
              </el-scrollbar>
            </div>

            <pagination
              v-show="taskHasDoneTotal > 0"
              :total="taskHasDoneTotal"
              layout="total, prev, pager, next,jumper"
              :page.sync="params.pageNum"
              :limit.sync="params.pageSize"
              @pagination="getList"
            />
          </div>

          <div
            v-if="changeTabsIndex == '2'"
            id="tableDataDom"
            class="el-common-card-content relactive"
          >
            <div v-if="taskToReadData.length === 0" class="text-center">
              暂无数据
            </div>
            <div class="el-alignment-table height-surplus-50-px width">
              <el-scrollbar class="width" style="height: 100%">
                <div
                  v-for="(item, index) of taskToReadData"
                  :key="index"
                  style="height:37px; lineheight:37px"
                  class="el-alignment-row flex align-items-center"
                  @click="checkTaskorg3(item)"
                >
                  <div class="el-left-point" />
                  <div
                    class="el-mid-content ovflowHidden flex-1"
                    :title="item.readTitle"
                  >
                    {{ item.readTitle }}
                  </div>
                  <div class="el-last-2-content">
                    <span class="icon iconfont" />
                    <span class="el-last-2-content-text">{{
                      item.sendPersonName
                    }}</span>
                  </div>

                  <div class="el-last-content">
                    <span class="icon iconfont" />
                    <span class="el-last-content-text">{{
                      item.sendTime
                    }}</span>
                  </div>
                </div>
              </el-scrollbar>
            </div>

            <pagination
              v-show="taskToReadTotal > 0"
              :total="taskToReadTotal"
              :page.sync="params.pageNum"
              :limit.sync="params.pageSize"
              layout="total, prev, pager, next,jumper"
              @pagination="getList"
            />
          </div>

          <div
            v-if="changeTabsIndex == '3'"
            id="tableDataDom"
            class="el-common-card-content relactive"
          >
            <div v-if="taskHasReadData.length === 0" class="text-center">
              暂无数据
            </div>
            <div class="el-alignment-table height-surplus-50-px width">
              <el-scrollbar class="width" style="height: 100%">
                <div
                  v-for="(item, index) of taskHasReadData"
                  :key="index"
                  style="height:37px; lineheight:37px"
                  class="el-alignment-row flex align-items-center"
                  @click="checkTaskorg4(item)"
                >
                  <div class="el-left-point" />
                  <div
                    class="el-mid-content ovflowHidden flex-1"
                    :title="item.readTitle"
                  >
                    {{ item.readTitle }}
                  </div>
                  <div class="el-last-2-content">
                    <span class="icon iconfont" />
                    <span class="el-last-2-content-text">{{
                      item.sendPersonName
                    }}</span>
                  </div>

                  <div class="el-last-content">
                    <span class="icon iconfont" />
                    <span class="el-last-content-text">{{
                      item.sendTime
                    }}</span>
                  </div>
                </div>
              </el-scrollbar>
            </div>

            <pagination
              v-show="taskHasReadTotal > 0"
              :total="taskHasReadTotal"
              :page.sync="params.pageNum"
              :limit.sync="params.pageSize"
              layout="total, prev, pager, next,jumper"
              @pagination="getList"
            />
          </div>
        </div>
      </el-col>

      <el-col :span="6" class="height-420-px" style="padding-left: 4px">
        <div
          class="el-common-card height flex-dir-col-jus-cen flex-dir-col-jus-cen-style-1"
          style="padding: 15px 20px"
        >
          <div
            class="
              top-img-tip
              flex
              align-items-center
              margin-bottom-10-px
              just-spc-aro
            "
          >
            <div class="el-one-top-img pointer" :class="perBtn1?'':'prohibit-btn'" @click="goPushPage(perBtn1?'1':'4')">
              <img src="@/assets/images/xzwt.png" alt="">
              <div class="el-one-top-img-text">日常问题录入</div>
            </div>

            <div class="el-one-top-img pointer" :class="perBtn2?'':'prohibit-btn'" @click="goPushPage(perBtn2?'2':'4')">
              <img src="@/assets/images/cgx.png" alt="">
              <div class="el-one-top-img-text">实时报告录入</div>
            </div>

            <div class="el-one-top-img pointer" :class="perBtn3?'':'prohibit-btn'" @click="goPushPage(perBtn3?'3':'4')">
              <img src="@/assets/images/wtcx.png" alt="">
              <div class="el-one-top-img-text">日常问题查询</div>
            </div>
          </div>

          <div class="flex-1">
            <div id="chooseTimeTip" class="chooseTimeTip width">
              <!-- <el-date-picker
                ref="chooseTimeTip"
                v-model="chooseTimeTip"
                type="date"
                popper-class="popperClass"
                :picker-options="customPickerOptions"
                placeholder="选择"
                @blur="chooseTimeTipBlur"
                @change="chooseTimeTipChange"
              /> -->

              <el-calendar v-model="chooseTimeTip">
                <template slot="dateCell" slot-scope="{ date, data }">
                  <p :class="data.isSelected ? 'is-selected' : ''" @click="goTimeList(data)">
                    <span
                      v-if="
                        ordinaryCalendarBlockData.timedOutDates.indexOf(
                          data.day
                        ) == -1 &&
                          ordinaryCalendarBlockData.noTimeoutDates.indexOf(
                            data.day
                          ) == -1
                      "
                      class="pu-time"
                    >{{ data.day.split("-")[2] }}</span>
                    <span
                      v-if="
                        ordinaryCalendarBlockData.timedOutDates.indexOf(
                          data.day
                        ) != -1
                      "
                      class="chaoshi-time"
                    >{{ data.day.split("-")[2] }}</span>
                    <span
                      v-if="
                        ordinaryCalendarBlockData.noTimeoutDates.indexOf(
                          data.day
                        ) != -1
                      "
                      class="weichaoshi-time"
                    >{{ data.day.split("-")[2] }}</span>
                  </p>
                </template>
              </el-calendar>

            </div>

            <div class="time-choose flex align-items-center">
              <div class="left-time-choose">图例：</div>
              <div class="flex align-items-center just-spc-between">
                <div class="flex align-items-center one-time-choose">
                  <div
                    class="one-time-choose-bg"
                    style="background-color: #ff4d4e"
                  />
                  <div class="one-time-choose-tip">选中</div>
                </div>

                <div class="flex align-items-center one-time-choose">
                  <div
                    v-if="ordinaryCalendarBlockData.timedOutDates"
                    class="one-time-choose-bg"
                    style="background-color: #f7e6e4; color: #ff4b3b"
                  >
                    {{ ordinaryCalendarBlockData.timedOutDates.length || "0" }}
                  </div>
                  <div
                    v-if="!ordinaryCalendarBlockData.timedOutDates"
                    class="one-time-choose-bg"
                    style="background-color: #f7e6e4; color: #ff4b3b"
                  >
                    {{ "0" }}
                  </div>
                  <div class="one-time-choose-tip">超时</div>
                </div>

                <div class="flex align-items-center one-time-choose">
                  <div
                    v-if="ordinaryCalendarBlockData.noTimeoutDates"
                    class="one-time-choose-bg"
                    style="background-color: #e5ebf7; color: #2e74ff"
                  >
                    {{ ordinaryCalendarBlockData.noTimeoutDates.length || "0" }}
                  </div>

                  <div
                    v-if="!ordinaryCalendarBlockData.noTimeoutDates"
                    class="one-time-choose-bg"
                    style="background-color: #e5ebf7; color: #2e74ff"
                  >
                    {{ "0" }}
                  </div>
                  <div class="one-time-choose-tip">未超时</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>

    <el-row style="padding-top: 4px">
      <el-col :span="6" class="height-380-px" style="padding-right: 4px">
        <BlockCard title="问题处理进度" class="height">
          <echarsHuan4 id="echars-huan-4" :chars-data="charsData" />
        </BlockCard>
      </el-col>

      <el-col
        :span="12"
        class="height-380-px"
        style="padding-left: 4px; padding-right: 4px"
      >
        <BlockCard title="全部问题" class="height">
          <el-scrollbar class="width height">
            <div class="el-process_con widht height">
              <div
                v-for="(item, index) of ordinaryAllProblemData"
                :key="index"
                class="el-process_con-one"
                @click="handleDetail(item)"
              >
                <div class="el-alignment-row flex align-items-center border-0">
                  <div class="el-left-point" />
                  <div
                    class="el-mid-content ovflowHidden flex-1"
                    :title="item.problemTitle"
                  >
                    {{ item.problemTitle }}
                  </div>

                  <div class="el-last-2-content">
                    <span class="icon iconfont" />
                    <span class="el-last-2-content-text">{{ item.name }}</span>
                  </div>
                  <div class="el-last-content">
                    <span class="icon iconfont" />
                    <span class="el-last-content-text">{{ item.time }}</span>
                  </div>
                </div>

                <div
                  class="
                    widht
                    el-process_con_block
                    flex
                    align-items-center
                    just-spc-between
                  "
                >
                  <div
                    v-for="(item2, index2) of item.violDailyFlowInfoList"
                    :key="index2"
                    class="el-one-process-diat flex align-items-center"
                  >
                    <div class="left-ciycle flex-dir-col-jus-cen-algin-center">
                      <div
                        class="
                          el-top-ciycle
                          margin-bottom-8-px
                          flex
                          align-items-center
                          just-con-cen
                        "
                        :class="elProcessStyle(item2)"
                      >
                        {{ index2 + 1 }}
                      </div>
                      <div class="el-peocess-bottom-text">
                        {{ item2.problemStatus }}
                      </div>
                    </div>

                    <div
                      class="el-process-points"
                      :class="elProcessStyle2(item2)"
                    />
                  </div>
                </div>
              </div>
            </div>
          </el-scrollbar>
        </BlockCard>
      </el-col>
    </el-row>
    <taskToDo
      v-if="selectValue"
      :key="index"
      ref="todo"
      tab-flag="1"
      :select-value="selectValue"
      @refresh="getList"
    />

    <TaskHasdone
      ref="todo2"
      tab-flag="3"
      :select-value="selectValue2"
      @refresh="getList"
    />

    <taskToRead
      ref="todo3"
      tab-flag="1"
      :select-value="selectValue3"
      @refresh="getList"
    />

    <taskRead ref="todo4" :select-value="selectValue4" @refresh="getList" />

    <el-dialog
      v-if="dialogVisible2"
      :title="dialogTitle"
      :visible.sync="dialogVisible2"
      width="90%"
      :append-to-body="true"
      :before-close="handleClose"
      :close-on-click-modal="false"
      class="no-header"
    >
      <oveTimequestionSearch :send-home-data="sendHomeData" />
    </el-dialog>

    <el-dialog
      v-if="dialogVisible"
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="90%"
      :append-to-body="true"
      :before-close="handleClose"
      :close-on-click-modal="false"
      class="no-header"
    >
      <questionSearch :send-home-data-base="sendHomeData" personal="personal" />
    </el-dialog>
    <!-- 全部问题详情 -->
    <el-dialog
      v-bind="$attrs"
      :visible.sync="visible"
      width="90%"
      :title="'日常问题-' + rows.problemTitle"
      append-to-body="true"
    >
      <Details
        v-if="rows"
        :key="rows.id"
        :select-value="rows"
        :active-name="activeName"
        :proc-ins-id="rows.procInsId"
      />
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="close">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import BlockCard from '@/components/BlockCard'
import ehcarsHuan from '@/components/echars/echars-huan'
import echarsHuan4 from '@/components/echars/echars-huan-4'
import TaskToDo from '@/views/workflow/tasklist/common/taskToDo'
import TaskHasdone from '@/views/workflow/tasklist/common/taskHasdone'
import TaskToRead from '@/views/workflow/tasklist/common/taskToRead'
import taskRead from '@/views/workflow/tasklist/common/taskRead'
import TrigTag from '@/components/trig-tag'
import oveTimequestionSearch from './components/oveTimequestionSearch'
import questionSearch from '@/views/home/<USER>/questionSearch'
import Details from '@/views/daily/details' // tree
import { Loading } from 'element-ui'
import pubSub from 'pubsub-js'
import {
  ordinaryTimeoutBlock,
  ordinaryCalendarBlock,
  problemStatusNumsList,
  ordinaryAllProblem
} from '@/api/personal/index'
import {
  taskToDo,
  taskHasDone,
  taskToRead,
  taskHasRead
} from '@/api/workflow/task'
import { getMonth } from '@/utils/index'
export default {
  components: {
    BlockCard,
    ehcarsHuan,
    echarsHuan4,
    TaskToDo,
    TaskHasdone,
    TaskToRead,
    taskRead,
    TrigTag,
    oveTimequestionSearch,
    questionSearch,
    Details
  },
  data() {
    return {
      changeTabsIndex: '0',
      chooseTimeTip: '',
      setClick: false,
      customDateArr: [],
      customDateArr2: [],
      ordinaryTimeoutBlockData: '',
      ordinaryCalendarBlockData: {
        timedOutDates: [],
        noTimeoutDates: []
      },
      ordinaryAllProblemData: '',
      problemStatusNumsListData: '',
      charsData: [],
      taskToDoData: [],
      taskHasDoneData: [],
      taskToReadData: [],
      taskHasReadData: [],
      params: {
        pageNum: 1,
        pageSize: 10
      },
      taskToTotal: 0,
      taskHasDoneTotal: 0,
      taskToReadTotal: 0,
      taskHasReadTotal: 0,
      selectValue: {},
      selectValue2: {},
      selectValue3: {},
      selectValue4: {},
      index: 0,
      calendarData1: [],
      calendarData2: [],
      loadingInstance: '',
      handleColor: {
        0: '#6AC9B4',
        1: '#6AC9B4',
        2: '#F3545C',
        3: '#909399',
        4: '#F8A334',
        5: '#80A2C9',
        6: '#54c4f3'
      },
      dialogTitle: '',
      dialogVisible2: false,
      dialogVisible: false,
      sendHomeData: '',
      visible: false, // 全部问题详情
      rows: {}, // 全部问题单行数据
      activeName: '0', // 全部问题
      perBtn1:false,//日常问题录入
      perBtn2:false,//实时报告录入
      perBtn3:false,//日常问题查询
    }
  },
  computed: {
    customPickerOptions() {
      const that = this
      return {
        cellClassName(Date) {
          if (that.customDateArr) {
            if (
              that.customDateArr.includes(
                that.$moment(Date).format('YYYY-MM-DD')
              )
            ) {
              return 'custom_date_class'
            }
          }
          if (that.customDateArr2) {
            if (
              that.customDateArr2.includes(
                that.$moment(Date).format('YYYY-MM-DD')
              )
            ) {
              return 'custom_date_class-2'
            }
          }
        }
      }
    },
    elProcessStyle() {
      return function(item) {
        if (item.startTime && item.endTime) {
          return 'el-process_style_color_2'
        }

        if (item.startTime && !item.endTime) {
          return 'el-process_style_color_1'
        }
      }
    },
    elProcessStyle2() {
      return function(item) {
        if (item.startTime && item.endTime) {
          return 'el-process-point_style_color_2'
        }

        if (item.startTime && !item.endTime) {
          return 'el-process-point_style_color_2'
        }
      }
    }
  },
  mounted() {
    // 接收echars订阅
    pubSub.subscribe('echars_huan-4', (msg, data) => {
      console.log(data)
      this.goAllList(data.type, data.element)
    })

    this.ordinaryTimeoutBlock()
    this.problemStatusNumsList()
    this.ordinaryAllProblem()
    this.taskToDoFun()
    this.taskHasDone()
    this.taskToRead()
    this.taskHasRead()
    this.btnLimitsAuthority()
    setTimeout(() => {
      // this.addEvent()
      this.ordinaryCalendarBlock(getMonth())
    }, 500)

    this.$nextTick(() => {
      // 点击前一个月
      const prevBtn = document.querySelector(
        '.el-calendar__button-group .el-button-group>button:nth-child(1)'
      )
      prevBtn.addEventListener('click', () => {
        var reg = /[\u4e00-\u9fa5]/g
        var str = document.querySelector('.el-calendar__title').innerHTML.replace(/[ ]/g, '')
        str = str.replace(reg, '')
        var year = str.substr(0, 5)
        var month = str.substr(5, str.length - 1)
        if (parseFloat(month) < 10) {
          month = '0' + month
        }
        this.ordinaryCalendarBlock(year + '' + month)
      })

      const SecondBtn = document.querySelector(
        '.el-calendar__button-group .el-button-group>button:nth-child(2)'
      )

      SecondBtn.addEventListener('click', () => {
        var reg = /[\u4e00-\u9fa5]/g
        var str = document.querySelector('.el-calendar__title').innerHTML.replace(/[ ]/g, '')
        str = str.replace(reg, '')
        var year = str.substr(0, 5)
        var month = str.substr(5, str.length - 1)
        if (parseFloat(month) < 10) {
          month = '0' + month
        }
        this.ordinaryCalendarBlock(year + '' + month)
      })
      // const dayBtn = document.querySelector(
      //   '.el-calendar__button-group .el-button-group>button:nth-child(2)'
      // )
      // dayBtn.addEventListener('click', () => {
      //   this.judgeDate()
      // })
      const nextBtn = document.querySelector(
        '.el-calendar__button-group .el-button-group>button:nth-child(3)'
      )
      nextBtn.addEventListener('click', () => {
        var reg = /[\u4e00-\u9fa5]/g
        var str = document.querySelector('.el-calendar__title').innerHTML.replace(/[ ]/g, '')
        str = str.replace(reg, '')
        var year = str.substr(0, 5)
        var month = str.substr(5, str.length - 1)
        if (parseFloat(month) < 10) {
          month = '0' + month
        }
        if (month.length === 1) {
          month = '0' + month
        }
        this.ordinaryCalendarBlock(year + '' + month)
      })
    })
  },
  methods: {
    //按钮权限
    btnLimitsAuthority(){

      const arr = this.$store.state.permission.routes
      this.perBtn1 = this.hasValueInNestedArray(arr,'daily/enter')//日常问题录入
      this.perBtn2 = this.hasValueInNestedArray(arr,'actual/actualQuestionDrafts')//实时报告录入
      this.perBtn3 = this.hasValueInNestedArray(arr,'daily')//日常问题查询
    },
    handleClose() {
      this.dialogVisible2 = false
      this.dialogVisible = false
    },
    // 超时记录，超时明细
    goTimeList(data) {
      this.sendHomeData = {}
      this.sendHomeData = data.day
      this.dialogTitle = '超时列表'
      this.dialogVisible2 = true
    },
    // 累计超时
    goOverTimeList() {
      this.sendHomeData = ''
      this.dialogTitle = '超时列表'
      this.dialogVisible2 = true
    },
    // 全部问题
    goAllList(type, data) {
      console.log(this.sendHomeData)
      this.sendHomeData = {}
      if (type == 'wtcljd') {
        this.sendHomeData.status = data.status || ''
      }
      this.dialogTitle = '日常问题查询'
      this.dialogVisible = true
    },
    // 日常问题录入
    goPushPage(type) {
      if (type == '1') {
        this.$router.push({ path: '/daily/daily/enter' })
      }
      if (type == '2') {
        this.$router.push({ path: '/actual/actual/actualQuestionDrafts' })
      }
      if (type == '3') {
        this.$router.push({ path: '/daily/daily' })
      }
      if (type == '4') {//无跳转

      }
    },

    hasValueInNestedArray(arr,value) {
      return  arr.some(item =>Array.isArray(item.children) ? this.hasValueInNestedArray(item.children, value) : item.path == value);

    },
    openLoading() {
      this.loadingInstance = Loading.service({
        target: document.querySelector('#tableDataDom'),
        spinner: 'el-icon-loading', // 自定义加载图标类名
        text: '正在加载...', // 显示在加载图标下方的加载文案
        lock: false // lock的修改符--默认是false
      })
      return this.loadingInstance
    },
    chooseTimeTipChange() {
      // this.$refs.chooseTimeTip.focus()
    },
    chooseTimeTipBlur() {
      // this.$refs.chooseTimeTip.focus()
    },
    changeTabs(type) {
      if (this.loadingInstance) {
        this.loadingInstance.close()
      }
      this.changeTabsIndex = type
      this.params.pageNum = 1
      this.openLoading()
      if (type == '0') {
        this.taskToDoFun()
      }
      if (type == '1') {
        this.taskHasDone()
      }
      if (type == '2') {
        this.taskToRead()
      }
      if (type == '3') {
        this.taskHasRead()
      }
      setTimeout(() => {
        this.loadingInstance.close()
      }, 10000)
    },
    taskToDoFun() {
      taskToDo(this.params, {}).then((response) => {
        const { code, rows, total } = response
        if (code == '200') {
          this.taskToDoData = rows
          this.taskToTotal = total
          this.$forceUpdate()
        }
        this.$nextTick(() => { // 以服务的方式调用的 Loading 需要异步关闭
          this.loadingInstance.close()
        })
      })
    },
    taskHasDone() {
      taskHasDone(this.params, {}).then((response) => {
        const { code, rows, total } = response
        if (code == '200') {
          this.taskHasDoneData = rows
          this.taskHasDoneTotal = total
          this.$forceUpdate()
        }
        this.$nextTick(() => { // 以服务的方式调用的 Loading 需要异步关闭
          this.loadingInstance.close()
        })
      })
    },
    taskToRead() {
      taskToRead(this.params, {}).then((response) => {
        const { code, rows, total } = response
        if (code == '200') {
          this.taskToReadData = rows
          this.taskToReadTotal = total
          this.$forceUpdate()
        }

        this.$nextTick(() => { // 以服务的方式调用的 Loading 需要异步关闭
          this.loadingInstance.close()
        })
      })
    },
    taskHasRead() {
      taskHasRead(this.params, {}).then((response) => {
        const { code, rows, total } = response
        if (code == '200') {
          this.taskHasReadData = rows
          this.taskHasReadTotal = total
          this.$forceUpdate()
        }
        this.$nextTick(() => { // 以服务的方式调用的 Loading 需要异步关闭
          this.loadingInstance.close()
        })
      })
    },
    checkTaskorg(item) {
      // this.selectValue = item;
      // this.$nextTick(() => {
      //   this.$refs.todo.show();
      // });
      const depts = this.$store.getters.depts
      /*if (item.taskId != item.taskIdVar) {
        this.$modal.msgError(
          '待办异常，请把此问题提交至沃运营平台，支撑顾问会尽快受理您的问题。'
        )
      } else */
        if (item.performerOu !== this.$store.getters.dept.deptId) {
        for (let i = 0; i < depts.length; i++) {
          if (depts[i].deptId == item.performerOu) {
            this.$modal.msgError(
              '当前任务属于【' + depts[i].orgName + '】，请切换岗位后查看'
            )
          }
        }
      } else {
        this.selectValue = item
        this.index++
        this.$nextTick(() => {
          this.$refs.todo.show()
        })
      }
    },

    checkTaskorg2(item) {
      this.selectValue2 = item
      this.$nextTick(() => {
        this.$refs.todo2.show()
      })
    },

    checkTaskorg3(item) {
      this.selectValue3 = item
      this.$nextTick(() => {
        this.$refs.todo3.show()
      })
    },
    checkTaskorg4(item) {
      console.log(item)
      this.selectValue4 = item
      this.$nextTick(() => {
        this.$refs.todo4.show()
      })
    },
    getList(val) {
      if (val) {
        this.params.pageNum = val.page
        this.openLoading()
      }
      if (this.changeTabsIndex == '0') {
        this.taskToDoFun()
      }
      if (this.changeTabsIndex == '1') {
        this.taskHasDone()
      }
      if (this.changeTabsIndex == '2') {
        this.taskToRead()
      }
      if (this.changeTabsIndex == '3') {
        this.taskHasRead()
      }
      setTimeout(() => {
        this.loadingInstance.close()
      }, 10000)
    },
    async addEvent() {
      await this.$nextTick()
      if (this.setClick) return
      document
        .querySelector('.el-month-table')
        .addEventListener('click', () => {
          this.monthChange()
        })
      document
        .querySelectorAll(
          "[aria-label='下个月'],[aria-label='上个月'],[aria-label='后一年'],[aria-label='前一年']"
        )
        .forEach((item) =>
          item.addEventListener('click', () => {
            this.monthChange()
          })
        )
      this.setClick = true
    },
    monthChange() {
      this.customDateArr = []
      this.customDateArr2 = []
      var year = parseInt(
        document.querySelector('.el-date-picker__header-label').innerHTML
      )
      var month = parseInt(
        document.querySelector("[aria-label='后一年']").previousElementSibling
          .innerHTML
      )
      if (month < 10) {
        month = '0' + month
      }

      setTimeout(() => {
        // this.addEvent()
        this.ordinaryCalendarBlock(year + '' + month)
      }, 500)
    },

    async initCustomDate(Date) {
      setTimeout(() => {
        this.customDateArr = this.ordinaryCalendarBlockData.timedOutDates
        this.customDateArr2 = this.ordinaryCalendarBlockData.noTimeoutDates
      }, 100)
    },
    ordinaryTimeoutBlock() {
      ordinaryTimeoutBlock({}).then((response) => {
        const { code, data } = response
        if (code == '200') {
          this.ordinaryTimeoutBlockData = data
        }
      })
    },
    ordinaryCalendarBlock(times) {
      this.ordinaryCalendarBlockData.timedOutDates = []
      this.ordinaryCalendarBlockData.noTimeoutDates = []
      ordinaryCalendarBlock(times).then((response) => {
        const { code, data } = response
        if (code == '200') {
          if (data.timedOutDates) {
            data.timedOutDates.forEach(item => {
              this.ordinaryCalendarBlockData.timedOutDates.push(item)
            })
          }
          if (data.noTimeoutDates) {
            data.noTimeoutDates.forEach(item => {
              this.ordinaryCalendarBlockData.noTimeoutDates.push(item)
            })
          }
          this.$forceUpdate()
        }
      })
    },
    problemStatusNumsList() {
      problemStatusNumsList({}).then((response) => {
        const { code, data } = response
        if (code == '200') {
          this.problemStatusNumsListData = data
          var sum = 0
          for (var i = 0; i < data.length; i++) {
            sum += Number(data[i].value)
          }
          this.charsData = []
          if (
            this.problemStatusNumsListData &&
            this.problemStatusNumsListData.length > 0
          ) {
            this.problemStatusNumsListData.forEach((item) => {
              this.charsData.push({
                name: item.name,
                value: item.value,
                status: item.status,
                percent: (Number(data[0].value) / Number(sum)) * 100 || '0.00'
              })
            })
          }
        }
      })
    },
    ordinaryAllProblem() {
      ordinaryAllProblem({}).then((response) => {
        const { code, data } = response
        if (code == '200') {
          if (data && data.length > 0) {
            data.forEach((item) => {
              if (
                item.violDailyFlowInfoList &&
                item.violDailyFlowInfoList.length > 0
              ) {
                const violDailyFlowInfoList = item.violDailyFlowInfoList
                violDailyFlowInfoList.forEach((item2) => {
                  if (item2.startTime && !item.endTime) {
                    item.name = item2.userName
                    item.time = item2.startTime
                  }
                })
              }
            })
            this.ordinaryAllProblemData = data
          }
        }
      })
    },
    // 查看全部问题详情
    handleDetail(row) {
      this.rows = row
      this.visible = true
    }
  }
}
</script>

<style lang="scss" scoped>
.popperClass {
  top: 200px !important;
  left: 1520px !important;
  width: 360px !important;
  z-index: 200 !important;
  display: block !important;
  .el-picker-panel__content {
    width: 338px !important;
  }
}
</style>
<style scoped lang="scss">
.home-container{
  padding-top:8px;
}
.height-420-px {
  height: 490px;
}
.height-380-px {
  height: 380px;
}
::v-deep .public-box {
  background-color: transparent;
  border-radius: 4px;
  box-shadow: 0px 0px 10px 0px rgb(155 11 9 / 10%);
  position: relative;
  .public-box-content {
    height: calc(100% - 45px) !important;
    box-sizing: border-box !important;
  }
}
.el-title-right-num {
  position: absolute;
  top: 12px;
  right: 20px;
  font-size: 16px;
  font-family: Microsoft YaHei;
  font-weight: bold;
  color: #181818;
}
.time-choose {
  margin-top: 8px;
  margin-left: 46px;
}
.height-95-px {
  height: 95px;
}
.margin-top-8-px {
  margin-top: 8px;
}
.margin-bottom-8-px {
  margin-bottom: 8px;
}
.margin-top-12-px {
  margin-top: 12px;
}

.height-surplus-50-px {
  height: calc(100% - 50px);
}
.height-surplus-107-px {
  height: calc(100% - 107px);
}
.height-surplus-18-px {
  height: calc(100% - 18px);
}
::v-deep .el-calendar-table td.is-today{
  color: #676a6c!important;
}
::v-deep .el-common-card-content .pagination-container {
  margin: 0px !important;
  padding: 0px !important;
  display: flex;
  align-items: center;
  justify-content: center;
}
::v-deep .el-pagination {
  text-align: center;
  right: auto !important;
}

::v-deep .el-date-editor .el-input__icon {
  opacity: 0;
}

::v-deep .el-scrollbar__wrap {
  overflow-x: hidden;
  padding-right: 15px;
}
::v-deep .el-calendar__header {
  padding: 6px 20px;
  position: relative;
  top: 10px;
}
::v-deep .el-calendar-table .el-calendar-day {
  height: 40px;
  font-size: 14px;
}
::v-deep .el-calendar__body {
  padding: 12px 20px 5px;
  .el-calendar-day {
    p {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 100%;
    }
  }
  .is-selected {
    span.pu-time {
      width: 15px;
      height: 15px;
      border-radius: 5px;
      background-color: rgb(255, 77, 78);
      color: #fff;
      padding: 10px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
  span.chaoshi-time {
    width: 15px;
    height: 15px;
    border-radius: 5px;
    background-color: rgb(247, 230, 228);
    color: rgb(255, 75, 59);
    padding: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  span.weichaoshi-time {
    width: 15px;
    height: 15px;
    border-radius: 5px;
    background-color: rgb(229, 235, 247);
    color: rgb(46, 116, 255);
    padding: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
.no-header {
  ::v-deep .public-box-content{
     height: 772px!important;
  }
}

@media screen and (max-width: 1400px) {
  .public-box-element {
    padding: 0px 10px;
  }
  .el-common-header {
    padding: 0px 10px;
  }
  .flex-dir-col-jus-cen-style-1 {
    padding:8px 10px!important;
  }
  .el-one-top-img img {
    width: 50px;
    height: 50px;
  }
   ::v-deep .el-calendar__header{
     padding: 6px 5px;
     font-size: 12px;
  }
  ::v-deep .el-button-group button {
    padding: 7px 10px!important;
  }
   ::v-deep .el-calendar__body {
        padding: 12px 5px 5px;
  }
  .time-choose {
    margin-left: 10px;
  }
 ::v-deep .public-box .public-box-element {
    padding:0px 12px!important;
  }
  .el-process-points {
    margin-left: 10px;
  }
  .el-process_con_block {
    padding: 8px 5px;
  }
}
</style>
